import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 工作站任务量统计
 */
export default class StatJobSumCountBar extends Chart {
  /**
   * 初始化图表 - 工作站任务量统计
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('bar', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "工作站任务量统计";
    this.stationList = [];
    
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'elDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        option: {
          type: "datetimerange"
        }
      },
      {
        label: '工作站',
        prop: 'stationIds',
        type: 'elSelect',
        defaultValue: [],
        optionList: [],
        option: {
          multiple: true
        }
      }
    ]

  }

  async getStationList() {
    const { data } = await requestCache("/athena/station/findAll");
    const propItem = this.filterList.find(item => item.prop === 'stationIds');
    this.stationList = (data || []).map(item => {
      return {
        label: item.id,
        value: item.id
      }
    })

    const stationIdList = (this.stationList || []).map(item => item.value);
    propItem.optionList = this.stationList;
    propItem.defaultValue = stationIdList.splice(0,10)
  }

  // 数据准备
  async preDataReady() {
    await this.getStationList();
  }

  async request(params = {}) {
    const date = params?.date || [];
    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 7;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);
    const stationIdList = (this.stationList || []).map(item => item.value);
    
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stat/job/sumCount', {
      startTime,
      endTime,
      statType: 'JOB_COUNT',
      timeRange: 5,
      stationIds: params.stationIds || stationIdList.splice(0,10)
    })

    return data;
  }
  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const xAxisData =  data.xAxis || {};
    const type2Data = data.type2Data || {};
    const { canceledCount, completedCount } = type2Data;
    const { tooltipFormatterToHtml } = this
    // const 
    return parseEchartOption({
      title: { text: this.title || '' },
      xAxis: { type: 'category', data: xAxisData },
      yAxis: { type: 'value' },
      grid: { left: 30, right: 30, bottom: 30 },
      legend: {},
      tooltip: {
        show: true,
        trigger: 'axis',
        formatter(params, ticket, callback) {
          const axisValue = params[0].axisValue
          return tooltipFormatterToHtml(`工作站: ${axisValue}`, params)
        },
      },
      series: [
        {
          name: '已完成',
          type: 'bar',
          stack: 'Ad',
          data: completedCount || []
        },
        {
          name: '已取消',
          type: 'bar',
          stack: 'Ad',
          data: canceledCount || []
        }
      ]
    })
  }
}