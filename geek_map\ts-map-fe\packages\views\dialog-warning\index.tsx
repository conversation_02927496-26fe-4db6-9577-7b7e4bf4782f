/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Modal, Collapse } from "antd";

const { Panel } = Collapse;
function DialogWarning() {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState([]);

  useEffect(() => {
    _$utils.reqGet("/athena/warehouse/monitor/getSystemStartupAbnormal").then(res => {
      const { code, data } = res || {};
      if (code !== 0 || !data || data.length <= 0) return;
      setList(data);
      setVisible(true);
    });
  }, []);

  const handleOk = () => {
    _$utils.reqPost("/athena/warehouse/monitor/confirmSystemStartupAbnormal").then(res => {
      if (res.code !== 0) return;
      setVisible(false);
    });
  };

  return (
    <Modal
      maskClosable={false}
      closable={false}
      keyboard={false}
      open={visible}
      onOk={handleOk}
      destroyOnClose
      width="50%"
      wrapClassName="map2d-warning-dialog"
    >
      <Collapse defaultActiveKey={[0]} accordion className="warning-dialog-list">
        {list.map((item, index) => {
          return (
            <Panel key={index} header={t(item.category)}>
              {item.errorList.map((error: any, j: any) => {
                return (
                  <p key={j}>
                    <strong style={{ fontSize: 13, fontWeight: 600 }}>{t(error.object)}: </strong>
                    <label style={{ fontSize: 13, fontWeight: 600 }}>{t(error.exception)}, </label>
                    <span>{t(error.solution)}</span>
                  </p>
                );
              })}
            </Panel>
          );
        })}
      </Collapse>
    </Modal>
  );
}

export default DialogWarning;
