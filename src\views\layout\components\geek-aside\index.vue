<template>
  <el-aside class="geek-aside" :class="{ 'is-open': sidebarCollapse }">
    <span class="collapse-btn" @click.stop="$emit('handleSidebarStatus')" @click.prevent />

    <h1 class="title">
      <img v-if="isShowGeekIcon" src="@imgs/common/logo.png" />
      <span class="text">RMS</span>
    </h1>

    <div class="aside-menu-container">
      <geek-aside-menu :sidebarCollapse="sidebarCollapse" />
    </div>
  </el-aside>
</template>

<script>
import { mapState } from "vuex";
import GeekAsideMenu from "./geek-aside-menu";

export default {
  name: "GeekAside",
  components: { GeekAsideMenu },
  props: ["isShowGeekIcon", "sidebarCollapse", "sidebarHidden"],
  computed: {
    ...mapState(["menuList"]),
  },
};
</script>

<style lang="less" scoped>
@title-height: @g-header-height;
@aside-max-width: @g-slide-max-width; //240px
@aside-min-width: @g-slide-min-width; //36px

@aside-bg: @g-aside-bg;
@title-bg: @g-nav-bg;
.geek-aside {
  position: relative;
  height: calc(100%);
  width: @aside-max-width !important;
  flex: 0 0 @aside-max-width !important;
  background: @aside-bg;
  overflow: hidden;
  .g-box-shadow-no-top(rgba(0, 0, 0));
  z-index: 9;
  .title {
    .g-flex();
    justify-content: flex-start;
    padding: 3px 0 0 12px;
    height: @title-height;
    .g-box-shadow-bottom(@title-bg);

    img {
      width: 90px;
      flex: 0 0 90px;
      margin-right: 16px;
    }
    .text {
      font-weight: 600;
      font-size: 22px;
      color: #2194d3;
      flex: 1;
    }
  }
  .collapse-btn {
    position: absolute;
    right: 0;
    top: 3px;
    height: 40px;
    width: 38px;
    background: url(~@imgs/layout/icon-collapse-bai.png) no-repeat 55% 50%;
    background-size: 20px;
    transform: rotate(0deg);
    transition: 0.38s;
    transform-origin: 50% 50%;
    cursor: pointer;
    z-index: 9;
  }

  .aside-menu-container {
    height: calc(100% - @title-height - 10px);
    overflow: auto;
    margin-right: -2px;
    &::-webkit-scrollbar {
      width: 2px;
    }
  }
}

.geek-aside.is-open {
  width: @aside-min-width !important;
  flex: 0 0 @aside-min-width !important;
  padding-top: @title-height;
  .title {
    display: none;
  }
  .collapse-btn {
    transform: rotate(90deg);
  }
}
</style>
