import Chart, { requestCache } from "../common";

/**
 * 2.1.4执行中任务  废弃, 接口数据不支持
 */
export default class TaskExecutedCred extends Chart {
  /**
   * 初始化图表 - 2.1.4执行中任务
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('cred', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "执行中任务";
  }

  async request(params) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stats/query/job/dashboard/count/', {
      date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
      showCount: true,
      showCountGroup: true,
      ...params
    })
    // 需要请求  执行中的任务数和已经下发的任务数
    const jobSplitCountData = await requestCache('/athena/stats/query/job/split/count/', {
      date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
      cycle: "60",
      showReceive: true,
      showToStationDone: true,
      showOrgData: false,
      ...params
    })
    const allJobStageListCount = jobSplitCountData.data.countMap.allJobStageListCount;
    const doneJobCount = data.countMap.doneJobCount;
    return { allJobStageListCount, doneJobCount };
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    return {
      id: 'doneJobCount',
      title: this.title || '',
      number: data.allJobStageListCount - data.doneJobCount || 0,
      color: "#8543e0",
    }
  }
}
