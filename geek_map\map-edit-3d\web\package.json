{"name": "map-edit-3d-example", "version": "0.1.0", "description": "example", "main": "index.js", "scripts": {"__webpack:dev": "webpack serve --config ./build/webpack.dev.config.js --color", "dev": "cross-env CONF_ENV=dev  npm run __webpack:dev"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@babel/core": "^7.19.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.19.1", "@babel/preset-env": "^7.19.1", "@babel/runtime": "^7.12.5", "babel-loader": "^8.2.5", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.1", "eslint": "^8.24.0", "eslint-config-prettier": "^8.5.0", "eslint-loader": "^4.0.2", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.5.1", "html-webpack-plugin": "^5.5.0", "less": "^4.1.3", "less-loader": "^11.0.0", "mini-css-extract-plugin": "^2.6.1", "postcss-loader": "^7.0.1", "prettier": "^2.7.1", "style-loader": "^3.3.1", "vue-loader": "^15.10.0", "vue-template-compiler": "^2.6.12", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "^4.11.1", "webpack-merge": "^5.8.0"}, "dependencies": {"vue": "^2.6.12"}}