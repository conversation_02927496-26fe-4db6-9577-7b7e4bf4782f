<template>
  <section>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-return="onReturn" />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @selectionChange="handleSelection"
      @page-change="pageChange"
      style="margin-top: 10px"
    >
      <template #operations="{ row }">
        <div v-if="row.boxStatus === 'POSITION_CONFIRMING'">
          <el-button type="primary" size="mini" class="btn-opt" @click="itemSave(row)">
            {{ $t("lang.rms.fed.confirm") }}
          </el-button>
          <el-button type="primary" size="mini" class="btn-opt" @click="itemChange(row)">
            {{ $t("lang.rms.box.changeBtn") }}
          </el-button>
        </div>
      </template>
    </geek-customize-table>

    <edit-dialog ref="editDialog" @updateTableList="getTableList" />
  </section>
</template>

<script>
import EditDialog from "./components/editDialog";

export default {
  name: "PackQueryIndex",
  components: { EditDialog },
  data() {
    return {
      // 搜索条件
      form: {
        robotId: "",
        boxStatus: "",
        boxCode: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          robotId: {
            label: "lang.rms.fed.inputRobotId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          boxStatus: {
            label: "lang.rms.box.boxStatus",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.wholeStatus",
              },
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
            ],
          },
          boxCode: {
            label: "lang.rms.box.boxCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.box.returnAllLoadedBox",
            handler: "on-return",
            type: "success",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {
          selection: true,
          index: true,
          "row-key": "boxCode",
          "reserve-selection": true,
        },
        columns: [
          {
            label: "lang.rms.box.boxCode",
            prop: "boxCode",
          },
          {
            label: "lang.rms.box.boxStatus",
            prop: "boxStatusCode",
            formatter: (row, column, cellValue, index) => {
              return this.$t(cellValue);
            },
          },
          { label: "lang.rms.fed.listRobotId", prop: "robotId", width: "100" },
          { label: "lang.rms.box.robotLayer", prop: "robotLayer", width: "80" },
          { label: "lang.rms.box.placeLatticeCode", prop: "placeLatticeCode", width: "180" },
          {
            label: "lang.rms.box.boxPlaceRackCode",
            prop: "location",
            formatter: (row, column, cellValue, index) => {
              if (!cellValue) return "--";
              return `X:${cellValue.x},Y:${cellValue.y}`;
            },
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "170",
            slotName: "operations",
          },
        ],
      },
      selectList: [], // 可进行一键还箱
    };
  },
  computed: {
    returnArr() {
      let arr = [];
      this.tableData.forEach(item => {
        if (item.boxStatus === "LOADED") {
          arr.push(item.boxCode);
        }
      });
      return arr;
    },
  },
  watch: {
    returnArr(v) {
      let formConfig = this.formConfig;
      formConfig.operations[1].disabled = v.length === 0;
      this.formConfig = Object.assign({}, formConfig);
    },
  },
  activated() {
    this.getTableList();
    this.getBoxStatus();
  },
  methods: {
    itemSave(row) {
      $req.post("/athena/box/confirmBoxStatus", { boxCode: row.boxCode }).then(res => {
        if (res.code === 0) {
          this.$success(this.$t("lang.common.success"));
          this.getTableList();
        }
      });
    },
    itemChange(row) {
      this.$refs.editDialog.open(row);
    },
    handleSelection(selection) {
      this.selectList = selection.map(i => i.boxCode);
    },
    // 归还
    async onReturn() {
      if (this.selectList && this.selectList.length) {
        const title = this.$t("lang.rms.fed.region");
        const tips = this.$t("lang.rms.fed.containerGoReturnArea");
        const that = this;
        this.$prompt(title, tips, {
          closeOnClickModal: false,
          inputValidator(val) {
            if (!val) return true;
            if (!/^[+]{0,1}(\d+)$/.test(val)) return that.$t("lang.rms.fed.pleaseEnterAnNumber");
            return true;
          },
          async beforeClose(action, instance, done) {
            if (action !== "confirm") return done();
            instance.confirmButtonLoading = true;
            const areaId = instance.inputValue || "";
            try {
              const { code, msg } = await $req.post("/athena/box/returnAllLoadedBox", {
                boxCodes: that.selectList,
                areaId,
              });
              instance.confirmButtonLoading = false;
              if (!code) {
                that.$message.success(that.$t("lang.common.success"));
                done();
                that.getTableList();
              }
            } catch (e) {
              instance.confirmButtonLoading = false;
            }
          },
        });
      }
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getBoxStatus() {
      $req.get("/athena/box/listBoxStatus").then(res => {
        let list = res.data || [];
        this.formConfig.configs.boxStatus.options = list.map(item => {
          return {
            value: item.status,
            label: this.$t(item.statusCode),
          };
        });
      });
    },
    getTableList() {
      const params = {
        robotId: this.form.robotId || null,
        boxStatus: this.form.boxStatus || null,
        boxCode: this.form.boxCode || null,
        page: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      $req.get("/athena/box/list", params).then(res => {
        let result = res.data;
        if (!result) return;
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          pageCount: result.pageCount || 0,
        });
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
