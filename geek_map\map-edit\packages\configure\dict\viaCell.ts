/**
 * 表示空载和负载
 */
 export const VIA_CELL_DICT_BOTH = "BOTH";
 /**
  * 表示负载
  */
 export const VIA_CELL_DICT_LOAD = "LOAD";
 /**
 * 表示空载
 */
 export const VIA_CELL_DICT_UNLOAD = "UNLOAD";

 
 export const VIA_CELL_BOTH_DICT = [
   // 空载和负载
   {
     label: "空载和负载",
     value: VIA_CELL_DICT_BOTH,
   },
   // 负载状态
   {
     label: "lang.rms.api.result.edit.map.loadStatus",
     value: VIA_CELL_DICT_LOAD,
   },
   // 空载状态
   {
     label: "lang.rms.api.result.edit.map.unloadStatus",
     value: VIA_CELL_DICT_UNLOAD,
   },
 ];
 
export const VIA_DIRECTION_DICT_BOTH = "BOTH";
export const VIA_DIRECTION_DICT_ENTER = "ENTER";
export const VIA_DIRECTION_DICT_LEAVE = "LEAVE";
export const VIA_CELL_DIRECTION_DICT = [
  {
    label: "BOTH",
    value: VIA_DIRECTION_DICT_BOTH,
  },
  {
    label: "ENTER",
    value: VIA_DIRECTION_DICT_ENTER,
  },
  {
    label: "LEAVE",
    value: VIA_DIRECTION_DICT_LEAVE,
  },
];