import { NodeAttrEditConf } from "@packages/type/editUiType";
import { useAttrStore } from "@packages/store/attr";
import { BASE_CONF_FN } from "./fromData/cellBaseConf";
import { LOCATION_CONF_FN } from "./fromData/cellLocationConf";
import { FUNCTION_CONF_FN } from "./fromData/cellFunctionConf";
import { MODEL_CONF_FN } from "./fromData/cellModelConf";
import { STATION_CONF_FN } from "./fromData/deviceStationConf";
import { CHARGER_CONF_FN } from "./fromData/deviceChargerConf";
import { LINE_BASE_CONF_FN } from "./fromData/lineBaseConf";
import { LINE_CONTROL_CONF_FN } from "./fromData/lineControlConf";
import { AREA_BASE_CONF_FN } from "./fromData/areaBaseConf";
import { MAKER_BASE_CONF_FN } from "./fromData/deviceMakerBaseConf";
import { MAKER_LOCAL_CONF_FN } from "./fromData/deviceMakerLocalConf";
import { ELEVATOR_CONF_FN } from "./fromData/deviceElevatorConf";
//安全设备
import {SAFE_BASE_CONF_FN} from "./fromData/deviceSafeBaseConf"
// import {SAFE_LOCAL_CONF_FN} from "./fromData/deviceSafeLocalConf"

/**
 * 单元格from数据
 * @param baseTabsRef
 * @returns
 */
export const getCellTabConf = (baseTabsRef: any): NodeAttrEditConf[] => {
  const attrStore = useAttrStore();
  return [
    BASE_CONF_FN(baseTabsRef, attrStore),
    LOCATION_CONF_FN(baseTabsRef, attrStore),
    FUNCTION_CONF_FN(baseTabsRef, attrStore),
    MODEL_CONF_FN(baseTabsRef, attrStore),
  ];
};

/**
 * 线段from数据
 * @param baseTabsRef
 * @returns
 */
export const getLineTabConf = (baseTabsRef: any): NodeAttrEditConf[] => {
  const attrStore = useAttrStore();
  return [LINE_BASE_CONF_FN(baseTabsRef, attrStore), LINE_CONTROL_CONF_FN(baseTabsRef, attrStore)];
};

/**
 * 区域from数据
 * @param baseTabsRef
 * @returns
 */
export const getAreaTabConf = (baseTabsRef: any): NodeAttrEditConf[] => {
  const attrStore = useAttrStore();
  return [AREA_BASE_CONF_FN(baseTabsRef, attrStore)];
};

/**
 * 设备from数据
 */
export const getDeviceTabConf = (baseTabsRef: any): NodeAttrEditConf[] => {
  const attrStore = useAttrStore();
  return [
    STATION_CONF_FN(baseTabsRef, attrStore),
    CHARGER_CONF_FN(baseTabsRef, attrStore),
    MAKER_BASE_CONF_FN(baseTabsRef, attrStore),
    MAKER_LOCAL_CONF_FN(baseTabsRef, attrStore),
    ELEVATOR_CONF_FN(baseTabsRef, attrStore),
    SAFE_BASE_CONF_FN(baseTabsRef, attrStore),
    // SAFE_LOCAL_CONF_FN(baseTabsRef, attrStore)
  ];
};
