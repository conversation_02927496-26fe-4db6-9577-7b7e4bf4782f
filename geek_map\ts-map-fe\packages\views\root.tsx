/* ! <AUTHOR> at 2022/08/31 */
import i18nLang from "../lang/i18n";
import { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { ConfigProvider, Spin, Modal } from "antd";
import { createMap2D, getMap2D, $eventBus, $audio } from "../singleton";

import PanelTop from "./panel-top";
import PanelRight from "./panel-right";
import BoxWarning from "./box-warning";
import DialogWarning from "./dialog-warning";
import DialogHelp from "./dialog-help";
import DialogMessageCount from "./dialog-message-count";
import PanelDeviceWarning from "./panel-device-warning";
import PopStation from "./pop-station";
import WarnAudio from "./warn-audio";

const { confirm } = Modal;
const defaultLang = i18nLang.getAntdLang();
type PropsRoot = {
  hideRightPanel: boolean; //
  isIframe: string; // "1"
};
function Root(props: PropsRoot) {
  const { t } = useTranslation();
  const [ready, setReady] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);
  const [local, setLocal] = useState(defaultLang);
  const [messageCount, setMessageCount] = useState(null);
  const [mapConfig, setMapConfig] = useState({});
  const [logicAreas, setLogicAreas] = useState([]);
  const [speedLimitAreas, setSpeedLimitAreas] = useState([]);
  const [mapReleased, setMapReleased] = useState(true);
  const [cardMsgId, setCardMsgId] = useState(null);
  const [showWarnAudio, setShowWarnAudio] = useState(false);
  const refMapBox = useRef();

  // 数据 驱动地图加载loading
  useEffect(() => {
    $eventBus.on("wsMapLoading", data => {
      setLoading(data.loading);
      setReady(true);
    });
    $eventBus.on("wsMessageCount", data => {
      // const { notificationCount, backLogCount } = data;
      setMessageCount(data);
    });
    $eventBus.on("wsMapConfigRight", data => {
      if (!data) return;
      setMapConfig(Object.assign({}, mapConfig, data));
    });
    $eventBus.on("wsDataLogicAreas", wsData => {
      setLogicAreas(wsData);
    });

    $eventBus.on("wsDataSpeedLimitAreas", wsData => {
      setSpeedLimitAreas(wsData);
    });
    $eventBus.on("wsMapReleased", wsData => {
      setMapReleased(wsData.mapReleased);
    });

    $eventBus.on("mapClickRealtimeObstacle", data => {
      let warningText = data.code.toString() + t("lang.rms.fed.signalLoss");
      confirm({
        icon: <ExclamationCircleOutlined />,
        content: warningText,
        onOk() {
          const map2D = getMap2D();
          const reqMsg = "RealtimeObstacleInstructionRequestMsg";
          const resMsg = "RealtimeObstacleInstructionResponseMsg";
          map2D.mapWorker
            .reqSocket(reqMsg, { tagId: data.code, instruction: "REMOVE_OBSTACLE" })
            .then(res => {
              if (res.msgType !== resMsg) return;
              _$utils.wsCmdResponse(res?.body || {});
            });
        },
      });
    });

    $eventBus.on("mapClickKnockArea", data => {
      let warningText = t("lang.rms.config.robot.path.cancelKnockAreaStop");
      confirm({
        icon: <ExclamationCircleOutlined />,
        content: warningText,
        onOk() {
          const map2D = getMap2D();
          const reqMsg = "WarehouseInstructionRequestMsg";
          const resMsg = "WarehouseInstructionResponseMsg";
          map2D.mapWorker
            .reqSocket(reqMsg, { areaId: data.code, instruction: "RESUME_KNOCK_AREA" })
            .then(res => {
              if (res.msgType !== resMsg) return;
              _$utils.wsCmdResponse(res?.body || {});
            });
        },
      });
    });
    return () => {
      $eventBus.off("wsMapLoading");
      $eventBus.off("wsMessageCount");
      $eventBus.off("wsMapConfigRight");
      $eventBus.off("wsDataLogicAreas");
      $eventBus.off("mapClickRealtimeObstacle");
      $eventBus.off("mapClickKnockArea");
      $eventBus.off("wsDataSpeedLimitAreas");
    };
  }, []);

  // 数据 驱动语音事件绑定
  useEffect(() => {
    let conf = localStorage.getItem("Geek_RMSConfig")
      ? JSON.parse(localStorage.getItem("Geek_RMSConfig"))
      : {};
    if (conf && conf.voicePromptWhenExceptionOccurs) {
      setShowWarnAudio(true);
    }

    $eventBus.on("wsMessageCardAudio", data => {
      // console.log("data", data);
      if (data?.id) {
        setCardMsgId(new String(data.id));
      }

      if (!data?.eventType || !data?.eventObj || !data?.eventContent) return;
      try {
        $audio.addText(
          t(data.eventType) + t(data.eventObj) + _$utils.transMsgLang(data.eventContent),
        );
      } catch (e) {
        console.error(e);
      }
    });
    return () => {
      $eventBus.off("wsMessageCardAudio");
    };
  }, []);

  // iframe 驱动地图国际化语言
  useEffect(() => {
    i18nLang.changeLang();
    $eventBus.on("iframeLangChange", data => {
      i18nLang.changeLang();
      setLocal(i18nLang.getAntdLang(data.lang));
    });
    return () => {
      $eventBus.off("iframeLangChange");
    };
  }, []);

  // 地图resize
  useEffect(() => {
    const resizeMap = () => {
      const map2D = getMap2D();
      map2D && map2D.mapRender.resize();
    };
    window.addEventListener("resize", resizeMap, false);
    return () => {
      window.removeEventListener("resize", resizeMap, false);
    };
  }, []);

  // iframe 驱动地图创建
  useEffect(() => {
    $eventBus.on("iframeCreateMap", () => {
      createMap2D(refMapBox.current, { domOffset: [38, 0, 0, 0] });
    });
    _$utils.__isMapDomMounted = true;

    if (props.isIframe !== "rms") {
      // 不走iframe 直接打开页面的时候走这里 生产环境isIframe必须等于test 才能生效
      $eventBus.emit("iframeCreateMap");
    }
    return () => {
      $eventBus.off("iframeCreateMap");
      getMap2D()?.destroy();
      _$utils.__isMapDomMounted = false;
    };
  }, []);

  return (
    <ConfigProvider locale={local} componentSize="middle">
      <Spin className="map2d-spin" spinning={loading} size="large" tip="loading..." />
      <div ref={refMapBox} className="map2d-box" />
      <PanelTop messageCount={messageCount} />
      {!props.hideRightPanel && ready && (
        <PanelRight
          mapReleased={mapReleased}
          mapConfig={mapConfig}
          logicAreas={logicAreas}
          speedLimitAreas={speedLimitAreas}
        />
      )}
      {ready && <DialogHelp />}
      <DialogMessageCount messageCount={messageCount} />
      <PanelDeviceWarning />
      <PopStation />
      <BoxWarning />
      <DialogWarning />
      {showWarnAudio && <WarnAudio cardMsgId={cardMsgId} />}
    </ConfigProvider>
  );
}

export default Root;
