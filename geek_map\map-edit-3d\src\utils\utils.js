import * as THREE from "three";

export const throttle = function (fn, interval) {
  let __self = fn,
    timer,
    firstTime = true;
  return function () {
    const args = arguments,
      __me = this;
    if (firstTime) {
      __self.apply(__me, args);
      return (firstTime = false);
    }
    if (timer) return false;
    timer = setTimeout(() => {
      clearTimeout(timer);
      timer = null;
      __self.apply(__me, args);
    }, interval || 500);
  };
};

export const getWsUrl = () => {
  let protocol = window.location.protocol === "http:" ? "ws" : "wss";
  let hostname = window.location.host;
  if ($req && $req.isDev) {
    hostname = new URL($req.API_URL).hostname;
  }
  const RMSPermission = $utils.Data.getRMSPermission();
  const token = RMSPermission ? `?token=${$utils.Data.getToken()}` : "";

  return `${protocol}://${hostname}/athena-monitor${token}`;
};

export const lookForModelGroup = (mesh, name) => {
  if (mesh.name.includes(name)) {
    return mesh;
  }
  if (mesh.type === "Scene") {
    return null;
  }
  return lookForModelGroup(mesh.parent, name);
};

export const nextTick = () => {
  return new Promise(resolve => {
    setTimeout(resolve, 34);
  });
};

// 扩展属性（后期考虑封装一个类）
export const addMethods = obj => {
  let _color = null;
  //[jscastro] added property for wireframes state
  Object.defineProperty(obj, "color", {
    get() {
      return _color;
    },
    set(value) {
      if (_color === value) return;
      obj.traverse(function (c) {
        if (c.type == "Mesh" || c.type == "SkinnedMesh") {
          let materials = [];
          if (!Array.isArray(c.material)) {
            materials.push(c.material);
          } else {
            materials = c.material;
          }
          let m = materials[0];
          if (value) {
            c.userData.materials = m;
            c.material = new THREE.MeshStandardMaterial();
            c.material.color.setHex(value);
          } else {
            c.material.dispose();
            c.material = c.userData.materials;
            c.userData.materials.dispose();
            c.userData.materials = null;
          }
        }
      });
      _color = value;
    },
  });

  Object.defineProperty(obj, "status", {
    get() {
      return obj.userData.__status;
    },
    // work / error / offline / normal;
    set(value) {
      obj.userData.__status = value;
      const isStatusModel = obj.getObjectByName("status");
      if (String(value).trim() === "normal")
        return isStatusModel && (isStatusModel.visible = false);
      if (isStatusModel) return (isStatusModel.visible = true);
      const box3 = new THREE.Box3().setFromObject(obj.clone());
      const diffX = Math.abs(box3.max.x - box3.min.x);
      const diffZ = Math.abs(box3.max.z - box3.min.z);
      const ring = new THREE.RingGeometry(Math.min(diffX, diffZ), 1.4 * Math.min(diffX, diffZ), 64);
      const mesh = new THREE.Mesh(
        ring,
        new THREE.MeshStandardMaterial({
          color: 0x0077f0,
          side: THREE.DoubleSide,
          roughness: 1,
          metalness: 0,
        }),
      );
      mesh.name = "status";
      mesh.rotation.x = -Math.PI / 2;
      mesh.visible = true;
      obj.add(mesh);
    },
  });
};
