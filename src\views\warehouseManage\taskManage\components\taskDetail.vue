<template>
  <m-dialog
    :is-need-footer="false"
    :visible="visible"
    @closed="handleCloseDialog"
    :title="$t('lang.rms.fed.taskDetail')"
  >
    <div class="ui-taskDetail__content">
      <!-- 子任务详情 -->
      <p class="mt15 mb10 f16">{{ $t("lang.rms.fed.taskInfo") }}</p>
      <el-descriptions :column="3" border>
        <!-- 子任务ID -->
        <el-descriptions-item :label="$t('lang.rms.fed.taskId')">{{
          initRow.jobId || "--"
        }}</el-descriptions-item>
        <!-- 容器编码 -->
        <el-descriptions-item :label="$t('lang.rms.web.container.containerCode')">{{
          initRow.container || "--"
        }}</el-descriptions-item>
        <!-- 任务状态 -->
        <el-descriptions-item :label="$t('lang.rms.web.monitor.robot.taskState')">{{
          initRow.jobStatus || "--"
        }}</el-descriptions-item>
        <!-- 机器人ID -->
        <el-descriptions-item :label="$t('lang.mb.robotManage.robotId')">{{
          initRow.robotId || "--"
        }}</el-descriptions-item>
        <!-- 任务耗时 -->
        <el-descriptions-item :label="$t('lang.rms.fed.taskExpense')">{{
          initRow.costTime || "--"
        }}</el-descriptions-item>
      </el-descriptions>
      <p class="mt20 f16 mb10">{{ $t("lang.rms.fed.taskDetail") }}</p>
      <!-- 后期可能会加 -->
      <!-- <el-input v-model="filterText">
        <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
      </el-input> -->
      <el-tree
        :data="treeData"
        :props="{ children: 'childTopics' }"
        :filter-node-method="filterNode"
        ref="tree"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <p class="content">
              <span class="b">{{ $t(data.traceTopic) }}</span>
              ：{{ $t(data.resultDesc, data.resultDescValues || []) || "--" }}
            </p>
            <p class="time el-button--text">{{ formatterTime(data.traceTime) }}</p>
          </div>
        </template>
      </el-tree>
    </div>
  </m-dialog>
</template>
<script>
/**
 *  lang.rms.fed.taskInfo: 任务信息
 *  lang.rms.fed.taskExpense: 任务耗时
 */
export default {
  props: {
    visible: Boolean,
    initRow: Object,
  },
  data() {
    return {
      treeData: this.initRow.traces || [],
      filterText: "",
    };
  },
  methods: {
    fetchTaskDetail() {},
    handleCloseDialog() {
      this.$emit("update:visible", false);
    },
    formatterTime(value) {
      if (!value) return "--";
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss") || "";
    },
    filterNode(value, data) {
      if (!value) return true;
      return this.$t(data.traceTopic).indexOf(value) !== -1;
    },
    handleSearch() {
      this.$refs.tree.filter(this.filterText);
    },
  },
};
</script>
<style lang="less" scoped>
.custom-tree-node {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
</style>
