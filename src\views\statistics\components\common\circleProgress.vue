<template>
  <div class="circle-progress" :style="circleStyles" >
    <div class="circle-progress__percent" :style="{ fontSize }">{{ percent }}%</div>
    <svg class="circle-progress__svg">
      <circle class="circle-progress__background" :cx="center" :cy="center" :r="radius" />
      <circle class="circle-progress__progress" :cx="center" :cy="center" :r="radius" :stroke-dasharray="circumference" :stroke-dashoffset="progressOffset" />
    </svg>
  </div>
</template>

<script>
export default {
  name: 'CircleProgress',
  props: {
    percent: {
      type: Number,
      required: true,
      validator: value => value >= 0 && value <= 100
    },
    size: {
      type: Number,
      default: 100,
      validator: value => value > 0
    },
    strokeWidth: {
      type: Number,
      default: 10,
      validator: value => value > 0
    },
    strokeColor: {
      type: String,
      default: '#5cb85c'
    }
  },
  computed: {
    center() {
      return this.size / 2;
    },
    radius() {
      return (this.size - this.strokeWidth) / 2;
    },
    circumference() {
      return 2 * Math.PI * this.radius;
    },
    progressOffset() {
      const progress = this.percent / 100;
      return this.circumference * (1 - progress);
    },
    circleStyles() {
      return {
        width: `${this.size}px`,
        height: `${this.size}px`
      };
    },
    fontSize() {
      return `${this.size / 5}px`;
    }
  }
};
</script>

<style scoped>
.circle-progress {
  position: relative;
  display: inline-block;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.circle-progress__percent {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  margin-top: -0.5em;
  font-size: 1.2em;
}
.circle-progress__svg {
  display: block;
  width: 100%;
  height: 100%;
}
.circle-progress__background {
  fill: none;
  stroke: #e6e6e6;
  stroke-width: 10;
  stroke-linecap: round;
}
.circle-progress__progress {
  fill: none;
  stroke: var(--circle-progress-stroke-color, #5cb85c);
  stroke-width: var(--circle-progress-stroke-width, 10);
  stroke-linecap: round;
  transform-origin: center;
  transform: rotate(-90deg);
  transition: stroke-dashoffset 0.5s ease;
}
</style>
