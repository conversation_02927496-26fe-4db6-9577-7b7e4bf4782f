<template>
  <div class="app-container">
    <el-card>
      <el-form ref="customizeForm" label-position="top" :inline="true" label-width="80px">
        <el-form-item :label="$t('lang.rms.fed.adjust.log.startTime')">
          <el-date-picker
            v-model="dataRange"
            type="datetimerange"
            range-separator="-"
            :start-placeholder="$t('lang.rms.fed.startTime')"
            :end-placeholder="$t('lang.rms.fed.endTime')"
          />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.adjust.log.crossArea')" :placeholder="$t('lang.rms.fed.pleaseChoose')">
          <el-select v-model="crossAreaFlag">
            <el-option value="" :label="$t('lang.rms.fed.wholeStatus')" />
            <el-option value="0" :label="$t('lang.rms.fed.adjust.disabledDiffArea')" />
            <el-option value="1" :label="$t('lang.rms.fed.adjust.enabledDiffArea')" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.adjust.log.shelfScore.from')">
          <el-select v-model="shelfScoreFrom">
            <el-option value="" :label="$t('lang.rms.fed.wholeStatus')" />
            <el-option :value="0" label="wms" />
            <el-option :value="1" label="ems" />
          </el-select>
        </el-form-item>

        <el-form-item class="operation-button">
          <el-button type="primary" @click="onQuery">
            {{ $t("lang.rms.fed.query") }}
          </el-button>
          <el-button @click="onReset">{{ $t("lang.rms.fed.reset") }}</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="mt-20">
      <el-table ref="multipleTable" :data="tableData" class="map-management-table">
        <el-table-column type="expand">
          <table-expend slot-scope="props" :row-data="props.row" />
        </el-table-column>
        <el-table-column type="index" label="#" width="50" />
        <!-- 调整范围 -->
        <el-table-column
          prop="adjustAreas"
          :label="$t('lang.rms.fed.adjust.log.adjustArea')"
          :formatter="formatterAdjustAreas"
        />
        <!-- 是否跨区调整 -->
        <el-table-column
          prop="crossAreaFlag"
          :label="$t('lang.rms.fed.adjust.log.crossArea')"
          :formatter="formatterCrossAreaFlag"
        />
        <!-- 调整货架数量 -->
        <el-table-column prop="adjustShelfNum" :label="$t('lang.rms.fed.adjust.log.adjustShelf.num')" />
        <!-- 货架分数来源 -->
        <el-table-column
          prop="shelfScoreFrom"
          :label="$t('lang.rms.fed.adjust.log.shelfScore.from')"
          :formatter="formatterShelfScoreFrom"
        />
        <!-- 开始调整时间 -->
        <el-table-column
          prop="createTime"
          :label="$t('lang.rms.fed.adjust.log.startTime')"
          width="160"
          :formatter="formatterTime"
        />
        <!-- 操作 -->
        <el-table-column :label="$t('lang.rms.fed.chargerOperating')" width="100">
          <div slot-scope="scope">
            <el-button type="text" @click="showDetail(scope.row)">
              {{ $t("lang.rms.fed.adjustDetailBtn") }}
            </el-button>
          </div>
        </el-table-column>
      </el-table>

      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-count="totalPage"
        :page-sizes="[10, 25, 50, 100]"
        layout="sizes, prev, pager, next"
        background
        class="geek-pagination"
        @size-change="pageSizeChange"
        @current-change="currentPageChange"
      />
    </el-card>
  </div>
</template>
<script>
import tableExpend from "./table-expand";

export default {
  components: { tableExpend },
  data() {
    return {
      dataRange: "",
      crossAreaFlag: "",
      shelfScoreFrom: "",

      tableData: [],
      // tableData: [
      //   {
      //     "id": 11,
      //     "adjustAreas": null,
      //     "crossAreaFlag": null,
      //     "adjustShelfNum": 1,
      //     "shelfScoreFrom": true,
      //     "createTime": "2022-03-31 20:56:21",
      //     "finishTime": "2022-03-31 20:56:21",
      //     "shelfAdjustDetailEntities": [
      //       {
      //         "id": 9,
      //         "recordId": 11,
      //         "shelfCode": "dd",
      //         "shelfScore": null,
      //         "shelfOldArea": null,
      //         "shelfOldZone": null,
      //         "shelfOldPlacement": null,
      //         "shelfNewArea": null,
      //         "shelfNewPlacement": null,
      //         "shelfNewZone": null,
      //         "createTime": null,
      //         "finishTime": null
      //       }
      //     ]
      //   },
      //   {
      //     "id": 10,
      //     "adjustAreas": null,
      //     "crossAreaFlag": null,
      //     "adjustShelfNum": 1,
      //     "shelfScoreFrom": true,
      //     "createTime": "2022-03-31 20:56:21",
      //     "finishTime": "2022-03-31 20:56:21",
      //     "shelfAdjustDetailEntities": [
      //       {
      //         "id": 8,
      //         "recordId": 10,
      //         "shelfCode": "dd",
      //         "shelfScore": 1,
      //         "shelfOldArea": 2,
      //         "shelfOldZone": 3,
      //         "shelfOldPlacement": 4,
      //         "shelfNewArea": 5,
      //         "shelfNewPlacement": 6,
      //         "shelfNewZone": 7,
      //         "createTime": 8,
      //         "finishTime": 9
      //       }
      //     ]
      //   }
      // ],

      totalPage: 1,
      currentPage: 1,
      pageSize: 10,
    };
  },
  activated() {
    this.getList();
  },
  methods: {
    /* 格式化内容 */
    formatterAdjustAreas(row, column, cellValue, index) {
      if (cellValue) {
        return cellValue.join(",");
      } else {
        return cellValue;
      }
    },
    formatterCrossAreaFlag(row, column, cellValue, index) {
      if (cellValue) {
        return this.$t("lang.rms.fed.adjust.enabledDiffArea");
      } else {
        return this.$t("lang.rms.fed.adjust.disabledDiffArea");
      }
    },
    formatterShelfScoreFrom(row, column, cellValue, index) {
      if (cellValue) {
        return "ems";
      } else {
        return "wms";
      }
    },

    formatterTime(row, column, cellValue, index) {
      if (!cellValue) {
        return null;
      }
      return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
    },
    getList() {
      let startTime = "",
        endTime = "";
      const dataRange = this.dataRange;
      if (dataRange) {
        startTime = new Date(dataRange[0]).getTime();
        endTime = new Date(dataRange[1]).getTime();
      }
      const params = {
        crossAreaFlag: this.crossAreaFlag,
        shelfScoreFrom: this.shelfScoreFrom,
        startTime,
        endTime,
        currentPage: this.currentPage,
        pageSize: this.pageSize,
      };

      console.log(params);
      $req.post("/athena/shelfStaticAdjust/recordList", params).then(res => {
        if (res.code !== 0 || !res.data || !res.data.content || !Array.isArray(res.data.content)) return;
        this.tableData = res.data.content;
        this.totalPage = res.data.totalPages;
      });
    },
    showDetail(rowData) {
      this.$refs.multipleTable.toggleRowExpansion(rowData, true);
    },
    onQuery() {
      this.getList();
    },
    onReset() {
      this.$refs.customizeForm.resetFields();

      this.dataRange = "";
      this.crossAreaFlag = "";
      this.shelfScoreFrom = "";
    },

    currentPageChange(currentPage) {
      this.currentPage = currentPage;
      this.getList();
    },
    pageSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.operation-button {
  vertical-align: bottom;
}
</style>
