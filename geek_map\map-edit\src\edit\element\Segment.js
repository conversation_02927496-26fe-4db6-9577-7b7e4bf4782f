import Line from './baseElement/Line'
import {Graphics} from "pixi.js";
import LineHitArea from "./baseElement/LineHitArea";
import SegmentArrow from './baseElement/SegmentArrow'
import CtrlPoint from './baseElement/CtrlPoint'
import Selected from "../selected/Selected";
import Control from "../control/Control";
import {isHoverNode,pixi2cad} from '../utils/utils'
import {lineStyle} from '../config'
const {INACTIVE_LINE,ACTIVE_LINE} = lineStyle
import {cad2pixi} from '../utils/utils'
import EventBus from "../eventBus/EventBus";
import Store from '../store/Store'
import LayerManager from "../layerManager/LayerManager";
export default class Segment {
  //保存点下那一瞬间的点数据
  static _downInfo = null
  //创建
  static add(data) {
    const {points,unloadDirs,loadDirs,segmentType,segmentId} = data
    //y轴倒置，CAD和canvas的坐标系不同
    const invertPoints = points.map(item => {
      return cad2pixi(item)
    })
    if(!invertPoints || invertPoints.length < 2) return null
    const $container = new Graphics();
    $container.buttonMode = true
    $container.interactive = true
    $container.interactiveChildren = true
    $container.sortableChildren = true
    $container.segmentId = segmentId
    const $line = new Graphics()
    $line.name = 'line'
    $line.interactive = true
    $line.cursor = 'pointer'
    $line.loadDirs = loadDirs
    $line.unloadDirs = unloadDirs
    $line.segmentId = segmentId
    $line.zIndex = 1
    //渲染点
    invertPoints.forEach(item => {
      const {x,y,nodeId,cellCode} = item
      const $point = CtrlPoint.render(x,y)
      $point.visible = false
      $point.name = 'point'
      $point.zIndex = 2
      $point.nodeId = nodeId
      $point.cellCode = cellCode
      //将节点与线的关系进行存储
      if(nodeId) Store.node2Line.insert(nodeId,segmentId)
      $container.addChild($point)
      $point
        .on('pointerdown',(e) => {
          const {x,y,nodeId,cellCode} = $point
          this._downInfo = {x,y,nodeId,cellCode}
          Control.enableDrag(false)
          $point.alpha = 0.5;
          $point.dragging = true;
        })
        .on('pointerup', (e) => {
          // downInfo = null
          Control.enableDrag(true)
          $point.alpha = 1;
          $point.dragging = false;
          if($point.isUpdate){
            this._updateFinished($point.parent)
            $point.isUpdate = false
          }
        })
        .on('pointerupoutside', () => {
          Control.enableDrag(true)
          $point.alpha = 1;
          $point.dragging = false;
          if($point.isUpdate){
            this._updateFinished($point.parent)
            $point.isUpdate = false
          }
        })
        .on('pointermove',(e) => {
          if ($point.dragging) {
            //设置为更新态
            $point.isUpdate = true
            const p = e.data.getLocalPosition($point.parent);
            const hoverNode = isHoverNode(p)
            let updateAttr;
            if(hoverNode){
              const {x,y,nodeId,cellCode} = hoverNode
              updateAttr = {x,y,nodeId,cellCode}
              Store.node2Line.insert(nodeId,segmentId)
            }else{
              const origNodeId = $point.nodeId
              //非附着状态，移除点和线的关系
              if(origNodeId){
                Store.node2Line.delete(origNodeId,segmentId)
              }
              updateAttr = {x:p.x,y:p.y,nodeId:null,cellCode:null}
            }
            //设置为更新态
            if($point.x !== updateAttr.x || $point.y !== updateAttr.y) {
              $point.isUpdate = true
            }
            Object.assign($point,updateAttr)
            this._updateEditLine($point.parent)
          }
        })
    })
    Line.render($line,invertPoints,{width:lineStyle.width,color: INACTIVE_LINE})
    const bounds = LineHitArea.render(lineStyle.width,$line.currentPath.points)
    $line.hitArea = bounds
    //渲染线箭头
    SegmentArrow.render($line,{paths:invertPoints,color:INACTIVE_LINE})
    // const $hitArea = new Graphics()
    // $hitArea.zIndex = 1000
    // $hitArea.name = 'hitArea'
    // this._addHitArea($hitArea,bounds)
    // $container.addChild($hitArea)

    $container.addChild($line)
    $container.type = segmentType
    return $container
  }
  //更新
  static update($el,item) {
    const {points,segmentId,unloadDirs,loadDirs} = item
    //y轴倒置，CAD和canvas的坐标系不同
    const invertPoints = points.map(item => {
      return cad2pixi(item)
    })
    const $line = $el.getChildByName('line')
    //元素属性赋值
    const lineAttr = {unloadDirs,loadDirs}
    Object.assign($line,lineAttr)
    const $points = $el.children.filter(child => child.name === 'point')
    invertPoints.forEach((item,index) => {
      Object.assign($points[index],item)
    })
    $line.clear()
    const $selected = Selected.getSelected(segmentId)
    const isActive = $selected && $selected.layerName === 'LINE'
    const color = isActive ? ACTIVE_LINE : INACTIVE_LINE
    Line.render($line,invertPoints,{width:lineStyle.width,color})
    //设置可点击区域
    const bounds = LineHitArea.render(lineStyle.width,$line.currentPath.points)
    $line.hitArea = bounds
    //设置箭头
    SegmentArrow.render($line,{paths:invertPoints,color})
  }
  //更新线
  static _updateEditLine($el) {
    const $points = $el.children.filter(child => child.name === 'point')
    const $line = $el.getChildByName('line')
    // const $hitArea = $el.getChildByName('hitArea')
    const paths = $points.map(p => {
      const {x,y,cellCode,nodeId} = p
      return {x,y,cellCode,nodeId}
    })
    $line.clear()
    Line.render($line,paths,{width:lineStyle.width,color: ACTIVE_LINE})
    //设置可点击区域
    const bounds = LineHitArea.render(lineStyle.width,$line.currentPath.points)
    $line.hitArea = bounds
    SegmentArrow.render($line,{paths,color:ACTIVE_LINE})
    // this._addHitArea($hitArea,bounds)
  }
  //更新完成触发
  static _updateFinished($el) {
    const {segmentId,type:segmentType} = $el
    const $points = $el.children.filter(child => child.name === 'point')
    const $line = $el.getChildByName('line')
    //判断线上的所有点是否都link到cell上
    const isAllLink = $points.every($p => $p.nodeId)
    // console.log('快让爷看看',isAllLink)
    // if(!isAllLink) return
    const points = $points.map(p => {
      const {x,y,cellCode,nodeId} = p
      //判断是否有nodeId，有的话维护节点与线的关系
      if(nodeId){
        Store.node2Line.insert(nodeId,segmentId)
        return pixi2cad({x,y,cellCode,nodeId})
      }else{
        // const {x,y,cellCode,nodeId} = this._downInfo
        Store.node2Line.insert(this._downInfo.nodeId,segmentId)
        return pixi2cad({...this._downInfo})
      }
      // return pixi2cad({x,y,cellCode,nodeId})
    })
    const updateOp = {
      id:'LINE',
      data:[{segmentId,points,segmentType}],
      isSaveHistory:isAllLink
    }
    LayerManager.updateElements(updateOp)
    this._downInfo = null
    //更新回调触发
    // EventBus.$emit('updated',updateOp)
  }
  //hitArea,渲染可点击区域
  static _addHitArea($el,shape) {
    $el.clear()
    $el.beginFill(0xef973b, 0.6);
    $el.drawShape(shape);
    $el.endFill();
  }
}
