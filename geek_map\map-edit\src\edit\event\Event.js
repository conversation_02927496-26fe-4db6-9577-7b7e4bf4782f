import {getGlobalViewport} from "../global";
import Mode from '../Mode'
import Selected from '../selected/Selected'
//事件触发
import DefaultEvent from './DefaultEvent'
import AreaEvent from './AreaEvent'
import SingleLaneEvent from './SingleLaneEvent'
import SegmentEvent from './SegmentEvent'
import BezierSegmentEvent from './BezierSegmentEvent'
import ElementEvent from './ElementEvent'
import MultiElementEvent from './MultiElementEvent'
import DeviceEvent from './DeviceEvent'
//批量修改事件
import MultiModifyEvent from './MultiModifyEvent'
//功能添加
import FnChooseEvent from './FnChooseEvent'
class Event {
  //记录时间戳
  static timestamp = null
  //down 和 up的时间间隔
  static isClick = true
  static isRightClick = false
  static activeKey = null
  //viewport 事件列表
  static eventList = {}
  //初始化viewport事件
  static initEvent() {
    const viewport = getGlobalViewport();
    viewport.on('clicked',(e) => this._runHandler('clicked',e))
    viewport.on('mousedown',(e) => this._runHandler('mousedown',e))
    viewport.on('mouseup',(e) => this._runHandler('mouseup',e))
    viewport.on('mousemove',(e) => this._runHandler('mousemove',e))
    // viewport.on('mouseover',(e) => this._runHandler('mouseover',e))
    // viewport.on('mouseout',(e) => this._runHandler('mouseout',e))
    // viewport.on('mouseenter',(e) => this._runHandler('mouseenter',e))
    // viewport.on('mouseleave',(e) => this._runHandler('mouseleave',e))
    viewport.on('rightclick',(e) => this._runHandler('rightclick',e))
    document.addEventListener("keydown", (e) => this._runHandler('keydown',e));
    document.addEventListener("keyup", (e) => this._runHandler('keyup',e));
    //绑定默认事件
    this.bindEventByMode(Mode.mode)
  }
  //通过不同模式绑定不同事件
  static bindEventByMode(mode){
    let events;
    const {action,options} = mode
    if(action === 'DEFAULT'){
      events = DefaultEvent.addEvents()
      this.addEvent('default', events)
      return
    }
    const {type,name,defConfig = {}} = options
    if(action === 'ADD'){
      switch (type){
        case 'ELEMENT':
          events = ElementEvent.addEvents()
          break;
        case 'MULTI_ELEMENT':
          events = MultiElementEvent.addEvents()
          break;
        case 'DEVICE':
          events = DeviceEvent.addEvents()
          break;
        case 'LINE':
          events = SegmentEvent.addEvents(defConfig)
          break;
        case 'BEZIER':
          events = BezierSegmentEvent.addEvents(defConfig)
          break;
        case 'AREA':
          //单行道，特殊的面
          if(name === 'SINGLE_LANE'){
            events = SingleLaneEvent.addEvents()
          }else{
            events = AreaEvent.addEvents()
          }
          break;
        default:
          events = DefaultEvent.addEvents()
      }
    }
    //批量修改
    if(action === 'MULTI_MODIFY'){
      events = MultiModifyEvent.addEvents(options)
    }
    //针对功能单元格选点
    if(action === 'FN_CHOOSE'){
      events = FnChooseEvent.addEvents(options)
    }
    this.addEvent('addElement', events)
  }
  //添加面元素回调
  static _runHandler(eventName,e){
    for(const name in this.eventList){
      //当前绑定事件集
      const events = this.eventList[name]
      if(events && events[eventName]) events[eventName](e);
    }
  }
  //添加viewport事件
  static addEvent(eventName,cbList) {
    this.eventList[eventName] = cbList
  }
  //绑定元素事件
  static eventCtrl() {
    //绑定
    const bind = ($el,name,handler) => {
      $el.on(name,handler)
    }
    //解绑
    const unbind = ($el,name,handler) => {
      $el.off(name,handler)
    }
    return {
      bind,
      unbind
    }
  }
  static reset() {
    this.eventList = {}
    FnChooseEvent.destroy()
  }
}
export default Event
