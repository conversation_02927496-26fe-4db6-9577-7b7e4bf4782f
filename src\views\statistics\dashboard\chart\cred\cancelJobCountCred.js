import Chart, { requestCache } from "../common";

/**
 * 2.3.5路径运行平均速度
 */
export default class CancelJobCountCred extends Chart {
  /**
   * 初始化图表 - 取消的任务数
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('cred', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "取消的任务数";
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'elDatePicker',
        defaultValue: new Date(),
        option: {

        }
      }
    ]
  }

  async request(params) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stats/query/job/dashboard/count/', {
      date: $utils.Tools.formatDate(params.date || new Date, "yyyy-MM-dd"),
      showCount: true,
      showCountGroup: true,
    })
    
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    return {
      id: 'cancelJobCountCred',
      title: this.title || '',
      number: data.countMap.cancelJobCount || 0,
      color: "#8543e0",
    }
  }
}