<template>
  <div class="sec-flex-box">
    <section class="library-list">
      <h5 class="title">
        <span>{{ $t("lang.rms.fed.selectConfigurationLibrary") }}</span>
        <el-input
          v-model="libraryName"
          :placeholder="$t('lang.rms.fed.pleaseEnter')"
          class="input-with-select"
        >
          <el-button slot="append" icon="el-icon-search" @click="getLibraries" />
        </el-input>
      </h5>
      <ul>
        <li
          v-for="(item, index) in libraries"
          :key="index"
          :class="{ active: item.id === currentId }"
          @click="libSelected(item)"
        >
          {{ item.name }}
        </li>
      </ul>
    </section>

    <library-detail
      class="library-detail"
      :current-library="currentLibrary"
      @updatedLibrary="updatedLibrary"
      @updatedLibraryParams="updatedLibraryParams"
    />
  </div>
</template>
<script>
import libraryDetail from "./components/libraryDetail";

export default {
  name: "ParamLibrary",
  components: { libraryDetail },
  data() {
    return {
      libraryName: "",

      libraries: [],
      currentId: null,
      currentLibrary: {},
    };
  },
  activated() {
    this.getLibraries();
  },
  methods: {
    libSelected(item) {
      if (this.currentId === item.id) return;
      this.currentId = item.id;
      this.getCurrentLibrary();
    },
    updatedLibrary(id) {
      this.libraryName = "";
      this.currentId = id;
      this.getLibraries("updated");
    },
    updatedLibraryParams(id, data) {
      this.currentId = id;
      const type = Number(data.searchType);
      const params = {
        searchType: type,
        search: type === 3 ? Number(data.isImmediate) : data.searchValue,
        language: data.language,
      };
      this.getCurrentLibraryParams(params);
    },
    // 请求库列表
    getLibraries(type) {
      let params = { search: this.libraryName };
      $req
        .post("/athena/config/template/list", $utils.Tools.getParams(params), {
          headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" },
        })
        .then(res => {
          if (res.code !== 0) return;
          let libraries = res.data;
          if (libraries.length <= 0) return;

          this.libraries = libraries;
          if (this.currentId === null || type !== "updated") this.currentId = libraries[0].id;
          this.getCurrentLibrary();
        });
    },
    // 请求指定的配置库
    getCurrentLibrary() {
      $req
        .post(
          "/athena/config/template/show?templateId=" + this.currentId,
          // $utils.Tools.getParams(data),
          { headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" } },
        )
        .then(res => {
          if (res.code !== 0) return;
          this.currentLibrary = res.data;
        });
    },

    // 请求指定的配置库
    getCurrentLibraryParams(data) {
      $req
        .post(
          "/athena/config/template/show?templateId=" + this.currentId,
          $utils.Tools.getParams(data),
          { headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" } },
        )
        .then(res => {
          if (res.code !== 0) return;
          this.currentLibrary = res.data;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.sec-flex-box {
  .g-flex();
  .g-box-shadow-no-bottom();
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;

  .library-list,
  .library-detail {
    height: 100%;

    border-top: 0;
    overflow: auto;
  }

  .library-list {
    flex: 0 0 260px;
    border-right: 5px solid #eee;

    > h5.title {
      padding: 12px;
      border-bottom: 1px solid #eee;

      span {
        display: block;
        margin-bottom: 5px;
        font-size: 13px;
        font-weight: 600;
      }
    }

    > ul {
      padding: 12px;

      li {
        padding: 10px 0;
        border-bottom: 1px solid #eee;
        font-size: 14px;
        cursor: pointer;

        &.active {
          color: #409eff;
          font-weight: 700;
        }
      }
    }
  }

  .library-detail {
    width: 100%;
    border-left: 5px solid #eee;
  }
}
</style>
