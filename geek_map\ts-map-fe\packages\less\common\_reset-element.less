@charset "utf-8";
//此文件重置p、label等标签样式
/**-----------------------------定义-----------------------------**/
@common-reset-a-color: #444;
@common-reset-font-size: 0.16rem;

/**-----------------------------样式-----------------------------**/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: inherit;
  font-size: @common-reset-font-size;
  font-weight: normal;
}

th,
em,
i,
code,
address,
caption,
cite,
dfn,
var {
  font-style: normal;
  font-weight: normal;
  font-family: inherit;
}

ul,
li {
  list-style: none;
}

img {
  height: auto;
  max-width: 100%;
  vertical-align: middle;
  border: 0;
}

fieldset {
  border: 0;
}

object,
embed {
  max-width: 100%;
}

ol,
ul {
  font-family: inherit;
  list-style: none;
}

a {
  font-family: inherit;
  box-sizing: border-box;
  vertical-align: baseline;
  background: transparent;
  text-decoration: none;
  touch-callout: none;
  color: @common-reset-a-color;
}

a:active {
  color: @common-reset-a-color;
}

p {
  font-family: inherit;
  white-space: normal;
  word-wrap: break-word;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
  font-family: inherit;

  th {
    font-weight: bold;
    vertical-align: bottom;
  }

  td {
    font-weight: normal;
    vertical-align: top;
  }
}
