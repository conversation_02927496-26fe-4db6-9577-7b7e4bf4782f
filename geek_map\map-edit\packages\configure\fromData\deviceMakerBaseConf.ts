import { useCodeQualified } from "@packages/hook/useRules";
import { NodeAttrEditConf } from "@packages/type/editUiType";

// 反光柱新增的配置内容
export const MAKER_BASE_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  return {
    name: "makerBase",
    tabTitle: "lang.rms.fed.basis",
    labelWidth: "102px",
    formItem: [
      // 反光柱编号
      {
        prop: "code",
        label: "lang.rms.edit.map.reflector.code",
      },

      // 反光柱别名
      {
        prop: "name",
        label: "lang.rms.edit.map.reflector.alias",
        component: "elInput",
        rules: [useCodeQualified()],
        maxlength: 13,
        showWordLimit: true,
      },
    ],
  };
};
