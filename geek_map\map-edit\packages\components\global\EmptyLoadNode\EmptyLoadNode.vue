<template>
  <div class="empty-load-node">
    <span
      class="triangle-left"
      :class="{ active: [3, 2].includes(value) }"
      @click="handelLeft"
    ></span>
    <span
      class="triangle-all"
      :class="{ active: [1, 2, 3].includes(value) }"
      @click="handelAll"
    ></span>
    <span
      class="triangle-right"
      :class="{ active: [2, 1].includes(value) }"
      @click="handelRight"
    ></span>
  </div>
</template>

<script setup lang="ts">
import { ref, Ref, watch } from "vue";
const props = defineProps<{
  modelValue: number;
}>();

const emits = defineEmits<{
  (event: "update:modelValue", value: number): void;
  (event: "change", value: number): void;
}>();

/**
 * 0. 无
 * 1. 右
 * 2. 全部
 * 3. 左
 */
const value: Ref<number> = ref(props.modelValue || 0);

watch(
  () => props.modelValue,
  () => {
    value.value = props.modelValue;
  },
);

function handelLeft() {
  switch (value.value) {
    case 3:
      value.value = 0;
      break;
    case 2:
      value.value = 1;
      break;
    case 1:
      value.value = 2;
      break;
    case 0:
      value.value = 3;
      break;
  }
  emits("update:modelValue", value.value);
  emits("change", value.value);
}
function handelAll() {
  switch (value.value) {
    case 3:
    case 1:
    case 0:
      value.value = 2;
      break;
    case 2:
      value.value = 0;
      break;
  }
  emits("update:modelValue", value.value);
  emits("change", value.value);
}
function handelRight() {
  switch (value.value) {
    case 3:
      value.value = 2;
      break;
    case 1:
      value.value = 0;
      break;
    case 0:
      value.value = 1;
      break;
    case 2:
      value.value = 3;
      break;
  }
  emits("update:modelValue", value.value);
  emits("change", value.value);
}
</script>

<style scoped lang="scss">
.empty-load-node {
  padding-left: 20px;
  height: 26px;
  line-height: 26px;
  .triangle-all,
  .triangle-left,
  .triangle-right {
    cursor: pointer;
    display: inline-block;
  }
  .triangle-all {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background-color: #696969;
    margin: 0 10px;

    &.active {
      background-color: #1890ff;
    }
  }

  .triangle-left,
  .triangle-right {
    width: 0;
    height: 0;
    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;

    &.active {
      color: #1890ff;
    }
  }

  .triangle-left {
    border-right: 20px solid;
    color: #696969;
  }

  .triangle-right {
    color: #696969;
    border-left: 20px solid;
  }
}
</style>
