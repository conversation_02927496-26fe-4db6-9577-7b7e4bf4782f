import {getGlobalViewport} from "../global";
export default class Control {
  //是否阻止viewport的默认事件
  static preventVpEvent = false
  //默认both,unload,load
  static dirType = 'both'
  //改变空负载展示
  static changeDir(dirType = 'both') {
    this.dirType = dirType
  }
  static enableDrag(flag){
    const viewport = getGlobalViewport()
    // if(!viewport) return throw error("不存在viewport");
    viewport.drag({
      pressDrag: flag
    })
  }
  static reset() {
    this.dirType = 'both'
  }
}
