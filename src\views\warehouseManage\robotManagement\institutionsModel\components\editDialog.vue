<template>
  <el-dialog
    :title="$t(title)"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="520px"
  >
    <geek-customize-form ref="refForm" :form-config="formConfig">
      <template #title1="{ config }">
        <span class="f-title">{{ $t(config.label) }}:</span>
      </template>
      <template #file>
        <el-upload
          v-if="!file && !rowFile"
          action="javascript:void(0)"
          drag
          accept=".glb, .gltf"
          :limit="1"
          :before-upload="() => false"
          :on-change="handleUpload"
          class="edit-upload"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            <p>{{ $t("lang.rms.api.result.warehouse.dragFileHere") }}</p>
            <p style="font-size: 12px; color: #bbb">
              {{ $t("lang.rms.api.result.warehouse.uploadOntologyModel") }}
            </p>
          </div>
        </el-upload>
        <el-tag v-else closable @close="delFile">
          {{ file || rowFile }}
        </el-tag>
      </template>
      <template #title2="{ config }">
        <span class="f-title">{{ $t(config.label) }}:</span>
      </template>
      <template #title3="{ config }">
        <span class="f-title">{{ $t(config.label) }}:</span>
      </template>
      <template #title4="{ config }">
        <span class="f-title">{{ $t(config.label) }}:</span>
      </template>
    </geek-customize-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditMechanismModelDialog",
  props: ["robotSeries"],
  data() {
    return {
      dialogVisible: false,
      operation: "",
      file: "",
      rowFile: "",
      rowData: {},
      modes: [],
      motions: [],
    };
  },
  computed: {
    title() {
      switch (this.operation) {
        case "add":
          return this.$t('lang.venus.common.dict.create') + this.$t("lang.rms.api.result.warehouse.ontologyModel");
        case "edit":
          return  this.$t('lang.rms.fed.buttonEdit') + this.$t("lang.rms.api.result.warehouse.ontologyModel");
        case "view":
          return  this.$t('lang.rms.fed.buttonView') + this.$t("lang.rms.api.result.warehouse.ontologyModel");
        default:
          return this.$t('lang.venus.common.dict.create') + this.$t("lang.rms.api.result.warehouse.ontologyModel");
      }
    },

    formConfig() {
      let disabled = false;
      if (this.operation == "view") disabled = true;
      return {
        attrs: {
          labelWidth: "160px",
          labelPosition: "right",
          disabled,
        },
        configs: {
          title1: {
            label: "lang.rms.api.result.warehouse.baseProperies",
            slotTitle: "title1",
          },
          name: {
            label: "lang.rms.api.result.warehouse.ontologyName",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterOntologyName",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseEnterOntologyName"),
                trigger: "blur",
              },
            ],
          },
          series: {
            label: "lang.rms.api.result.warehouse.series",
            tag: "select",
            default: "",
            required: true,
            placeholder: "lang.rms.fed.dockingSide",
            options: this.robotSeries,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.enterProductSerials"),
                trigger: "change",
              },
            ],
          },
          file: {
            label: "lang.rms.api.result.warehouse.robotOntologyModel",
            slotName: "file",
          },
          title2: {
            label: "lang.rms.api.result.warehouse.physicalProperty",
            slotTitle: "title2",
          },
          length: {
            label: "lang.rms.fed.length",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterLengthOfModel",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (value == 0) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          width: {
            label: "lang.rms.fed.width",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterModelWidth",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (value == 0) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          height: {
            label: "lang.rms.fed.high",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterModelHeight",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (value == 0) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          diameter: {
            label: "lang.rms.api.result.warehouse.rotationDiameter",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRotationDiameter",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          weight: {
            label: "lang.rms.api.result.warehouse.bodyWeight",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterBodyWeight",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          headOffsetRatio: {
            label: "lang.rms.api.result.warehouse.xDirection",
            default: "",
            tag: "input",
          },
          tailOffsetRatio: {
            label: "lang.rms.api.result.warehouse.yDirection",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterYScale",
          },
          title3: {
            label: "lang.rms.api.result.warehouse.motionAttr",
            slotTitle: "title3",
          },
          navigationModes: {
            label: "lang.rms.api.result.warehouse.navigationMode",
            tag: "select",
            default: "",
            required: true,
            multiple: true,
            options: this.modes,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseSelectNavMode"),
                trigger: ["blur", "change"],
              },
            ],
          },
          maxVelocity: {
            label: "lang.rms.api.result.warehouse.maxDrivingSpeed",
            tag: "input",
            default: "",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMaxDrivingSpeed",
          },
          maxAngularVelocity: {
            label: "lang.rms.api.result.warehouse.maxTuringSpeed",
            tag: "input",
            default: "",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMaxTurnSpeed",
          },
          maxAcceleration: {
            label: "lang.rms.fed.robotParamConfig.maxAcc",
            tag: "input",
            default: "",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMaxAcceleration",
          },
          movingModes: {
            label: "lang.rms.api.result.warehouse.moveMode",
            tag: "select",
            default: "",
            required: true,
            multiple: true,
            options: this.motions,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseSelectMovMethod"),
                trigger: ["blur", "change"],
              },
            ],
          },
          maxPositionError: {
            label: "lang.rms.api.result.warehouse.maxDistanceError",
            tag: "input",
            default: "",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMaxDistanceError",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMaxDistanceError"),
                trigger: "blur",
              },
            ],
          },
          title4: {
            label: "lang.rms.api.result.warehouse.powerProperties",
            slotTitle: "title4",
          },
          batteryType: {
            label: "lang.rms.api.result.warehouse.batteryType",
            tag: "select",
            default: 0,
            options: [
              { value: 0, label: "lang.rms.fed.battery.lto" },
              { value: 1, label: "lang.rms.fed.battery.lfpo" },
              { value: 2, label: "lang.rms.api.result.warehouse.LithiumTernary" },
            ],
          },
          maxBatteryCapacity: {
            label: "lang.rms.api.result.warehouse.batteryMaxMah",
            tag: "input",
            default: "",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMaxBatteryMah",
          },
          workingTimePerPercent: {
            label: "lang.rms.api.result.warehouse.workingPowerConsumption",
            tag: "input",
            default: "",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterWorkAh",
          },
          idleTimePerPercent: {
            label: "lang.rms.api.result.warehouse.idlePowerConsumption",
            tag: "input",
            default: "",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterIdleAh",
          },
          sleepingTimePerPercent: {
            label: "lang.rms.api.result.warehouse.sleepPowerConsumption",
            tag: "input",
            default: "",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterSleepAh",
          },
        },
      };
    },
  },
  methods: {
    open(type, data) {
      this.getDictionary();
      this.operation = type;
      this.rowData = data || {};
      this.dialogVisible = true;
      this.rowFile = data.chassisImage || "";

      const params = {
        name: data?.name || "",
        series: data?.series || "",
        length: data.hasOwnProperty("length") ? data.length : "",
        width: data.hasOwnProperty("width") ? data.width : "",
        height: data.hasOwnProperty("height") ? data.height : "",
        diameter: data.hasOwnProperty("diameter") ? data.diameter : "",
        weight: data.hasOwnProperty("weight") ? data.weight : "",
        headOffsetRatio: data.hasOwnProperty("headOffsetRatio") ? data.headOffsetRatio : "",
        tailOffsetRatio: data.hasOwnProperty("tailOffsetRatio") ? data.tailOffsetRatio : "",
        navigationModes: data?.navigationModes || [],
        maxVelocity: data.hasOwnProperty("maxVelocity") ? data.maxVelocity : "",
        maxAngularVelocity: data.hasOwnProperty("maxAngularVelocity") ? data.maxAngularVelocity : "",
        maxAcceleration: data.hasOwnProperty("maxAcceleration") ? data.maxAcceleration : "",
        movingModes: data?.movingModes || [],
        maxPositionError: data.hasOwnProperty("maxPositionError") ? data.maxPositionError : "",
        batteryType: data?.batteryType || 0,
        maxBatteryCapacity: data.hasOwnProperty("maxBatteryCapacity") ? data.maxBatteryCapacity : "",
        workingTimePerPercent: data.hasOwnProperty("workingTimePerPercent") ? data.workingTimePerPercent : "",
        idleTimePerPercent: data.hasOwnProperty("idleTimePerPercent") ? data.idleTimePerPercent : "",
        sleepingTimePerPercent: data.hasOwnProperty("sleepingTimePerPercent") ? data.sleepingTimePerPercent : "",
      };

      this.$nextTick(() => {
        this.$refs.refForm.reset();
        this.$refs.refForm.setData(params);
      });
    },
    close() {
      this.file = "";
      this.rowFile = "";
      this.dialogVisible = false;
    },
    // 保存
    save() {
      if (this.operation == "view") {
        this.close();
        return;
      }

      this.$refs.refForm.validate().then(data => {
        let params = Object.assign({}, data, {
          hasBackupSensor: data.hasBackupSensor ? 1 : 0,
        });
        if (this.operation == "edit") params.id = this.rowData.id;

        const formData = new FormData();
        for (const key in params) {
          formData.append(key, params[key] ?? "");
        }
        if (this.file) formData.append('file', this.file);
        $req.post("/athena/robot/manage/chassisSave", formData).then(res => {
          this.$success();
          this.close();
          this.$emit("updateList");
        });
      });
    },

    handleUpload(file) {
      if (file.size >= 52428800) {
        this.$error(this.$t("lang.rms.api.result.warehouse.uploadOntologyModel"));
        return;
      }

      const demoData = new FormData();
      demoData.append("file", file.raw);
      this.file = demoData.get("file");
    },
    delFile() {
      this.file = "";
      this.rowFile = "";
    },

    getDictionary() {
      $req.post("/athena/dict/query", { types: ["NAVIGATION_MODE", "ROBOT_MOTION_MODE"] }).then(res => {
        if (res.code !== 0) return;
        const list = res?.data["NAVIGATION_MODE"] || [];
        const list1 = res?.data["ROBOT_MOTION_MODE"] || [];
        this.modes = list.map(item => {
          let label;
          if (item.fieldCode == 0) {
            label = "lang.rms.fed.navigation.slam";
          } else {
            label = "lang.rms.fed.navigation.qr";
          }
          return { label, value: item.fieldCode };
        });
        this.motions = list1.map(item => ({ label: item.fieldCode, value: item.fieldCode }));
      });
    },
  },
};
</script>

<style lang="less" scoped>
.f-title {
  font-weight: 700;
  padding-bottom: 12px;
  display: block;
  color: #929292;
  font-size: 16px;
}
.edit-upload {
  :deep(.el-upload) {
    width: 100%;
    .el-upload-dragger {
      width: unset;
    }
  }
}
</style>
