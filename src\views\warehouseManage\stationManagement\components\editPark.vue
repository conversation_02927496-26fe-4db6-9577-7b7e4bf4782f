<template>
  <el-dialog :title="$t('lang.rms.fed.buttonEdit')" :visible.sync="dialogVisible" :show-close="false" width="520px">
    <geek-customize-form ref="parkForm" :form-config="formConfig" />

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditParkDialog",
  props: ["robotTypes"],
  data() {
    return {
      dialogVisible: false,
      stationId: "",
      rowData: {},
      formConfig: {
        attrs: {
          labelWidth: "138px",
          labelPosition: "right",
        },
        configs: {
          isWorking: {
            label: "lang.rms.fed.status",
            default: false,
            tag: "switch",
          },
          place: {
            label: "lang.rms.fed.WorkStationFace",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "east",
                label: "lang.rms.fed.east",
              },
              {
                value: "south",
                label: "lang.rms.fed.south",
              },
              {
                value: "west",
                label: "lang.rms.fed.west",
              },
              {
                value: "north",
                label: "lang.rms.fed.north",
              },
            ],
          },
          maxQueueSize: {
            label: "lang.rms.fed.maxQueueNumber",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
          // 停靠机器人类型
          robotTypes: {
            label: "lang.rms.fed.WorkStationAllowedRobotTypes",
            multiple: true,
            default: [],
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.robotTypes,
          },
          // 优先级
          priority: {
            label: "lang.rms.web.monitor.robot.robotPriority",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
          // 排队等待点数量
          waitCellNumber: {
            label: "lang.rms.fed.WorkStationWaitCellNumber",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
          // 负载顶升高度
          loadHeight: {
            label: "lang.rms.fed.WorkStationLoadHeight",
            default: "",
            tag: "input-number",
            placeholder: "mm",
            "controls-position": "right",
          },
          // 空载顶升高度
          unloadHeight: {
            label: "lang.rms.fed.WorkStationUnloadHeight",
            default: "",
            tag: "input-number",
            placeholder: "mm",
            "controls-position": "right",
          },
        },
      },
    };
  },
  watch: {
    robotTypes(arr) {
      this.formConfig.configs.robotTypes.options = arr;
    },
  },
  methods: {
    open(data, stationId) {
      const params = {
        isWorking: data.isWorking == "true" || data.isWorking == true ? true : false,
        place: data.place || "",
        maxQueueSize: data.maxQueueSize || 0,
        robotTypes: data.robotTypes || [],
        priority: data.priority || 0,
        waitCellNumber: data.waitCellNumber || 0,
        loadHeight: data.loadHeight || 0,
        unloadHeight: data.unloadHeight || 0,
      };
      this.rowData = data;
      this.stationId = stationId;
      this.dialogVisible = true;
      this.$nextTick(() => this.$refs.parkForm.setData(params));
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      const formData = this.$refs.parkForm.getData();

      const params = Object.assign({}, formData, {
        stationId: this.stationId,
        parkId: this.rowData.parkId,
        isWorking: formData.isWorking.toString(),
      });
      $req.post("/athena/station/management/updateStationPark", params).then(res => {
        this.$success();
        this.dialogVisible = false;
        this.$emit("updateMainList");
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
