import * as THREE from "three";

export const P40 = (model, config) => {
  const [{ scene: body }, { scene: plant }] = model;
  const group = new THREE.Group();
  group.name = "robot-p40-group";
  const box = new THREE.Box3().setFromObject(body.clone());
  let h = box.max.y - box.min.y;
  body.name = "robot-p40";
  group.add(body);

  plant.scale.y = 0.7;
  plant.name = "robot-p40-plant";
  group.add(plant);

  const boxMesh = new THREE.Mesh(
    new THREE.BoxBufferGeometry(box.max.x, 0.2, box.max.z),
    new THREE.MeshStandardMaterial({
      color: 0x0077f0,
      side: THREE.FrontSide,
      roughness: 1,
      metalness: 0,
    }),
  );
  boxMesh.name = "robot-p40-box";
  boxMesh.visible = false;
  boxMesh.position.y = h + h / 2;
  group.add(boxMesh);

  group.rotation.y = -90 * (Math.PI / 180);
  config.next && config.next(group);
};

export const P800 = (model, config) => {
  const [{ scene: body }, { scene: tray }] = model;
  const group = new THREE.Group();
  group.name = "robot-p800-group";
  body.name = "robot-p800";
  group.add(body);

  tray.name = "robot-p40-tray";
  group.add(tray);

  // group.rotation.z = 90 * (Math.PI / 180);
  // group.rotation.y = -90 * (Math.PI / 180);

  config.next && config.next(group);
};

// 这个模型中心点有点偏移；需要修正
export const RS = (model, config) => {
  const [{ scene: body }, { scene: tray }, { scene: lift }, { scene: lattice }] = model;
  const group = new THREE.Group();
  group.name = "robot-rs-group";

  // load body
  body.name = "robot-rs-body";
  group.add(body);

  // load lattice
  lattice.name = "robot-rs-lattice";
  lattice.visible = false;
  group.add(lattice);

  const liftGroup = new THREE.Group();
  liftGroup.name = "robot-rs-liftGroup";

  // load tray
  tray.name = "robot-rs-tray";
  liftGroup.add(tray);

  // load lift
  lift.name = "robot-rs-lift";
  liftGroup.add(lift);

  // load boxMesh
  const boxMesh = new THREE.Mesh(
    new THREE.BoxBufferGeometry(0.3, 0.3, 0.3),
    new THREE.MeshStandardMaterial({
      color: 0x0077f0,
      side: THREE.DoubleSide,
      roughness: 1,
      metalness: 0,
      transparent: true,
      opacity: 1,
    }),
  );
  boxMesh.name = "robot-rs-boxMesh";
  boxMesh.visible = false;
  liftGroup.add(boxMesh);

  group.add(liftGroup);
  // group.position.x = -(box.max.x - box.min.x) / 4;
  group.rotation.y = -90 * (Math.PI / 180);

  config.next && config.next(group);
};
