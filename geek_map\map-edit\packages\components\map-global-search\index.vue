<template>
  <div class="treeselect" ref="treeSelectRef">
    <el-select-v2
      size="mini"
      class="selectV2"
      v-model="selectValue"
      :options="selectV2Options"
      popper-class="globalSelectV2Poper"
      remote
      multiple
      filterable
      collapse-tags
      collapse-tags-tooltip
      :remote-method="remoteMethod"
      @visible-change="visibleChange"
      @change="changeSelect"
    >
    </el-select-v2>
    <el-tree-v2
      ref="treeRef"
      class="selectTreeV2"
      v-show="treeVisible"
      show-checkbox
      :props="treeProp"
      :data="treeOptionI18n"
      :default-checked-keys="selectValue"
      :filter-method="treeFilterMethod"
      @check-change="treeNodeCheck"
    >
      <template #default="{ node }">
        <span class="treeV2Text">{{ $t(node.label) }}</span>
      </template>
    </el-tree-v2>
  </div>
</template>

<script lang="ts" setup>
import { ref, Ref, computed, ComputedRef, onMounted, watch } from "vue";
import { useI18n } from "vue-i18n";
import { useEditMap } from "@packages/hook/useEdit";
import { addEventListener } from "@packages/hook/useEvent";
import type { TreeNode } from "element-plus/es/components/tree-v2/src/types";

const { t } = useI18n();
const editMap = useEditMap();
// 可筛选项目
const selectValue: Ref<string[]> = ref([]);
// 树数据
const treeOptions: Ref<any[]> = ref([]);
// 扁平化数组, 供给selectV2使用
const delayerList: Ref<any[]> = ref([]);
// 目前可用选项
const selectV2Options: ComputedRef<{ label: string; value: string }[]> = computed(() => {
  return delayerList.value
    .filter(item => {
      return selectValue.value.includes(item.value);
    })
    .map(item => {
      return {
        label: t(item.label),
        value: item.value,
      };
    });
});

// 树数据
const treeOptionI18n = computed(() => {
  return setI18nRecursion(treeOptions.value);
});

/**
 * 地图初始化完成/包括保存刷新时的操作
 */
addEventListener("map:ready", () => {
  const dataAll = editMap.value?.getAllData();
  if (dataAll) {
    treeOptions.value = [];
    const labelDict = {
      AREA: "areaCode",
      CELL: "cellCode",
      CHARGER: "chargerId",
      ELEVATOR: "elevatorId",
      LINE: "segmentId",
      MARKER: "name",
      STATION: "stationId",
    };
    const valueDict = {
      AREA: "id",
      CELL: "nodeId",
      CHARGER: "id",
      ELEVATOR: "id",
      LINE: "segmentId",
      MARKER: "id",
      STATION: "id",
    };
    const i18nDict = {
      AREA: "lang.rms.fed.area",
      CELL: "lang.rms.fed.cell",
      CHARGER: "lang.rms.fed.optionCharger",
      ELEVATOR: "lang.rms.fed.elevator",
      SAFE: "lang.rms.map.dmp.edit",
      LINE: "lang.rms.fed.line",
      MARKER: "lang.rms.web.map.element.marker",
      STATION: "lang.rms.fed.station",
    };

    Object.keys(dataAll).forEach(key => {
      const labelDictByKey = (<any>labelDict)[key];
      const valueDictByKey = (<any>valueDict)[key];
      const i18nDictByKey = (<any>i18nDict)[key];
      const list = dataAll[key].map((item: any) => {
        return {
          label: item[labelDictByKey],
          value: item[valueDictByKey],
          type: key,
        };
      });
      // console.log("treeOptions::", treeOptions);
      // console.log("treeOptions::i18nDictByKey", i18nDictByKey);
      // console.log("treeOptions::key", key);
      // console.log("treeOptions::list", list);
      if (!!i18nDictByKey && list.length > 0) {
        treeOptions.value.push({
          label: i18nDictByKey,
          value: key,
          children: list,
        });
      }

      delayerList.value.push({ label: i18nDictByKey, value: key, children: list }, ...list);
    });
  }
});

/**
 * 递归翻译tree v2国际化
 * 后面联动的检索需要用到
 * @param data
 */
function setI18nRecursion(data: any[]) {
  const list: any[] = [];
  data.forEach(({ label, children, ...itemData }) => {
    const option = { label: t(label), ...itemData };
    if (children) {
      option.children = setI18nRecursion(children);
    }
    list.push(option);
  });
  return list;
}

const treeRef = ref();
const treeVisible: Ref<boolean> = ref(false);
const treeProp = { value: "value" };
function visibleChange(visible: boolean) {
  if (visible) {
    treeVisible.value = visible;
    treeRef.value!.filter("");
  }
}

function treeFilterMethod(query: string, node: TreeNode) {
  return node.label!.includes(query);
}

function remoteMethod(keyword: string) {
  treeRef.value!.filter(keyword);
}

/**
 * 点击树菜单
 * @param data
 * @param check
 */
function treeNodeCheck() {
  const selectList = treeRef.value!.getCheckedKeys() || [];

  /**
   * 这里需要处理一下展示的逻辑
   * 父节点没有key, 如果有key字段则检查有没有父节点,
   */

  let list: string[] = [];
  const parentNodeList: string[] = [];
  // 1. 标记下标, 以供数据处理完成后恢复排序
  const dataList = selectList.map((item: any) => {
    const dataItem = delayerList.value.find(delayerItem => delayerItem.value === item);
    if (!dataItem.type) {
      parentNodeList.push(dataItem.value);
    }
    return dataItem;
  });

  dataList.forEach((item: any) => {
    if (item.type) {
      if (!parentNodeList.includes(item.type)) {
        list.push(item.value);
      }
    } else {
      list.push(item.value);
    }
  });

  selectValue.value = list;
}

const treeSelectRef = ref();
onMounted(() => {
  document.body.addEventListener("click", event => {
    const path: string[] = (<any>event).path;
    if (!path) return (treeVisible.value = false);
    const treeSelEl = treeSelectRef.value;
    if (!path.includes(treeSelEl)) {
      treeVisible.value = false;
    }
  });
});

function changeSelect(list: string[]) {
  treeRef.value!.setCheckedKeys(list);
}

watch(selectValue, values => {
  const selectList = treeRef.value?.getCheckedNodes() || [];
  const selLayerMap: { [k: string]: string[] } = {};

  selectList.forEach((item: any) => {
    const type = item.type;
    if (type) {
      selLayerMap[type] || (selLayerMap[type] = []);
      selLayerMap[type].push(item.value);
    }
  });

  const multiSearchData = Object.keys(selLayerMap).map(layerName => {
    const ids = selLayerMap[layerName];
    return { layerName, ids };
  });

  editMap.value?.multiSearch(multiSearchData);
});
</script>

<style scoped lang="scss">
.treeselect {
  position: absolute;
  right: 220px;
  z-index: 6;
  width: 300px;
  top: 0px;

  .selectV2 {
    top: 0px;
    position: absolute;
    width: 100%;
  }

  .selectTreeV2 {
    position: absolute;
    top: 32px;
    left: 0;
    /* height: 600px; */
    width: 100%;
    background: #fff;
    box-shadow: 0px 0px 10px 0px #eee;
    padding: 5px;
    box-sizing: border-box;
    border: 1px solid #eee;
  }

  .treeV2Text {
    font-size: 14px;
  }
}
</style>

<style>
.treeselect .selectV2 .el-select-v2__wrapper {
  border-radius: 0px;
}

.globalSelectV2Poper {
  display: none !important;
}
</style>
