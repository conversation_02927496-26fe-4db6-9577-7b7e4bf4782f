/* ! <AUTHOR> at 2022/08/27 */
import * as PIXI from "pixi.js";

class LayerLocation implements MRender.Layer {
  private floorId: floorId;
  private mapCore: any;
  private meshList: Array<any> = [];
  private container: PIXI.Container;
  private rendered: boolean = false;
  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "location";
    container.zIndex = utils.getLayerZIndex("cell");
    container.interactiveChildren = false;
    container.visible = false;
    this.container = container;

    this.mapCore = mapCore;
    this.floorId = floorId;
  }

  render(): void {
    const _this = this;
    const mapCore = _this.mapCore;
    const mapData = mapCore.mapData;
    const cells: mCellData[] = mapData.cell.getByFloorId(this.floorId);
    if (!cells.length) return;

    const utils = mapCore.utils;
    const vColor = utils.getShaderColor("LOCATION") || 0x000000;
    const shader = utils.getShader("rect");

    const fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    for (let i = 0, len = Math.ceil(cells.length / fragment); i < len; i++) {
      const arr = cells.slice(i * fragment, i * fragment + fragment);

      let geometries: any[] = [];
      let position, geometry;
      arr.forEach(mCellData => {
        position = _this.formatLocation(mCellData);
        geometry = utils.drawGeometry("rect", position, vColor);
        geometries.push(geometry);
      });

      if (!geometries.length) return;

      let mesh = utils.createMesh(geometries, shader);
      mesh.name = "location";
      mesh.mapType = "location";
      mesh.interactive = mesh.buttonMode = false;

      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }

    this.rendered = true;
  }

  toggle(isShow: boolean): void {
    if (isShow && !this.rendered) {
      this.render();
    }
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];
    this.rendered = false;
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.meshList = null;
    this.mapCore = null;
    this.rendered = null;
  }

  private formatLocation(options: mCellData): any {
    const location = options.location;
    const size = 0.05;

    const lx = Number((location.x - size).toFixed(3));
    const ly = Number((-location.y - size).toFixed(3));
    const lx1 = Number((location.x + size).toFixed(3));
    const ly1 = Number((-location.y + size).toFixed(3));
    return [lx, ly, lx1, ly, lx1, ly1, lx, ly1];
  }
}
export default LayerLocation;
