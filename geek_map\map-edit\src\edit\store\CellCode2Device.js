export default class CellCode2Device {
  constructor(props) {
    this.cellCode2Device = new Map()
  }

  //插入数据
  insert(cellCode,ops){
    const {layerName,id} = ops
    if(!this.cellCode2Device.has(cellCode)){
      this.cellCode2Device.set(cellCode,[ops])
    }else{
      const relatedArr = this.cellCode2Device.get(cellCode)
      const filter = relatedArr.filter(item => (item.layerName === layerName && item.id === id))
      if(!filter.length){
        relatedArr.push(ops)
        this.cellCode2Device.set(cellCode,relatedArr)
      }
    }
  }
  //删除cellCode和设备的关系
  delete(cellCode,ops){
    const {layerName,id} = ops
    if(ops){
      const relatedArr = this.cellCode2Device.get(cellCode)
      // const arr = this.cellCode2Device[cellCode]
      if(!relatedArr || !relatedArr.length) return
      const index = relatedArr.findIndex(item => (item.layerName === layerName && item.id === id))
      relatedArr.splice(index,1)
      if(relatedArr.length){
        this.cellCode2Device.set(cellCode,relatedArr)
      }else{
        this.cellCode2Device.delete(cellCode)
      }
    }else{
      this.cellCode2Device.delete(cellCode)
    }
  }
  //当cellCode发生变更时，更新关系
  update(oldCellCode,newCellCode) {
    if(oldCellCode === newCellCode) return
    const relatedArr = this.cellCode2Device.get(oldCellCode)
    this.cellCode2Device.set(newCellCode,relatedArr)
    this.cellCode2Device.delete(oldCellCode)
  }
  getDevice(cellCode){
    console.log(this.cellCode2Device)
    return this.cellCode2Device.get(cellCode) || []
  }
  clear() {
    this.cellCode2Device.clear()
  }
}
