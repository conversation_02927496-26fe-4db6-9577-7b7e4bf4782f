// 留接口后续做回退，撤销之类的动作；
// import { CUSTOM_MODEL_TYPE } from "../constant/elements";
import mapDataInstance from "../store/data";

let _undoStash = [];
class Command {
  constructor(options) {
    this.Map3d = options.Map3d;
    this.needSave = false;
  }
  set undoStash(value) {
    _undoStash.push(value);
    this.needSave = !!_undoStash.length;
  }

  destory() {
    _undoStash = [];
  }
  exec(action, config, success) {
    action && !action.includes("pure:") && (this.undoStash = { action, config });
    this._dealAction(action, config, success);
  }
  _dealAction(action, config, success) {
    switch (action) {
      case "update":
        this._update(config, success);
        break;
      case "add":
        this._add(config, success);
        break;
      case "del":
        this._del(config, success);
        break;
      default:
        break;
    }
  }
  _update(config, success) {
    const { oldValue, newValue } = config;
    this.Map3d.modelInstances[oldValue.uuid].update &&
      this.Map3d.modelInstances[oldValue.uuid].update(newValue, oldValue);
    success && success();
  }
  _add(config, success) {
    const { value } = config;
    const { category } = value;
    this.Map3d._renderModel([value], {
      done() {
        success && success();
      },
    });
    mapDataInstance.addModelData(category, value);
  }
  // 支持单个、多个删除；
  _del(config, success) {
    const { value } = config;
    const dels = Array.isArray(value) ? value : [value];
    for (let i = 0; i < dels.length; i++) {
      const item = dels[i];
      const { category } = item;
      const obj = this.Map3d.modelInstances[item.uuid];
      this.Map3d.scene.remove(obj.model);
      delete this.Map3d.modelInstances[item.uuid];
      mapDataInstance.delModelData(category, value);
    }
    success && success();
  }
}

export default Command;
