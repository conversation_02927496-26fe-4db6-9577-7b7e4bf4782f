/* ! <AUTHOR> at 2022/08/27 */
import * as PIX<PERSON> from "pixi.js";
import LayerRoadLoad from "./road-load";
import LayerRoadUnload from "./road-unload";

class LayerRoad implements MRender.Layer {
  floorId: floorId;
  layerRoadLoad: LayerRoadLoad = new LayerRoadLoad();
  layerRoadUnload: LayerRoadUnload = new LayerRoadUnload();
  private floor: any;
  private fillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  private container: any;
  private mapCore: MRender.MainCore;
  private meshList: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  render(segments: Array<any>): void {
    const mapCore = this.mapCore;

    const fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    for (let i = 0, len = Math.ceil(segments.length / fragment); i < len; i++) {
      const arr = segments.slice(i * fragment, i * fragment + fragment);

      const { sLineGeometry, bezier } = this.resolveSegments(arr);
      if (sLineGeometry) {
        const sLine = new PIXI.Graphics(sLineGeometry);
        this.meshList.push(sLine);
        this.container.addChild(sLine);
      }
      if (bezier) {
        this.meshList.push(bezier);
        this.container.addChild(bezier);
      }
    }
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    const _this = this;
    _this.meshList.forEach(mesh => mesh.destroy(true));
    _this.meshList = [];

    _this.layerRoadLoad.destroy();
    _this.layerRoadUnload.destroy();
    _this.container.destroy({ children: true });
  }

  destroy(): void {
    this.repaint();
    const _this = this;
    _this.layerRoadLoad = null;
    _this.layerRoadUnload = null;

    _this.fillStyle = null;
    _this.lineStyle = null;
    _this.floor = null;
    _this.container = null;
    _this.mapCore = null;
    _this.meshList = null;
  }

  init(): void {
    const mapCore = this.mapCore;
    const floorId = this.floorId;

    const fillStyle = this.fillStyle;
    fillStyle.color = mapCore.utils.getOriginColor("SEGMENT");
    fillStyle.visible = true;
    fillStyle.alpha = 0.8;
    this.lineStyle.visible = false;

    let container = new PIXI.Container();
    container.name = "road";
    container.zIndex = mapCore.utils.getLayerZIndex("road");
    container.interactiveChildren = false;
    container.visible = true;
    this.container = container;

    this.layerRoadLoad.init(mapCore, floorId);
    this.layerRoadUnload.init(mapCore, floorId);

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(
      container,
      this.layerRoadLoad.getContainer(),
      this.layerRoadUnload.getContainer(),
    );
  }

  private resolveSegments(segments: Array<any>): any {
    const _this = this;
    const utils = _this.mapCore.utils,
      fillStyle = _this.fillStyle,
      lineStyle = _this.lineStyle;

    let sum = 0;
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();

    let sumB = 0;
    let bezier = new PIXI.Graphics();
    bezier.interactive = bezier.buttonMode = false;

    let _segments: mSegment[] = [];
    for (let i = 0, len = segments.length; i < len; i++) {
      const item = segments[i];
      const segmentType = item?.segmentType;
      if (segmentType === "S_LINE") {
        const options = utils.formatRoadLine(item);
        if (!options) continue;
        ++sum;
        graphicsGeometry.drawShape(new PIXI.Polygon(options["shapeData"]), fillStyle, lineStyle);
        _segments.push(options);
      }

      if (segmentType === "BEZIER") {
        const options = utils.formatRoadBezier(item);
        if (!options) continue;
        if (sumB === 0) {
          bezier.lineStyle(options["lineWidth"] * 2, utils.getOriginColor("SEGMENT"));
        }
        ++sumB;
        const paths = options["paths"];
        bezier.moveTo(paths[0].x, paths[0].y);
        bezier.bezierCurveTo(
          paths[1].x,
          paths[1].y,
          paths[2].x,
          paths[2].y,
          paths[3].x,
          paths[3].y,
        );
        _segments.push(options);
      }
    }

    let data: { sLineGeometry: any; bezier: any } = { sLineGeometry: null, bezier: null };
    if (sum > 0) {
      data.sLineGeometry = graphicsGeometry;
    }
    if (sumB > 0) {
      bezier.endFill();
      data.bezier = bezier;
    }

    if (_segments.length) {
      _this.mapCore.mapData.segment.setData(this.floorId, _segments);
    }

    return data;
  }
}
export default LayerRoad;
