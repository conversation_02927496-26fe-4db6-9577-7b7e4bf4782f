<template>
  <section>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      @row-add="rowAdd"
      @row-edit="rowEdit"
      @row-config-tag="rowConfigTag"
      @row-del="rowDel"
      style="margin-top: 10px"
    />

    <callbackMsgChannelTagDialog
      v-if="channelTagDialog"
      :channel-tag-dialog.sync="channelTagDialog"
      :item-channel-data="itemData"
      @save="save"
      @update:channelTagDialog="getTableList()"
    />

    <el-dialog
      :title="$t(dialogTitle)"
      :visible.sync="channelEditDialog"
      :before-close="closeChannelEditDialog"
      center
      :append-to-body="true"
      :close-on-click-modal="false"
      width="60%"
    >
      <el-form
        v-if="channelEditDialog"
        ref="ruleForm"
        label-position="top"
        label-width="80px"
        :model="itemData"
        :rules="rules"
        class="padding_20"
      >
        <el-col :span="7">
          <el-form-item
            :label="$t('lang.rms.fed.channelId')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
            prop="channelId"
          >
            <el-input v-if="isEdit" v-model="itemData.channelId" type="text" readonly="readonly" />
            <el-input v-else v-model="itemData.channelId" type="text" />
          </el-form-item>
        </el-col>
        <el-col :span="7" class="ml-10">
          <el-form-item
            :label="$t('lang.rms.fed.channelType')"
            :rules="[{ required: true, message: $t('lang.rms.fed.choose') }]"
            prop="channelType"
          >
            <el-select v-model="itemData.channelType" :placeholder="$t('lang.rms.fed.pleaseChoose')">
              <el-option v-for="(item, key) in channelTypeList" :key="key" :label="item" :value="key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7" class="ml-10">
          <el-form-item
            :label="$t('lang.rms.fed.enable')"
            :rules="[{ required: true, message: $t('lang.rms.fed.choose') }]"
            prop="enable"
          >
            <el-select v-model="itemData.enable" :placeholder="$t('lang.rms.fed.pleaseChoose')">
              <el-option v-for="(item, key) in enableList" :key="key" :label="$t(item)" :value="key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item
            :label="$t('lang.rms.fed.callbackMaxRetryTimes')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
            prop="maxRetryTimes"
          >
            <el-input v-model="itemData.maxRetryTimes" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="7" class="ml-10">
          <el-form-item
            :label="$t('lang.rms.fed.callbackMaxRetryTimeout')"
            :rules="[{ required: true, message: $t('lang.rms.fed.pleaseEnter') }]"
            prop="maxRetryTimeout"
          >
            <el-input v-model="itemData.maxRetryTimeout" type="number" />
          </el-form-item>
        </el-col>
        <el-col :span="7" class="ml-10">
          <el-form-item :label="$t('lang.rms.fed.channelUrl')" prop="channelUrl" :rules="channelUrlRule">
            <el-input v-model="itemData.channelUrl" type="text" />
          </el-form-item>
        </el-col>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeChannelEditDialog">{{ $t("lang.common.cancel") }}</el-button>
        <el-button type="primary" :loading="loadingSave" @click="save">{{ $t("lang.rms.fed.save") }}</el-button>
      </span>
    </el-dialog>
  </section>
</template>
<script>
import callbackMsgChannelTagDialog from "./callbackMsgChannelTagDialog";

export default {
  name: "CallbackChannel",
  components: {
    callbackMsgChannelTagDialog,
  },
  data() {
    const isGuest = this.isRoleGuest();
    return {
      form: {
        channelId: "",
        channelType: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          channelId: {
            label: "lang.rms.fed.channelId",
            default: "",
            tag: "input",
          },
          channelType: {
            label: "lang.rms.fed.channelType",
            default: "",
            tag: "input",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: { index: true },
        actions: [
          {
            label: "lang.rms.fed.add",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          {
            label: "lang.rms.fed.channelId",
            align: "center",
            prop: "channelId",
          },
          {
            label: "lang.rms.fed.channelType",
            prop: "channelType",
            width: "160",
          },
          {
            label: "lang.rms.fed.enable",
            prop: "enable",
            width: "120",
            formatter: (row, column, cellValue, index) => {
              switch (cellValue) {
                case 1:
                  return this.$t("lang.rms.fed.enable");
                case 0:
                  return this.$t("lang.rms.fed.disable");
                default:
                  return cellValue;
              }
            },
          },
          {
            label: "lang.rms.fed.callbackMaxRetryTimes",
            prop: "maxRetryTimes",
            width: "100",
          },
          {
            label: "lang.rms.fed.callbackMaxRetryTimeout",
            prop: "maxRetryTimeout",
            width: "100",
          },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                width: "180",
                operations: [
                  {
                    label: "lang.rms.fed.edit",
                    handler: "row-edit",
                  },
                  {
                    label: "lang.rms.fed.callbackConfigChannelTag",
                    handler: "row-config-tag",
                  },
                  {
                    label: "lang.rms.fed.delete",
                    handler: "row-del",
                  },
                ],
              },
        ),
      },

      itemData: {},
      enableList: {
        1: "lang.rms.fed.enable",
        0: "lang.rms.fed.disable",
      },
      channelTypeList: {
        HTTP: "HTTP",
        WEBSOCKET: "WEBSOCKET",
        SOCKET: "SOCKET",
        RPC: "RPC",
        DMP: "DMP",
      },
      isEdit: false,
      channelEditDialog: false,
      channelTagDialog: false,
      rules: {
        channelId: [{ required: true, message: this.$t("lang.rms.fed.pleaseEnter") }],
      },
      loadingSave: false
    };
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? "lang.rms.fed.edit" : "lang.rms.fed.add";
    },
    channelUrlRule() {
      const HTTP = "HTTP";
      return this.itemData.channelType === HTTP
        ? [{ required: true, message: this.$t("lang.rms.fed.pleaseEnter") }]
        : [];
    },
  },
  activated() {
    this.getTableList();
  },
  methods: {
    // 编辑
    rowAdd() {
      this.isEdit = false;
      this.channelEditDialog = true;
      this.channelTagDialog = false;
      // 编辑前先重置属性框
      this.$nextTick(() => {
        this.$refs["ruleForm"].resetFields();
        this.itemData = {};
      });
    },
    // 编辑
    rowEdit(row) {
      this.isEdit = true;
      this.channelEditDialog = true;
      this.channelTagDialog = false;
      // 编辑前先重置属性框
      this.$nextTick(() => {
        this.$refs["ruleForm"].resetFields();
        this.itemData = { ...row };
        this.itemData.enable = row.enable + "";
      });
    },
    // 配置标签
    rowConfigTag(row) {
      this.itemData = JSON.parse(JSON.stringify(row));
      this.channelTagDialog = true;
      this.channelEditDialog = false;
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        const msgChannelIds = [];
        msgChannelIds.push(row.id);
        const para = { msgChannelIds };
        $req.post("/athena/apiCallback/deleteMsgChannel", para).then(res => {
          if (res?.code === 0) {
            this.$success(this.$t(res.msg));
            this.getTableList();
          }
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { channelId, channelType } = this.form;
      const { currentPage, pageSize } = this.tablePage;

      let data = { language: $utils.Data.getLocalLang() };
      if (channelId) data.channelId = channelId;
      if (channelType) data.channelType = channelType;
      $req
        .post(`/athena/apiCallback/msgChannelPageList?currentPage=${currentPage}&pageSize=${pageSize}`, data)
        .then(res => {
          let result = res?.data;
          if (!result) return;
          this.tableData = result.recordList || [];
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: result.currentPage || 1,
            pageCount: result.pageCount || 0,
          });
        });
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
    // 保存
    save() {
      if(this.loadingSave) return
      this.loadingSave = true;
      // channelTagDialog
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          // this.loadingSave = true;
          const data = this.itemData;
          data.msgChannelId = this.itemData.id;
          // 保存通道配置
          $req
            .post("/athena/apiCallback/saveMsgChannel", data)
            .then(json => {
              if (json.code === 0) {
                this.$message.success(this.$t(json.msg));
                // this.channelEditDialog = false;
                this.getTableList();
              } else {
              }
              this.channelEditDialog = false
              this.loadingSave = false;
            })
            .catch(() => {
              this.channelEditDialog = false
              this.loadingSave = false;
            });
        }else{
          this.loadingSave = false
        }
      });
    },
    closeChannelEditDialog() {
      this.channelEditDialog = false;
      this.itemData = {};
    },
  },
};
</script>
<style scoped>
.mt-20 {
  margin-top: 20px;
}

.ml-10 {
  margin-left: 10px;
}

.w_100x {
  width: 100%;
}

.btnwarp {
  padding: 43px 0 0;
}
</style>
