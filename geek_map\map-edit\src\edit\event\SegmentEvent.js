import {getGlobalViewport} from "../global";
import CtrlPoint from '../element/baseElement/CtrlPoint';
import Line from '../element/baseElement/Line'
import SegmentArrow from '../element/baseElement/SegmentArrow'
import {Graphics} from "pixi.js";
import Mode from '../Mode'
import LayerManager from '../layerManager/LayerManager'
import {isHoverNode,pixi2cad,createId} from '../utils/utils'
import EventBus from "../eventBus/EventBus";
import {lineStyle} from "../config";
const {ACTIVE_LINE} = lineStyle
export default class SegmentEvent {
  static addEvents(defConfig) {
    const vp = getGlobalViewport()
    const operateLayerInstance = LayerManager.get('OPERATE')
    const $operateLayer = operateLayerInstance.container
    const $line = new Graphics()
    //默认空负载正方向
    $line.loadDirs = 1
    $line.unloadDirs = 1
    const $container = new Graphics();
    let paths = []
    $container.addChild($line)
    $operateLayer.addChild($container)
    const finished = () => {
      if(paths.length < 2) {
        $operateLayer.removeChildren()
        Mode.resetMode()
        return
      }
      //将pixi坐标转化为cad坐标
      const invertPaths = paths.map(path => {
        return pixi2cad(path)
      })
      const addOp = {
        id: 'LINE',
        data:[{
          segmentId:createId(),
          points:invertPaths,
          segmentType:'S_LINE',
          loadDirs:1,
          unloadDirs:1,
          ...defConfig
        }]
      }
      Mode.resetMode()
      $operateLayer.removeChildren()
      LayerManager.addElements(addOp)
    }
    const events = {
      clicked:e => {
        const p = e.world
        const hoverNode = isHoverNode(p)
        let $point;
        let pathLen = paths.length
        if(!hoverNode) return EventBus.$emit('message',{type:'warning',text:'请点击在单元格上'});
        // if(hoverNode){
        //   const {x,y,nodeId,cellCode} = hoverNode
        //   //进行重复校验，新的点与前一个点不允许相等
        //   if(pathLen && (paths[pathLen - 1].nodeId === nodeId)) return
        //   $point = CtrlPoint.render(x,y)
        //   paths.push({x,y,nodeId,cellCode})
        // }else{
        //   $point = CtrlPoint.render(p.x,p.y)
        //   paths.push({x:p.x,y:p.y,nodeId:null,cellCode:null})
        // }
        const {x,y,nodeId,cellCode} = hoverNode
        //进行重复校验，新的点与前一个点不允许相等
        if(pathLen && (paths[pathLen - 1].nodeId === nodeId)) return
        $point = CtrlPoint.render(x,y)
        paths.push({x,y,nodeId,cellCode})
        $container.addChild($point)
        //渲染线
        $line.clear()
        Line.render($line,paths)
        SegmentArrow.render($line,{paths,color:ACTIVE_LINE})
      },
      mousemove:e => {
        const moveP = e.data.getLocalPosition(vp);
        if(!paths.length) return
        let renderP;
        const hoverNode = isHoverNode(moveP)
        if(hoverNode){
          const {x,y,nodeId,cellCode} = hoverNode
          renderP = {x,y,nodeId,cellCode}
        }else{
          renderP = {x:moveP.x,y:moveP.y,nodeId:null,cellCode:null}
        }
        const movePaths = [...paths,renderP]
        //渲染线
        $line.clear()
        Line.render($line,movePaths)
        SegmentArrow.render($line,{paths:movePaths,color:ACTIVE_LINE})
      },
      keyup:e => {
        const {key} = e
        if(key === 'Escape'){
          EventBus.$emit('keydown:Escape')
          finished()
        }
      }
    }
    return events
  }
}
