<template>
  <el-dialog
    :title="$t(title)"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="520px"
  >
    <geek-customize-form ref="refForm" :form-config="formConfig" />

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditRobotInstanceDialog",
  props: ["robotTypes"],
  data() {
    return {
      dialogVisible: false,
      operation: "",
      rowData: {},
    };
  },
  computed: {
    title() {
      switch (this.operation) {
        case "view":
          return "lang.rms.api.result.warehouse.viewRobotModelInstance";
        case "add":
          return "lang.rms.api.result.warehouse.createRobotModelInstance";
        case "edit":
          return "lang.rms.api.result.warehouse.editRobotModelInstance";
      }
    },

    formConfig() {
      let disabled = false;
      if (this.operation == "view") disabled = true;
      return {
        attrs: {
          labelWidth: "130px",
          labelPosition: "right",
          disabled,
        },
        configs: {
          robotId: {
            label: "lang.mb.robotManage.robotId",
            default: "",
            tag: "input",
            required: true,
            disabled: this.operation == "edit",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRobotID",
            rules: [
              {
                trigger: "blur",
                validator: (rule, value, callback) => {
                  if (!value) {
                    callback(new Error(this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotID")));
                  } else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          hostCode: {
            label: "lang.rms.api.result.warehouse.robotAlias",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRobotAlias",
          },
          robotModelId: {
            label: "lang.rms.api.result.warehouse.robotModel",
            tag: "select",
            default: "",
            required: true,
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.robotTypes,
            rules: [
              {
                trigger: "blur",
                validator: (rule, value, callback) => {
                  if (!value) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseChoose")));
                  } else callback();
                },
              },
            ],
          },
          productionBatch: {
            label: "lang.rms.api.result.warehouse.productionBatch",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRobotBatch",
          },
        },
      };
    },
  },
  methods: {
    open(type, data) {
      this.operation = type;
      this.rowData = data || {};
      this.dialogVisible = true;

      const params = {
        robotId: data?.robotId || "",
        hostCode: data?.hostCode || "",
        robotModelId: data?.robotModelEntity?.id || "",
        productionBatch: data?.productionBatch || "",
      };
      this.$nextTick(() => {
        this.$refs.refForm.reset();
        this.$refs.refForm.setData(params);
      });
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      if (this.operation == "view") {
        this.close();
        return;
      }

      this.$refs.refForm.validate().then(data => {
        let formData = Object.assign({}, data);
        if (this.operation == "edit") formData.id = this.rowData.id;

        $req.post("/athena/robot/manage/robotSave", formData).then(res => {
          this.$success();
          this.close();
          this.$emit("updateList");
        });
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
