<template>
  <section style="height: calc(100% - 32px); position: relative">
    <map-loading v-if="loading" />
    <div class="map-tools">
      <el-date-picker
        v-model="dataRange"
        type="datetimerange"
        size="mini"
        align="right"
        :start-placeholder="$t('lang.rms.fed.startTime')"
        :end-placeholder="$t('lang.rms.fed.endTime')"
        :default-time="['00:00:00', '23:59:59']"
      />
      <el-select v-model="heatType" size="mini" :placeholder="$t('lang.rms.fed.pleaseChoose')">
        <el-option v-for="item in heatTypes" :key="item.value" :label="$t(item.label)" :value="item.value" />
      </el-select>
      <el-select v-model="robotType" size="mini" :placeholder="$t('lang.rms.fed.pleaseChoose')" style="width: 100px">
        <el-option v-for="item in robotTypes" :key="item.value" :label="$t(item.label)" :value="item.value" />
      </el-select>

      <el-dropdown
        v-if="heatType === 'SHELF_PLACEMENT_HEAT'"
        split-button
        size="mini"
        type="primary"
        @command="handleCommand"
      >
        {{ dropText }}
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="item in dropdownList" :key="item.command" :command="item.command">
            {{ $t(item.label) }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-select
        v-if="dropCommand === 'd'"
        v-model="areaIds"
        size="mini"
        multiple
        collapse-tags
        placeholder="areaIds"
        style="width: 150px"
      >
        <el-option v-for="item in areas" :key="item.value" :label="$t(item.label)" :value="item.value" />
      </el-select>
      <el-select
        v-if="dropCommand === 'b' || dropCommand === 'c'"
        v-model="stationIds"
        size="mini"
        multiple
        collapse-tags
        placeholder="stationIds"
        style="width: 150px"
      >
        <el-option v-for="item in stations" :key="item.value" :label="$t(item.label)" :value="item.value" />
      </el-select>

      <el-button type="primary" size="mini" @click="getHeatData">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </div>
    <div id="J_Monitor2DMapBox" class="map-box"></div>

    <div class="tu-li-list">
      <p v-for="(item, key) in heatColors" :key="key">
        <span :style="{ background: `#${item.toString(16)}` }" />
        <label>{{ gradientRange[key] ? gradientRange[key].join(" ~ ") : "--" }}</label>
      </p>
    </div>
  </section>
</template>

<script>
import MapLoading from "../../components/map-loading.vue";
// import { MapRender, MapWorker } from "../../../../geek_modules/ts-map-fe/libs/monitor2d.min.js";
import { MapRender, MapWorker } from "geek-monitor2d";

let mapRender = null;
let mapWorker = null;
export default {
  name: "statisticsMap",
  components: { MapLoading },
  data() {
    const now = new Date();
    const y = now.getFullYear();
    const m = now.getMonth() + 1;
    const d = now.getDate();
    return {
      loading: false,
      heatTypes: [],
      robotTypes: [],
      areas: [],
      stations: [],
      dropdownList: [
        { command: "a", label: "货架老家位" },
        { command: "b", label: "单元格热力分布【关联工位】" },
        { command: "c", label: "区域热力分布【关联工位】" },
        { command: "d", label: "工位热力分布【关联区域】" },
      ],

      gradientRange: {},
      heatColors: {},
      areasData: null,

      dataRange: [`${y}-${m}-${d} 00:00:00`, `${y}-${m}-${d} 23:59:59`],
      robotType: "ALL",
      heatType: "",
      dropCommand: "a",
      areaIds: [],
      stationIds: [],
    };
  },
  computed: {
    dropText() {
      return this.dropdownList.find(item => item.command == this.dropCommand)?.label;
    },
  },
  watch: {
    heatType(val) {
      this.areaIds = [];
      this.stationIds = [];
      this.dropCommand = "a";
    },
  },
  mounted() {
    const $dom = document.getElementById("J_Monitor2DMapBox");
    if (!mapRender || !mapWorker) {
      $dom && this.initMap($dom);
    }
    this.getHeatTypes();
    this.getRobotTypes();
    this.getStations();
    this.getAreas();
  },
  destroyed() {
    if (mapRender) {
      mapRender.destroy();
      mapRender = null;
    }
    if (mapWorker) {
      mapWorker.destroy();
      mapWorker = null;
    }
  },
  methods: {
    getHeatData() {
      const dataRange = this.dataRange;
      const heatType = this.heatType;
      if (!dataRange || !dataRange[0] || !dataRange[1]) {
        this.$warning(this.$t("lang.rms.fed.chooseTimeRange"));
        return;
      }
      if (!heatType) {
        this.$warning(this.$t("lang.rms.fed.pleaseChoose"));
        return;
      }

      let params = {
        startTime1: new Date(dataRange[0]).getTime(),
        endTime1: new Date(dataRange[1]).getTime(),
        statType: heatType,
      };
      if (heatType === "SHELF_PLACEMENT_HEAT") {
        switch (this.dropCommand) {
          case "b":
            params.stationIds = this.stationIds;
            params.isNeedAreaHeat = false;
            break;
          case "c":
            params.stationIds = this.stationIds;
            params.isNeedAreaHeat = true;
            break;
          case "d":
            params.areaIds = this.areaIds;
            break;
        }
      }

      this.removeHeat();
      this.gradientRange = {};

      $req
        .post("/athena/map/monitor/stat/heat", params)
        .then(async res => {
          const data = res?.data?.dataMap || {};
          const robotType = this.robotType;
          this.gradientRange = res?.data?.gradientRange || {};

          let heatData1;
          if (robotType === "ALL") heatData1 = data?.time1["DEFAULT"] || {};
          else heatData1 = data?.time1[robotType] || {};

          if (heatType === "SHELF_HEAT") {
            this.resolveShelfHeat(heatData1);
          } else if (heatType === "SHELF_PLACEMENT_HEAT" && ["c"].includes(this.dropCommand)) {
            if (!this.areasData) await this.getAreasData();
            this.resolveAreaHeat(heatData1);
          } else {
            this.resolveCellHeat(heatData1);
          }
        })
        .catch(e => {
          console.error(e);
        });
    },

    removeHeat() {
      mapRender.renderFeatureColor({ type: "cell", clear: true });
      mapRender.renderFeatureColor({ type: "area", clear: true });
      mapRender.rerender();
      mapRender.toggleLayer("shelfHeat", false);
      mapRender.updateDisplays({});
    },

    resolveAreaHeat(heatAreas) {
      const areasData = this.areasData;
      heatAreas.forEach(item => {
        const cellList = areasData[item.code] || null;

        if (!cellList) return;
        mapRender.renderFeatureColor({
          type: "area",
          color: this.heatColors[item.gradient],
          codes: cellList,
        });
      });
      mapRender.rerender();
      // debugger;
    },

    resolveShelfHeat(heatShelves) {
      let shelfHeatData = {};
      heatShelves.forEach(item => {
        shelfHeatData[item.code] = item.gradient * 10;
      });

      mapRender.toggleLayer("shelfHeat", true, shelfHeatData);
      mapRender.updateDisplays({});
    },
    resolveCellHeat(heatCells) {
      let fCells = {};
      heatCells.forEach(cell => {
        if (!fCells[cell.gradient]) fCells[cell.gradient] = [];
        fCells[cell.gradient].push(cell.code);
      });

      for (let key in fCells) {
        mapRender.renderFeatureColor({
          type: "cell",
          color: this.heatColors[key],
          codes: fCells[key],
        });
      }
      mapRender.rerender();
    },

    initMap($dom) {
      mapRender = new MapRender($dom);
      const color = mapRender.getMapColors();
      this.heatColors = color.HOT_CONF;
      mapRender.ready(() => {
        mapWorker = new MapWorker(this.getWsUrl());
        mapWorker.onCallBack((dataType, data) => {
          switch (dataType) {
            case "wsInitFloors":
              mapRender && mapRender.renderFloors(data.floorsData);
              break;
            case "wsInitDisplay":
              mapRender && mapRender.renderDisplays({ isInitFinish: data?.isInitFinish, shelves: data.shelves });
              break;
          }
        });
        mapWorker.init();
        mapWorker.reqFloors();
      });
      mapRender.rendered(renderType => {
        switch (renderType) {
          case "floorRendered":
            this.loading = false;
            break;
          case "displayRendered":
            mapWorker.reqUpdate();
            break;
        }
      });

      mapRender.init();
    },

    async getAreasData() {
      const res = await $req.get("/athena/engine/tools/mapArea/getAllCells");
      this.areasData = res?.data || {};
    },

    getHeatTypes() {
      $req.get("/athena/map/monitor/stat/getAllStatTypes").then(res => {
        const data = res.data || {};

        let types = [];
        for (let key in data) {
          types.push({
            label: data[key],
            value: key,
          });
        }
        this.heatTypes = types;
      });
    },

    handleCommand(command) {
      this.areaIds = [];
      this.stationIds = [];
      this.dropCommand = command;
    },

    getRobotTypes() {
      $req.get("/athena/map/monitor/stat/getAllRobotTypes").then(res => {
        const data = res.data || [];

        this.robotTypes = [{ label: "ALL", value: "ALL" }].concat(
          data.map(item => {
            return {
              label: item,
              value: item,
            };
          }),
        );
      });
    },

    getStations() {
      $req.get("/athena/station/findAll").then(res => {
        const data = res.data || [];

        this.stations = data.map(item => ({ label: item.id, value: item.id }));
      });
    },

    getAreas() {
      $req.get("/athena/engine/tools/mapArea/getAllAreaIds").then(res => {
        const data = res.data || [];
        this.areas = data.map(item => ({ label: item, value: item }));
      });
    },

    getWsUrl() {
      let protocol = window.location.protocol === "http:" ? "ws" : "wss";
      let hostname = window.location.host;
      if ($req.isDev) {
        hostname = new URL($req.API_URL).hostname;
      }

      const RMSPermission = $utils.Data.getRMSPermission();
      const token = RMSPermission ? `?token=${$utils.Data.getToken()}` : "";

      return `${protocol}://${hostname}/athena-monitor${token}`;
    },
  },
};
</script>

<style lang="less" scoped>
@tool-height: 36px;
.map-tools {
  height: @tool-height;
  line-height: @tool-height - 4;
  padding: 0 5px;
  margin-top: -4px;
  .g-box-shadow-no-top();
}
.map-box {
  position: absolute;
  top: @tool-height;
  left: 0;
  bottom: 0;
  right: 0;
  margin: 0;
  padding: 0;
}
.tu-li-list {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 3px 6px;
  .g-box-shadow-no-top();
  background: #fff;
  opacity: 0.6;
  p {
    .g-flex();
    justify-content: flex-start;
    margin: 3px 0;
    span {
      display: inline-block;
      margin-right: 5px;
      width: 22px;
      height: 22px;
    }
    label {
      font-size: 14px;
    }
  }
}
</style>
