/* ! <AUTHOR> at 2021/01 */
const path = require("path");
const TerserPlugin = require("terser-webpack-plugin");
const CssMinimizerPlugin = require("css-minimizer-webpack-plugin"); // 压缩css
const { merge } = require("webpack-merge"); // 用于合并webpack配置
const webpackBaseConfig = require("./webpack.base.config");
const { publicPath } = require("../config/_conf/build.config");

module.exports = merge(webpackBaseConfig, {
  mode: "production",
  devtool: false,
  output: {
    publicPath: `${publicPath}/static/`,
    path: path.resolve("./dist/static"),
  },
  optimization: {
    minimize: true,
    minimizer: [
      new CssMinimizerPlugin(),
      new TerserPlugin({
        test: /\.js(\?.*)?$/i,
        exclude: [/monitor2D\//, /singleEdit2D\//, /configs\//],
        extractComments: false,
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true, // 干掉那些debugger;
          },
          mangle: true,
          output: {
            comments: false,
          },
        },
      }),
    ],
  },
});
