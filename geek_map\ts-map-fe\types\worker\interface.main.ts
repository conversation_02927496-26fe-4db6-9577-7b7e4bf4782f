/* ! <AUTHOR> at 2023/04/20 */

namespace MWorker {
  export interface Main {
    floorIds: floorId[];
    cellCodes: Array<code>;
    rackCodes: Array<code>;
    shelfCodes: Array<code>;
    chargerIds: Array<code>;
    robotsNeedCoverage: Array<code>;

    init(): void;
    /**
     * 请求楼层数据
     * @param floorIds
     */
    reqFloors(floorIds?: floorId[]): void;
    /** 请求update数据 */
    reqUpdate(): void;
    /** 请求socket指令 */
    reqSocket(msgType: MWorker.reqMsgType, cmd: any): Promise<any>;
    /** 请求实时数据 */
    reqQuery(params: { layer: string; code: code }): void;
    /** 获取当时查找时的数据 */
    getQueryData(params: { layer: string; code: code }): any;
    /** 获取当时批量查找时的数据 */
    getMultiQueryData(params: { layer: string; codes: Array<code> }): any;
    /** 停止搜索 */
    stopQuery(): void;
    /** 绑定数据 callback */
    onCallBack(cb: MWorker.wsCallback): void;
    /** 销毁websocket */
    destroy(): void;
  }
}
