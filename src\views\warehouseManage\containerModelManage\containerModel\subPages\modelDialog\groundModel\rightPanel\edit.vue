<template>
  <!-- 编辑任务 -->
  <div class="editTask">
    <!-- 地面支架模型 -->
    <el-row>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        <span class="taxt-red">*</span>
        {{ $t("lang.rms.fed.groundSupportModelAlias") }}:
      </el-col>
      <el-col :span="14">
        <el-input
          v-model="editTaskData.modelName"
          :disabled="viewDisabled"
          size="mini"
          maxlength="32"
          :placeholder="$t('lang.rms.fed.pleaseEnter')"
        />
      </el-col>
    </el-row>
    <!-- RMSDEV-21431 -->
    <!-- 地面支架类别
    <el-row>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        <span class="taxt-red">*</span>
        {{ $t("lang.rms.fed.groundModelCategory") }}:
      </el-col>
      <el-col :span="14">
        <el-input
          v-model="editTaskData.modelType"
          :disabled="viewDisabled"
          size="mini"
          maxlength="32"
          :placeholder="$t('lang.rms.fed.pleaseEnter')"
        />
      </el-col>
    </el-row> -->
    <el-row>
      <!-- SizeType -->
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.api.result.warehouse.supportedSizeType") }} :
      </el-col>
      <el-col :span="14">
        <el-select
          v-model="editTaskData.sizeTypes"
          :disabled="viewDisabled"
          filterable
          multiple
          allow-create
          default-first-option
          :placeholder="$t('lang.rms.fed.choose')"
        >
          <el-option
            v-for="item in sizeTypeDict"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <el-row>
      <!-- 是否下发给机器人 -->
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.containerManage.needSendRobot.msg") }} :
      </el-col>
      <el-col :span="14">
        <el-select
          v-model="editTaskData.needSendRobot"
          :disabled="viewDisabled"
          :placeholder="$t('lang.rms.fed.choose')"
          @change="handleNeedSendRobot"
        >
          <el-option :label="$t('lang.rms.fed.no')" value="0" />
          <el-option :label="$t('lang.rms.fed.yes')" value="1" />
        </el-select>
      </el-col>
    </el-row>
    <el-row>
      <!-- 下发模型ID -->
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        <el-tooltip
          class="item"
          effect="dark"
          :content="$t('lang.rms.containerManage.sendModelId.tips')"
          placement="top"
        >
          <el-button type="text"><i class="el-icon-question" /></el-button>
        </el-tooltip>
        <span>{{ $t("lang.rms.containerManage.sendModelId.msg") }} :</span>
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.sendModelId"
          :disabled="viewDisabled || String(editTaskData.needSendRobot) === '0'"
          :min="0"
          size="mini"
          :step="1"
        />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="modelTitle">{{ $t("lang.rms.fed.groundSupport") }}:</el-col>
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.length") }}(mm):
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.length"
          :disabled="viewDisabled"
          :min="dockModelMap[dockModelType].shelfMinHLen"
          :max="dockModelMap[dockModelType].shelfMaxHLen"
          size="mini"
          :step="1"
        />
      </el-col>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.width") }}(mm):
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.width"
          :disabled="viewDisabled"
          :min="dockModelMap[dockModelType].shelfMinWLen"
          :max="dockModelMap[dockModelType].shelfMaxWLen"
          size="mini"
          :step="1"
        />
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24" class="modelTitle">{{ $t("lang.rms.fed.groundSupportLeg") }}:</el-col>
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.length") }}(mm):
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.legLength"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].shelfMaxHLen"
          size="mini"
          :step="1"
        />
      </el-col>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.width") }}(mm):
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.legWidth"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].shelfMaxWLen"
          size="mini"
          :step="1"
        />
      </el-col>
    </el-row>

    <!-- 通行 -->
    <el-row>
      <el-col :span="24" class="modelTitle">{{ $t("lang.rms.fed.container.pass") }}:</el-col>
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.length") }}(mm):
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.passLength"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].passMaxLen"
          size="mini"
          :step="1"
        />
      </el-col>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.width") }}(mm):
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.passWidth"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].passMaxWidth"
          size="mini"
          :step="1"
        />
      </el-col>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle"> {{ $t("lang.rms.fed.textHeight") }}(mm): </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.passHeight"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].passMaxHeight"
          size="mini"
          :step="1"
        />
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24" class="modelTitle">{{ $t("lang.rms.fed.palletPosition") }}:</el-col>
      <el-col :span="10" class="labelTitle">{{ $t("lang.rms.fed.yAxisDeviation") }}(mm):</el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.offsetY"
          :disabled="viewDisabled"
          :min="0"
          :max="100000"
          size="mini"
          :step="1"
        />
      </el-col>
    </el-row>

    <div class="dividerStyle" />

    <div class="buttomBtnSeat" />

    <div class="buttomBtns"></div>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapState } from "vuex";

export default {
  props: {
    viewDisabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // 编辑任务数据
      editTaskData: {
        id: "",
        modelCategory: "holder", // holder地⾯⽀架 shelf货架
        modelType: "", // 地图支架类别
        mapId: "",
        modelName: "",
        length: 0,
        width: 0,
        legLength: 0,
        legWidth: 0,
        offsetY: 0,
        needSendRobot: "0",
        sendModelId: "",
        passLength: 0,
        passWidth: 0,
        passHeight: 0,
      },

      // loading
      dockModelType: "def",
      saveLoading: false,
      requsetTaskLoading: false,
      requsetTaskTypeListLoading: false,
      dockModelMap: {
        M100: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1000,
          shelfDefHLen: 800,
          shelfMaxWLen: 1100,
          shelfMaxHLen: 900,
          shelfMinWLen: 600,
          shelfMinHLen: 600,
        },
        M1000: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1220,
          shelfDefHLen: 1020,
          shelfMaxWLen: 1500,
          shelfMaxHLen: 1500,
          shelfMinWLen: 1020,
          shelfMinHLen: 1020,
        },
        def: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1000,
          shelfDefHLen: 1000,
          shelfMaxWLen: 50000,
          shelfMaxHLen: 50000,
          shelfMinWLen: 500,
          shelfMinHLen: 500,
          passMaxLength: 50000,
          passMaxWidth: 50000,
          passMaxHeight: 50000,
        },
      },
    };
  },
  computed: {
    ...mapState("containerModal", ["maxModelId", "sizeTypeDict"]),
    editData() {
      return this.$store.state.containerModal.editData;
    },
  },
  activated() {
    this.dockModelType = "def";
  },
  watch: {
    editTaskData: {
      handler(val, oldVal) {
        this.setGroundData(val);
      },
      deep: true,
    },
    dockModelType(value) {
      const { dockModelMap, editTaskData } = this;
      const item = dockModelMap[value];
      editTaskData.legLength = item.legDefHLen;
      editTaskData.legWidth = item.legDefWLen;
      editTaskData.length = item.shelfDefHLen;
      editTaskData.width = item.shelfDefWLen;
    },
    editData: {
      handler(val) {
        if (!val || !val.modelName) return;
        console.log(val);
        this.editTaskData = {
          id: val.id || "",
          modelCategory: "holder", // holder地⾯⽀架 shelf货架
          mapId: "",
          modelName: val.modelName,
          length: val.length || 1000,
          width: val.width || 500,
          legLength: val.legLength || 0,
          legWidth: val.legWidth || 0,
          offsetY: val.offsetY || 0,
          needSendRobot: String(val.needSendRobot ?? "0"),
          sendModelId: val.sendModelId,
          passLength: val.passLength || 0,
          passWidth: val.passWidth || 0,
          passHeight: val.passHeight || 0,
        };
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    ...mapMutations("containerModal", ["setGroundData"]),
    init() {
      const { dockModelMap, editTaskData, dockModelType } = this;
      const item = dockModelMap[dockModelType] || {};
      editTaskData.legLength = item.legDefHLen || 0;
      editTaskData.legWidth = item.legDefWLen || 0;
      editTaskData.length = item.shelfDefHLen || 0;
      editTaskData.width = item.shelfDefWLen || 0;
    },
    /**
     * 清空任务数据(主要是清空ID)
     */
    claerTask() {
      delete this.editTaskData.id;
      this.editTaskData.mapId = "";
      this.editTaskData.modelName = "";
      this.editTaskData.length = "";
      this.editTaskData.width = "";
      this.editTaskData.legLength = "";
      this.editTaskData.legWidth = "";
      this.editTaskData.offsetY = "";
    },

    /**
     * 创建一个新任务
     *
     * @param {} name 任务名
     */
    createTask(name) {
      this.claerTask();
      this.init();
      this.editTaskData.modelName = name;
    },

    /**
     * 保存编辑界面
     */
    saveEditTaskView() {
      const { editTaskData } = this;

      if (!editTaskData.length || !editTaskData.width) {
        this.$message.error(this.$t("lang.rms.fed.trueSize"));
        return;
      }

      this.saveLoading = true;

      return $req.post("/athena/shelfModel/save", editTaskData).then(res => {
        this.saveLoading = false;
        res.code !== 0 ? this.$message.error(this.$t(res.msg)) : this.closeEditTaskView();
      });
    },

    /**
     * 关闭编辑界面
     */
    closeEditTaskView() {
      // 关闭直线绘制
      this.$emit("close");
    },

    /**
     * 获取任务组数据
     *
     * @param {} id
     */
    editTask(id) {
      this.requsetTaskLoading = true;
      $req.get("/athena/shelfModel/getById", { id }).then(res => {
        this.requsetTaskLoading = false;
        if (res.code === 0) {
          this.editTaskData.id = res.data.id;
          this.editTaskData.mapId = "";
          this.editTaskData.modelName = res.data.name;
          this.editTaskData.length = res.data.length;
          this.editTaskData.width = res.data.width;
          this.editTaskData.legLength = res.data.legLength;
          this.editTaskData.legWidth = res.data.legWidth;
          this.editTaskData.offsetY = res.data.offsetY;
          this.editTaskData.needSendRobot = res.data.needSendRobot;
          this.editTaskData.sendModelId = res.data.sendModelId;
          this.editTaskData.passLength = res.data.passLength || 0;
          this.editTaskData.passWidth = res.data.passWidth || 0;
          this.editTaskData.passHeight = res.data.passHeight || 0;
        }
      });
    },
    handleNeedSendRobot(val) {
      if (String(val) === "0") this.editTaskData.sendModelId = "";
      else if (!this.editTaskData.sendModelId) this.editTaskData.sendModelId = this.maxModelId;
    },
  },
};
</script>

<style scoped lang="less">
.labelTitle {
  text-align: right;
  padding-right: 8px;
}
.modelTitle {
  text-align: left;
  height: 30px;
  font-weight: 600;
}
.editTask {
  height: 100%;
  display: flex;
  flex-direction: column;

  .modelTitle {
    text-align: left;
  }

  .hr {
    height: 5px;
  }

  .pointsBoxsMain {
    flex: 1;
    padding: 10px;
    overflow: auto;
    border-radius: 10px;
    border: 1px solid #ccc;
  }

  .taskTitle {
    text-align: left;
    padding-top: 20px;
    padding-left: 10px;
  }

  .taskAddTaskTitle {
    height: 32px;
    line-height: 32px;
    text-align: left;
  }

  .pointsBox {
    text-align: left;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px;
    position: relative;
    margin-bottom: 10px;

    .clearPoint {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }

  .buttomBtnSeat {
    height: 50px;
  }

  .buttomBtns {
    margin: 20px 0;
    display: block;
    width: 100%;
    text-align: center;
  }
}

.divider-hr {
  margin: 12px 0;
}

.mb5 {
  margin-bottom: 5px;
}

.mb15 {
  margin-bottom: 15px;
}
.taxt-red {
  color: red;
}

.dividerStyle {
  display: block;
  height: 1px;
  width: 100%;
  margin: 24px 0;
  background-color: #dcdfe6;
  position: relative;
}
</style>
