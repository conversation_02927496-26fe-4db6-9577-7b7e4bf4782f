/* ! <AUTHOR> at 2021/01 */
import _fn from "../_fn";

export default {
  // 判断字符串中是否含有中午
  hasChinese(str) {
    return /[\u4E00-\u9FA5]+/g.test(str);
  },

  /**
   * 获取url中的参数
   * @param name
   * @returns {string|null}
   */
  getUrlParameter(name) {
    let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    let r = window.location.search.substring(1).match(reg);
    if (r != null) return decodeURIComponent(r[2]);
    return null;
  },

  /**
   * data序列化
   * @param obj
   * @returns {string}
   */
  getParams(obj) {
    let arr = [];
    for (let key in obj) {
      arr.push(`${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`);
    }
    return arr.length > 0 ? arr.join("&") : "";
  },

  /**
   * formatDate('123243546546', "yyyy-MM-dd hh:mm:ss.S") ==> 2013-08-06 08:09:04.423
   * formatDate(new Date(), "yyyy-M-d h:m:s.S")      ==> 2013-8-6 8:9:4.18
   * formatDate(new Date(), "yyyy年M月d日")           ==> 2013年8月6日
   * @param date : 日期格式或String
   * @param fmt
   * @returns {*}
   */
  formatDate(date, fmt) {
    if (_fn.dataType(date) !== "Date") {
      date = _fn.formatStandardDate(date);
    }
    return _fn.formatDate(date, fmt);
  },

  /**
   * 格式化当前时间
   * @param fmt
   * @returns {*}
   */
  formatNow(fmt) {
    let date = new Date();
    return _fn.formatDate(date, fmt);
  },

  /**
   * 国际化 翻译key
   * @param key
   * @returns {VueI18n.TranslateResult|string}
   */
  transMsgLang(key) {
    if (!key) return "";
    let json;
    try {
      json = JSON.parse(key);
    } catch (e) {
      const arr = key.split(","),
        code = arr.shift();
      if (arr.length > 0) {
        return $app.$i18n.t(code, arr);
      }
      return $app.$i18n.t(key);
    }

    if ($utils.Type.isObject(json)) {
      if (json.p) {
        return $app.$i18n.t(
          json.c,
          json.p.map(x => this.transMsgLang(x)),
        );
      } else {
        return $app.$i18n.t(json.c);
      }
    } else {
      return $app.$i18n.t(json);
    }
  },

  /**
   * 根据总条数计算总页数
   * @param count:总条数
   * @param pageSize:每页几条
   * @returns {number}
   */
  getTotalPages(count, pageSize) {
    let num = 1;
    num = count > 0 ? (count < pageSize ? 1 : count % pageSize ? parseInt(count / pageSize) + 1 : count / pageSize) : 0;
    return num;
  },

  /**
   * 防抖
   * @param func
   * @param wait: 间隔时间
   * @param immediate: 是否立即执行一次
   * @returns {function(): *}
   */
  debounce(func, wait, immediate) {
    let timeout, result;

    let debounced = function () {
      let context = this;
      let args = arguments;

      if (timeout) clearTimeout(timeout);
      if (immediate) {
        // 如果已经执行过，不再执行
        let callNow = !timeout;
        timeout = setTimeout(function () {
          timeout = null;
        }, wait);
        if (callNow) result = func.apply(context, args);
      } else {
        timeout = setTimeout(function () {
          func.apply(context, args);
        }, wait);
      }
      return result;
    };

    debounced.cancel = function () {
      clearTimeout(timeout);
      timeout = null;
    };

    return debounced;
  },

  /**
   * 克隆数据（深拷贝）
   * @param data
   * @returns {*[]}
   */
  cloneData(data) {
    let _clone;
    if (data && typeof data !== "object") {
      _clone = data;
    } else if (data && typeof data === "object") {
      _clone = Array.isArray(data) ? [] : {};
      for (let key in data) {
        if (data.hasOwnProperty(key)) {
          let v = data[key];

          if (v && typeof v === "object") {
            _clone[key] = this.cloneData(v);
          } else {
            _clone[key] = v;
          }
        }
      }
    }
    return _clone;
  },

  setFavIcon() {
    if (!$utils?._staticConfig?.showGeekIcon) return;
    let head = document.getElementsByTagName("head")[0],
      cssURL = "./static/favicon.ico",
      linkTag = document.createElement("link");
    linkTag.href = cssURL;
    linkTag.setAttribute("rel", "icon");
    head.appendChild(linkTag);
  },

  async toLogin() {
    const RMSConfigRes = await $req.reqRMSConfig();
    const RMSConfig = RMSConfigRes.data;

    const ssoLoginUrl = RMSConfig?.ssoLoginUrl || "";
    console.log(ssoLoginUrl);
    const reg = /(http|https):\/\/([\w.]+\/?)\S*/;
    if (reg.test(ssoLoginUrl)) {
      window.location.replace(ssoLoginUrl);
    } else {
      $app.$router.push({ path: "/login" });
    }
    console.log("ssoLoginUrl or /login");
  },

  getDefaultActive(defaultActive, tabNamePerssion) {
    if (tabNamePerssion[defaultActive]) {
      return defaultActive;
    }
    for (const key in tabNamePerssion) {
      if (Object.hasOwnProperty.call(tabNamePerssion, key)) {
        const tabPermissionIsTrue = tabNamePerssion[key];
        if (tabPermissionIsTrue) {
          return key;
        }
      }
    }
  },

  getRouteQueryTabName(activeName, tabNamePerssion) {
    const routeQueryTabName = $app.$route?.query?.targetTabName;
    if (routeQueryTabName && routeQueryTabName !== activeName) {
      if (!tabNamePerssion[routeQueryTabName]) {
        $app.$error($app.$t("lang.rms.web.noPermissionToThatPage"));
      }
      return routeQueryTabName;
    }

    return activeName;
  },
};
