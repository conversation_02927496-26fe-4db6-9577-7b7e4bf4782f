/**
 * 这里声明了mapEdit的方法
 */
interface ModeType {
  title?: string;
  action: "ADD" | "DEFAULT" | "MULTI_MODIFY" | "FN_CHOOSE";
  options: {
    selectedNodeId?: Number;
    type?: string;
    name?: string;
    bezierType?: "S" | "CW" | "CCW";
    isQrNode?: boolean;
    isRect?: boolean;
    [key: string]: any;
  } | null;
}

interface UpdateOps {
  id: string;
  data: {
    nodeId?: number;
    segmentId?: number;
    [key: string]: any;
  }[];
  isSaveHistory?: boolean;
}

interface DeleteOps {
  id: string;
  data: number[];
  isSaveHistory?: boolean;
}

interface ShowLayerParams {
  id: string;
  type?: string;
}

interface AddElementParams {
  id: string;
  data: any[];
}

interface MultiSearchParams {
  layerName: string;
  ids: string[];
}

interface SelectedParams {
  layerName: string;
  id: string;
}

export declare class EditRef {
  app: any;
  /**
   * 设置分辨率
   */
  setResolution(resolution: number): void;
  /**
   * 初始化地图
   */
  initMap(): void;
  /**
   * 初始化地图数据
   */
  initData(data: any): void;
  /**
   * 设置居中
   */
  setCenter(): void;
  /**
   * 切换操作模式, 目前最多为添加
   */
  changeMode(mode: ModeType): void;
  /**
   * 更新图层数据(批量)
   * @param updateOps
   */
  updateElements(updateOps: UpdateOps): void;
  /**
   * 绑定事件
   */
  bindEventBus(name: string, callback: Function): void;
  offEventBus(name: string, callback: Function): void;
  // emitEvent(name: string,data:any): void;
  /**
   * 自适应屏幕尺寸
   */
  resize(): void;
  /**
   * 删除任意元素(批量)
   * @param deleteOps
   */
  deleteElements(deleteOps: DeleteOps): void;
  /**
   * 历史回退
   */
  historyBack(): void;
  /**
   * 撤销历史回退
   */
  historyForward(): void;

  /**
   * 可以展示空载/负载
   * @param type { 'both' | 'load' | 'unload' }
   */
  showDirByType(type: "both" | "load" | "unload"): void;

  getAllData(): any;
  getDeleteData(): any;
  /**
   * 展示某类型的数据
   * @param data
   */
  showLayer(data: ShowLayerParams): void;
  /**
   * 隐藏某类型
   * @param data
   */
  hideLayer(data: ShowLayerParams): void;

  /**
   * 新增一个元素
   * @param data
   */
  addElements(data: AddElementParams): void;

  getAllSelected(): any[];

  /**
   * 是否进入多选模式
   * @param status
   */
  setMultiSelectedStatus(status: boolean): void;

  /**
   * 检索元素
   * @param param0
   */
  search(option: { layerName: string; id: string[] }): void;

  /**
   * 删除所有选中的元素
   */
  resetAllSelected(): void;

  /**
   * 批量选择某些点
   * @param ids
   */
  multiSearch(options: MultiSearchParams[]): void;

  /**
   * 获取某个图层的所有数据
   */
  getLayerData(type: string): any[];

  resetMode(): void;

  setSelected(options: SelectedParams): void;

  /**
   * 销毁
   */
  destroy(): void;

  /**
   * 获取一个唯一ID
   */
  createId(): number;
  /**
   * 复制选中的节点
   * @param offsetX x
   * @param offsetY y
   */
  copy(offsetX: number, offsetY: number): void;

  // 全局点位偏移
  globalMove(offsetX: number, offsetY: number, angles: number): any;
  //功能选择
  fnChoose(nodeIds: []): void;
  getNodeIdsByCellCodes(cellCodes: []): any[];
  fnChooseFinish(): [];
  getNodeIdsByCellCodes(cellCodes: []): any[];

  getAreaInfoByType(type: string): any[];
  //挂一个全局的 EditMap
  setGlobalEditMap(editMap: any): void;

  //可选择的层
  triggerLayers(layerNames: string[]): void;
}
