<template>
  <section>
    <geek-customize-form ref="refForm" :form-config="formConfig" style="max-width: 600px; margin: 10px auto">
      <template #recycleId="{ config }">
        <el-select v-model="recycleId" v-bind="config.attr" clearable @change="recycleIdChange">
          <el-option
            v-for="item in maintainAreas"
            :key="item.recycleId"
            :value="item.recycleId"
            :label="item.recycleId"
          />
        </el-select>
      </template>
    </geek-customize-form>

    <div class="btn-form">
      <el-button type="primary" @click="recoverMaintain">
        {{ $t("lang.rms.fed.returnPlace") }}
      </el-button>
    </div>
  </section>
</template>
<script>
export default {
  name: "RobotReturnTab",
  props: ["maintainAreas"],
  data() {
    return {
      recycleId: "",

      curMaintainArea: null,
    };
  },
  computed: {
    formConfig() {
      return {
        attrs: {
          labelWidth: "200px",
          labelPosition: "right",
        },
        configs: {
          recycleId: {
            label: "lang.rms.fed.serviceArea",
            tag: "select",
            slotName: "recycleId",
            required: true,
          },
          robotInTask: {
            label: "lang.rms.fed.robotInTask",
            default: "",
            placeholder: "--",
            tag: "input",
            disabled: true,
            readonly: true,
          },
          robotId: {
            label: "lang.rms.fed.robot",
            tag: "input",
            default: "",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterTheRobotID"),
                trigger: "blur",
              },
              {
                trigger: "blur",
                validator: (rule, value, callback) => {
                  const reg = new RegExp("^[0-9]*$");
                  if (reg.test(value)) {
                    callback();
                  } else {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  }
                },
              },
            ],
          },
        },
      };
    },
  },
  activated() {
    this.recycleId = "";
    this.$refs.refForm.reset();
  },
  methods: {
    recoverMaintain() {
      this.$refs.refForm.validate().then(data => {
        const cmd = {
          instruction: "RECOVER_IN_MAINTAIN",
          recycleId: data.recycleId,
          robotId: Number(data.robotId),
        };

        this.$emit("reqReturnWorker", cmd);
      });
    },

    recycleIdChange(value) {
      this.recycleId = value;
      this.$refs.refForm.setData({ recycleId: value });
    },

    clearInputRobotId() {
      this.$refs.refForm.setData({ robotId: "" });
    },
  },
};
</script>

<style lang="less" scoped>
.btn-form {
  max-width: 600px;
  margin: 10px auto;
  text-align: right;
}
.btn-big {
  margin: 40px 0 0;
  padding: 40px 0;
  border-top: 1px solid #eeeeee;
  text-align: center;
  .el-button {
    height: 200px;
    width: 200px;
    border-radius: 100px;
    font-size: 22px;
  }
  .el-button:active {
    background-color: #c00 !important;
  }
}
</style>
