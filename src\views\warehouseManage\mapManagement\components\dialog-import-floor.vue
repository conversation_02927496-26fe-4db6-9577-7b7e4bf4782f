<template>
  <div>
    <el-dialog
      :title="dialogImportFloor.title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      class="geek-dialog-form"
      width="420px"
      @close="close"
    >
      <div slot="title">
        <el-tabs v-model="activeTab">
          <el-tab-pane :label="$t('lang.rms.fed.importFloor')" name="tab-importFloor">
          </el-tab-pane>
          <el-tab-pane :label="$t('lang.rms.fed.importStorage')" name="tab-importStorage">
          </el-tab-pane>
        </el-tabs>
      </div>
      <div v-if="errorMessage" class="errorMsg">
        {{ $t(errorMessage) }}
      </div>
      <el-form
        v-loading="uploading"
        label-position="right"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.1)"
      >
        <el-form-item
          v-if="activeTab === 'tab-importFloor'"
          :label="$t('lang.rms.fed.floor')"
          prop="floorId"
        >
          <el-input v-model="floorId" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
        </el-form-item>
        <el-form-item
          v-if="activeTab === 'tab-importStorage'"
          :label="$t('lang.rms.fed.storage')"
          prop="floorId"
        >
          <el-input v-model="floorId" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
        </el-form-item>
        <el-form-item prop="file">
          <el-upload
            ref="floorUpload"
            drag
            :auto-upload="false"
            action=""
            accept=".dxf, .xml, .zip"
            :limit="2"
            :file-list="fileList"
            :on-change="fileChange"
            :on-remove="fileRemove"
            :http-request="uploadFloor"
            class="map-management-upload"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              <em>{{ $t("lang.rms.fed.selectOrDropFile") }} (*.xml/*.dxf/*.zip)</em>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item v-if="activeTab === 'tab-importFloor'">
          <el-radio-group v-model="importOption" size="small">
            <el-radio :label="1" border>
              {{ $t("lang.rms.fed.map.import.cleanAndImport") }}
            </el-radio>
<!--            <el-radio :label="2" border :disabled="!dxfImport">-->
<!--              {{ $t("lang.rms.fed.map.import.incrementalImport") }}-->
<!--            </el-radio>-->
          </el-radio-group>
        </el-form-item>
        <div v-if="dxfImport && activeTab === 'tab-importFloor'" class="check-box">
          <el-checkbox v-model="updateBackgroundImage" />
          <span>{{ $t("lang.rms.fed.map.import.updateBackgroundImage") }}</span>
        </div>
        <!--是否为四向车地图-->
        <!-- <div class="check-box">
          <el-checkbox v-model="skypick" />
          <span class="required" style="flex: 1;">{{ $t("lang.rms.fe.map.import.isSkypickFloor") }}</span>
        </div> -->
        <div v-if="activeTab === 'tab-importFloor'">
          <div v-if="importOption === 1" class="check-box">
            <el-checkbox v-model="confirmedOverwriting" />
            <span class="required">{{ $t("lang.rms.fed.map.import.confirmFloorClean") }}</span>
          </div>
          <div v-if="importOption === 2" class="check-box tips">
            <span>{{ $t("lang.rms.fed.map.import.incremental.tip") }}</span>
          </div>
        </div>
        <div v-if="activeTab === 'tab-importStorage'" class="check-box tips">
          <el-checkbox v-model="confirmedOverwriting" />
          <span class="required">{{ $t("lang.rms.fed.map.import.storage.tip") }}</span>
        </div>
      </el-form>

      <div slot="footer">
        <el-button
          :disabled="
            disable || fileList.length === 0 || (importOption === 1 && !confirmedOverwriting)
          "
          :loading="uploading"
          type="success"
          @click="submit"
        >
          {{ $t("lang.rms.fed.upload") }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog
        :title="$t('lang.rms.fed.line.errorList')"
        :visible.sync="dialogDataErrorVisible"
        :close-on-click-modal="false"
        width="520px"
    >
      <el-table :data="errorDataList" :border="false" max-height="200">
        <el-table-column
            prop="startPoint"
            :label="
            $t('lang.rms.web.monitor.robot.startPoint') + $t('lang.rms.web.monitor.robot.location')
          "
        />
        <el-table-column
            prop="endPoint"
            :label="
            $t('lang.rms.web.monitor.robot.endPoint') + $t('lang.rms.web.monitor.robot.location')
          "
        />
        <el-table-column prop="errorPathType" :label="$t('lang.rms.fed.line.errorInfo')" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex';

export default {
  name: 'DialogImportFloor',
  data() {
    return {
      // 是否为四向车地图
      skypick: false,
      activeTab: "tab-importFloor",
      floorId: "",
      importOption: 1,
      dxfImport: false,
      updateBackgroundImage: false,
      confirmedOverwriting: false,
      fileList: [],
      disable: false,
      uploading: false,

      dialogDataErrorVisible: false,
      errorDataList: [],
      errorMessage: '', // 报错提示
    };
  },
  computed: {
    ...mapState('mapManagement', ['dialogImportFloor']),
    visible: {
      get() {
        this.floorId = this.dialogImportFloor.floorId;
        return this.dialogImportFloor.visible;
      },
      set(val) {
        const { visible } = this.dialogImportFloor;
        if (!val && val !== visible) {
          this.hideDialog();
        }
      },
    },
    rowData() {
      return this.dialogImportFloor.rowData;
    },
  },
  methods: {
    ...mapMutations('mapManagement', ['hideDialog']),
    close() {
      Object.assign(this.$data, this.$options.data());
    },
    uploadFloor(params) {
      const formData = new FormData();
      // 判断导入地图的类型
      const mapMark = this.activeTab === "tab-importStorage" ? "skypick" : "";
      formData.append('mapMark', mapMark);
      formData.append('file', params.file);
      formData.append('mapId', this.rowData.id);
      formData.append('floorId', this.floorId);
      formData.append('importOption', this.importOption);
      formData.append('updateBackgroundImage', this.updateBackgroundImage);

      $req
        .post('/athena/map/manage/uploadMap', formData)
        .then(res => {
          this.reqSuccess();
          this.errorMessage = '';
          if (res.data) {
            this.getDataError(res.data);
          } else {
            this.visible = false;
            let msg = $utils.Tools.transMsgLang(res.msg);
            this.$success(msg);
          }
        })
        .catch(e => {
          this.errorMessage = e.msg;
          this.disable = false;
          this.uploading = false;
        });
    },
    getDataError(data) {
      this.errorDataList = data.map(item => {
        const { startPoint, endPoint } = item.path;
        const errorPathType =
          item.errorType == 1
            ? this.$t('lang.rms.fed.notALine')
            : this.$t('lang.rms.fed.notOnLine');
        if (item.errorType) {
          return {
            startPoint: JSON.stringify(startPoint),
            endPoint: JSON.stringify(endPoint),
            errorPathType,
          };
        }
      });
      this.dialogDataErrorVisible = true;
    },
    submit() {
      this.uploading = true;
      this.disable = true;
      this.$refs.floorUpload.submit();
    },
    reqSuccess() {
      this.disable = false;
      this.uploading = false;
      this.$emit('refreshList');
      // this.$emit('updateStatusTest');
    },
    fileChange(file, fileList) {
      //判断上传的文件类型
      const fileName = file.name.substring(file.name.lastIndexOf('.') + 1)
      const acceptFile = ['dxf','xml','zip']
      const extension = acceptFile.includes(fileName)
      if (!extension) {
        this.$message({
          message: this.$t('lang.rms.api.result.monitor.map.xml.unsupportedMapFile'),
          type: 'error'
        })
        return false
      }
      this.fileList = [file];
      if (file.name.toLowerCase().endsWith('.xml')) {
        this.dxfImport = false;
        this.importOption = 1;
      } else {
        this.dxfImport = true;
      }
    },
    fileRemove(file, fileList) {
      console.log(file);
      this.fileList = [];
    },
  },
};
</script>

<style lang="less" scoped>
.check-box {
  .g-flex();
  align-items: flex-start;

  &.tips {
    padding-left: 14px;
    padding-top: 10px;

    span {
      font-weight: 400;
    }
  }

  .el-checkbox {
    margin-top: 2px;
  }

  > span {
    line-height: 22px;
    padding-left: 6px;
    font-weight: 500;

    &.required {
      color: indianred;
    }
  }
}
.errorMsg {
  color: indianred;
  margin-bottom: 10px;
}
</style>
