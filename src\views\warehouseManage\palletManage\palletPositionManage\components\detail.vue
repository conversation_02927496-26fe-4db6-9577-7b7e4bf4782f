<template>
  <section v-show="dialogVisible" class="pallet-detail">
    <div class="detail-head">
      <h5>{{ title }}</h5>
      <div>
        <el-button type="primary" size="small" @click="save">
          {{ $t("lang.rms.fed.save") }}
        </el-button>
        <el-button size="small" @click="cancel">
          {{ $t("lang.common.cancel") }}
        </el-button>
      </div>
    </div>

    <el-alert
      :title="$t('lang.rms.palletPositionManage.baseinfo')"
      type="info"
      :closable="false"
      style="margin: 10px 0"
    />

    <geek-customize-form ref="refForm" :form-config="formConfig" style="width: 600px" />
  </section>
</template>
<script>
export default {
  data() {
    return {
      dialogVisible: false,
      type: "",
      palletRackList: [],

      rowData: {},
    };
  },
  computed: {
    title() {
      const type = this.type;
      switch (type) {
        case "add":
          return this.$t("lang.rms.fed.add");
        case "edit":
          return this.$t("auth.rms.mapManage.button.edit");
        default:
          return "--";
      }
    },

    formConfig() {
      let disabled = false;
      if (this.operation == "view") disabled = true;

      const heightLabel = "this.rowData.layer";

      return {
        attrs: {
          labelWidth: "200px",
          labelPosition: "right",
          disabled,
        },
        configs: {
          palletLatticeCode: {
            label: "lang.rms.palletPositionManage.palletLatticeCode",
            default: "",
            tag: "input",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterContent"),
                trigger: "change",
              },
            ],
          },
          palletRackCode: {
            label: "lang.rms.palletPositionManage.palletRackCode",
            tag: "select",
            default: "",
            filterable: true,
            required: true,
            options: this.palletRackList,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterContent"),
                trigger: "change",
              },
            ],
          },
          layer: {
            label: "lang.rms.palletPositionManage.layer",
            default: "",
            tag: "input",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterContent"),
                trigger: "change",
              },
              {
                pattern: /^[1-9]\d*$/,
                message: this.$t("lang.rms.fed.pleaseEnterAnNumber"),
              },
            ],
          },
          height: {
            label:
              //TODO: 需要根据 上一个input 输入的值 进行变更
              (this.rowData?.layer || 1) +
              this.$t("lang.rms.palletPositionManage.layerDes") +
              this.$t("lang.rms.palletPositionManage.height"),
            default: "",
            tag: "input",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterContent"),
                trigger: "change",
              },
              {
                pattern: /^[1-9]\d*$/,
                message: this.$t("lang.rms.fed.pleaseEnterAnNumber"),
              },
            ],
          },
          applyPalletCode: {
            label: "lang.rms.palletPositionManage.applyPalletCode",
            default: "",
            tag: "input",
          },
          occupyPalletCode: {
            label: "lang.rms.palletPositionManage.occupyPalletCode",
            default: "",
            tag: "input",
          },
          palletLatticeHostCode: {
            label: "lang.rms.palletPositionManage.palletLatticeHostCode",
            default: "",
            tag: "input",
          },
        },
      };
    },
  },
  methods: {
    open(type, row) {
      this.getPalletRackList();
      this.type = type;
      this.rowData = row || {};
      this.dialogVisible = true;

      const params = {
        palletLatticeCode: row?.palletLatticeCode || "",
        palletRackCode: row?.palletRackCode || "",
        layer: row?.layer || "",
        height: row?.height || "",
        applyPalletCode: row?.applyPalletCode || "",
        occupyPalletCode: row?.occupyPalletCode || "",
        palletLatticeHostCode: row?.palletLatticeHostCode || "",
      };
      this.$nextTick(() => {
        this.$refs.refForm.reset();
        this.$refs.refForm.setData(params);
      });
    },
    save() {
      this.$refs.refForm.validate().then(data => {
        const url = this.type === "add" ? "/athena/palletLattice/add" : "/athena/palletLattice/update";
        const formData = Object.assign({}, this.rowData, data);

        $req.post(url, formData).then(res => {
          if (res.code !== 0) return;
          this.$success();
          this.cancel();
          this.$emit("updateList");
        });
      });
    },
    // 取消
    cancel() {
      this.dialogVisible = false;
      this.$emit("update:listVisible", true);
    },

    getPalletRackList() {
      $req.get("/athena/palletRack/findCodes").then(res => {
        const data = res.data || [];

        this.palletRackList = data.map(item => ({ label: item, value: item }));
      });
    },
  },
};
</script>

<style lang="less" scoped>
.pallet-detail {
  width: 100%;
  padding: 10px 15px;
  background: #fff;

  .detail-head {
    .g-flex();
    h5 {
      font-size: 16px;
      display: flex;
      align-items: center;
      &::before {
        content: "";
        display: inline-block;
        height: 21px;
        width: 4px;
        border-radius: 4px;
        background: #409eff;
        margin-right: 10px;
        vertical-align: text-bottom;
      }
    }
  }
}
</style>
