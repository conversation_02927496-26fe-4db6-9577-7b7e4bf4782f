<template>
  <!-- 机器人升级日志 -->
  <geek-main-structure class="robot-upgrade-log">
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />

    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      style="margin-top: 10px"
    />
  </geek-main-structure>
</template>

<script>
export default {
  name: "RobotUpgradeLog",
  components: {},
  data() {
    return {
      form: {},
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          robotId: {
            label: "lang.rms.fed.listRobotId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterTheRobotID",
          },
          status: {
            label: "lang.rms.fed.state",
            tag: "select",
            default: "",
            options: [
              {
                value: 0,
                label: "lang.rms.fed.upgradeIsNowBeingPrepared",
              },
              {
                value: 1,
                label: "lang.rms.fed.underUpgrade",
              },
              {
                value: 2,
                label: "lang.rms.fed.upgradeIsDone",
              },
              {
                value: 3,
                label: "lang.rms.fed.upgradesAnomaly",
              },
            ],
          },
          version: {
            label: "lang.rms.fed.softwareOfRobot",
            default: "",
            tag: "input",
          },
          createTime: {
            label: "lang.rms.fed.updateDate",
            default: "",
            valueFormat: "yyyy-MM-dd",
            type: "daterange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startDate",
            "end-placeholder": "lang.rms.fed.endData",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: { index:true },
        columns: [
          { label: "lang.rms.fed.listRobotId", prop: "robotId" },
          { label: "lang.rms.fed.edition", prop: "version" },
          {
            label: "lang.rms.fed.state",
            prop: "status",
            formatter: (row, column, cellValue, index) => {
              const statusList = {
                0: "lang.rms.fed.upgradeIsNowBeingPrepared",
                1: "lang.rms.fed.underUpgrade",
                2: "lang.rms.fed.upgradeIsDone",
                3: "lang.rms.fed.upgradesAnomaly",
              };
              return this.$t(statusList[cellValue]);
            },
          },
          {
            label: "lang.rms.fed.upgradeTime",
            prop: "createTime",
            formatter: (row, column, cellValue, index) => {
              return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
            },
          },
        ],
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    // 列表接口请求
    getTableList() {
      $req
        .get("/athena/robot/software/center", {
          ...this.form,
          pageSize: this.tablePage.pageSize,
          currentPage: this.tablePage.currentPage,
          params: true,
        })
        .then(res => {
          let result = res.data || {};
          this.tableData = result.recordList || [];
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: result.currentPage || 1,
            pageCount: result.pageCount || 0,
          });
        });
    },
  },
};
</script>

<style lang="less" scoped>
.form-content {
  .form-flex-wrap {
    display: flex;
    flex-wrap: wrap;
  }
  .el-form-item {
    margin-right: 8px;
  }
  .btn-content {
    display: flex;
    align-items: flex-end;
    margin-bottom: 18px;
  }
}
</style>
