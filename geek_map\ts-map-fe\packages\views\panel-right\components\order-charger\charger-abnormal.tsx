/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Collapse } from "antd";

import OrderGrid from "../common/order-grid";

const { Panel } = Collapse;
type PropsOrderData = {
  visible: boolean;
  errors: Array<any>;
};
function ChargerAbnormal(props: PropsOrderData) {
  const { t } = useTranslation();
  const [errorCount, setErrorCount] = useState(0);
  const [warnCount, setWarnCount] = useState(0);
  const [errorList, setErrorList] = useState([]);

  // list
  useEffect(() => {
    if (!props.visible) return;
    const list = props.errors;
    // const list = [
    //   {
    //     eventLevel: "WARNING", // 级别 WARNING(警告) 和 ERROR（错误） 和 INFO（普通）
    //     createTime: "2019-12-06 20:32:11", // 开始时间
    //     solution: "lang.rms.monitor.charger.errorSolution", // 解决方案
    //     eventContent: "lang.rms.monitor.charger.errorSolution", // 错误title
    //   },
    //   {
    //     eventLevel: "ERROR", // 级别 WARNING(警告) 和 ERROR（错误） 和 INFO（普通）
    //     createTime: "2019-12-06 20:32:11", // 开始时间
    //     solution: "lang.rms.monitor.charger.errorSolution", // 解决方案
    //     eventContent: "lang.rms.monitor.charger.errorSolution", // 错误title
    //   },
    // ];
    let warnCount = 0,
      errorCount = 0;
    list.forEach((item, i) => {
      if (item.eventLevel === "WARNING") ++warnCount;
      if (item.eventLevel === "ERROR") ++errorCount;
    });

    setWarnCount(warnCount);
    setErrorCount(errorCount);
    setErrorList(list);
  }, [props.errors]);

  // 显隐
  useEffect(() => {
    if (!props.visible) return;
    return () => {
      setErrorCount(0);
      setWarnCount(0);
      setErrorList([]);
    };
  }, [props.visible]);

  return (
    props.visible && (
      <>
        <p className="charger-error-number">
          <label>{t("lang.rms.fed.chargerAnomaly statistics")} : </label>
          <span>
            {t("lang.rms.fed.chargerErrorToast")}*<em>{errorCount}</em>
          </span>
          <span>
            {t("lang.rms.fed.chargerCaveat")}*<em>{warnCount}</em>
          </span>
        </p>

        <Collapse defaultActiveKey={[0]} accordion className="charger-error-list">
          {errorList.map((item, index) => {
            return (
              <Panel
                key={index}
                header={
                  <span
                    className={[
                      item.eventLevel === "ERROR" ? "type-error" : "",
                      item.eventLevel === "WARNING" ? "type-warning" : "",
                    ].join(" ")}
                  >
                    {item.eventLevel} {t(item.eventContent)}
                  </span>
                }
              >
                <OrderGrid
                  items={[
                    {
                      label: t("lang.rms.fed.chargerLevel"),
                      value: item?.eventLevel || "--",
                    },
                    {
                      label: t("lang.rms.fed.textStartTime"),
                      value: item?.createTime || "--",
                    },
                    {
                      label: t("lang.rms.fed.textDuration"),
                      value: item?.createTime || "--",
                    },
                    {
                      label: t("lang.rms.fed.chargerSolution"),
                      value: t(item.solution || ""),
                    },
                  ]}
                />
              </Panel>
            );
          })}
        </Collapse>
      </>
    )
  );
}

export default ChargerAbnormal;
