import WSMessage from "./wsMessage";

class MonitorWebSocket {
  constructor(url, dispatch) {
    this.ws = null;
    this.wsStatus = 0; // websocket状态， 0 idle, 1 creating, 2 open, 3 closed
    this.wsUrl = url;
    this.initMsgParams = null;
    this.isDestroy = false;
    this.responseFlag = false;
    this.responseTimmer = null;
    this.restarTimmer = null;

    this.WebSocketMessage = new WSMessage(dispatch);
    this._createSocket(url);
  }

  reqFloorMap(params) {
    if (this.ws && this.wsStatus === 2) {
      this.ws.send(params);
      this._heartbeatDetect();
    }
    this.initMsgParams = params;
  }

  reqUpdateMap(params) {
    if (this.ws) {
      this.ws.send(params);
      this._heartbeatDetect();
    } else {
      console.log(">>>>>> socket error: 断开，等待重连");
    }
  }

  reqChangeFloor(params) {
    if (this.ws) {
      this.ws.send(params);
      this._heartbeatDetect();
    }
    this.WebSocketMessage.dispatchFloorChange();
  }

  // 60s内必须给答复，否则重连；
  _heartbeatDetect() {
    this.responseFlag = false;

    if (this.isDestroy) return;
    if (this.responseTimmer) {
      clearTimeout(this.responseTimmer);
      this.responseTimmer = null;
    }
    this.responseTimmer = setTimeout(() => {
      if (!this.responseFlag) {
        if (this.ws) this.ws.close();
        clearTimeout(this.responseTimmer);
        this.responseTimmer = null;
      }
    }, 60 * 1000);
  }

  _createSocket(url) {
    let wsStatus = this.wsStatus;
    if (wsStatus !== 0 && wsStatus !== 3) return;
    console.log(">>>>>> socket create: " + Date.now());
    const websocket = new WebSocket(url);
    websocket.onopen = this.onopen.bind(this);
    websocket.onmessage = this.onmessage.bind(this);
    websocket.onclose = this.onclose.bind(this);

    this.ws = websocket;
  }

  onopen(e) {
    this.wsStatus = 2;
    console.log(">>>>>> socket connected: " + Date.now());
    if (this.initMsgParams) {
      this.ws.send(this.initMsgParams);
      this._heartbeatDetect();
    }
  }

  onmessage(e) {
    this.responseFlag = true;
    let data = JSON.parse(e.data);
    if (!data) return;
    const msg = data.msgType;
    if (!msg) return;

    this.WebSocketMessage[msg] && this.WebSocketMessage[msg](data);
  }

  onclose(e) {
    this.wsStatus = 3;
    console.error(">>>>>> websocket closed:", e);

    this.ws.onopen = undefined;
    this.ws.onmessage = undefined;
    this.ws.onclose = undefined;
    this.ws = null;

    if (this.isDestroy) return;
    if (this.restarTimmer) {
      clearTimeout(this.restarTimmer);
      this.restarTimmer = null;
    }
    this.restarTimmer = setTimeout(() => {
      this._createSocket(this.wsUrl);
    }, 3000);
  }

  destroy() {
    this.isDestroy = true;
    if (this.restarTimmer) {
      clearTimeout(this.restarTimmer);
      this.restarTimmer = null;
    }
    if (this.responseTimmer) {
      clearTimeout(this.responseTimmer);
      this.responseTimmer = null;
    }
    if (this.ws) this.ws.close();
    if (this.WebSocketMessage) this.WebSocketMessage.destroy();
    this.WebSocketMessage = null;
    this.initMsgParams = null;
  }
}

export default MonitorWebSocket;
