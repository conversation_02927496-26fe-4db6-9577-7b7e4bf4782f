<template>
  <geek-customize-table
    :table-config="tableConfig"
    :data="tableData"
    @row-del="rowDel"
    @row-export="rowExport"
    @row-application="rowApply"
  />
</template>

<script>
export default {
  name: "LanguageList",
  props: {
    tableData: Array,
  },
  data() {
    const permission = !this.isRoleGuest();
    return {
      tableConfig: {
        columns: [
          { label: "lang.rms.fed.languageName", prop: "languageName" }, // "语言名称"
          { label: "lang.rms.fed.languageField", prop: "languageCode" }, // "语言字段"
          { label: "lang.rms.fed.uploadTime", prop: "createTime", width: "180" },
          { label: "lang.rms.fed.updateTime", prop: "updateTime", width: "180" },
          {
            label: "lang.rms.fed.listOperation",
            width: "180",
            align: "center",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.delete",
                permission,
                handler: "row-del",
              },
              {
                label: "lang.rms.fed.buttonExport",
                handler: "row-export",
              },
              {
                label: "lang.rms.fed.application",
                permission,
                handler: "row-application",
              },
            ],
          },
        ],
      },
    };
  },
  activated() {
    this.$emit("getTableList");
  },
  methods: {
    rowDel(rowData) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        $req
          .postParams("/athena/api/coreresource/i18n/deleteLanguage", {
            languageCode: rowData.languageCode,
          })
          .then(res => {
            if (res.code === 0) {
              this.$success(this.$t("lang.rms.api.result.ok"));
              this.$emit("getTableList");
            }
          });
      });
    },

    // 应用
    rowApply(rowData) {
      $req
        .postParams("/athena/api/coreresource/i18n/applyLanguage", {
          languageCode: rowData.languageCode,
        })
        .then(res => {
          if (res.code === 0) {
            this.$success(this.$t("lang.rms.api.result.ok"));
            $utils.Data.setLocalLang(rowData.languageCode);
            this.$emit("getTableList");
          }
        });
    },

    // 导出
    rowExport(rowData) {
      $req
        .postParams("/athena/api/coreresource/i18n/exportI18nItem", {
          languageCode: rowData.languageCode,
        })
        .then(res => {
          if (res.code === 0) {
            if (window.location.host == "127.0.0.1" || window.location.host == "localhost") {
              window.open("http://" + process.env.VUE_APP_serverIP + res.data);
            } else {
              window.open(window.location.origin + res.data);
            }
          }
        });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>

<style lang="less" scoped>
.table-content {
  box-shadow: 0px -5px 10px -5px rgb(0 0 0 / 10%);

  .btn-opt {
    padding: 3px 5px;
    min-height: 10px;
  }
}
</style>
