import {Point} from "pixi.js";

export default class DL {
  constructor() {
    this.cursor = new Point();
    this.scale = 1;
    this.defaultOps = {
      dash: [10, 5],
      width: 1,
      color: 0xffffff,
      alpha: 1,
      scale: 1,
      useTexture: false,
      alignment: 0.5
    }
    this.renderOps = {}
    this.$g = null
    this.dash = null
    this.dashSize = null
  }
  init($g,options) {
    this.renderOps = Object.assign({},this.defaultOps,options)
    this.dash = this.renderOps.dash;
    this.dashSize = this.dash.reduce(function (a, b) { return a + b; });
    this.$g = $g
    this.$g.lineStyle({
      width: this.renderOps.width * this.renderOps.scale,
      color: this.renderOps.color,
      alpha: this.renderOps.alpha,
      cap: this.renderOps.cap,
      join: this.renderOps.join,
      alignment: this.renderOps.alignment
    });
  }
  distance(x1, y1, x2, y2) {
    return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  }
  moveTo(x,y) {
    this.lineLength = 0;
    this.cursor.set(x, y);
    this.start = new Point(x, y);
    this.$g.moveTo(this.cursor.x, this.cursor.y);
  }
  lineTo(x,y) {
    if (typeof this.lineLength === undefined) {
      this.moveTo(0, 0);
    }
    const length = this.distance(this.cursor.x, this.cursor.y, x, y);
    const angle = Math.atan2(y - this.cursor.y, x - this.cursor.x);
    const cos = Math.cos(angle);
    const sin = Math.sin(angle);
    let x0 = this.cursor.x;
    let y0 = this.cursor.y;
    // find the first part of the dash for this line
    const place = this.lineLength % (this.dashSize * this.scale);
    let dashIndex = 0, dashStart = 0;
    let dashX = 0;
    for (let i = 0; i < this.dash.length; i++) {
      const dashSize = this.dash[i] * this.scale;
      if (place < dashX + dashSize) {
        dashIndex = i;
        dashStart = place - dashX;
        break;
      }
      else {
        dashX += dashSize;
      }
    }
    let remaining = length;
    // let count = 0
    while (remaining > 0) { // && count++ < 1000) {
      const dashSize = this.dash[dashIndex] * this.scale - dashStart;
      const dist = remaining > dashSize ? dashSize : remaining;
      x0 += cos * dist;
      y0 += sin * dist;
      if (dashIndex % 2) {
        this.$g.moveTo(x0, y0);
      }
      else {
        this.$g.lineTo(x0, y0);
      }
      remaining -= dist;
      dashIndex++;
      dashIndex = dashIndex === this.dash.length ? 0 : dashIndex;
      dashStart = 0;
    }
    this.lineLength += length;
    this.cursor.set(x, y);
  }
  destroy() {
    this.cursor = new Point();
    this.scale = 1;
    this.renderOps = {}
    this.$g = null
    this.dash = null
    this.dashSize = null
  }
}
