/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { $eventBus } from "../../singleton";

function BoxWarning() {
  const [color, setColor] = useState("transparent");

  useEffect(() => {
    $eventBus.on("wsSystemWarningBox", color => {
      setColor(color);
    });
    return () => {
      $eventBus.off("wsSystemWarningBox");
    };
  }, []);

  return <div className="map2d-warning-box" style={{ borderColor: color }} />;
}

export default BoxWarning;
