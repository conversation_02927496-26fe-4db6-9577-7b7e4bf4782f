var dealImage = (base64, callback) => {
  var newImage = new Image()
  var quality = 0.5 //压缩系数0-1之间
  newImage.src = base64
  newImage.setAttribute('crossOrigin', 'Anonymous')	//url为外域时需要
  var imgWidth, imgHeight
  newImage.onload = function() {
    imgWidth = this.width
    imgHeight = this.height
    var canvas = document.createElement('canvas')
    var ctx = canvas.getContext('2d')
    if (Math.max(imgWidth, imgHeight) > newImage.width) {
      if (imgWidth > imgHeight) {
        canvas.width = newImage.width
        canvas.height = newImage.width * imgHeight / imgWidth
      } else {
        canvas.height = newImage.width
        canvas.width = newImage.width * imgWidth / imgHeight
      }
    } else {
      canvas.width = imgWidth
      canvas.height = imgHeight
      quality = 0.5
    }
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    ctx.drawImage(this, 0, 0, canvas.width, canvas.height)
    var base64 = canvas.toDataURL('image/png', quality) //压缩语句
    // 如想确保图片压缩到自己想要的尺寸,如要求在100kb以下，请加以下语句，quality初始值根据情况自定
    // while (base64.length / 1024 > 100) {
    //   quality -= 0.3
    //   base64 = canvas.toDataURL('image/png', quality)
    // }
    console.log(88888888888, (base64.length))
    callback(base64, newImage)//必须通过回调函数返回，否则无法及时拿到该值
  }
}

export default dealImage
