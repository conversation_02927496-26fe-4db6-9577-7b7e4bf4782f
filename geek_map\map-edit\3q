Merge branch 'v5.6.0.2' of ssh://gitlab.geekplus.cc:40001/rms-fed/map-edit into v5.6.0.2

# Conflicts:
#	packages/configure/function.conf.ts
#
# It looks like you may be committing a merge.
# If this is not correct, please run
#	git update-ref -d MERGE_HEAD
# and try again.


# Please enter the commit message for your changes. Lines starting
# with '#' will be ignored, and an empty message aborts the commit.
#
# <AUTHOR> <EMAIL>
#
# On branch v5.6.0.2
# All conflicts fixed but you are still merging.
#
# Changes to be committed:
#	modified:   packages/api/map/type/getFloorDetail.ts
#	modified:   packages/assets/iconFont/demo_index.html
#	modified:   packages/assets/iconFont/iconfont.css
#	modified:   packages/assets/iconFont/iconfont.js
#	modified:   packages/assets/iconFont/iconfont.json
#	modified:   packages/assets/iconFont/iconfont.ttf
#	modified:   packages/assets/iconFont/iconfont.woff
#	modified:   packages/assets/iconFont/iconfont.woff2
#	modified:   packages/components/global/PointSelect/PointSelectv2.vue
#	modified:   packages/components/map-global-search/index.vue
#	modified:   packages/components/map-global-search/v2.vue
#	modified:   packages/components/map-mouse-menu/main.ts
#	modified:   packages/components/map-panel-attr/components/area/index.vue
#	modified:   packages/components/map-panel-attr/components/batchCell/index.vue
#	modified:   packages/components/map-panel-attr/components/cell/components/funItem.vue
#	modified:   packages/components/map-panel-attr/components/cell/components/func_rackAngle.vue
#	deleted:    packages/components/map-panel-attr/components/cell/components/func_rackAnglev2.vue
#	modified:   packages/components/map-panel-attr/components/globalCell/index.vue
#	modified:   packages/components/map-panel-attr/index.vue
#	modified:   packages/configure/dict/nodeType.ts
#	modified:   packages/configure/dict/shelfAngleCellFun.ts
#	modified:   packages/configure/dict/shelfReturn.ts
#	deleted:    packages/configure/dict/viaCell.ts
#	modified:   packages/configure/disabledByNodeType.ts
#	modified:   packages/configure/fromData/areaBaseConf.ts
#	modified:   packages/configure/fromData/cellBaseConf.ts
#	modified:   packages/configure/fromData/cellFunctionConf.ts
#	modified:   packages/configure/fromData/cellModelConf.ts
#	modified:   packages/configure/fromData/deviceChargerConf.ts
#	modified:   packages/configure/fromData/deviceElevatorConf.ts
#	modified:   packages/configure/fromData/deviceStationConf.ts
#	modified:   packages/configure/function.conf.ts
#	modified:   packages/configure/panelModel/area.ts
#	modified:   packages/configure/panelModel/grid.ts
#	modified:   packages/configure/toolPanel.conf.ts
#	modified:   packages/configure/topRightPanels.conf.ts
#	modified:   packages/hook/useEdit.ts
#	modified:   packages/hook/useEvent.ts
#	modified:   packages/hook/useI18n.ts
#	modified:   packages/logics/i18n.ts
#	modified:   packages/logics/iframeListener.ts
#	modified:   packages/store/app.ts
#	modified:   packages/store/attr.ts
#	modified:   packages/type/edit.ts
#	modified:   packages/views/EditMap/EditMap.vue
#	modified:   src/edit/Edit.js
#	deleted:    src/edit/api/choose.js
#	modified:   src/edit/api/copy.js
#	modified:   src/edit/api/event.js
#	modified:   src/edit/api/search.js
#	modified:   src/edit/config.js
#	modified:   src/edit/element/baseElement/BezierArrow.js
#	modified:   src/edit/event/ElementEvent.js
#	modified:   src/edit/event/Event.js
#	deleted:    src/edit/event/FnChooseEvent.js
#	modified:   src/edit/store/CellCode2Device.js
#
