<template>
  <div>
    <el-dropdown trigger="click" class="geek-user-info" @command="handleCommand">
      <div class="avatar-wrapper">
        {{ name }}
        <i class="el-icon-caret-bottom"></i>
      </div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="toDashboard">
          {{ $t("lang.rms.fed.first") }}
        </el-dropdown-item>
        <el-dropdown-item v-if="permission" command="openChangePassword" divided>
          {{ $t("lang.rms.fed.changePassword") }}
        </el-dropdown-item>
        <el-dropdown-item command="logout" divided>
          {{ $t("lang.rms.fed.exit") }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <ChangePasswordPop
      v-if="permission"
      :change-password-show="changePasswordShow"
      @updateVisible="val => (changePasswordShow = val)"
    />
  </div>
</template>

<script>
import ChangePasswordPop from "@/views/login/components/changePasswordPop";

export default {
  name: "GeekUserInfo",
  components: { ChangePasswordPop },
  data() {
    const permission = $utils.Data.getRMSPermission();
    return {
      name: $utils.Data.getUserInfo(),
      permission,
      changePasswordShow: false,
    };
  },

  methods: {
    handleCommand(command) {
      if (command === "logout") {
        this.logout();
      } else if (command === "toDashboard") {
        this.toDashboard();
      } else if (command === "openChangePassword") {
        this.changePasswordShow = true;
      }
    },
    logout() {
      $req.post("/athena/api/coreresource/auth/logout/v1").then(res => {
        $utils.Data.removeAllStorage();
        this.$router.push("/login");
      });
    },
    toDashboard() {
      if (this.$route.name === "dashboard") return;
      this.$router.push("/dashboard");
    },
  },
};
</script>

<style lang="less" scoped>
.geek-user-info {
  margin: 0 10px 0 5px;
  cursor: pointer;

  .avatar-wrapper {
    .g-flex();
    line-height: 50px;
    min-width: 20px;
    justify-content: flex-end;
    font-size: 13px;
    color: @g-nav-right-color;
  }
}
</style>
