<template>
  <section style="padding-top: 10px">
    <geek-customize-form v-show="listVisible" :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <geek-customize-table
      v-show="listVisible"
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      @row-add="rowAdd"
      @row-edit="rowEdit"
      @row-del="rowDel"
      style="margin-top: 10px"
    />

    <pallet-rack-manage-detail ref="detail" :listVisible.sync="listVisible" @updateList="getTableList" />
  </section>
</template>

<script>
import PalletRackManageDetail from "./components/detail.vue";
export default {
  name: "PalletRackManage",
  components: { PalletRackManageDetail },
  data() {
    const isGuest = this.isRoleGuest();
    return {
      listVisible: true,
      form: {
        palletRackCode: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          palletRackCode: {
            label: "lang.rms.palletPositionManage.palletRackCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: { index: true },
        actions: [
          {
            label: "lang.rms.fed.add",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          {
            label: "lang.rms.palletPositionManage.palletRackCode",
            prop: "palletRackCode",
          },
          {
            label: "lang.rms.palletRackManage.totalFloors",
            prop: "totalFloors",
          },
          {
            label: "lang.rms.palletRackManage.locationCellCode",
            prop: "locationCellCode",
          },
          {
            label: "lang.rms.palletRackManage.angle",
            prop: "angle",
          },
          {
            label: "lang.rms.web.monitor.cell.fieldPrefix.length",
            prop: "length",
          },
          {
            label: "lang.rms.web.monitor.cell.fieldPrefix.width",
            prop: "width",
          },
          {
            label: "lang.rms.palletRackManage.isObstacle",
            prop: "isObstacle",
            formatter: (row, column, cellValue, index) => {
              return cellValue ? this.$t("lang.rms.fed.yes") : this.$t("lang.rms.fed.no");
            },
          },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                width: "170",
                operations: [
                  {
                    label: "lang.rms.fed.buttonEdit",
                    handler: "row-edit",
                  },
                  {
                    label: "auth.rms.mapManage.button.delete",
                    handler: "row-del",
                  },
                ],
              },
        ),
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.listVisible = false;
      this.$refs.detail.open("add", {});
    },
    rowEdit(row) {
      this.listVisible = false;
      this.$refs.detail.open("edit", row);
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        $req.get("/athena/palletRack/remove", { palletRackCode: row.palletRackCode }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t("lang.rms.api.result.ok"));
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    // 列表接口请求
    getTableList() {
      const params = {
        ...this.form,
        currentPage: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        params: true,
      };
      $req.get("/athena/palletRack/findList", params).then(res => {
        let result = res.data;
        if (!result) return;
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          pageCount: result.pageCount || 0,
        });
      });
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>

<style lang="less" scope></style>
