/* ! <AUTHOR> at 2022/09/06 */
import OrderRobot from "./order-robot";
import OrderShelf from "./order-shelf";
import OrderCell from "./order-cell";
import OrderCharger from "./order-charger";
import OrderDevice from "./order-device";
import OrderRack from "./order-rack";
import OrderZone from "./order-zone";
import OrderWarehouse from "./order-warehouse";
import OrderInstruction from "./order-instruction";
import OrderRobotDeadlock from "./order-robot-deadlock";

type PropsOrderGroup = {
  currentMenu: string | number;
  mapReleased: boolean;
  mapConfig: MWorker.mapConfig;
  logicAreas: Array<any>;
  speedLimitAreas: Array<any>;
  deadRobots: { robotData: any; robotCodes: Array<code> };
};
function OrderGroup(props: PropsOrderGroup) {
  return (
    <>
      <OrderRobot isCurrent={props.currentMenu === "OrderGroupRobot"} />
      <OrderShelf isCurrent={props.currentMenu === "OrderGroupShelf"} />
      <OrderCell isCurrent={props.currentMenu === "OrderGroupCell"} />
      <OrderCharger isCurrent={props.currentMenu === "OrderGroupCharger"} />
      <OrderDevice isCurrent={props.currentMenu === "OrderGroupDevice"} />
      <OrderWarehouse
        isCurrent={props.currentMenu === "OrderGroupWarehouse"}
        mapReleased={props.mapReleased}
      />
      <OrderRack isCurrent={props.currentMenu === "OrderGroupRack"} />
      <OrderInstruction // 业务指令
        isCurrent={props.currentMenu === "OrderGroupInstruction"}
        mapConfig={props.mapConfig}
      />
      <OrderZone
        isCurrent={props.currentMenu === "OrderGroupZone"}
        mapConfig={props.mapConfig}
        logicAreas={props.logicAreas}
        speedLimitAreas={props.speedLimitAreas}
      />
      <OrderRobotDeadlock
        isCurrent={props.currentMenu === "OrderGroupDeadlock"}
        deadRobots={props.deadRobots}
      />
    </>
  );
}

export default OrderGroup;
