/**
 * 这里是对editMap的触发事件的处理
 * 预设了许多标准事件处理, 在新增事件的处理时请注意结构
 */
import editVNode from "@packages/hook/useMapVNode";
import { useEditMap, setAreaNodeDefData } from "@packages/hook/useEdit";
import { ToolPanelType } from "../type/editUiType";
import { EditRef } from "@packages/type/edit";
import { MapNodeDto, MapSegmentDto, MapAreaDto } from "@packages/type/editNode";
import { useAttrStore } from "@packages/store/attr";
import { useHistoryStore, MapHistory } from "@packages/store/mapHistory";
import { saveEditMap } from "@packages/hook/useSave";
import { getSelectNodeType } from "@packages/hook/useRightFrom";
import {
  NODE_IDKEY_MAP,
  AREA_BLOCK,
  DEVICE_REFLECTIVE,
  AREA_SINGLE_LANE,
  AREA_RESTRICT_BIG_ARC_AREA,
} from "@packages/configure/dict/nodeType";
import { NODE_AREA } from "@packages/configure/dict/nodeType";
import { ElMessage } from "element-plus";

const eventListener: { [key: string]: Function[] } = {};
/****************************
 *   这里是一些预设的事件处理   *
 ****************************/

/**
 * 调用这个函数可以重置当前地图操作状态
 * 默认不会重置当前的操作, 如选中和操作类型
 * @param editMapRef 地图实例
 */
export function defOperationFn(
  editMapRef: EditRef,
  option?: { isDefMode?: boolean; isClearSelect?: boolean; isEsc?: boolean | false },
) {
  triggerEventListener("map:resetOperation");

  const attrStore = useAttrStore();
  if (!editMapRef) {
    console.info("defOperationFn 没有正确执行! 没有找到 editMapRef");
    return;
  }

  /**
   * 重置时取消选点模式
   */
  if (attrStore.isMultipleSelectMode || attrStore.isSingleSelectMode) {
    attrStore.clearMultipleSelectMode();
  }

  const leftRefs = editVNode.useLeftPanels();
  if (option?.isDefMode) {
    editMapRef.changeMode({ action: "DEFAULT", options: null });
    leftRefs.setActiveByName("boxSelect", false);
  }

  if (option?.isClearSelect) {
    attrStore.clearSelect();
  }

  attrStore.setCurOperationBySpecial("none");
  attrStore.setDrawAreaType("none");
  // attrStore.setAreaAddStatus(false)
  // 判断是否是区域, 如果处于区域则展示区域
  if (attrStore.curSelectType === "AREA") {
    editMapRef.showLayer({ id: "AREA" });
  } else {
    editMapRef.hideLayer({ id: "AREA" });
  }

  // 取消选中绘制
  const refs = editVNode.useHeaderPanels();
  switch (attrStore.curSelectType) {
    case "CELL":
      if (option?.isEsc) {
        refs.clearChildActiveByName("add");
      } else {
        refs.clearChildActiveByNameExclude("add");
      }
      // refs.clearChildActiveByName("add");
      break;
    case "LINE":
      refs.clearChildActiveByName("addTopology");
      break;
    case "DEVICE":
      refs.clearChildActiveByName("addDevice");
      break;
    case "AREA":
      refs.clearChildActiveByName("addAreaMain");
      break;
  }
}

// 地图居中
eventListener["map:center"] = [(editMapRef: EditRef) => editMapRef.setCenter()];

//添加区域事件触发
eventListener["map:addAreaTrigger"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    const attrStore = useAttrStore();
    // 添加前先清空一下选中
    attrStore.clearSelect();
    if (option?.active) {
      const isAreaAdd = option?.areaType === 'AREA_ADD_FN';
      attrStore.setAreaAddStatus(isAreaAdd)
    }else{
      attrStore.setAreaAddStatus(false)
      defOperationFn(editMapRef, { isDefMode: true })
    }
  },
];
// 添加区域
eventListener["map:addArea"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    const attrStore = useAttrStore();
    // 添加前先清空一下选中
    attrStore.clearSelect();

    if (option?.active) {
      const isRect = option.areaType === AREA_BLOCK;
      editMapRef.changeMode({
        action: "ADD",
        options: {
          type: "AREA",
          name: option?.areaType,
          // isRect,
        },
      });
      attrStore.setDrawAreaType(option?.areaType || "none");
      //判断是否为区域添加状态
      // const isAreaAdd = option?.areaType === 'AREA_ADD_FN';
      // attrStore.setAreaAddStatus(isAreaAdd)

      // 开始绘制
      // if(!isAreaAdd){
      //   attrStore.setDrawAreaType(option?.areaType || "none");
      // }


      // 当前操作节点为绘制区域
      attrStore.setCurOperationBySpecial("drawArea");
      // 设置预设数据, 独木桥区域在未创建时就预设了数据
      attrStore.setTemporaryData({});
      // 这里需要设置一下当前的图层为区域图层
      attrStore.setLayerName("AREA");

      if (option?.areaType === AREA_SINGLE_LANE) {
        // 如果添加的区域是单行道独木桥, 则暂时隐藏区域展示, 否则可能无法选中单元格
        editMapRef.hideLayer({ id: "AREA" });
      }
    } else {
      defOperationFn(editMapRef, { isDefMode: true });
    }
  },
];

// 添加曲线
eventListener["map:addBezier"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    if (option?.active) {
      const attrStore = useAttrStore();

      // 添加前先清空一下选中
      attrStore.clearSelect();

      // 当前操作节点为绘制线
      attrStore.setCurOperationBySpecial("lineType");

      editMapRef.changeMode({
        action: "ADD",
        options: {
          type: "BEZIER",
          name: "BEZIER",
          //CCW:逆时针,CW：顺时针,S：曲线
          bezierType: "S",
          defConfig: {
            supportSideWay: false,
            obstacleAvoidance: -1,
            highAreaObstacleAvoidance:  -1
          }
        },
      });
    } else {
      defOperationFn(editMapRef, { isDefMode: true });
    }
  },
];

// 添加顺时针曲线
eventListener["map:addBezierByCW"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    if (option?.active) {
      const attrStore = useAttrStore();

      // 添加前先清空一下选中
      attrStore.clearSelect();

      // 当前操作节点为绘制线
      attrStore.setCurOperationBySpecial("lineType");

      editMapRef.changeMode({
        action: "ADD",
        options: {
          type: "BEZIER",
          name: "BEZIER",
          //CCW:逆时针,CW：顺时针,S：曲线
          bezierType: "CW",
          defConfig: {
            supportSideWay: false,
            obstacleAvoidance: -1,
            highAreaObstacleAvoidance:  -1
          }
        },
      });
    } else {
      defOperationFn(editMapRef, { isDefMode: true });
    }
  },
];

eventListener["map:addBezierByCCW"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    if (option?.active) {
      const attrStore = useAttrStore();

      // 添加前先清空一下选中
      attrStore.clearSelect();

      // 当前操作节点为绘制线
      attrStore.setCurOperationBySpecial("lineType");

      editMapRef.changeMode({
        action: "ADD",
        options: {
          type: "BEZIER",
          name: "BEZIER",
          //CCW:逆时针,CW：顺时针,S：曲线
          bezierType: "CCW",
          defConfig: {
            supportSideWay: false,
            obstacleAvoidance: -1,
            highAreaObstacleAvoidance:  -1
          }
        },
      });
    } else {
      defOperationFn(editMapRef, { isDefMode: true });
    }
  },
];

// 添加直线
eventListener["map:addLine"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    if (option?.active) {
      editMapRef.changeMode({
        action: "ADD",
        options: {
          type: "LINE",
          name: "LINE",
          defConfig: {
            supportSideWay: false,
            obstacleAvoidance: -1,
            highAreaObstacleAvoidance:  -1
          }
        },
      });

      const attrStore = useAttrStore();

      // 添加前先清空一下选中
      attrStore.clearSelect();

      // 当前操作节点为绘制线
      attrStore.setCurOperationBySpecial("lineType");
    } else {
      defOperationFn(editMapRef, { isDefMode: true });
    }
  },
];

// 添加单元格
eventListener["map:addElement"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    const addElementName = option?.addElementName || "";
    const addQR = option?.addQR;
    const attrStore = useAttrStore();

    // 添加前先清空一下选中
    attrStore.clearSelect();

    if (!option?.active) {
      return defOperationFn(editMapRef, { isDefMode: true });
    }

    if (addElementName) {
      editMapRef.changeMode({
        action: "ADD",
        options: {
          type: "ELEMENT",
          name: addElementName,
          isQrNode: addQR,
        },
      });
    } else {
      console.warn("触发map:addElement事件, 但为获取到对应的addElementName参数 OPTION: >>", option);
    }
  },
];
// 批量添加单元格
eventListener["map:addBatchCell"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    const attrStore = useAttrStore();

    // 添加前先清空一下选中
    attrStore.clearSelect();

    if (!option?.active) {
      return defOperationFn(editMapRef, { isDefMode: true });
    }

    editMapRef.changeMode({
      action: "ADD",
      options: {
        type: "MULTI_ELEMENT",
      },
    });

    attrStore.setAddBatchCellPattern();
  },
];

eventListener["map:reset"] = [
  (editMapRef: EditRef) => {
    // const attrStore = useAttrStore();
    defOperationFn(editMapRef, { isDefMode: true, isClearSelect: true, isEsc: true });
  },
]

/**
 * 添加设备
 */
eventListener["map:addDevice"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    const addElementName = option?.addElementName || "";
    const attrStore = useAttrStore();

    // 添加前先清空一下选中
    attrStore.clearSelect();
    if (!option?.active) {
      return defOperationFn(editMapRef, { isDefMode: true });
    }

    // 如果是添加反光柱, 则默认打开反光柱展示
    if (addElementName === DEVICE_REFLECTIVE) {
      const leftRefs = editVNode.useLeftPanels();
      leftRefs.setActiveByName("visReflector", true);
      triggerEventListener("map:visibleMaker", { option });
    }

    if (addElementName) {
      editMapRef.changeMode({
        action: "ADD",
        options: {
          type: "DEVICE",
          name: addElementName,
        },
      });
    } else {
      console.warn("触发map:addElement事件, 但为获取到对应的addElementName参数 OPTION: >>", option);
    }

    // if (addElementName )
  },
];

// 恢复鼠标选择
eventListener["map:defAult"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    console.log("ttt", option);
    const attrStore = useAttrStore();
    if (option && option.name === "addAreaMain" && option.active) {
      // 展示区域
      editMapRef.showLayer({ id: "AREA" });
    } else {
      // 不展示区域
      editMapRef.hideLayer({ id: "AREA" });
    }
    const curSelectType =
      {
        addDevice: "DEVICE",
        add: "CELL",
        addTopology: "LINE",
        addAreaMain: "AREA",
      }[option?.name || ""] || "CELL";

    // 设置当前操作节点为 展示区域
    attrStore.setCurSelectType(<any>curSelectType);
    editMapRef.changeMode({ action: "DEFAULT", options: null });
    attrStore.setDrawAreaType("none");
  },
];

// 新增元素
eventListener["map:added"] = [
  (
    editMapRef: EditRef,
    option: { properties: MapNodeDto | MapSegmentDto | MapAreaDto; layerName: string }[],
  ) => {
    // 如果此时是批量添加的状态, 这里需要记录一下这个线的数据
    const attrStore = useAttrStore();
    if (attrStore.isAddBatchCellPattern && !attrStore.addBatchByLineData) {
      attrStore.setAddBatchByLineData(<MapSegmentDto>option[0].properties);
    }
    // 新增完成后重置状态
    // defOperationFn(editMapRef);
    attrStore.setAreaAddStatus(false)
    option.forEach(({ properties, layerName }) => {
      // 如果新增的是区域类型的点位, 需要请求一个areaId
      // 🦸‍♀️ 理论上区域是不会出现同时添加2个的情况的, 所以没必要等待上一次nextId完成后再继续
      // ✋ 这里单行道比较特殊, 不要触发这里
      if (
        getSelectNodeType(properties) === NODE_AREA &&
        (<any>properties).areaType !== AREA_SINGLE_LANE
      ) {
        setAreaNodeDefData(properties, layerName);
      }
      //如果是单行道的话
      // if (
      //   getSelectNodeType(properties) === NODE_AREA &&
      //   (<any>properties).areaType === AREA_SINGLE_LANE
      // ) {
      //   const {id} = <any>properties
      //   editMapRef.setSelected({layerName:'AREA', id})
      //   console.log(properties)
      //   // attrStore.setCurSelect(properties);
      // }
    });
  },
];

// 保存
eventListener["map:save"] = [
  () => {
    saveEditMap();
  },
];

// 选中点位
eventListener["map:selected"] = [
  (editMapRef: EditRef, options: EditMapEvnetBySelectType[]) => {
    // 设置当前选中数据
    const attrStore = useAttrStore();
    const properties = options.map(item => item.properties);
    attrStore.setCurSelect(properties);
    attrStore.setLayerName(options[0].layerName);
  },
];

// 取消选中
eventListener["map:unSelected"] = [
  (editMapRef: EditRef, option: MapNodeDto) => {
    // 设置当前选中数据
    useAttrStore().clearSelect();
  },
];

// 撤销
eventListener["map:historyBack"] = [
  (editMapRef: EditRef, option: MapNodeDto) => {
    editMapRef.historyBack();
    useAttrStore().clearSelect();
  },
];

// 取消撤销
eventListener["map:historyForward"] = [
  (editMapRef: EditRef, option: MapNodeDto) => {
    editMapRef.historyForward();
    useAttrStore().clearSelect();
  },
];

// 展示空/负载
eventListener["map:roadBothLoad"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    if (option?.active) {
      editMapRef.showDirByType("both");
    } else {
      option!.active = true;
    }
  },
];

// 展示空载
eventListener["map:roadUnLoad"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    if (option?.active) {
      editMapRef.showDirByType("unload");
    } else {
      option!.active = true;
    }
  },
];

// 添加空载/负载
eventListener["map:addGrid"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    const headerRefs = editVNode.useHeaderPanels();
    const leftRefs = editVNode.useLeftPanels();
    const curActive = leftRefs.getChildActiveItemByPath([]);

    const attrStore = useAttrStore();
    const selectNode = attrStore.selectNodes;
    const ignoreCell = ['QUEUE_CELL','STATION_CELL','TURN_CELL']
    //特殊处理queue_cell,station_cell,turn_cell 不允许改变空负载方向，只展示
    const selectFilter = selectNode.filter(item => {
      const data: { [k: string]: any } = { ...item };
      return !ignoreCell.includes(data.cellType)
    })
    // console.log('给爷瞧瞧',selectFilter)
    if (option && curActive && attrStore.layerName) {
      editMapRef.updateElements({
        id: attrStore.layerName,
        data: selectFilter.map(item => {
          const data: { [k: string]: any } = { ...item };
          if (curActive.option.name === "roadBothLoad") {
            data.unloadDirs = option.grid || [1, 1, 1, 1];
            data.loadDirs = option.grid || [1, 1, 1, 1];
          } else if (curActive.option.name === "roadUnLoad") {
            data.unloadDirs = option.grid || [1, 1, 1, 1];
          } else if (curActive.option.name === "roadLoad") {
            data.loadDirs = option.grid || [1, 1, 1, 1];
          }
          //添加空负载的时候，把displayLoadDirs  displayUnloadDirs通通置空
          data.displayLoadDirs = null
          data.displayUnloadDirs = null
          //如果存在带PATH的单元格类型，比如（E2W_PATH_CELL，W2E_PATH_CELL等，添加空载后转化为OMNI_DIR_CELL）
          if(data.cellType.includes('PATH')) data.cellType = 'OMNI_DIR_CELL';
          return data;
        }),
      });
    }
    headerRefs.clearChildActiveByName("addGrid");
  },
];

// 展示反光柱
eventListener["map:visibleMaker"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    if (option?.active) {
      editMapRef.showLayer({
        id: "MARKER",
      });
    } else {
      editMapRef.hideLayer({
        id: "MARKER",
      });
    }
  },
];

// 展示负载
eventListener["map:roadLoad"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    if (option?.active) {
      editMapRef.showDirByType("load");
    } else {
      option!.active = true;
    }
  },
];

// 拖拽结束
eventListener["map:dragEnd"] = [
  (editMapRef: EditRef, option: MapNodeDto) => {
    useAttrStore().setCurIndexNodeData(option);
  },
];

// 数据更新
eventListener["map:updated"] = [
  (editMapRef: EditRef, options: EditMapEvnetBySelectType[]) => {
    const attrStore = useAttrStore();
    const curNode = attrStore.curNodeDataByIndex;

    if (attrStore.isAddBatchCellPattern) {
      // 这里是对批量添加线的特殊处理
      const { addBatchByLineData } = attrStore;
      const curLine = options[0].properties;
      if (addBatchByLineData && addBatchByLineData.segmentId === curLine.segmentId) {
        attrStore.setAddBatchByLineData(<any>{ ...curLine });
      }

      return;
    }

    if (curNode) {
      const curNodeType = attrStore.curNodeTypeByIndex;
      const attrIdKey: string = NODE_IDKEY_MAP[<string>curNodeType] || "";
      const attrId = (<any>curNode)[attrIdKey];
      const updateCurNodeDta = options.find(item => {
        return (<any>item.properties)[<string>attrIdKey] === attrId;
      });
      updateCurNodeDta && attrStore.setCurIndexNodeData(updateCurNodeDta.properties);
    }
  },
];

eventListener["map:resize"] = [
  (editMapRef: EditRef) => {
    editMapRef.resize();
  },
];

// 全局点位编辑
eventListener["showGlobalCell"] = [
  (editMapRef: EditRef) => {
    const attrStore = useAttrStore();
    attrStore.setGlobalCell(true);
  },
];

// 格式刷
eventListener["map:formatBrush"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    const attrStore = useAttrStore();
    //高老板提的需求，要保留选中的内容，不能重置。。
    const allSelected = editMapRef.getAllSelected()
    const allSelectedProperties = allSelected.map(item => item.properties)
    // console.log(allSelectedProperties)
    if (option?.active) {
      // 删除元素
      editMapRef.changeMode({
        action: "MULTI_MODIFY",
        options: {
          selectedData: allSelectedProperties
        },
      });

      attrStore.setCurOperationBySpecial("formatBrush");
    } else {
      defOperationFn(editMapRef, { isDefMode: true });
    }
  },
];
// 模式重置
eventListener["map:modeReset"] = [
  (editMapRef: EditRef) => {
    defOperationFn(editMapRef, { isDefMode: true, isEsc: true });
  },
];

// ESC
eventListener["map:Escape"] = [
  (editMapRef: EditRef) => {
    // 延迟30s, 如果触发了added, 则会有一些内容不会处理
    setTimeout(() => {
      /**
       * 如果当前处于绘制区域/绘制线段时:
       * 1. 取消active
       * 2. 绘制区域时取消当前操作
       */
      const attrStore = useAttrStore();
      const refs = editVNode.useHeaderPanels();
      const curOperationBySpecial = attrStore.curOperationBySpecial;
      switch (curOperationBySpecial) {
        case "drawArea":
          attrStore.clearTemporaryData();
          attrStore.setDrawAreaType("none");
          attrStore.setCurOperationBySpecial("none");
          refs.clearChildActiveByName("addAreaMain");
          editMapRef.showLayer({ id: "AREA" });
          // 保证按下ESC当前为无法添加状态
          defOperationFn(editMapRef, { isDefMode: true });
          break;
        case "lineType":
          attrStore.setCurOperationBySpecial("none");
          refs.clearChildActiveByName("addTopology");
          // 保证按下ESC当前为无法添加状态
          defOperationFn(editMapRef, { isDefMode: true });
          break;
        case "formatBrush":
          // 格式刷
          defOperationFn(editMapRef, { isDefMode: true });
          break;
      }

      if (attrStore.isAddBatchCellPattern) {
        // 移除批量模式
        attrStore.clearAddBatchCellPattern();

        // 恢复到无操作模式
        defOperationFn(editMapRef);

        // 取消选中任何元素
        attrStore.clearSelect();
      }

      // 多选点/单选点的处理
      if (attrStore.isMultipleSelectMode || attrStore.isSingleSelectMode) {
        attrStore.clearMultipleSelectMode();
      }

      const leftRefs = editVNode.useLeftPanels();
      leftRefs.setActiveByName("boxSelect", false);
    }, 30);
  },
];

// 框选模式
eventListener["map:boxSelect"] = [
  (editMapRef: EditRef, { option }: ToolPanelType) => {
    if (option?.active) {
      editMapRef.setMultiSelectedStatus(true);
    } else {
      editMapRef.setMultiSelectedStatus(false);
    }
  },
];

/**
 * 新增事件, 支持监听预设事件, 可重复监听事件
 * @param eventName 事件名称
 * @param callback  事件回调
 */
export function addEventListener(eventName: string, callback: Function) {
  if (eventListener[eventName]) {
    eventListener[eventName].push(callback);
  } else {
    eventListener[eventName] = [callback];
  }
}

/**
 * 删除事件
 * @param eventName 事件名称
 * @param callback  事件回调, 如果传入则仅删除该事件回调, 如果不传入则全部删除
 */
export function removeEventListener(eventName: string, callback?: Function) {
  const curEvent = eventListener[eventName];
  if (curEvent) {
    if (callback) {
      const index = curEvent.findIndex(item => item === callback);
      index !== -1 && curEvent.splice(index, 1);
    } else {
      delete eventListener[eventName];
    }
  }
}

/**
 * 手动触发某事件
 * @param eventName 事件名称
 */
export function triggerEventListener(eventName: string, option?: any) {
  const editMapRef = useEditMap().value;
  if (eventListener[eventName]) {
    eventListener[eventName].forEach(callback => callback(editMapRef, option));
  } else {
    // 如果不是一个内置的事件, 则主动抛出, iframe响应
    try {
      window.parent?.postMessage({
        type: "event",
        body: {
          eventName,
          option,
        },
      });
    } catch (error) {}
  }
}

/*******************************************************
 *                      事件处理机制                     *
 * 如果是当前已经存在的事件, 则触发, 如果不是则像iframe上级传递 *
 *******************************************************/

/**
 * 触发事件, 由MapEdit调用触发
 * @param option 触发事件数据
 */
export function triggerEvent(option: ToolPanelType) {
  const eventName = option.option?.eventName;
  eventName && triggerEventListener(eventName, option);
}

interface EditMapEvnetBySelectType {
  $el: any;
  layerName: string;
  properties: MapNodeDto;
}

/**
 * 为eidtMap组件绑定一些事件的处理
 */
export function editBindEventBus() {
  const editMapRef = useEditMap().value;
  (<EditRef>editMapRef).bindEventBus("selected", (data: EditMapEvnetBySelectType[] | null) => {
    if (data?.length) {
      triggerEventListener("map:selected", data);
    } else {
      triggerEventListener("map:unSelected");
    }
  });

  // 添加元素
  (<EditRef>editMapRef).bindEventBus("added", (data: EditMapEvnetBySelectType) => {
    useAttrStore().setDrawAreaType("none");
    triggerEventListener("map:added", data);
  });

  /**
   * 图层更新, 如果此时有选中的元素, 更新元素数据
   */
  (<EditRef>editMapRef).bindEventBus("updated", (data: EditMapEvnetBySelectType[]) => {
    triggerEventListener("map:updated", data);
  });

  // 右键
  (<EditRef>editMapRef).bindEventBus("rightclick", ({ data }: any) => {
    triggerEventListener("map:rightclick", data);
  });

  // 按下ESC
  (<EditRef>editMapRef).bindEventBus("keydown:Escape", () => {
    triggerEventListener("map:Escape");
  });

  // 当模式重置, 如格式刷
  (<EditRef>editMapRef).bindEventBus("modeReset", () => {
    triggerEventListener("map:modeReset");
  });

  // 历史记录生成
  (<EditRef>editMapRef).bindEventBus(
    "historyChange",
    (data: { backList: MapHistory[]; forwardList: MapHistory[] }) => {
      const historyStore = useHistoryStore();
      historyStore.setBackList(data.backList);
      historyStore.setForwardList(data.forwardList);
      //如果没有记录的话
      if (!data.backList.length && !data.forwardList.length) {
        historyStore.setSaveState(true);
      } else {
        historyStore.setSaveState(false);
      }
    },
  );

  // tootip 一些操作提示
  (<EditRef>editMapRef).bindEventBus("message", (data: { type: string; text: string }) => {
    ElMessage({
      message: data.text,
      type: <any>data.type,
    });
  });
  // resize
  window.addEventListener("resize", () => {
    (<EditRef>editMapRef).resize();
  });
}

//是否可选择
export function triggerLayers(layerNames: string[]) {
  const editMapRef = useEditMap().value;
  (<EditRef>editMapRef).triggerLayers(layerNames);
}
