#!/bin/sh
# 获取当前触发的分支和commit
echo ">>>>>>>>>>>>>>> 当前git分支：${BRANCH}"
echo ">>>>>>>>>>>>>>> 当前git commit text：${COMMIT_TEXT}"
echo ">>>>>>>>>>>>>>> 当前构建触发用户：${AUTH}"

# sandbox相关的分支不打包
if [[ "${BRANCH}" =~ "sandbox" ]]
then
   echo "*************** ${BRANCH}新的分支管理，没有sandbox分支了"
   exit 1
fi

# stable分支只能管理员zhaojing打包
# if [[ "${BRANCH}" =~ "stable" ]] && [[ "${AUTH}" != *"zhaojing"* ]]
# then
#  echo "*************** stable分支提交者不是管理员！不打包！！！"
#  exit 1
# fi

# 对athena_fe分支进行对应
function resolve_branch_athena_fe(){
  if [[ "${BRANCH}" =~ "project" ]]
  then
    echo "athena-fe-${BRANCH#*project-}-$(echo "$BRANCH" | sed 's/^v\([^ -]*\)-project.*/\1/')"
  elif [[ "${BRANCH}" =~ "feature" ]]
  then
    echo "athena-fe-feature-$(echo "$BRANCH" | sed 's/^v\([^ -]*\)-feature.*/\1/')-${BRANCH#*feature-}"
  elif [[ "${BRANCH}" =~ "bugfix" ]]
  then
    echo "athena-fe-bugfix-$(echo "$BRANCH" | sed 's/^v\([^ -]*\)-bugfix.*/\1/')-${BRANCH#*bugfix-}"
  elif [[ "${BRANCH}" =~ "stable" ]]
  then
    echo "athena-fe-stable-$(echo "$BRANCH" | sed 's/^v\([^ -]*\)-stable.*/\1/')"
  elif [[ "${BRANCH}" =~ "v5.6.0" ]]
  then
    echo "athena-fe-test-${BRANCH: 1}"
  else
     exit 1
  fi 
}

athena_fe_branchName=$(resolve_branch_athena_fe)
if [ ! -n "${athena_fe_branchName}" ]
then
  echo "*************** ${athena_fe_branchName}不符合分支规范！不打包！！！"
  exit 1
fi
echo ">>>>>>>>>>>>>>> athena-fe 需要打包的branchName为 ${athena_fe_branchName}"

# 判断是否需要执行build，commit text 不匹配则退出
git_commit_text=${COMMIT_TEXT: 0: 13}
if [ "${git_commit_text}" != "JENKINS_BUILD" ]
then
  echo "*************** commit text 不符合build规范"
  echo "*************** commit text 不匹配 /^JENKINS_BUILD/"
  echo "*************** 退出执行指令"
  exit 1
fi

source /etc/profile
npm config set registry https://repo.geekplus.com/artifactory/api/npm/npm-virtual/
# npm config set registry https://registry.npmjs.org/
# npm config set registry https://registry.npmmirror.com/
echo ">>>>>>>>>>>>>>>  node版本：$(node -v)"
echo ">>>>>>>>>>>>>>>  当前npm源地址：$(npm config get registry)"

# 通知微信机器人
# 传参 $1 机器人发送内容
function wx_robot(){
  #wx_robot_url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=dddf9a8f-8384-42ee-b190-01e60ee5e8a5 
  wx_robot_url=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=da145354-f600-4cee-86a5-083a9407c138
  wx_robot_message=`echo ${COMMIT_TEXT} | sed 's/[[:space:]]/./g'`  
  curl ${wx_robot_url} \
   -H 'Content-Type: application/json' \
   -d '
   {
      "msgtype": "markdown",
      "markdown": {
      	"content": "<font color=\"warning\">'${JOB_NAME}'</font>：'$1' \n> 分支：`'${BRANCH}'` \n触发用户：<font color=\"info\">'${AUTH}'</font> \n> 提交信息: <font color=\"comment\">'${wx_robot_message}'</font> \n> 查看详情: ['${BUILD_TAG}']('${BUILD_URL}'/console)"
      }
	}'
}
wx_robot "构建开始！！！" ""

# git clone 方法
# 传参 $1:git path $2:git branch, $3:git url
function git_clone(){
  cd ${WORKSPACE}
  echo ">>>>>>>>>>>>>>> git 开始 $3 代码拉取"
  echo ">>>>>>>>>>>>>>> path: $1； branch: $2"
  
  if [ ! -d "$1" ]
  then 
  	# repo不存在则clone
    git clone -b $2 $3 $1
  fi 
  
  cd $1
  git fetch --all
  git remote prune origin
  current_branch=`git rev-parse --abbrev-ref HEAD`
  git reset --hard origin/${current_branch}
  echo ">>>>>>>>>>>>>>> git current_branch ${current_branch}"
  
  if git rev-parse --verify $2
  then 
    # 存在分支 
    echo ">>>>>>>>>>>>>>> 存在分支直接切换"
  	git checkout $2
  else
    echo ">>>>>>>>>>>>>>> 不存在分支-b切换" 
  	git checkout -b $2
  fi

  git branch
  echo ">>>>>>>>>>>>>>> git 分支切换完成" 
  pwd 
  git reset --hard origin/$2
  git pull --force origin $2:$2
  echo ">>>>>>>>>>>>>>> git 完成 $3 代码拉取"
}

# rms-fe 代码拉取
rms_path="rms-fe"
rms_url="ssh://**********************:40001/rms-fed/rms-5.6.git"
git_clone ${rms_path} ${BRANCH} ${rms_url}
# 执行项目中的脚本
sh ./build/bash/jenkins_build.sh ${BRANCH}

if [ $? -ne 0 ]
then
  wx_robot "打包失败！！！请项目管理员确认！"
  exit 1
fi

# 对athena_fe打包文件进行操作
# 传参 $1:branchName
function athena_fe_clone(){
  echo ">>>>>>>>>>>>>>> 开始对 athena_fe 打包文件进行操作"
  echo ">>>>>>>>>>>>>>> athena-fe 需要打包的分支为 $1"
  athena_path="athena-fe"
  # athena_branch="athena-fe-test-${BRANCH: 1}"
  athena_url="ssh://**********************:40001/system_rms/athena-fe.git"
  git_clone ${athena_path} $1 ${athena_url}
  
  echo ">>>>>>>>>>>>>>> 删除之前的打包代码" 
  rm -rf ${WORKSPACE}/${athena_path}/rms_build
  echo ">>>>>>>>>>>>>>> 移动打包后的文件夹到${athena_path}"
  mv ${WORKSPACE}/${rms_path}/dist ${WORKSPACE}/${athena_path}/rms_build
  
  echo ">>>>>>>>>>>>>>> git add,commit,push" 
  ls -lh
  git add --all
  git commit -m "${AUTH}::${COMMIT_TEXT#*JENKINS_BUILD}"
  git push origin $1
  
  if [ $? -eq 0 ]
  then
  	echo ">>>>>>>>>>>>>>> push $1 成功！" 
    echo ">>>>>>>>>>>>>>> 整个构建流程OK！" 
    echo ">>>>>>>>>>>>>>> 构建结束" 
  else
    echo "*************** 构建提交失败!!!"
  	wx_robot "构建失败！！！请项目管理员确认！"  
    exit 1
  fi
}

athena_fe_clone ${athena_fe_branchName}