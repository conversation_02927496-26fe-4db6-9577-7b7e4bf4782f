/* ! <AUTHOR> at 2021/07 */

import { fabric } from "fabric";

class EventsGroundErase {
  constructor($vm) {
    this.$vm = $vm;

    this.$fabric = null;
    this.layer = null;
    this.options = null;

    this.eraseRadius = -1;
    this.isDrag = false;
    this.mouse = null;
    this.erasePoint = { x: 0, y: 0 };
  }

  init() {
    this.$fabric = this.$vm.$fabric;
    let layer = this.$vm.layers["ground"];
    let actionAttr = this.$vm.actionAttr;
    this.options = {
      layer2d: layer.canvas2d,
      image: layer.image,
      centerPointX: layer.width / 2,
      centerPointY: layer.height / 2,
      boundary: actionAttr.groundBoundary,
      rotate: actionAttr.angle,
    };

    this._removeEraseMouse();
    this._createEraseMouse();
  }

  start(e) {
    this.isDrag = true;
    const { x, y } = e.absolutePointer;
    Object.assign(this.erasePoint, { x, y });

    let { layer2d, centerPointX, centerPointY, rotate, boundary } = this.options;

    layer2d.globalCompositeOperation = "destination-out";
    layer2d.lineWidth = boundary;

    // 旋转点
    layer2d.translate(centerPointX, centerPointY);
    layer2d.rotate((-rotate * Math.PI) / 180);
    layer2d.translate(-centerPointX, -centerPointY);

    layer2d.beginPath();
    layer2d.moveTo(x, y);
  }

  move(e) {
    const { x, y } = e.absolutePointer;
    Object.assign(this.erasePoint, { x, y });

    if (!this.isDrag) return;
    this.drawingEraser(x, y);
  }

  end(e) {
    this.isDrag = false;
    const { x, y } = e.absolutePointer;
    Object.assign(this.erasePoint, { x, y });

    this.drawingEraser(x, y);

    let { layer2d, centerPointX, centerPointY, rotate } = this.options;
    layer2d.closePath();
    layer2d.translate(centerPointX, centerPointY);
    layer2d.rotate((rotate * Math.PI) / 180);
    layer2d.translate(-centerPointX, -centerPointY);
    layer2d.globalCompositeOperation = "source-over";
  }

  setBoundary(boundary) {
    this.options.boundary = boundary;
    this._removeEraseMouse();
    this._createEraseMouse();
  }

  destroy() {
    if (!this.$fabric) return;
    this._removeEraseMouse();
  }

  drawingEraser(x, y) {
    let $fabric = this.$fabric,
      radius = this.eraseRadius;
    const { layer2d, image } = this.options;

    // 绘制直线
    layer2d.lineTo(x, y);
    layer2d.stroke();
    layer2d.closePath();

    // 绘制圆心
    layer2d.beginPath();
    layer2d.arc(x, y, radius, Math.PI * 2, 0, true);
    layer2d.fill();
    layer2d.closePath();

    // 等待下一次绘制直线
    layer2d.beginPath();
    layer2d.moveTo(x, y);

    image.render(layer2d);
    $fabric.requestRenderAll();
  }

  // 创建橡皮擦工具的鼠标
  _createEraseMouse() {
    let $fabric = this.$fabric,
      boundary = this.options.boundary,
      { x, y } = this.erasePoint;

    const radius = (this.eraseRadius = boundary / 2);
    const mouse = new fabric.Circle({
      top: y,
      left: x,
      radius,
      fill: "#FFFFFF",
      opacity: 0.7,
      stroke: "#000000",
      strokeWidth: 1,
    });

    mouse.selectable = false;
    this.mouse = mouse;
    $fabric.add(mouse);
    $fabric.requestRenderAll();
    this.__animationFrameUpdateMouse();
  }

  // 删除橡皮擦工具的鼠标
  _removeEraseMouse() {
    let $fabric = this.$fabric;

    if (this.mouse) {
      $fabric.remove(this.mouse);
      $fabric.requestRenderAll();
    }
    this.mouse = null;
  }

  // 渲染卡顿优化
  __animationFrameUpdateMouse() {
    if (!this.mouse) return;
    let $fabric = this.$fabric,
      { x, y } = this.erasePoint;

    const radius = this.eraseRadius;
    this.mouse
      .set({
        top: y - radius,
        left: x - radius,
      })
      .setCoords();
    $fabric.requestRenderAll();
    window.requestAnimationFrame(() => {
      this.__animationFrameUpdateMouse();
    });
  }
}

export default EventsGroundErase;
