/* ! <AUTHOR> at 2021/01 */

import axios from "axios";
import config from "../../../config/_conf/app.config";

const { isDev, API_URL } = config;
console.info("app.config::", JSON.stringify(config));

let service = axios.create({
  baseURL: isDev ? "/" : API_URL,
  // 是否携带cookie信息
  withCredentials: true,
  // 超时时间，单位ms
  timeout: 60000,
  // 处理上传进度事件
  onUploadProgress(progressEvent) {
    // Do whatever you want with the native progress event
  },
  // 处理下载进度事件
  onDownloadProgress(progressEvent) {
    // Do whatever you want with the native progress event
  },
});

// 添加请求拦截器
service.interceptors.request.use(
  config => {
    if (config.isStaticReq) {
      config.baseURL = isDev ? "/" : ".";
    }
    const lang = $utils.Data.getLocalLang();
    if (lang) {
      config.headers["Accept-Language"] = lang;
    }
    return config;
  },
  err => {
    return Promise.reject(err);
  },
);

// 添加响应拦截器
service.interceptors.response.use(
  response => {
    if (response.data.code !== 0 && !response.config.isStaticReq) {
      if (response.data.code === 2) {
        // 无登录状态
        $utils.Data.removeAllStorage();
        $utils.Tools.toLogin();
      } else if (response.config.intercept === true) {
        const msg = response.data.msg;
        const hasChinese = $utils.Tools.hasChinese(msg);
        const errorMsg = hasChinese ? msg : $utils.Tools.transMsgLang(msg);
        $app && $app.$error(errorMsg);
      }
    }
    return response;
  },
  err => {
    // 对响应错误做点什么
    return Promise.reject(err);
  },
);

export default service;
export const _isDev = isDev;
export const _API_URL = API_URL;
