/* ! <AUTHOR> at 2023/04/18 */

/**
 * 线段接口数据类型
 * @param segmentId 唯一线段ID
 * @param loadDirs 负载，0:无方向 1:始->终 3:终->始 2:双向
 * @param unloadDirs 空载，0:无方向 1:始->终 3:终->始 2:双向
 * @param points 点位信息
 * @param segmentLength 线段长
 * @param segmentType 线段类型
 * @param width 线段宽
 * @param 其他 可选
 */
type segment = {
  segmentId: code;
  loadDirs: 0 | 1 | 2 | 3;
  unloadDirs: 0 | 1 | 2 | 3;
  points: Array<location>;
  segmentLength: number;
  segmentType: string;
  width: number;
  [propName: string]: any;
};

/**
 * 线段地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param loadDirs 负载，0:无方向 1:始->终 3:终->始 2:双向
 * @param unloadDirs 空载，0:无方向 1:始->终 3:终->始 2:双向
 * @param segmentType 类型
 * @param lineWidth 线宽
 * @param angle 直线角度, bezier 曲线没有这个字段
 * @param sharpData 画直线用到了, bezier 曲线没有这个字段
 * @param 其他 可选
 */
type mSegment = {
  code: code;
  segmentType: string;
  paths: Array<location>;
  loadDirs: 0 | 1 | 2 | 3;
  unloadDirs: 0 | 1 | 2 | 3;
  lineWidth?: number;
  angle?: number;
  sharpData?: Array<number>;
  [propName: string]: any;
};
