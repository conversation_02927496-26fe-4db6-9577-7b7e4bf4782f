// 线段 - 基础

import { NodeAttrEditConf } from "@packages/type/editUiType";

export const LINE_BASE_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  return {
    name: "line",
    tabTitle: "lang.rms.fed.basis",
    formItem: [
      {
        prop: "loadDirs",
        label: "lang.rms.fed.loadDirection",
        component: "EmptyLoadNode",
      },
      {
        prop: "unloadDirs",
        label: "lang.rms.fed.unLoadDirection",
        component: "EmptyLoadNode",
      },
    ],
  };
};
