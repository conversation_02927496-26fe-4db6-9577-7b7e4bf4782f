<template>
  <section class="right-panel-content" :class="{ 'is-collapse': isCollapse }">
    <h3>{{ $t("lang.rms.fed.textInformationOverview") }}</h3>

    <el-autocomplete
      v-model="searchValue"
      class="autocomplete-input"
      :fetch-suggestions="querySearch"
      :trigger-on-focus="false"
      :placeholder="$t('lang.rms.fed.pleaseEnterContent')"
      clearable
      @select="onSelect"
      @clear="clearSearch"
    >
      <el-select
        slot="prepend"
        v-model="searchType"
        :placeholder="$t('lang.rms.fed.pleaseChoose')"
        style="min-width: 80px"
        @change="changeSearchType"
      >
        <el-option v-if="rackLayers" :label="$t('lang.rms.fed.initialization.box')" value="box" />
        <el-option v-if="rackLayers" :label="$t('lang.rms.fed.lattice')" value="lattice" />
        <el-option :label="$t('lang.rms.fed.robot')" value="robot" />
        <el-option :label="$t('lang.rms.fed.cell')" value="cell" />
        <el-option :label="$t('lang.rms.fed.station')" value="station" />
      </el-select>
    </el-autocomplete>

    <div v-show="detailData" class="panel-detail">
      <component :is="currentTabName" :detail-title="detailTitle" :detail-data="detailData || {}" />
    </div>
    <div v-show="!detailData" class="no-data" />

    <div class="icon-arrow" :class="{ active: isCollapse }" @click.stop="isCollapse = !isCollapse">
      <i :class="[!isCollapse ? 'el-icon-arrow-right' : 'el-icon-arrow-left']"></i>
    </div>
  </section>
</template>

<script>
import BoxDetail from "./components/detail-box.vue"; // 货箱
import LatticeDetail from "./components/detail-lattice.vue"; // 货位
import RobotDetail from "./components/detail-robot.vue"; // 机器人
import CellDetail from "./components/detail-cell.vue"; // 单元格
import StationDetail from "./components/detail-station.vue"; // ppp工作站
export default {
  name: "Monitor3DMapRightPanel",
  components: {
    BoxDetail,
    LatticeDetail,
    RobotDetail,
    CellDetail,
    StationDetail,
  },
  props: ["rackLayers", "selectInfo", "detailData"],
  data() {
    return {
      isCollapse: false,

      searchType: this.rackLayers ? "box" : "robot",
      searchValue: "",
    };
  },
  computed: {
    detailTitle() {
      switch (this.searchType) {
        case "box":
          return this.$t("lang.rms.fed.optionBox");
        case "lattice":
          return this.$t("lang.rms.fed.lattice");
        case "robot":
          return this.$t("lang.rms.fed.robot");
        case "cell":
          return this.$t("lang.rms.fed.cell");
        case "station":
          return this.$t("lang.rms.fed.station");
        default:
          return "";
      }
    },
    currentTabName() {
      switch (this.searchType) {
        case "box":
          return "BoxDetail";
        case "lattice":
          return "LatticeDetail";
        case "robot":
          return "RobotDetail";
        case "cell":
          return "CellDetail";
        case "station":
          return "StationDetail";
        default:
          return "";
      }
    },
  },
  watch: {
    detailData(nv, ov) {
      const searchType = this.searchType;
      if (!nv) return (this.searchValue = "");
      let data = nv;
      switch (searchType) {
        case "box":
          if (data["boxCode"] && this.searchValue !== data["boxCode"]) {
            this.searchValue = data.boxCode;
          }
          break;
        case "lattice":
          if (data["latticeCode"] && this.searchValue !== data["latticeCode"]) {
            this.searchValue = data.latticeCode;
          }
          break;
        case "cell":
          if (data["cellCode"] && this.searchValue !== data["cellCode"]) {
            this.searchValue = data.cellCode;
          }
          break;
        case "robot":
          if (data["id"] && this.searchValue !== data["id"]) {
            this.searchValue = data.id.toString();
          }
          break;
        case "poppickStation":
          if (data["id"] && this.searchValue !== data["id"]) {
            this.searchValue = data.id.toString();
          }
          break;
      }
    },
  },
  methods: {
    onSelect() {
      console.log("onSelect", this.searchType);
      const searchType = this.searchType;
      switch (searchType) {
        case "box":
          this._getRackInfoByBox(this.searchValue);
          break;
        case "lattice":
          // 后台接口404 感觉无用啊
          // this._getRackInfoByLattice(this.searchValue);
          this.$emit("searchSelect", { actionType: this.searchType, data: this.searchValue });
          break;
        case "cell":
        case "robot":
        case "station":
          this.$emit("searchSelect", { actionType: this.searchType, data: this.searchValue });
          break;
      }
    },
    clearSearch() {
      this.searchValue = "";
      this.$emit("clearSelect");
    },
    changeSearchType() {
      this.clearSearch();
      this.$emit("changeSearchType", this.searchType);
    },
    async querySearch(queryString, cb) {
      console.log("querySearch", queryString);
      const searchType = this.searchType;
      switch (searchType) {
        case "box":
        case "cell":
        case "lattice":
        case "station":
          cb([{ value: queryString }]);
          break;
        case "robot":
          if (queryString.length < 1) {
            cb([]);
          } else {
            try {
              let res = await this._getRobotQueryList(queryString);
              const robotQueryList = res.data || [];
              cb(
                robotQueryList.map(item => {
                  return { value: item.toString() };
                }),
              );
            } catch (e) {
              cb([]);
            }
          }
          break;
      }
    },
    _getRobotQueryList(queryString) {
      const url = "/athena/robotStatus/queryRobotIds";
      return $req.get(url, {
        info: queryString,
        robotStates: "", // 模糊查询出来的机器人状态，传空默认所有状态
      });
    },
    _getRackInfoByBox(boxCode) {
      $req
        .get("/athena/box/getRackInfoByBox", {
          boxCode,
        })
        .then(res => {
          const { latticeCode } = res.data || {};
          if (latticeCode) {
            this.$emit("searchSelect", { actionType: this.searchType, data: latticeCode });
          } else {
            console.error(
              "SearchBoxError:",
              "未收到当前rackCode的latticeCode返回值,api-/athena/box/getRackInfoByBox",
            );
          }
        });
    },
    _getRackInfoByLattice(latticeCode) {
      $req
        .get("/athena/box/getRackInfoByLattic", {
          latticCode: latticeCode,
        })
        .then(res => {
          const { rackCode } = res.data || {};
          if (rackCode) {
            // EventBus3D.emit("MonitorMap3D", "MapElementSelectTrigger", {
            //   code: latticeCode,
            //   type: "rack_lattice",
            //   rackCode,
            // });
            // EventBus3D.emit("MonitorMap3D", "WorkerDataQuery", {
            //   code: rackCode,
            //   latticeCode,
            //   type: "rack",
            // });
            // EventBus3D.emit("MonitorMap3D", "MapFilterElement", {
            //   code: latticeCode,
            //   type: "lattice",
            // });
          }
        });
    },
  },
};
</script>

<style scoped lang="scss">
.right-panel-content {
  position: absolute;
  right: 0;
  top: 0;
  max-height: calc(100%);
  padding-bottom: 10px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 3px 5px #d9d6d6;
  z-index: 10;
  transition-duration: 0.2s;
  width: 280px;

  &.is-collapse {
    transform: translateX(100%);
  }

  > h3 {
    padding: 10px;
    border-bottom: 1px solid #ddd;
    color: #4a8bce;
    background: #f5f7fa;
    font-weight: 600;
    font-size: 15px;
  }

  > .autocomplete-input {
    width: 100%;
    padding: 10px 10px 0 10px;
  }
}

.panel-detail {
  padding: 0 10px;
  height: calc(100% - 100px);

  > h5 {
    font-size: 13px;
    font-weight: 600;
    color: #666;
  }

  overflow-y: auto;
}

.no-data {
  min-height: 80px;
  background: url(./icon/no-message.png) no-repeat 50% 50%;
  background-size: 68px;
}

::v-deep .component-operate-detail {
  margin-top: 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  width: 260px;

  .el-card__header {
    padding: 0 10px;
    background: #f7f9fa;

    > .header {
      display: flex;
      height: 36px;
      line-height: 36px;
      justify-content: space-between;
      letter-spacing: 1px;

      > a {
        color: rgba(0, 0, 0, 0.65);

        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .el-card__body {
    padding: 5px;
  }
}

.icon-arrow {
  position: absolute;
  top: 46px;
  right: 280px;
  width: 16px;
  height: 32px;
  line-height: 32px;
  background: #fff;
  text-align: center;
  box-shadow: -5px -5px 5px -4px #d9d6d6, -5px 5px 5px -4px #d9d6d6;
  border-radius: 5px 0 0 5px;
  cursor: pointer;

  i {
    font-size: 14px;
  }

  &.active {
    width: 30px;
  }
}
</style>
