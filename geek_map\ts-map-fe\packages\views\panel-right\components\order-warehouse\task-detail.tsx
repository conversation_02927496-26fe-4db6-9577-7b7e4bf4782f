/* ! <AUTHOR> at 2022/09/06 */
import { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Modal, Table, TreeSelect, Button } from "antd";

type PropsOrderData = {
  id: string;
  onCancel: () => void;
};
function warehouseTaskDetail(props: PropsOrderData) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [columnsData, setColumnsData] = useState([]);
  const [tableList, setTableList] = useState([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [treeData, setTreeData] = useState([]);
  const [treeValue, setTreeValue] = useState([]);

  useEffect(() => {
    if (!props.id) return;

    queryHeader();
    return () => {
      onCancel();
    };
  }, [props.id]);

  useEffect(() => {
    if (!props.id) return;
    getTableList();
  }, [pagination.current]);

  const handleTableChange = (newPagination: any) => {
    setPagination(newPagination);
  };

  const getTableList = () => {
    setLoading(true);
    let params: any = {
      inspectionId: props.id,
      pageSize: pagination.pageSize,
      currentPage: pagination.current,
    };
    let arr = [];
    for (let key in params) {
      arr.push(`${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);
    }
    params = arr.length > 0 ? arr.join("&") : "";
    _$utils
      .reqPost("/athena/warehouse/inspection/findInspectionById", params, {
        "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
      })
      .then(res => {
        if (res.code === 0) {
          const data = res.data || {};
          setLoading(false);

          pagination.total = data.recordCount;
          setPagination({ ...pagination });
          setTableList(data.recordList || []);
        }
      });
  };

  //请求表头
  const queryHeader = () => {
    let inspectionHeader = _$utils.getRMSFEDConfig("inspectionHeader");
    if (inspectionHeader) {
      let columnList: any = [];
      let treeList: any = [];
      let treeV: any = [];
      inspectionHeader.forEach((item: any) => {
        const { title, dataIndex, isShow } = item;
        treeList.push({
          title,
          value: dataIndex,
        });

        if (isShow) {
          treeV.push(dataIndex);
          columnList.push({
            dataIndex,
            title: title,
          });
        }
      });

      setColumnsData(columnList);
      setTreeData(treeList);
      setTreeValue(treeV);
      console.log("这里读取的是localStorage");
      return;
    }
    _$utils.reqGet("/athena/warehouse/inspection/queryHeader").then(res => {
      if (res.code !== 0) return;
      const data = res.data || [];

      let inspectionHeader: any = [];
      let columnList: any = [];
      let treeList: any = [];
      let treeV: any = [];
      data.forEach((item: any) => {
        const { i18, field } = item;
        inspectionHeader.push({
          dataIndex: field,
          title: i18,
          isShow: true,
        });
        columnList.push({
          dataIndex: field,
          title: i18,
        });
        treeList.push({
          title: i18,
          value: field,
        });
        treeV.push(field);
      });
      _$utils.setRMSFEDConfig("inspectionHeader", inspectionHeader);
      setColumnsData(columnList);
      setTreeData(treeList);
      setTreeValue(treeV);
    });
  };

  const onCancel = () => {
    props.onCancel();
    setTableList([]);
    setPagination({ current: 1, pageSize: 10, total: 0 });
  };

  const onTreeChange = (newValue: string[], label: any, extra: any) => {
    let inspectionHeader = _$utils.getRMSFEDConfig("inspectionHeader");
    if (inspectionHeader) {
      let columnList: any = [];
      inspectionHeader.forEach((item: any) => {
        const { title, dataIndex } = item;
        if (newValue.includes(dataIndex)) {
          item.isShow = true;
          columnList.push({
            dataIndex,
            title,
          });
        } else {
          item.isShow = false;
        }
      });
      _$utils.setRMSFEDConfig("inspectionHeader", inspectionHeader);
      setColumnsData(columnList);
    }
    setTreeValue(newValue);
  };

  return (
    <Modal
      title={t("lang.rms.fed.textDetails")}
      centered
      open={true}
      footer={null}
      width={"80%"}
      maskClosable={false}
      onCancel={onCancel}
      wrapClassName="map2d-dialog-task-detail"
    >
      <div style={{ position: "relative", display: "flex", justifyContent: "flex-end" }}>
        <Button type="primary" size="small" style={{ marginRight: 5, position: "relative" }}>
          {t("lang.rms.fet.tableHeadCheck")}
          <TreeSelect
            treeData={treeData.map(item => {
              return {
                title: t(item.title),
                value: item.value,
              };
            })}
            value={treeValue}
            onChange={onTreeChange}
            dropdownMatchSelectWidth={false}
            placement="bottomRight"
            treeCheckable
            className="map2d-task-detail-tree"
            popupClassName="map2d-task-detail-tree-dropdown"
            dropdownStyle={{ maxHeight: 400, overflow: "auto", paddingRight: 10 }}
          />
        </Button>
      </div>

      <Table
        dataSource={tableList}
        rowKey={record => record.id.key}
        columns={columnsData.map(item => {
          return {
            title: t(item.title),
            dataIndex: item.dataIndex,
            render: (col: any) => {
              return <span style={col.val ? { color: "#ff2328" } : null}>{col.key}</span>;
            },
          };
        })}
        pagination={pagination}
        loading={loading}
        size="small"
        bordered
        onChange={handleTableChange}
        style={{ marginTop: 8, width: "100%", minHeight: 360 }}
      />
    </Modal>
  );
}

export default warehouseTaskDetail;
