<template>
  <geek-main-structure :space="false">
    <iframe id="J_Monitor2D" :src="url" class="single-map"></iframe>
  </geek-main-structure>
</template>

<script>
export default {
  name: "MapMonitor2D",
  data() {
    let url = window.location.pathname + "monitor2D";
    if ($req && $req.isDev) url = "/map-fe-2d";

    const rightPanel = this.$route.query.rightPanel;
    let param = rightPanel ? `&rightPanel=${rightPanel}` : "";
    return {
      url: `${url}?${__rms_iframe_chunk_id}&iframe=rms` + param,
      isLangDataReady: false,
      timer: null,
      checkTokenFlag: false,
    };
  },
  mounted() {
    $utils.postMessage("monitor2D", {
      type: "onloadMonitor2d",
      mapColor: CommonGeekMapColor,
      mapSetting: CommonGeekMapSetting,
    });
    window.addEventListener("message", this.receiveIframeMessage, false);
    this.checkTokenFlag = true;
    this._checkToken();
  },
  destroyed() {
    if (this.timer) clearTimeout(this.timer);
    this.checkTokenFlag = false;
    window.removeEventListener("message", this.receiveIframeMessage, false);
    $utils.destroyIframe();
  },
  methods: {
    receiveIframeMessage(event) {
      const msg = event.data || {};
      switch (msg.type) {
        case "to3D":
          console.log("receiveIframeMessage::", msg);
          this.$router.push({ path: "/monitor/robotControl3D" });
          break;
        case "noLogin":
          $utils.Data.removeAllStorage();
          $utils.Tools.toLogin();
          // this.$router.push({ path: "/login" });
          break;
      }
    },
    // 检查token
    _checkToken() {
      if (!this.checkTokenFlag) return;

      if (this.timer) clearTimeout(this.timer);
      this.timer = setTimeout(async () => {
        await $req.checkToken();
        this._checkToken(this.checkTokenFlag);
      }, 1000 * 5 * 60);
    },
  },
};
</script>

<style lang="less" scoped>
.single-map {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: 0;
  padding: 0;
}
</style>
