{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "moduleResolution": "node", "experimentalDecorators": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "resolveJsonModule": true, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env"], "paths": {"@packages/*": ["packages/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "packages/**/*.ts", "packages/**/*.vue"], "exclude": ["node_modules"]}