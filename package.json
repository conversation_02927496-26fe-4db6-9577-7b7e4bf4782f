{"name": "RMS-fe", "version": "5.0.0", "main": "./src/app.js", "author": "Geekplus", "license": "ISC", "scripts": {"__assume:git": "node ./build/tools/git_assume.js", "__unassume:git": "cross-env CONF_ENV=jenkins node ./build/tools/git_assume.js", "__pre:config": "node ./build/pre_build/index.js", "_dev:ts-map-fe": "node ./build/pre_build/auto_map2D_conf.js && npm run --prefix ./geek_map/ts-map-fe start", "_lib:ts-map-fe": "npm run --prefix ./geek_map/ts-map-fe build:lib", "_build:ts-map-fe": "npm run --prefix ./geek_map/ts-map-fe build:pro", "_dev:map-edit": "npm run --prefix ./geek_map/map-edit serve", "_build:map-edit": "npm run --prefix ./geek_map/map-edit build", "_dev:rms": "npm run __unassume:git && cross-env CONF_ENV=dev npm run __pre:config && webpack serve --config ./build/webpack.dev.config.js", "_build:rms": "cross-env CONF_ENV=pro npm run __pre:config && webpack --config ./build/webpack.pro.config.js", "dev": "concurrently -n map-edit,ts-map-fe,rms-fe \"npm run _dev:map-edit\" \"npm run _dev:ts-map-fe\" \"npm run _dev:rms\"", "build:pro": "npm run _build:ts-map-fe && npm run _build:map-edit && npm run _build:rms", "jenkins:build": "npm run __unassume:git && npm run build:pro", "analyze": "cross-env NODE_ENV=production npm_config_report=true npm run build:pro", "code:scan": "node ./build/codeScan/index.js", "check:scan": "node ./build/codeScan/check.js"}, "engines": {"node": ">=16.0", "npm": ">=8.0 <9.0"}, "dependencies": {"@tweenjs/tween.js": "^18.6.4", "axios": "^1.3.2", "diff": "^5.1.0", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "el-tree-transfer": "^2.4.7", "element-geek": "^1.0.27", "element-ui": "^2.15.12", "fabric": "^5.3.0", "flatbush": "^4.0.0", "geek-monitor2d": "^1.0.2", "geek-monitor2d-select": "^1.0.5", "intersects": "^2.7.2", "js-cookie": "^3.0.1", "js-md5": "^0.7.3", "mitt": "^3.0.0", "nprogress": "^0.2.0", "stats.js": "^0.17.0", "three": "^0.135.0", "vue": "^2.6.12", "vue-drag-resize": "^1.5.4", "vue-i18n": "^8.22.4", "vue-router": "^3.4.9", "vuex": "^3.6.0"}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.19.6", "@babel/preset-env": "^7.20.2", "@babel/runtime": "^7.20.13", "@babel/runtime-corejs3": "^7.20.13", "autoprefixer": "^10.4.13", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.2", "babel-plugin-component": "^1.1.1", "babel-preset-es2015": "^6.24.1", "clean-webpack-plugin": "^4.0.0", "concurrently": "^7.6.0", "copy-webpack-plugin": "^9.1.0", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^4.2.2", "cssnano": "^5.1.14", "eslint": "^8.43.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.15.1", "eslint-webpack-plugin": "^4.0.0", "geek-codescan": "^0.2.5", "html-webpack-plugin": "^5.5.0", "less": "^4.1.3", "less-loader": "^11.1.0", "log4js": "^6.7.1", "mini-css-extract-plugin": "^2.7.2", "portfinder": "^1.0.32", "postcss-loader": "^7.0.2", "postcss-pxtorem": "^6.0.0", "prettier": "^2.8.3", "process": "^0.11.10", "sass": "^1.58.0", "sass-loader": "^13.2.0", "sass-resources-loader": "^2.2.5", "terser-webpack-plugin": "^5.3.6", "vue-loader": "^15.9.6", "vue-template-compiler": "^2.7.14", "webpack": "^5.75.0", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1", "webpack-merge": "^5.8.0"}}