/* ! <AUTHOR> at 2023/04/21 */

type stopArea = { element: any; isShow: boolean };

class StopAreasData implements MRender.MapData {
  private mapData: { [propName: code]: stopArea } = {};
  /** 当前急停中的 areaID */
  private currentStopAreaIds: Array<code> = [];

  setData(code: code, data: stopArea) {
    this.mapData[code] = data;
  }

  getData(code: code) {
    return this.mapData[code];
  }

  getByFloorId(floorId: floorId): void {}

  getAll() {
    return this.mapData;
  }

  /**
   * 获取当前显示的stopAreaIds
   * @returns ids
   */
  getIds(): Array<code> {
    return this.currentStopAreaIds;
  }

  delData(code: code) {
    const element = this.mapData[code]?.element;
    element && element.destroy();
    delete this.mapData[code];
  }

  /**
   * 根据传入的areaIds显示急停area
   * @param areaIds 传入的需要显示的areaId
   */
  showStopArea(areaIds: Array<code> = []) {
    const mapData = this.mapData;

    areaIds.forEach(areaId => {
      if (mapData[areaId].isShow) return;
      mapData[areaId].element.visible = true;
    });
  }

  /**
   * 根据传入的areaIds自动判断显隐
   * @param areaIds
   */
  toggleStopArea(areaIds: Array<code>) {
    this.currentStopAreaIds = areaIds;
    const stopAreas = this.mapData;
    let stopArea;
    for (let areaId in stopAreas) {
      stopArea = stopAreas[areaId];
      if (areaIds.includes(areaId)) {
        if (stopArea.isShow) continue;
        else {
          stopArea.isShow = true;
          stopArea.element.visible = true;
        }
      } else {
        if (!stopArea.isShow) continue;
        stopArea.isShow = false;
        stopArea.element.visible = false;
      }
    }
  }

  /**
   * 根据当前数据更新一下stopArea
   */
  repaint() {
    const mapData = this.mapData;

    let stopArea;
    for (let areaId in mapData) {
      stopArea = mapData[areaId];
      if (stopArea.isShow) continue;
      else stopArea.element.visible = false;
    }
  }

  uninstall() {
    const data = this.mapData;

    let element;
    for (let key in data) {
      element = data[key]?.element;
      element && element.destroy();
    }
    this.mapData = {};

    this.currentStopAreaIds = [];
  }

  destroy() {
    this.uninstall();
  }
}

export default StopAreasData;
