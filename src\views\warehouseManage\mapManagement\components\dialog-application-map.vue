<template>
  <div>
    <el-dialog
      :title="$t(dialogApplicationMap.title)"
      :visible.sync="visible"
      :close-on-click-modal="false"
      class="geek-dialog-form"
      width="700px"
      @close="close"
    >
      <el-link
        v-if="activeType!= 2"
        type="primary"
        :underline="false"
        class="tips"
      >
        {{ $t("lang.rms.web.map.version.mapApplicationTips") }}
      </el-link>
      <el-link
        v-if="[4, 5, 6].includes(rowData.status) && activeType!= 2"
        type="danger"
        :underline="false"
        class="danger"
      >
        {{ $t("lang.rms.web.map.version.nonReleasedMapApplicationWarning") }}
      </el-link>
      <el-form label-position="right" label-width="120px">
        <el-form-item :label="$t('lang.rms.web.map.version.cuurentMap')">
          <el-input
            type="text"
            :value="currentMapName"
            disabled
          />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.web.map.version.mapsToBeApplied')">
          <el-input
            type="text"
            :value="rowData.name"
            disabled
          />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.web.map.version.mapChange')">
          <el-input v-model="remark" type="textarea" />
        </el-form-item>
        <el-form-item>
          <el-radio-group v-model="activeType" size="small">
            <el-radio :label="0" border>
              {{ $t("lang.rms.web.map.version.takeEffectAfterRestart") }}
            </el-radio>
            <el-radio :label="1" border>
              {{ $t("lang.rms.web.map.version.immediateEffect") }}
            </el-radio>
            <el-radio :label="2" border>
              {{ $t("lang.rms.fed.reloadFuncCellAndArea") }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class="form-tips">
          <el-link
            v-if="activeType == 0"
            type="primary"
            :underline="false"
            class="tips"
          >
            {{ $t("lang.rms.web.map.version.takeEffectAfterRestartTip") }}
          </el-link>
          <el-link
            v-if="activeType == 1"
            type="danger"
            :underline="false"
            class="danger"
          >
            {{ $t("lang.rms.web.map.version.ChooseEffectImmediately") }}
          </el-link>
          <el-link
            v-if="activeType == 2"
            type="danger"
            :underline="false"
            class="danger"
          >
            {{ $t("lang.rms.fed.reloadFuncCellAndAreaInfo") }}
          </el-link>
          <!-- <el-link v-if="activeType == 2" type="primary" :underline="false" class="tips">
            {{ $t("lang.rms.web.map.version.takeEffectAfterRestartTip") }}
          </el-link>
          <el-link v-else type="danger" :underline="false" class="danger">
            {{ $t("lang.rms.web.map.version.ChooseEffectImmediately") }}
          </el-link> -->
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button @click="visible = false">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button
          type="primary"
          :disabled="disable"
          @click="showAlert"
        >
          {{ $t("lang.rms.fed.application") }}
        </el-button>
      </div>
    </el-dialog>

    <el-dialog :title="$t('lang.rms.fed.replay.tip')" :visible.sync="alertVisable">
      <el-alert
        :title="$t('lang.rms.web.map.version.nonReleasedMapApplicationWarning')"
        type="error"
        :closable="false"
      ></el-alert>
      <el-alert
        v-if="activeType == 0"
        :title="$t('lang.rms.web.map.version.takeEffectAfterRestartTip')"
        type="error"
        :closable="false"
      ></el-alert>
      <el-alert
        v-if="activeType == 1"
        :title="$t('lang.rms.web.map.version.ChooseEffectImmediately')"
        type="error"
        :closable="false"
      ></el-alert>
      <el-alert
        v-if="activeType == 2"
        :title="$t('lang.rms.fed.reloadFuncCellAndAreaInfo')"
        type="error"
        :closable="false"
      ></el-alert>
      <span slot="footer" class="dialog-footer">
        <el-button @click="alertVisable = false">{{ $t("lang.common.cancel") }}</el-button>
        <el-button type="primary" @click="submit">{{ $t("lang.rms.fed.confirm") }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapMutations, mapState } from 'vuex'

export default {
  name: 'DialogApplicationMap',
  data() {
    return {
      currentMapName: '',
      remark: '',
      disable: false,
      activeType: 0,
      alertVisable: false
    }
  },
  computed: {
    ...mapState('mapManagement', ['dialogApplicationMap']),
    visible: {
      get() {
        return this.dialogApplicationMap.visible
      },
      set(val) {
        const { visible } = this.dialogApplicationMap
        if (!val && val !== visible) {
          this.hideDialog()
        }
      }
    },
    rowData() {
      return this.dialogApplicationMap.rowData
    }
  },
  activated() {
    this.getCurrentMap()
  },
  methods: {
    ...mapMutations('mapManagement', ['hideDialog']),
    close() {
      this.remark = ''
    },
    showAlert() {
      this.alertVisable = true;
    },
    submit() {
      this.alertVisable = false;
      this.disable = true
      $req
        .post('/athena/map/version/active', {
          mapId: this.rowData.id,
          activeType: this.activeType
        })
        .then(res => {
          this.reqSuccess(res.msg)
        })
        .catch(e => {
          this.disable = false
        })
    },
    reqSuccess(msg) {
      this.disable = false
      this.visible = false
      this.$emit('refreshList')
      msg = $utils.Tools.transMsgLang(msg)
      this.$success(msg)
    },
    getCurrentMap() {
      $req.post('/athena/map/version/current').then(res => {
        const data = res.data
        this.currentMapName = data.name
      })
    }
  }
}
</script>

<style lang="less" scoped>
.tips {
  margin-bottom: 10px;
  cursor: default;

  &:hover {
    color: #409eff;
  }
}

.danger {
  margin-bottom: 15px;
  cursor: default;

  &:hover {
    color: #f56c6c;
  }
}

.form-tips {
  .el-link {
    line-height: 20px;
  }

  .tips,
  .danger {
    margin-bottom: 0;
  }
}
</style>
