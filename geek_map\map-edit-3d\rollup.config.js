import commonjs from "@rollup/plugin-commonjs";
import url from "@rollup/plugin-url";
import babel from "@rollup/plugin-babel";
import { terser } from "rollup-plugin-terser"; // 压缩js
import vue from "rollup-plugin-vue";
import postcss from "rollup-plugin-postcss";
import postcssImport from "postcss-import";

import webWorkerLoader from "rollup-plugin-web-worker-loader";

export default {
  input: "./src/index.js",
  output: {
    name: "map-edit-3d",
    file: "./dist/index.min.js",
    format: "esm",
    inlineDynamicImports: true,
    intro: "var regeneratorRuntime = require('regenerator-runtime');\n",
    // globals: {
    //   three: "three",
    // },
  },
  plugins: [
    webWorkerLoader({ targetPlatform: "browser", skipPlugins: ["url"] }),
    url({
      include: ["**/*.png", "**/*.jpg"],
    }),
    // OMT(),
    vue({
      css: true,
      target: "browser",
      compileTemplate: true,
    }),
    postcss({
      extensions: [".css"],
      extract: true,
      plugins: [postcssImport()],
    }),
    babel({
      exclude: "node_modules/**",
      extensions: [".js"],
    }),
    commonjs(),
    terser(),
  ],
  external: ["three", "vue"],
};
