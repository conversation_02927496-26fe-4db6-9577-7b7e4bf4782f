import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Tooltip } from "antd";

import { getMap2D } from "../../../singleton";
import rightToolConfig from "../config/right-tools";

function MapRightTools() {
  const { t } = useTranslation();
  const [toolbar, setToolbar] = useState(rightToolConfig);

  // 点击toolbar
  const toolbarClick = (index: number) => {
    const item = toolbar[index];
    item.isActive = !item.isActive;

    const map2D = getMap2D();
    switch (item.name) {
      case "external-obstacle": // 外部障碍物
        map2D.mapRender.toggleLayer("realtimeObstacle", item.isActive);
        break;
      case "show-preempted-area": // 预占区域---(路径包络)显隐
        map2D.mapRender.toggleLayer("robotOccupy", item.isActive);
        break;
      case "display-all-path": // 路径全选---显隐机器人路径
        map2D.mapRender.toggleLayer("robotTrail", item.isActive);
        break;
      case "cell-hot": // 单元格热度
        map2D.mapRender.toggleLayer("cellHeat", item.isActive);
        break;
      case "shelf-heat-display": // 货架热度
        if (item.isActive) {
          map2D.mapWorker
            .reqSocket("QueryInstructionRequestMsg", {
              instruction: "SHELF_HEAT",
            })
            .then(res => {
              if (res.msgType !== "QueryInstructionResponseMsg" || res?.body?.code !== 0) return;
              const heatData: shelfHeatApiData = res?.body?.data || {};
              map2D.mapRender.toggleLayer("shelfHeat", true, heatData);
            });
        } else {
          map2D.mapRender.toggleLayer("shelfHeat", false);
        }
        break;
      case "show-unload-road-direct": // 显隐空载路线
        map2D.mapRender.toggleLayer("unload", item.isActive);
        break;
      case "show-load-road-direct": // 显隐负载路线
        map2D.mapRender.toggleLayer("load", item.isActive);
        break;
      case "show-station-ID": // 显隐工作站ID
        map2D.mapRender.enableScreenPositions(item.isActive, { type: "stations" });
        break;
      case "bumadian": // 中心点
        map2D.mapRender.toggleLayer("location", item.isActive);
        break;
    }
    setToolbar([...toolbar]);
  };

  return (
    <div className="map2d-toolbar-container right-bar">
      {toolbar.map((item, index) => (
        <Tooltip key={index} placement="bottom" title={t(item.lang)}>
          <span
            className={[
              "toolbar-menu monitorMap",
              `monitor-font-${item.name}`,
              item.isActive && item.isSwitch ? "is-active" : "",
            ].join(" ")}
            onClick={() => toolbarClick(index)}
          />
        </Tooltip>
      ))}
    </div>
  );
}

export default MapRightTools;
