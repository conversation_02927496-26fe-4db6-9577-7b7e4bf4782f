/**
 * 用来捕获 EditMap.vue 中可用的refs
 * 示例:
 * import mapVNode from "mapVNode";
 * mapVNode.usexxxx() 来获取可用的ref, use函数依赖Proxy动态生成, 只要EditMap有的这里就可以调用
 * 比如 mapVNode.useRightPanels() 就可以取到 ref值为 rightPanels 的内容
 *
 * 注意: mapVNode为只读属性, 为其赋值无效
 */

let editMapVNodes: any = null;

export function setRef(vnode: any) {
  editMapVNodes = vnode;
}

export default new Proxy({} as { [key: string]: Function }, {
  get: function (target, propKey) {
    let refsName = /use(?<ref>\w+)/.exec(propKey as string)?.groups?.ref;
    if (!refsName || !editMapVNodes) return null;
    refsName = (refsName || "").toLowerCase();
    if (refsName === "mainref") {
      return () => editMapVNodes;
    }
    return function getVNode() {
      const curRefName = Object.keys(editMapVNodes.$refs).find(key => {
        return key.toLowerCase() === refsName;
      });

      if (curRefName) {
        return editMapVNodes.$refs[curRefName];
      }

      return null;
    };
  },
});
