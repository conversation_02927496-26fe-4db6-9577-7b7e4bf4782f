<template>
  <el-dialog
      :title="$t('lang.rms.email.config.editNotice')"
      :visible.sync="showDialog"
      :before-close="closeDialog"
      :close-on-click-modal="false"
      width="28%">
    <el-form
        ref="editForm"
        :model="formData"
        :rules="rules"
        label-width="100px"
    >
      <el-form-item :label="$t('lang.rms.email.config.inputAppName')" prop="appName">
        <el-input v-model="formData.appName"></el-input>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.email.config.sender.address')" prop="sendMail">
        <el-input v-model="formData.sendMail">
          <template slot="append">{{sendMailSuffix}}</template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.email.config.inputSenderPassword')" prop="sendMailPassword" >
        <el-input
            v-model="formData.sendMailPassword"
            :show-password="true"
        />
      </el-form-item>
      <el-form-item :label="$t('lang.rms.email.config.receiver.address')"  prop="receiveMail">
        <el-select
            style="width: 100%"
            v-model="formData.receiveMail"
            multiple
            :multiple-limit="9"
            filterable
            allow-create
            default-first-option
            :placeholder="$t('lang.rms.email.config.inputInboxAddress')"
            @change="receiveMailChange"
        >
          <el-option
              v-for="item in formData.receiveMail"
              :key="item"
              :label="item"
              :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="SMTP" prop="senderSmtp">
        <el-input v-model="formData.senderSmtp">
        </el-input>
      </el-form-item> -->
      <el-form-item :label="$t('lang.rms.email.config.notice.frequency')"  prop="rate">
        <el-input type="number" v-model="formData.rate">
          <template slot="append">min</template>
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.email.config.isNotice')">
        <el-switch v-model="formData.isNotify"></el-switch>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">{{$t('lang.rms.fed.confirm')}}</el-button>
        <el-button @click="closeDialog">{{$t('lang.rms.fed.cancel')}}</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
export default {
  name: 'editDialog',
  props: {
    showDialog: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  mounted() {
    this.formData = { ...this.dialogData }
  },
  data() {
    //通知频率校验
    const rateValidator = (rule, value, callback) => {
      if (value > 0) {
        callback()
      } else {
        callback(new Error(this.$t('lang.rms.email.config.inputNaturalNumbers')))
      }
    }
    return {
      dialogVisible: false,
      //发送邮件后缀
      sendMailSuffix: '@geekplus.com',
      formData: {
        senderSmtp: '',
        appName: '',
        sendMail: '',
        sendMailPassword: '',
        receiveMail: [],
        rate: 30,
        isNotify: true
      },
      //检验规则
      rules: {
        appName: [
          { required: true, message: this.$t('lang.rms.fed.replay.projectName'), trigger: 'blur' },
        ],
        sendMail: [
          { required: true, message: this.$t('lang.rms.email.config.inputSenderAddress'), trigger: 'blur' },
          // { pattern: /^(([^<>()\[\]\.,;:\s@\"]+(\.[^<>()\[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i, message: '请输入正确邮箱地址', trigger: ['blur', 'change']}
        ],
        sendMailPassword: [
          { required: true, message: this.$t('lang.auth.UserAPI.item0181'), trigger: 'blur' },
        ],
        receiveMail: [
          { type: 'array', required: true, message: this.$t('lang.rms.email.config.inputReceiverAddress'), trigger: 'blur' }
        ],
        rate: [
          { type: 'number', required: true, trigger: 'blur', validator: rateValidator }
        ]
      }
    }
  },
  methods: {
    //关闭提示
    closeDialog() {
      this.$emit('closeDialog')
    },
    receiveMailChange(data) {
      const len = data.length
      if (!len) return
      //邮箱校验
      const emailRegExp = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
      const inputEmail = data[len - 1]
      const ok = emailRegExp.test(inputEmail)
      if (!ok) {
        this.$message({
          message: this.$t('lang.rms.email.config.inputRightEmail'),
          type: 'warning'
        });
        this.formData.receiveMail.pop()
      }
    },
    //数据提交
    submit() {
      this.$refs['editForm'].validate((valid) => {
        if (valid) {
          const { sendMail } = this.formData
          const sendData = {
            ...this.formData,
            sendMail: sendMail + this.sendMailSuffix
          }
          this.$emit('submit', sendData)
        } else {
          console.log('error submit!!');
          return false;
        }
      })
    },
  }
}
</script>

<style scoped>
.input-width {
  width: 200px;
}
</style>
