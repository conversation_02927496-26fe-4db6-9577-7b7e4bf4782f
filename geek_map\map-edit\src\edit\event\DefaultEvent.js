import Selected from "../selected/Selected";
import {getId} from '../utils/utils'
import Event from "../event/Event";
import {getGlobalViewport} from "../global";
//多选
import MultiSelected from '../selected/MultiSelected'
//单选
import SingleSelected from '../selected/SingleSelected'
import EventBus from "../eventBus/EventBus";
export default class DefaultEvent {
  static multiSelected = new MultiSelected()
  static singleSelected = new SingleSelected()
  //判断是否为右键点击
  static addEvents() {
    //重置选中模式为单选
    Selected.isMultipleSelected = false
    const vp = getGlobalViewport()
    let timestamp = null
    const events =  {
      clicked:e => {
        if(Event.isRightClick) return Event.isRightClick = false
        if(Date.now() - timestamp > 300) return timestamp = null
        if(Selected.isMultipleSelected){
          this.multiSelected.selected(e)
        }else{
          console.log('vp click')
          this.singleSelected.selected(e)
        }
      },
      mousemove:e => {
        const movePos = e.data.getLocalPosition(vp);
        if(Event.activeKey === 'Shift') {
          this.multiSelected.render(movePos)
        }
        if(Event.activeKey === 'Alt'){
          this.multiSelected.multiMoving(movePos)
        }
      },
      mousedown: e => {
        timestamp = Date.now()
        const startPos = e.data.getLocalPosition(vp);
        if(Event.activeKey === 'Shift') {
          this.multiSelected.start(startPos)
        }
        if(Event.activeKey === 'Alt'){
          this.multiSelected.multiMoveStart(startPos)
        }
      },
      mouseup: e => {
        this.multiSelected.end()
        this.multiSelected.multiMoveEnd()
      },
      keydown:e => {
        const {key} = e
        if(key === 'Escape'){
          this.multiSelected.destroy()
          EventBus.$emit('keydown:Escape')
        }
        Event.activeKey = key
      },
      keyup: (e) => {
        Event.activeKey = null
      },
      rightclick: (e) => {
        Event.isRightClick = true
        const target = e.target
        const $el = (target.isSprite || target.isSingleLane) ? target : target.parent
        const elementId = getId($el)
        const isHas = Selected.isHasSelected(elementId)
        if(isHas){
          EventBus.$emit('rightclick',e)
        }
      }
    }
    return events
  }
}
