import { Quadtree,Rectangle } from '@timohausmann/quadtree-ts/dist/quadtree.esm';
import LayerManager from "../layerManager/LayerManager";
//维护四叉树
export default class Tree {
  constructor() {
    this.tree = null
    // this._create()
  }
  create() {
    const cellLayerInstance = LayerManager.get('CELL')
    if(!cellLayerInstance) return null;
    const $cellContainer = cellLayerInstance.container
    const bounds = $cellContainer.getBounds()
    const {width,height,x,y} = bounds
    // const width = 500,height = -500,x = 0,y = 0;
    // const ops = {width,height,x,y, maxObjects: 10,maxLevels: 4000}
    const ops = {width,height,x,y}
    this.tree = new Quadtree(ops);
    $cellContainer.children.forEach(child => {
      this.insert(child)
    })
  }
  //插入
  insert(child) {
    if(!this.tree) return
    const {width,height,x,y,cellCode,nodeId} = child
    const rect = new Rectangle({
      x,
      y,
      width,
      height,
      data:{
        nodeId,
        cellCode
      }
    });
    this.tree.insert(rect);
  }
  //获取节点
  getTreeNodes({x,y,width,height}) {
    if(!this.tree) return []
    const rect = new Rectangle({
      x,
      y,
      width,
      height
    })
    // const nodes = this.tree.retrieve(rect);
    // return nodes
    return this.tree.retrieve(rect)
  }
  //重新生成四叉树
  // reBuild(){
  //   this.clear()
  //   const cellLayerInstance = LayerManager.get('CELL')
  //   const $cellContainer = cellLayerInstance.container
  //   $cellContainer.children.forEach(child => {
  //     this.insert(child)
  //   })
  // }
  reBuild(){
    this.clear()
    const cellLayerInstance = LayerManager.get('CELL')
    const $cellContainer = cellLayerInstance.container
    $cellContainer.children.forEach(child => {
      this.insert(child)
    })
  }
  //清空四叉树
  clear(){
    if(!this.tree) return;
    this.tree.clear();
  }
}
