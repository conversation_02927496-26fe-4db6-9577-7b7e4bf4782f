/* ! <AUTHOR> at 2021/01 */

export default {
  namespaced: true,
  state: {
    currentComponent: '',
    dialogImportMap: {
      visible: false,
      title: '',
      rowData: null
    },
    dialogMapAddCopy: {
      visible: false,
      title: '',
      rowData: null
    },
    dialogFloorCreate: {
      visible: false,
      title: '',
      rowData: null
    },
    dialogIssueMap: {
      visible: false,
      title: '',
      rowData: null
    },
    dialogApplicationMap: {
      visible: false,
      title: '',
      rowData: null
    },
    dialogImportFloor: {
      visible: false,
      title: '',
      rowData: null,
      floorId: ''
    },
    dialogImportMask: {
      visible: false,
      title: '',
      rowData: null,
      floorId: ''
    },
    dialogExportFloor: {
      visible: false,
      title: '',
      rowData: null,
      floorId: ''
    },
    //焦add,区域管理
    zoneAreaFilter: { areaType: null, areaId: null },
    areaClickId: null
  },
  getters: {
    zoneAreaFilter: state => state.zoneAreaFilter,
    areaClickId: state => state.areaClickId
  },
  mutations: {
    showDialog(state, data) {
      const { currentComponent, title, rowData } = data
      if (state[currentComponent].visible) return
      state.currentComponent = currentComponent
      const options = Object.assign(
        { visible: true },
        {
          title,
          rowData
        }
      )
      if (
        currentComponent === 'dialogImportFloor' ||
        currentComponent === 'dialogImportMask' ||
        currentComponent === 'dialogExportFloor'
      ) {
        options.floorId = data.floorId || ''
      }
      state[currentComponent] = options
    },
    hideDialog(state) {
      const currentComponent = state.currentComponent
      if (!state[currentComponent].visible) return
      $app.$set(state[currentComponent], 'visible', false)
    },
    //改变区域类型
    zoneAreaFilterChange(state, filter) {
      state.zoneAreaFilter = filter
    },
    //点击的区域id
    areaClickTrigger(state, id) {
      state.areaClickId = id
    }
  }
}
