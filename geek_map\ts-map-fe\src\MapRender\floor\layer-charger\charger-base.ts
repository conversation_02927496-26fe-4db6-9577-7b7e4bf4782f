/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerCharger implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  private chargerIcons: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const utils = this.mapCore.utils;

    let container = new PIXI.Container();
    container.name = "charger";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("charger");
    this.container = container;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  render(arr: Array<chargerData>): void {
    const _this = this;
    const container = _this.container;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils,
      textureIcon = utils.getResources("charger");

    let item, options;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatCharger(item);
      let chargerIcon = _this.drawIcon(options, textureIcon);
      let charger = _this.drawSprite(options);
      container.addChild(charger, chargerIcon);
      mapData.charger.setData(options.code, { element: charger, options });
      _this.chargerIcons.push(chargerIcon);
    }
  }

  update(arr: Array<chargerData>) {
    const _this = this;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    let item, options, code, charger;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatCharger(item);
      code = options["code"];
      charger = mapData.charger.getData(code);
      if (charger) {
        mapData.charger.setData(code, Object.assign(charger, { options }));
        _this.updateSprite(charger);
      } else {
        charger = _this.drawSprite(options);
        _this.container.addChild(charger);
        mapData.charger.setData(code, { element: charger, options });
      }
    }
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    // 删除charge icon 对象
    this.chargerIcons.forEach(icon => icon.destroy());
    this.chargerIcons = [];
    console.log("charge repaint, mapData会处理");
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.chargerIcons = null;
  }

  private updateSprite(charger: { element: any; options: mChargerData }) {
    const { element, options } = charger;
    element.tint = options.statusColor;
  }

  private drawIcon(options: mStationData, texture: any) {
    const { width, height, position, direction } = options;

    let sprite: any = new PIXI.Sprite(texture);
    sprite.width = sprite.height = Math.min(width, height);
    sprite.interactive = sprite.buttonMode = false;
    sprite.anchor.set(0.5, 0.5);
    sprite.position.set(position.x, position.y); // 使图片居中
    sprite.rotation = direction; // 设置方向

    return sprite;
  }

  private drawSprite(options: mStationData) {
    const { code, width, height, position, statusColor } = options;

    let sprite: any = new PIXI.Sprite(PIXI.Texture.WHITE);
    sprite.mapType = "charger";
    sprite.name = code;
    sprite.width = width;
    sprite.height = height;
    sprite.tint = statusColor;
    sprite.interactive = sprite.buttonMode = true;
    sprite.anchor.set(0.5, 0.5);
    sprite.position.set(position.x, position.y); // 使图片居中

    return sprite;
  }
}
export default LayerCharger;
