class Graphic {
  constructor(options) {
    this.scene = null;
  }
  // todo: 功能待完善，主要考虑是否抽象一个graphic类
  drawPolygon(points, config) {
    const shape = new THREE.Shape();
    shape.moveTo(points[0].x, points[0].y);
    for (let i = 1; i < points.length; i++) {
      shape.lineTo(points[i].x, points[i].y);
    }
    shape.autoClose = true;
    const planeMeshGeometry = new THREE.ShapeBufferGeometry(shape);
    const meterial = new THREE.MeshBasicMaterial({
      color: 0xffa500,
      side: THREE.DoubleSide,
      transparent: true,
      opacity: 0.5,
    });
    let planeMesh = new THREE.Mesh(planeMeshGeometry, meterial);
    let pointArr = shape.getPoints();
    const geometryPoints = new THREE.BufferGeometry().setFromPoints(pointArr);
    let frame = new THREE.Line(geometryPoints, new THREE.LineBasicMaterial({ color: 0x00bfff }));
    let group = new THREE.Group();
    group.add(planeMesh);
    group.add(frame);
    group.position.y = 2;
    group.rotation.x = -Math.PI / 2;
    this.scene.add(group);
  }
}

export default Graphic;
