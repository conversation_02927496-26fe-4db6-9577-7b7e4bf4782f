/* ! <AUTHOR> at 2021/01 */

export default {
  reqLangMsg(langType) {
    const baseLangCodeUrl = "/athena/api/coreresource/i18n/getLangItems/v1?languageCode=";
    return $req.get(baseLangCodeUrl + langType);
  },
  reqStaticConfig() {
    const basePermissionSystemUrl = "/static/configs/config.json?t=" + Date.now();
    return $req.getStatic(basePermissionSystemUrl);
  },
  reqGitVersionConfig() {
    const gitVersionConfigUrl = "/static/configs/version.config.json?t=" + Date.now();
    return $req.getStatic(gitVersionConfigUrl);
  },
  reqRMSConfig() {
    const url = "/athena/feConfig/getAll";
    return $req.get(url);
  },
  reqRMSDict(types) {
    const url = "/athena/dict/query";
    return $req.post(url, { types: types || [] });
  },

  reqRMSSsoCheck(sessionId) {
    const url = "/athena/sso/auth/check";
    return $req.get(url, { ssoCode: sessionId });
  },

  checkToken() {
    const url = "/athena/warehouse/auth/monitor";
    return $req.get(url);
  },
};
