import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 机器人利用率
 */
export default class utilizationRateLine extends Chart {
  /**
   * 初始化图表 - 机器人利用率
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('line', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "机器人利用率";
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'elDatePicker',
        defaultValue: $utils.Tools.formatDate(new Date(), "yyyy-MM-dd"),
        option: {}
      }
    ]
  }

  async request(params) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stats/query/robot/snapshot', {
      date: $utils.Tools.formatDate(params.date || new Date, "yyyy-MM-dd"),
      cycle : "5"
    })
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const totalDataItem = data?.robotSnapshotList || [];
    const list = totalDataItem.filter(item => item.haveData).map(item => {
      return {
        RS_COUNT: item['DEFAULT_RS_COUNT'] || item['RS_COUNT'] || 0,
        RS_WORK: item['DEFAULT_RS_WORK'] || item['RS_WORK'] || 0,
        P40_COUNT: item['DEFAULT_P40_COUNT'] || item['P40_COUNT'] || 0,
        P40_WORK: item['DEFAULT_P40_WORK'] || item['P40_WORK'] || 0,
        snapshotTime: item.snapshotTime,
      }
    });

    const xAxisData = list.map(item => {
      return $utils.Tools.formatDate(item.snapshotTime, "yyyy-MM-dd hh:mm:ss")
    });

    const rsUtilizationRate = list.map(item => {
      if (!item.RS_WORK) return 0;
      return (1 - Number((item.RS_WORK / item.RS_COUNT).toFixed(2))) * 100 || 0
    });;

    const p40UtilizationRate = list.map(item => {
      if (!item.P40_WORK) return 0;
      return (1 - Number((item.P40_WORK / item.P40_COUNT).toFixed(2))) * 100 || 0
    });
    
    const { tooltipFormatterToHtml } = this;

    return parseEchartOption({
      title: { text: this.title || '' },
      xAxis: { type: 'category', data: xAxisData },
      yAxis: { type: 'value' },
      legend: {
        data: ['RS机器人', 'P40机器人']
      },
      grid: {
        left: 50,
        right: 20,
        bottom: 30,
        top: 50
      },
      tooltip: {
        show: true,
        trigger: 'axis',
        formatter(params, ticket, callback) {
          const axisValue = params[0].axisValue;
          return tooltipFormatterToHtml(`时间: ${axisValue}`, params)
        },
      },
      series: [
        { data: rsUtilizationRate, type: 'line', name: 'RS机器人', },
        { data: p40UtilizationRate, type: 'line', name: 'P40机器人', },
      ]
    })
  }
}