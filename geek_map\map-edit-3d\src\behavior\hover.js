import Base from "../core/abstractPlugin";
import * as THREE from "three";
import { CELL_SPACE } from "../constant/elements";
class hoverElement extends Base {
  constructor(options) {
    super(options);
    this.raycaster = new THREE.Raycaster();
    this.pointer = new THREE.Vector2();
    this.hoverGeo = null;
    this.PluginName = "hoverPlugin";
  }
  // 禁用操作
  deactivated() {
    this.EventInstance.off("hoveElement");
  }
  destroyed() {
    this.addCategory = "";
    this.EventInstance.off("hoveElement");
    this.formatter = null;
  }
  // 激活操作
  activated() {
    this.hoverGeo && this.hidden();
    !this.hoverGeo && this._createGeo();
    const next = () =>
      this.EventInstance.add("hoveElement", {
        moveHandle: event => this._render(event),
        dragoverHandle: event => this._render(event),
      });
    this.Map3d.scene.getObjectByName("floorBox")
      ? next()
      : this.Emitter.on("after:initMap", () => next());
  }
  hoverByPoint(point = null) {
    if (!point) return;
    const cell = this.Store.findSelectCell(point);
    if (cell) {
      const { width, length, startBounds } = cell;
      this.hoverGeo.visible = true;
      this.hoverGeo.geometry = new THREE.PlaneGeometry(
        cell.length * (1 - CELL_SPACE),
        cell.width * (1 - CELL_SPACE),
      );
      this.hoverGeo.position.set(startBounds.x + length / 2, 0.01, -(startBounds.y + width / 2));
    } else {
      this.hidden();
    }
  }
  hidden() {
    this.hoverGeo.visible = false;
  }
  _createGeo() {
    this.hoverGeo = new THREE.Mesh(
      new THREE.PlaneGeometry(1, 1),
      new THREE.MeshBasicMaterial({ color: 0xf4c413 }),
    );
    this.hoverGeo.name = "hover";
    this.hoverGeo.rotateX(-Math.PI / 2);
    this.hidden();
    this.Map3d.scene.add(this.hoverGeo);
  }
  _render(event) {
    const result = this.Map3d.search.get([this.Map3d.scene.getObjectByName("floorBox")], event);
    if (!result) return;
    const cell = result.data[0];
    if (cell) {
      const { width, length, startBounds } = cell;
      this.hoverGeo.visible = true;
      this.hoverGeo.geometry = new THREE.PlaneGeometry(
        cell.length * (1 - CELL_SPACE),
        cell.width * (1 - CELL_SPACE),
      );
      this.hoverGeo.position.set(startBounds.x + length / 2, 0.01, -(startBounds.y + width / 2));
    } else {
      this.hidden();
    }
  }
}

export default hoverElement;
