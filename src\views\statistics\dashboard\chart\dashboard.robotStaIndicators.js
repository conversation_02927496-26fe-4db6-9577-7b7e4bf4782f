import RobotTotalCredByRs from "./cred/robotTotalCredByRs";
import RobotWorkCredByRs from "./cred/robotWorkCredByRs";
import RobotIdleCredByRs from "./cred/robotIdleCredByRs";
import RobotChargCredByRs from "./cred/robotChargCredByRs";
import RobotRemoveCredByRs from "./cred/robotRemoveCredByRs";
import UtilizationRateCredByRs from "./cred/utilizationRateCredByRs";

import RobotWorkCredByP40 from "./cred/robotWorkCredByP40";
import RobotTotalCredByP40 from "./cred/robotTotalCredByP40";
import RobotIdleCredByP40 from "./cred/robotIdleCredByP40";
import RobotChargCredByP40 from "./cred/robotChargCredByP40";
import RobotRemoveCredByP40 from "./cred/robotRemoveCredByP40";
import UtilizationRateCredByP40 from "./cred/utilizationRateCredByP40";
// 机器人统计指标相关

import UtilizationRateLine from "./line/utilizationRateLine";

export default [
  // RS机器人总数 
  new RobotTotalCredByRs({ width: '16.6%', height: 5 }),
  // RS工作站中机器人
  new RobotWorkCredByRs({ width: '16.6%', height: 5 }),
  // RS空闲的机器人
  new RobotIdleCredByRs({ width: '16.6%', height: 5 }),
  // RS充电中机器人
  new RobotChargCredByRs({ width: '16.6%', height: 5 }),
  // RS离线中机器人
  new RobotRemoveCredByRs({ width: '16.6%', height: 5 }),
  // RS机器人利用率
  new UtilizationRateCredByRs({ width: '16.6%', height: 5 }),
  // P40机器人总数
  new RobotTotalCredByP40({ width: '16.6%', height: 5 }),
  // P40工作站中机器人
  new RobotWorkCredByP40({ width: '16.6%', height: 5 }),
  // P40空闲的机器人
  new RobotIdleCredByP40({ width: '16.6%', height: 5 }),
  // P40故障中机器人
  new RobotChargCredByP40({ width: '16.6%', height: 5 }),
  // P40离线中机器人
  new RobotRemoveCredByP40({ width: '16.6%', height: 5 }),
  // P40机器人利用率
  new UtilizationRateCredByP40({ width: '16.6%', height: 5 }),
  new UtilizationRateLine({ width: '100%', height: '400px' }),
]
