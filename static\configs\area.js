/**
 * 区域配置
 * 1、支持多楼层配置，area4NextDisplay对象的属性为多个楼层的id
 * 2、每个耧层的区域数据配置在boundaryPoints二维数组内，每个区域分别有四个点位坐标，
 * 第一个点位的x轴坐标与第二个点位的x轴坐标需要不相等，第三个点位的y轴和第二个点位的y轴坐标需要不相等
 */
// window.area4NextDisplay = {
//     // 楼层
//     '1': {
//         floorId: 1,
//         areaId: 1,
//         color: 0xFF01,
//         pendingRobots: [],
//         grantedRobots: [],
//         // 区域点位坐标
//         boundaryPoints: [
//             [
//                 { x: 200, y: 0 },
//                 { x: 202, y: 0 },
//                 { x: 202, y: 40 },
//                 { x: 200, y: 40 }
//             ],
//             [
//                 { x: 210.496, y: 83.692 },
//                 { x: 214.246, y: 83.692 },
//                 { x: 214.246, y: 78.692 },
//                 { x: 210.496, y: 78.692 }
//             ],
//         ]
//     }
// }
