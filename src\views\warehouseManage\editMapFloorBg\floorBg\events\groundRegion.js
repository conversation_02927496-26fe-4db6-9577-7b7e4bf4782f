/* ! <AUTHOR> at 2021/07 */

class groundRegion {
  constructor($vm) {
    this.$vm = $vm;

    this.$fabric = null;
    this.options = null;

    this.isDrag = false;
    this.point = { x1: 0, y1: 0, x2: 0, y2: 0 };
    this.groundColor = "#ffffff";
  }

  init() {
    this.$fabric = this.$vm.$fabric;
    let layer = this.$vm.layers["ground"];
    let actionAttr = this.$vm.actionAttr;
    this.options = {
      layer2d: layer.canvas2d,
      image: layer.image,
      centerPointX: layer.width / 2,
      centerPointY: layer.height / 2,
      rotate: actionAttr.angle,
    };
    this.$fabric.selection = true;
    this.$fabric.selectable = true;
  }

  start(e) {
    this.isDrag = true;

    const { layer2d, centerPointX, centerPointY, rotate } = this.options;
    const { x, y } = e.absolutePointer;

    this.point = { x1: x, y1: y, x2: x, y2: y };

    // 无轮是如何绘制的 先旋再说
    layer2d.translate(centerPointX, centerPointY);
    layer2d.rotate((-rotate * Math.PI) / 180);
    layer2d.translate(-centerPointX, -centerPointY);
  }

  move(e) {
    if (!this.isDrag) return;
  }

  end(e) {
    this.isDrag = false;

    const { layer2d, centerPointX, centerPointY, rotate } = this.options;
    const { x, y } = e.absolutePointer;
    this.point.x2 = x;
    this.point.y2 = y;

    this.saveRepairRegion();

    // 恢复旋转点
    layer2d.translate(centerPointX, centerPointY);
    layer2d.rotate((rotate * Math.PI) / 180);
    layer2d.translate(-centerPointX, -centerPointY);

    // todo 添加history
  }

  destroy() {
    if (!this.$fabric) return;
    this.$fabric.selection = false;
    this.$fabric.selectable = false;
  }

  saveRepairRegion() {
    const { layer2d, image, boundary } = this.options;
    const { x1, y1, x2, y2 } = this.point;

    layer2d.lineWidth = boundary;
    layer2d.fillStyle = this.groundColor;

    layer2d.fillRect(x1, y1, x2 - x1, y2 - y1);

    image.render(layer2d);
    this.$fabric.requestRenderAll();
  }
}

export default groundRegion;
