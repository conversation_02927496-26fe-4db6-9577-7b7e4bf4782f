import { useI18n } from "@packages/hook/useI18n";

// 全局点位编辑
export const cellEditConfFn = () => {
  const { t } = useI18n();
  return [
    {
      prop: "intervalGap",
      label: "lang.rms.fed.stepDistance",
      component: "elInputNumber",
      describe: `${t('lang.rms.fed.stepUnit')}/m`,
      step: 0.1,
      precision: 3,
      max: 10000,
      min: 0.001,
    },
    {
      prop: "intervalAngle",
      label: "lang.rms.fed.stepAngle",
      component: "elInputNumber",
      describe: `${t('lang.rms.fed.stepUnit')}/m`,
      step: 0.1,
      precision: 3,
      max: 360,
      min: 0.001,
    },
    {
      prop: "moveX",
      label: `${t('lang.rms.fed.offsetByXorY', ['X'])}`,
      component: "elInputNumber",
      describe: `${t('lang.rms.fed.offsetDistance')}/m`,
      precision: 3,
      max: 1000000,
      min: -1000000,
      appendAttrsFn(value: number, dataAll: { [k: string]: any }) {
        return {
          step: dataAll.intervalGap || 0.1,
        };
      },
    },
    {
      prop: "moveY",
      label: `${t('lang.rms.fed.offsetByXorY', ['Y'])}`,
      component: "elInputNumber",
      describe: `${t('lang.rms.fed.offsetDistance')}/m`,
      precision: 3,
      max: 1000000,
      min: -1000000,
      appendAttrsFn(value: number, dataAll: { [k: string]: any }) {
        return {
          step: dataAll.intervalGap || 0.1,
        };
      },
    },
    {
      prop: "angle",
      label: "lang.rms.fed.offsetAngle",
      component: "elInputNumber",
      describe: `${t('lang.rms.fed.offsetDistance')}/m`,
      precision: 3,
      max: 360,
      min: -360,
      appendAttrsFn(value: number, dataAll: { [k: string]: any }) {
        return {
          step: dataAll.intervalAngle || 0.1,
        };
      },
    },
  ];
};
