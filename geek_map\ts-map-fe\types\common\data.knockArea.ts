/* ! <AUTHOR> at 2023/04/19 */

/**
 * 机器人相撞区域接口数据类型
 * @param logicId 唯一区域ID
 * @param knockAreaApex 位置数据
 * @param 其他 可选
 */
type knockAreaData = {
  logicId: code;
  knockAreaApex: location[];
  [propName: string]: any;
};

/**
 * 机器人相撞区域地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param type 区域类型 knockArea
 * @param shapeData 宽 0.8
 * @param 其他 可选
 */
type mKnockAreaData = {
  code: code;
  type: "knockArea";
  shapeData: number[];
  [propName: string]: any;
};
