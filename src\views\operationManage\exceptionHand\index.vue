<template>
  <geek-main-structure class="exceptionHandBox">
    <el-card>
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.exchange.shelf") }}</span>
      </div>
      <el-form ref="form" :model="form" label-width="80px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.optionShelf') + 1">
              <geek-fuzzy-search :id="shelfId" query-type="Qshelf" @fuzzySearchBub="val => fuzzySearchBub(val, 1)" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.optionShelf') + 2">
              <geek-fuzzy-search :id="shelfId" query-type="Qshelf" @fuzzySearchBub="val => fuzzySearchBub(val, 2)" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <div class="btnwarp">
              <el-button type="primary" @click="getShelf">
                {{ $t("lang.rms.fed.submit") }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 修改货架老家位置 -->
    <el-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.modifyHomeLocShelf") }}</span>
      </div>
      <el-form>
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.optionShelf')">
              <geek-fuzzy-search :id="shelfId" query-type="Qshelf" @fuzzySearchBub="val => fuzzySearchBub(val, 4)" />
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.shelfPosition')">
              <el-input v-model="cellCode" :placeholder="$t('lang.rms.fed.pleaseEnterTargetCellNumber')" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <div class="btnwarp2">
              <el-button type="primary" @click="submitReviseLocation">
                {{ $t("lang.rms.fed.submit") }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.workstation.cancellation") }}</span>
      </div>
      <el-form>
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.web.monitor.robot.workStationId')">
              <geek-fuzzy-search :id="shelfId" query-type="Qstation" @fuzzySearchBub="val => fuzzySearchBub(val, 3)" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <div class="btnwarp2">
              <el-button type="primary" @click="submitShelf">
                {{ $t("lang.rms.fed.submit") }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.task.canceled") }}</span>
      </div>
      <el-form>
        <el-row>
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.web.monitor.robot.taskId')">
              <el-input v-model="taskId" type="number" :placeholder="$t('lang.rms.web.monitor.robot.taskId')" />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <div class="btnwarp2">
              <el-button type="primary" @click="submitTaskId">
                {{ $t("lang.rms.fed.submit") }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.speed.stored.speedControllerManage") }}</span>
      </div>
      <el-form>
        <el-row>
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.mapArea.id')">
              <el-input v-model="areaIds" :placeholder="$t('lang.rms.fed.mapArea.id')" />
              <div v-show="areaIdTip" class="areaIdTip">
                {{ $t("lang.rms.fed.area.error.speedControllerManage") }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <div class="btnwarp2">
              <el-button type="primary" @click="submitAreaId(true)">
                {{ $t("lang.rms.fed.speed.start.speedControllerManage") }}
              </el-button>
              <el-button type="primary" @click="submitAreaId(false)">
                {{ $t("lang.rms.fed.speed.stop.speedControllerManage") }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 货架移除需求 -->
    <el-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.shelfRemove") }}</span>
      </div>
      <el-form>
        <el-row>
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.shelfNumber')">
              <el-input v-model="shelfCode" :placeholder="$t('lang.rms.fed.pleaseEnterShelfCode')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <div class="btnwarp2">
              <el-button type="primary" @click="submitRemoveShelf">
                {{ $t("lang.rms.fed.submit") }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 解锁账号 -->
    <el-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.unlockUsername") }}</span>
      </div>
      <el-form>
        <el-row>
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.userName')">
              <el-input
                v-model="userName"
                :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.fed.userName')}`"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <div class="btnwarp2">
              <el-button type="primary" @click="submitDeblocking">
                {{ $t("lang.rms.fed.submit") }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    
    <!-- 滚筒按需到达 -->
    <!-- <el-card class="mt-5">
      <div slot="header" class="clearfix">
        <span>{{ $t("lang.rms.fed.rollersArriveDemand") }}</span>
      </div>
      <el-form>
        <el-row>
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.web.station.stationId')">
              <el-input v-model="rollersArriveDemandStationId" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <div class="btnwarp2">
              <el-button type="primary" @click="completedRollArrDe">
                {{ $t("lang.rms.fed.textCompleted") }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card> -->
  </geek-main-structure>
</template>

<script>
/**
 *  lang.rms.fed.shelfRemove 货架移除
 */
export default {
  data() {
    return {
      shelfId: "",
      form: {
        sourceShelfCode: "",
        targetShelfCode: "",
      },
      cancelForm: {
        stationId: null,
      },
      taskId: null,
      tasksetInterval: null,
      options: [],
      headers: { headers: { "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8" } },
      areaIds: null,
      areaIdTip: false,
      shelfCode: null,
      cellCode: "",
      localShelfCode: "",
      userName: "",
      rollersArriveDemandStationId: ""
    };
  },
  watch: {
    areaIds() {
      const reg = /^((-1|[1-9]\d*),)*(-1|[1-9]\d*)$/;
      reg.test(this.areaIds) ? (this.areaIdTip = false) : (this.areaIdTip = true);
    },
  },
  beforeDestroy() {
    clearInterval(this.tasksetInterval);
  },
  methods: {
    getShelf() {
      // 异常处理货架交换
      $req
        .post(
          "/athena/shelf/swapShelf",
          $utils.Tools.getParams({
            sourceShelfCode: this.form.sourceShelfCode,
            targetShelfCode: this.form.targetShelfCode,
          }),
          this.headers,
        )
        .then(res => {
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },
    submitShelf() {
      if (!this.cancelForm.stationId) {
        this.$error($utils.Tools.transMsgLang("lang.rms.api.result.warehouse.stationIdNull"));
        return false;
      }
      // 异常处理货架工作站取消
      $req
        .post(
          "athena/station/cancelTask",
          $utils.Tools.getParams({ stationId: this.cancelForm.stationId }),
          this.headers,
        )
        .then(res => {
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },
    submitTaskId() {
      if (!this.taskId) {
        this.$error($utils.Tools.transMsgLang("lang.rms.fed.taskIdCannotBeEmpty"));
        return false;
      }
      // 异常处理货架任务取消
      $req.post("/athena/task/cancel", $utils.Tools.getParams({ taskId: this.taskId }), this.headers).then(res => {
        this.$message({
          message: $utils.Tools.transMsgLang(res.msg),
          type: res.code === 0 ? "success" : "error",
        });
      });
    },
    async submitRemoveShelf() {
      if (!this.shelfCode) return this.$error(this.$t("lang.rms.fed.pleaseEnterShelfCode"));
      const { code } = await window.$req.post("/athena/exceptionHandle/removeShelf", {
        shelfCode: this.shelfCode,
      });
      if (!code) return this.$success(this.$t("lang.common.success"));
    },
    async submitReviseLocation() {
      if (!this.localShelfCode) return this.$error(this.$t("lang.rms.fed.pleaseEnterShelfCode"));
      if (!this.cellCode) return this.$error(this.$t("lang.rms.fed.pleaseEnterTargetCellNumber"));
      const { code } = await window.$req.postParams("/athena/shelf/changeShelfPlacement", {
        shelfCode: this.localShelfCode,
        cellCode: this.cellCode,
      });

      if (code === 0) return this.$success(this.$t("lang.common.success"));
    },
    submitAreaId(active) {
      const areaIdArr = this.areaIds ? this.areaIds.split(",") : [];
      if (!this.areaIds || areaIdArr.length === 0 || this.areaIdTip) {
        this.$error($utils.Tools.transMsgLang("lang.rms.api.result.areaIdIsNotAllowNull"));
        return false;
      }
      $req
        .post("/athena/exceptionHandle/changeSpeed", {
          areaIds: areaIdArr,
          active: active,
        })
        .then(res => {
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },
    // 接收子组件传递过来的数据
    fuzzySearchBub(params, val) {
      if (params.pointer.value) {
        //   // 使用模糊查询的情况
        if (val === 1) {
          this.form.sourceShelfCode = params.pointer.value;
        } else if (val === 2) {
          this.form.targetShelfCode = params.pointer.value;
        } else if (val === 4) {
          this.localShelfCode = params.pointer.value;
        } else {
          this.cancelForm.stationId = params.pointer.value;
        }
      } else {
        // 没有使用模糊查询的情况
        if (val === 1) {
          this.form.sourceShelfCode = params.pointer;
        } else if (val === 2) {
          this.form.targetShelfCode = params.pointer;
        } else if (val === 4) {
          this.localShelfCode = params.pointer;
        } else {
          this.cancelForm.stationId = params.pointer;
        }
      }
    },

    // 解锁账号
    submitDeblocking() {
      $req
        .post("/athena/api/coreresource/auth/user/unlockUser/v1", {
          userName: this.userName,
        })
        .then(res => {
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    },

    completedRollArrDe() {
      if (!this.rollersArriveDemandStationId) {
        this.$message({
          message: this.$t('lang.rms.api.result.parameter.stationIdNull'),
          type: 'error'
        })
        return;
      }
      $req
        .get(`/athena/engine/tools/arrivedGroupTask/removeExceptionTask?stationId=${this.rollersArriveDemandStationId}`)
        .then(res => {
          this.rollersArriveDemandStationId = ""
          this.$message({
            message: $utils.Tools.transMsgLang(res.msg),
            type: res.code === 0 ? "success" : "error",
          });
        });
    }
  },
};
</script>

<style lang="less" scoped>
.exceptionHandBox {
  .el-select {
    width: 100%;
  }
  .btnwarp {
    padding: 43px 0 0;
  }
  .btnwarp2 {
    padding: 33px 10px 0;
  }
  :deep(input[type="number"]) {
    -moz-appearance: textfield;
  }
  :deep(input[type="number"]::-webkit-inner-spin-button),
  :deep(input[type="number"]::-webkit-outer-spin-button) {
    -webkit-appearance: none;
    margin: 0;
  }
  .mt-5 {
    margin-top: 5px;
  }
  .areaIdTip {
    color: red;
  }
}
</style>
