<template>
  <el-dialog
    :title="$t('lang.rms.fed.selectParameters')"
    :visible.sync="dialogVisible"
    :before-close="close"
    width="72%"
    class="dialog-params-tree"
  >
    <button class="addBtn" @click="add"><i class="el-icon-arrow-right"></i></button>
    <tree-transfer
      ref="paramsTree"
      :title="title"
      :from_data="fromData"
      :to_data="toData"
      :pid="parent_id"
      :defaultProps="defaultProps"
      :defaultCheckedKeys="selectedParams"
      :defaultExpandedKeys="[1]"
      :mode="mode"
      height="100%"
      filter
      :placeholder="$t('lang.rms.fed.pleaseEnter')"
      @left-check-change="leftChange"
      @right-check-change="rightChange"
      :checkOnClickNode="true"
      :checkStrictly='checkall'
    >
      <template slot="title-left">
        <div class="countBox">{{ checkedKeyLength }}/{{ currentTreeNumber }}</div>
      </template>
      <template slot="title-right">
        <i class="el-icon-delete" @click="clear"></i>
      </template>
    </tree-transfer>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.rms.fed.cancel") }}</el-button>
      <el-button type="primary" :disabled="!toData.length" @click="handleSave">{{ $t("lang.rms.fed.confirm") }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
import treeTransfer from "el-tree-transfer";
export default {
  components: {
    treeTransfer,
  },
  data() {
    return {
      dialogVisible: false,
      currentId: null,
      selectedParams: [],
      currentTreeNumber: 0,
      checkedKeyLength: 0,
      topKeyList: [],
      childKeyList: [],
      defaultProps: {
        children: "childConfig",
        label: "paramsLabel",
      },
      parent_id: "pid",
      mode: "transfer",
      fromData: [],
      toData: [],
      leftDataList: [],
      rightDataList: [],
      checkedList: [],
      selectDele: [],
      selectedData: [],
      checkall:false
    };
  },
  computed: {
    title() {
      return [this.$t("lang.rms.config.page.allParam"), this.$t("lang.rms.config.page.selectedParam")]
    }
  },
  methods: {
    open(currentLibrary, paramsTree) {
      // console.log(currentLibrary, paramsTree)
      //默认选中
      let selectedData =
        currentLibrary && currentLibrary.hasOwnProperty("items") ? currentLibrary.items : [];
      let selectedParams = [];
      selectedData.map(item => selectedParams.push(item.id));

      const { topKeyList, childKeyList } = this._deepData(paramsTree);
      this.currentId = currentLibrary.template.id;
      this.fromData = paramsTree;
      // console.log("this.fromData ", this.fromData);
      this.fromData.forEach(item => {
        // item.pid = 0;
        // item.id = 1;
      });
      this.selectedParams = selectedParams;
      this.currentTreeNumber = childKeyList.length;
      this.topKeyList = topKeyList;
      this.childKeyList = childKeyList;
      this.dialogVisible = true;
      this.checkedKeyLength = selectedParams.length;
      this.selectedData = selectedData;
    },
    _deepData(list, topKeyList = [], childKeyList = []) {
      list.forEach(item => {
        if (item.childConfig) {
          topKeyList.push(item.code);
          this._deepData(item.childConfig, topKeyList, childKeyList);
        } else {
          childKeyList.push(item.code);
        }
        if (!item.id) {
          var id = Math.random().toString(36).substr(2, 3);
          item.id = id;
        }
      });
      return {
        topKeyList,
        childKeyList,
      };
    },
    leftChange() {
      //选中的数据
      this.leftDataList = this.$refs.paramsTree.getChecked().leftNodes;
      const toData = [];
      this.leftDataList.forEach(item => {
        if (!item.childConfig) {
          toData.push(item);
        }
      });
      //选中的个数
      this.checkedKeyLength = toData.length;
    },
    add() {
      //选中的数据
      this.leftDataList = this.$refs.paramsTree.getChecked().leftNodes;
      //只展示选中的子节点
      this.leftDataList.forEach(item => {
        if (!item.childConfig) {
          this.toData.push(item);
        }
      });
      this.checkall=true;
      this.rightDataList = [];
      this.$refs.paramsTree.$refs["wl-transfer-component"]["to_check_all"] = null;
      this.$refs.paramsTree.$refs["wl-transfer-component"]["to_is_indeterminate"] = null;
    },
    rightChange() {
      this.selectDele = [];
      // 选中的数据
      this.rightDataList = this.$refs.paramsTree.getChecked().rightNodes;
      this.rightDataList.forEach(item => {
        this.selectDele.push(item.id);
      });
      // console.log("00", this.$refs.paramsTree);
    },
    clear() {
      if (this.rightDataList.length) {
        const newArr = this.toData.filter(item => {
          return !this.selectDele.includes(item.id);
        });
        this.toData = newArr;
        //清除右边选中样式
        this.$refs.paramsTree.$refs["wl-transfer-component"]["to_check_all"] = false;
        this.$refs.paramsTree.$refs["wl-transfer-component"]["to_is_indeterminate"] = false;
        this.checkall=false;
      }
    },
    close() {
      Object.assign(this.$data, this.$options.data());
    },
    handleSave() {
      // if (this.toData.length == 0 && this.selectedData.length !== 0) {
      //   this.selectedData.map(item => this.checkedList.push(item.code));
      // } else {
      //   this.toData.map(item => this.checkedList.push(item.code));
      // }
      this.toData.map(item => this.checkedList.push(item.code));
      // 配置库-参数项添加
      $req
        .post("/athena/config/template/item/add?templateId=" + this.currentId, this.checkedList)
        .then(res => {
          this.$emit("updatedLibrary", this.currentId);
          this.close();
        });
    },
  },
};
</script>
<style lang="less">
.dialog-params-tree {
  .el-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    height: calc(100% - 200px);
  }
  .el-dialog__body {
    padding: 15px 20px;
    flex: 1;
    max-height: 100% !important;
    // overflow: auto;
  }
  .el-input {
    width: 220px;
  }
}
</style>
<style lang="less" scoped>
/deep/.el-dialog {
  border-radius: 12px;
  // position: relative;
}
/deep/.wl-transfer {
  padding-right: 30px;
}
.count {
  position: absolute;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 400;
  font-size: 0.12rem;
  top: 88px;
  color: #999ea5;
}
/deep/.el-icon-delete {
  position: absolute;
  color: #999ea5;
  top: 12px;
  right: 20px;
  cursor: pointer;
}
.countBox {
  position: absolute;
  color: #999ea5;
  top: 0px;
  right: 20px;
  font-size: 13px;
}
/deep/.wl-transfer .transfer-center-item {
  display: none;
}
.addBtn {
  width: 45px;
  height: 45px;
  background: #999ea5;
  position: absolute;
  z-index: 999;
  top: 46%;
  right: 47.5%;
  cursor: pointer;
  background: #f2f3f5;
  border-radius: 4px;
  font-size: 16px;
}
</style>
