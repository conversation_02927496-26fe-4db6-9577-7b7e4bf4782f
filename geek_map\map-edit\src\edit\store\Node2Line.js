//节点与线的关系
export default class Node2Line {
  constructor() {
    //节点与线的关联关系
    this.node2Line = {}
  }
  //插入数据
  insert(nodeId,segmentId){
    if(!this.node2Line[nodeId]){
      this.node2Line[nodeId] = []
    }
    if(!this.node2Line[nodeId].includes(segmentId)){
      this.node2Line[nodeId].push(segmentId)
    }
  }
  //删除节点或某个线数据
  delete(nodeId,segmentId){
    if(segmentId){
      const arr = this.node2Line[nodeId]
      if(!arr || !arr.length) return
      const index = arr.indexOf(segmentId)
      this.node2Line[nodeId].splice(index,1)
    }else{
      // this.node2Line.delete(nodeId)
      delete this.node2Line[nodeId]
    }
  }
  getLine(nodeId){
    return this.node2Line[nodeId]
  }
  clear() {
    this.node2Line = {}
  }
}
