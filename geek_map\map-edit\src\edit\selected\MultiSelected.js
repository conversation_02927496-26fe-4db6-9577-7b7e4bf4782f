import LayerManager from "../layerManager/LayerManager";
import {Graphics} from "pixi.js";
import Rect from "../element/baseElement/Rect";
import Control from "../control/Control";
import {pixi2cad,cad2pixi,getId, isInsideRect, toFixed} from '../utils/utils'
import Selected from '../selected/Selected'
import {cloneDeep} from 'lodash';
import EventBus from "../eventBus/EventBus";
export default class MultiSelected {
  constructor() {
    this.startPos = null
    this.endPos = null
    this.multiStartPos = null
    this.w = 0
    this.h = 0
    this.$operateLayer = null
    this.$rect = null
    this.moveOnce = true
  }
  //判断是否为多选态
  _isMultipleSelected() {
    return Selected.isMultipleSelected
  }
  //执行批量移动
  multiMoveStart(startPos){
    if(!this._isMultipleSelected()) return
    this.multiStartPos = startPos
    console.log(startPos,'我开始批量移动拉')
    // const allSelected = Selected.getAllSelected()
    // const len = allSelected.length
    // if(!len) return
    Control.enableDrag(false)
  }
  multiMoving(endPos){
    if(!this._isMultipleSelected() || !this.multiStartPos) return
    // if(!this.multiStartPos) return
    const {x: ex, y: ey} = pixi2cad(endPos)
    const {x: sx, y: sy} = pixi2cad(this.multiStartPos)
    const offsetX = ex - sx
    const offsetY = ey - sy
    const allSelected = Selected.getAllSelected()
    // const len = allSelected.length
    // if (!len) return
    // console.log(allSelected)
    const updateData = allSelected.map(item => {
      const {properties} = item
      const {location, startBounds} = properties
      const {x, y} = location
      const {x: bx, y: by} = startBounds
      const offsetLocation = {x: toFixed(x + offsetX), y: toFixed(y + offsetY)}
      const offsetStartBounds = {x: toFixed(bx + offsetX), y: toFixed(by + offsetY)}
      const cloneP = cloneDeep(properties)
      return {...cloneP, location: offsetLocation, startBounds: offsetStartBounds}
    })
    LayerManager.updateElements({id: 'CELL', data: updateData, isSaveHistory: this.moveOnce})
    this.multiStartPos = cad2pixi({x: sx + offsetX, y: sy + offsetY})
    this.moveOnce = false
  }
  multiMoveEnd(){
    if(!this.multiStartPos) return
    this.multiStartPos = null
    this.moveOnce = true
    //结束时重新更新下数据，让历史记录
    // const allSelected = Selected.getAllSelected()
    // const updateData = allSelected.map(item => {
    //   const {properties} = item
    //   const cloneP = cloneDeep(properties)
    //   return cloneP
    // })
    // LayerManager.updateElements({id: 'CELL', data: updateData})
  }
  //选中触发
  selected(e){
    const target = e.event.target
    if(target.name !== 'element') return
    // if(['viewport','device'].includes(target.name)) return
    const $el = target.isSprite ? target : target.parent
    // const {nodeId,segmentId,id} = $el
    // const selectedId = nodeId || segmentId || id
    const selectedId = getId($el)
    if(Selected.isHasSelected(selectedId)){
      Selected.resetSelected(selectedId)
    }else{
      Selected.renderSelected(selectedId,$el)
    }
    const selectedData = Selected.getAllSelected()
    EventBus.$emit('selected',selectedData)
  }
  render(endPos= {x:0,y:0}) {
    if(!this.$rect) return
    this.endPos = endPos
    const {x:ex,y:ey} = this.endPos
    const {x:sx,y:sy} = this.startPos
    const w = this.w = ex - sx
    const h = this.h = ey - sy
    const renderOps = {
      x:sx,y:sy,w,h
    }
    this.$rect.clear()
    Rect.render(this.$rect,renderOps)
  }
  start(startPos = {x:0,y:0}) {
    Control.enableDrag(false)
    const operateLayerInstance = LayerManager.get('OPERATE')
    this.$operateLayer = operateLayerInstance.container
    this.$rect = new Graphics()
    this.$operateLayer.addChild(this.$rect)
    this.startPos = startPos
    Selected.resetAllSelected()
  }
  end(){
    const {w,h,startPos,endPos} = this
    // this.multiStartPos = null
    if(!startPos) return
    const {x:sx,y:sy} = startPos
    const {x:ex,y:ey} = endPos
    const rectOp = {x:Math.min(sx,ex),y:Math.min(sy,ey),width:Math.abs(w),height:Math.abs(h)}
    const result = isInsideRect(rectOp)
    if(result.length){
      Selected.isMultipleSelected = true
      result.forEach(item => {
        const {nodeId} = item
        const {$el} = LayerManager.getProperties({layerName:'CELL',id:nodeId})
        Selected.renderSelected(nodeId,$el)
      })
    }
    this.clear()
    Control.enableDrag(true)
    const selectedData = Selected.getAllSelected()
    EventBus.$emit('selected',selectedData)
  }
  clear() {
    this.$rect = null
    // this.multiStartPos = null
    this.startPos = null
    this.w = null
    this.h = null
    if(this.$operateLayer) this.$operateLayer.removeChildren()
  }
  destroy() {
    // if(!Selected.isMultipleSelected) return
    this.clear()
    Selected.isMultipleSelected = false
    Selected.resetAllSelected()
    EventBus.$emit('selected',null)
  }
}
