<template>
  <div class="annular-item">
    <h6 class="title">{{ title }}</h6>
    <div class="content" ref="content">
      <circle-progress v-if="width" :percent="number" :size="width" />
    </div>
  </div>
</template>

<script>
import CircleProgress from "./circleProgress";
export default {
  name: "statisticsNumberItem",
  props: {
    option: {
      type: Object,
    },
  },
  components: { CircleProgress },
  data() {
    return {
      width: 0,
      centerW: 0
    }
  },
  computed: {
    number() {
      return this.option?.number || 0;
    },
    title() {
      return this.option?.title || "";
    },
    style() {
      const { number } = this;
      return {
        transform: `rotate(${number / 100}turn)`,
        background: number >= 50 ? '#4479FD' : '#ccc',
      }
    }
  },
  mounted() {
    setTimeout(() => {
      const { offsetWidth, offsetHeight } = this.$refs.content;
      this.width = (offsetWidth > offsetHeight ? offsetHeight : offsetWidth) * 0.9;
      this.centerW = this.width - 30;
    }, 50)
  },
};
</script>

<style lang="less" scoped>
.annular-item {
  border-radius: 3px;
  background: #fff;
  border: 1px solid #eee;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .title {
    height: 36px;
  }

  >h6 {
    padding: 8px 12px;
    font-weight: 600;
    color: #666;
    text-align: center;
    font-size: 14px;
    border-bottom: 1px solid #eee;
  }

  .content {
    flex: 1;
    position: relative;
  }

}
</style>
