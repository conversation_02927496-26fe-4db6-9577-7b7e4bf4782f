/* ! <AUTHOR> at 2021/01 */

const _fn = {
  // 处理接口获取的日期格式兼容safari
  formatStandardDate(date) {
    // if(Object.prototype.toString.call(date)==='[object String]'){
    //     return new Date(date.replace(/(\+\d{2})(\d{2})$/, "$1:$2"));
    // }
    return new Date(date);
  },
  // 格式化日期
  formatDate(date, fmt) {
    let o = {
      "M+": date.getMonth() + 1, // 月份
      "d+": date.getDate(), // 日
      "h+": date.getHours(), // 小时
      "m+": date.getMinutes(), // 分
      "s+": date.getSeconds(), // 秒
      "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
      S: date.getMilliseconds(), // 毫秒
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (let k in o) {
      if (new RegExp("(" + k + ")").test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length),
        );
      }
    }
    return fmt;
  },

  // 类型判断
  dataType(data) {
    const type = Object.prototype.toString.call(data);
    switch (type) {
      case "[object Object]":
        return "Object";
      case "[object Array]":
        return "Array";
      case "[object Date]":
        return "Date";
      case "[object Boolean]":
        return "Boolean";
      case "[object String]":
        return "String";
      case "[object Number]":
        return "Number";
      case "[object Null]":
        return "Null";
      case "[object Undefined]":
        return "Undefined";
      case "[object Function]":
        return "Function";
      case "[object Error]":
        return "Error";
      case "[object RegExp]":
        return "RegExp";
      case "[object Symbol]":
        return "Symbol";
      case "[object HTMLDocument]":
        return "document";
      case "[object window]":
        return "window";
      default:
        return "Unknow";
    }
  },
};

export default _fn;
