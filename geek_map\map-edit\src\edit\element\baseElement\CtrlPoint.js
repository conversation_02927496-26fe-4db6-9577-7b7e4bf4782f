// import {Graphics} from "pixi.js";
import { SmoothGraphics as Graphics } from '@pixi/graphics-smooth'
import Point from "./Point";

export default class CtrlPoint {
  static render(x,y) {
    const outerPStyle = {
      color: 0xffffff,
      alpha: 1
    }
    // '#4e586c'
    const innerPStyle = {
      color: 0x4e586c,
      alpha: 1
    }
    const $outerP = new Graphics();
    const $innerP = new Graphics();
    Point.render($outerP, {x,y,r:4,style:outerPStyle})
    Point.render($innerP, {x:0,y:0,r:2,style:innerPStyle})
    $outerP.addChild($innerP)
    $outerP.buttonMode = true;
    $outerP.interactive = true;
    $outerP.visible = true
    return $outerP
  }
}
