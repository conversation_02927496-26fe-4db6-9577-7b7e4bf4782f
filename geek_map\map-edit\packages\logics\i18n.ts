import type { App } from "vue";
import type { I18n } from "vue-i18n";
import { createI18n } from "vue-i18n";
import { useAppStore } from "@packages/store/app";
import { getLangItems } from "@packages/api/language";

export const LANGUAGE_I18N_MAP = [
  ["zhcn", "zhtw", "jajp", "enus", "zhhk", "heil", "dede", "nlbe", "frfr", "kokr", "eses"],
  ["zh_cn", "zh_tw", "ja_jp", "en_us", "zh_hk", "he_il", "de_de", "nl_be", "fr_fr", "ko_kr", "es_es"],
];
 
import zhcn from "element-plus/es/locale/lang/zh-cn";
import zhtw from "element-plus/es/locale/lang/zh-tw";
import enus from "element-plus/es/locale/lang/en";
import ja from "element-plus/es/locale/lang/ja";
import he from "element-plus/es/locale/lang/he";
import de from "element-plus/es/locale/lang/de";
import nl from "element-plus/es/locale/lang/nl";
import ft from "element-plus/es/locale/lang/fr";
import kokr from "element-plus/es/locale/lang/ko";
import eses from "element-plus/es/locale/lang/es";

const Ellangs = { zhcn, enus, ja, de, zhtw, zhhk: zhtw, he, nl, ft, kokr, eses };

export let i18n: any,
  initRequest: boolean = false;
// setup i18n instance with glob
export async function setupI18n(app: App) {
  const appStore = useAppStore();
  const elLangData: any = {};
  Object.keys(Ellangs).forEach((fileName: string) => {
    const { el } = (Ellangs as any)[fileName];
    elLangData[toName(fileName)] = { el };
  });

  console.log('elLangData > ', elLangData)
  i18n = createI18n({
    locale: toName(appStore.language || "zh-CN"),
    globalInjection: true,
    allowComposition: true,
    silentTranslationWarn: true,
    messages: {
      ...elLangData,
    },
  }) as I18n;

  app.use(i18n);
  (window as any).i18n = i18n;
}

export function setLanguageData(languageData: { [key: string]: string }, languageType?: string) {
  const addLangType = toName(languageType || useAppStore().language || "zh-CN");
  const messageData = { ...i18n.global.messages };
  const curLangData = (messageData as any)[addLangType] || {};
  const curAdd = {
    ...curLangData,
    ...languageData,
  };
  console.log('change language: ', addLangType);
  i18n.global.setLocaleMessage(addLangType, curAdd);
}

export function setLanguageType(type: string) {
  const addLangType = toName(type || "zh-CN");
  queryLanguageData(addLangType)?.then(() => {
    // 请求国际化
    i18n.global.locale = addLangType;
  });
}

export function queryLanguageData(language: string) {
  const index = LANGUAGE_I18N_MAP[0].indexOf(language);
  initRequest = true;
  if (index !== -1) {
    return getLangItems({ languageCode: LANGUAGE_I18N_MAP[1][index] }).then(({ code, data }) => {
      if (code === 0) {
        setLanguageData(data, LANGUAGE_I18N_MAP[0][index]);
      }
    });
  }
}

export function toName(name: string): string {
  return name.replaceAll(/[-_]/g, "").toLowerCase();
}

export function setLanguage(language: string) {
  const locale = toName(language);
  // const appStore = useAppStore();
  i18n.global.locale = locale;
  // if (appStore.autoI18n && !initRequest) {
  queryLanguageData(locale);
  // }
}
