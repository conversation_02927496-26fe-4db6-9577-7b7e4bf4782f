import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Tree, Select } from "antd";
import { getMap2D, $eventBus } from "../../../../singleton";

import filterData from "./filter-data";

function FilterSelect() {
  const { t } = useTranslation();
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [value, setValue] = useState(undefined);
  const [treeData, setTreeData] = useState([]);
  const [defaultKey, setDefaultKey] = useState("");

  useEffect(() => {
    $eventBus.on("wsFastSearchTop", (data: any[]) => {
      filterData.setData(data);

      // 急停区域
      const stopAreas = filterData.getStopAreas();
      if (stopAreas.length) {
        const map2D = getMap2D();
        map2D.mapRender.renderArea("STOP", stopAreas);
      }
      // 限速区域
      const speedLimitAreas = filterData.getSpeedLimitAreas();
      if (speedLimitAreas.length) {
        const map2D = getMap2D();
        map2D.mapRender.renderArea("REAL_TIME_SPEED_LIMIT_AREA", speedLimitAreas);
      }

      const treeData = filterData.getTreeData();
      setTreeData(treeData);
      setDefaultKey(filterData.getDefaultKey());
    });
    return () => {
      $eventBus.off("wsFastSearchTop");
    };
  }, []);

  const treeSelect = (selectedKeys: any, e: any) => {
    let newValue = e.node.value;

    const dataMap = filterData.getDataMap();
    let { rootType, type, data } = dataMap[newValue];
    const map2D = getMap2D();

    if (newValue === value) {
      setValue(undefined);
      setSelectedKeys([]);
      map2D.mapRender.triggerFastFilter(rootType as any, { action: "remove" });
      return;
    }

    setValue(newValue);
    setSelectedKeys([newValue]);
    let funcCellCodes: Array<code> = [];
    let areasData: Array<any> = [];

    switch (rootType) {
      case "FUNCTIONAL_CELL":
        let robotTypes: any = null;
        data.forEach((item: any) => {
          const cellCodes = item.functionalArea || [];
          funcCellCodes = funcCellCodes.concat(cellCodes);
          const rts = item?.extraProperties?.robotTypes || [];
          if (!rts.length) return;
          if (!robotTypes) robotTypes = {};
          for (let i = 0, len = cellCodes.length; i < len; i++) {
            robotTypes[cellCodes[i]] = rts;
          }
        });
        map2D.mapRender.triggerFastFilter(rootType, {
          cellCodes: funcCellCodes,
          action: "render",
        });
        // 休息点
        if (robotTypes) {
          map2D.mapRender.renderCellText(true, robotTypes);
        } else {
          map2D.mapRender.renderCellText(false);
        }
        // if (type === "RestPointFuncCell") {
        //   console.log("RestPointFuncCell 休息点");
        // }
        break;
      case "CELL_SIZE_TYPE":
        map2D.mapRender.triggerFastFilter(rootType, { sizeType: newValue, action: "render" });
        break;
      case "WAREHOUSE_AREA":
        if (Object.prototype.toString.call(data) !== "[object Array]") data = [data];
        data.forEach((item: any) => {
          let area: any = {
            floorId: item.extraProperties?.floorId,
            areaId: item.extraProperties?.areaId,
            areaType: item.extraProperties?.areaType,
          };
          if (item.extraProperties?.polygons) area.polygons = item.extraProperties.polygons;
          else area.cellCodes = item.functionalArea || [];
          areasData.push(area);
        });
        map2D.mapRender.triggerFastFilter(rootType, {
          areaType: type,
          areasData,
          action: "render",
        });
        break;
    }
  };

  const onSearch = (value: string) => {
    const treeData = filterData.getTreeData();
    if (!value) {
      setTreeData(treeData);
      return;
    }
    let filterTree = onFilterTreeData(treeData, value);
    setTreeData(filterTree);
  };

  const onFilterTreeData = (treeData: any[], val: string) => {
    let filterTree: any[] = [];
    treeData.forEach(item => {
      const data = item?.children || [];
      let title;
      if (item.extraText) title = `${t(item.title)} (${item.extraText})`;
      else title = t(item.title);

      if (data.length) {
        let children = onFilterTreeData(data, val);
        let obj = {
          ...item,
          children,
        };
        if (children && children.length) {
          filterTree.push(obj);
        } else if (title.includes(val)) {
          filterTree.push({ ...item });
        }
      } else {
        if (title.includes(val)) {
          filterTree.push({ ...item });
        }
      }
    });

    return filterTree;
  };

  return (
    <Select
      value={value}
      allowClear
      showSearch
      placement="bottomLeft"
      placeholder={t("lang.rms.fed.fastSearch")}
      style={{ width: "100%" }}
      className="map2d-fast-search"
      dropdownStyle={{ maxHeight: 280, overflow: "auto", paddingRight: 10 }}
      dropdownMatchSelectWidth={false}
      onClear={() => {
        treeSelect([], { node: { value } });
      }}
      onSearch={onSearch}
      dropdownRender={() => (
        <Tree
          height={260}
          selectedKeys={selectedKeys}
          blockNode={true}
          virtual={true}
          className="map2d-fast-search-dropdown"
          treeData={treeData}
          defaultExpandedKeys={[defaultKey]}
          onSelect={treeSelect}
          titleRender={(node: any) => {
            if (node.extraText) return `${t(node.title)} ${node.extraText}`;
            else return t(node.title);
          }}
        />
      )}
    />
  );
}

export default FilterSelect;
