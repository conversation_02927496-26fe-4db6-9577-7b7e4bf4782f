/* ! <AUTHOR> at 2023/04/20 */

namespace MRender {
  /** mesh 类 */
  export interface Mesh {
    /**
     * 创建mesh对象
     * @param geometries
     * @param shader
     */
    createMesh(geometries: any[], shader: any): any;
    /**
     * rect geometry
     * @param position
     * @param vColor
     */
    drawGeometryRect(position: Array<number>, vColor: any): any;
    /**
     * 不需要设置颜色的icon geometry
     * @param position
     */
    drawGeometryIcon(position: Array<number>): any;
    /**
     * 设置颜色的icon geometry
     * @param position
     * @param vColor
     */
    drawGeometryIconColor(position: Array<number>, vColor: any): any;

    /** rect shader */
    getShaderRect(): any;

    /** 不需要设置颜色的 icon shader */
    getShaderIcon(texture: any): any;

    /** 需要设置颜色的 icon shader */
    getShaderIcon(texture: any): any;
  }
}
