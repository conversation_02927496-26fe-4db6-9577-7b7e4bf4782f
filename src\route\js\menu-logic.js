/* ! <AUTHOR> at 2021/02 */

export default {
  /**
   * 权限模式下处理后台获取的 menuList codes
   * @param menuList
   * @param menuCodes
   * @param btnCodes
   * @returns {{menuCodes: *[], btnCodes: *[]}}
   */
  getPermissionCodes(menuList, menuCodes = [], btnCodes = []) {
    menuList.forEach(item => {
      menuCodes.push(item.code);

      let btns = item.btn;
      if (btns && btns.length > 0) {
        btns.forEach(btn => {
          btnCodes.push(btn.code);
        });
      }

      let child = item.children;
      if (child && child.length > 0) {
        this.getPermissionCodes(item.children, menuCodes, btnCodes);
      }
    });
    return { menuCodes, btnCodes };
  },

  /**
   * 权限模式 获取 aside menu data
   * @param menuRoutes
   * @param rootMenu
   * @param menuCodes
   * @returns {*[]}
   */
  getPermissionAsideMenu(menuRoutes, rootMenu) {
    return this._resolve3LevelMenu(menuRoutes, rootMenu);
  },

  /**
   * 非权限模式的admin用户 获取 aside menu data
   * @param menuRoutes
   * @param rootMenu
   * @returns {*[]}
   */
  getNoPermissionAdminAsideMenu(menuRoutes, rootMenu) {
    return this._resolve3LevelMenu(menuRoutes, rootMenu);
  },

  /**
   * 非权限模式的guest用户 获取 aside menu data
   * @param menuRoutes
   * @param rootMenu
   * @returns {*[]}
   */
  getNoPermissionGuestAsideMenu(menuRoutes, rootMenu) {
    return this._resolve3LevelMenu(menuRoutes, rootMenu, true);
  },

  _resolve3LevelMenu(menuRoutes, rootMenu, isNoPermissionMenu = false) {
    let menuObj = {};
    rootMenu.forEach(item => {
      const { icon, title } = item.meta;
      const path = item.path;
      menuObj[path] = {
        title,
        icon,
        path,
        children: [],
      };
    });

    let level2Menu = null;
    menuRoutes.forEach(item => {
      const { pid, title, notMenu, noPermissionGuest, hasSubMenu } = item.meta;
      if (level2Menu && level2Menu.path === pid) {
        level2Menu.children.push({
          path: item.path,
          title,
        });
        return;
      }
      if (!pid || notMenu || !menuObj[pid]) return;
      if (isNoPermissionMenu && !noPermissionGuest) return;
      if (hasSubMenu) {
        level2Menu = {
          title,
          path: item.path,
          children: [],
        };
        menuObj[pid].children.push(level2Menu);
      } else {
        menuObj[pid].children.push({
          path: item.path,
          title,
        });
      }
    });
    return Object.values(menuObj);
  },
};
