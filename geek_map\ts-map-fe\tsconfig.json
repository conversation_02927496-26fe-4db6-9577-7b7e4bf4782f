{
  "compilerOptions": {
    "noEmit": true, // 不输出文件，只做类型检查
    "jsx": "react-jsx", // 允许编译器支持编译react代码,
    "strict": true,
    "allowJs": true,
    "target": "es6",
    "module": "ESNext",
    "lib": [
      "dom",
      "DOM.Iterable",
      "ESNext"
    ],
    "noImplicitAny": true, // 不需要显示地声明变量的类型any
    "allowSyntheticDefaultImports": true, // 允许混合编译JavaScript文件
    "esModuleInterop": true, // 允许我们使用commonjs的方式import默认文件, import React from 'react';
    "moduleResolution": "node", // 决定了编译器的工作方式,
    "resolveJsonModule": true, // "resolveJsonModule"如果为false,那么将无法识别.json后缀名文件.而且它必须和"moduleResolution":"node"连用
    "strictNullChecks": false,
    "typeRoots": [
      "./node_modules/@types",
      "./types/*.ts",
      "./types/common/*.ts",
      "./types/render/*.ts",
      "./types/worker/*.ts"
    ]
  },
  "include": [
    "packages/html/**/*.tsx",
    "packages/**/*.tsx",
    "packages/**/*.ts",
    "src/**/*.ts",
    "types/**/*.ts",
    "types/**/*.d.ts",
    "index.ts"
  ],
  "exclude": [
    "node_modules"
  ]
}