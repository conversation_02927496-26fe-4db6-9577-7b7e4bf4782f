<template>
  <el-card shadow="never" class="component-operate-detail">
    <div slot="header" class="header">
      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>
    </div>
    <order-group-grid>
      <grid-item :label="$t('lang.rms.fed.textNodeCode')" :value="detailData.cellCode || '--'" />
      <grid-item
        :label="$t('lang.rms.fed.textIndexCoordinates')"
        :value="detailData.index || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.textAbsoluteCoordinate')"
        :value="detailData.location || '--'"
      />
      <grid-item :label="$t('lang.rms.fed.textNodeType')" :value="detailData.cellType || '--'" />
      <grid-item
        :label="$t('lang.rms.fed.textNodeStatus')"
        :value="detailData.cellStatus || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.textLoadDirectionMatrix')"
        :value="detailData.direction || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.textUnloadDirectionMatrix')"
        :value="detailData.dirUnload || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.textLoadDirectionNode')"
        :value="detailData.loadAdjacentCells || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.textUnloadDirectionNode')"
        :value="detailData.unloadAdjacentCells || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.textAllocatedRobotID')"
        :value="detailData.allocatedRobotId !== -1 ? detailData.allocatedRobotId : '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.textOccupiedRobotID')"
        :value="detailData.occupyRobotId !== -1 ? detailData.occupyRobotId : '--'"
      />
      <grid-item
        :label="$t('lang.rms.web.monitor.cell.fieldPrefix.occupiedShelfCode')"
        :value="detailData.occupiedShelfCode || '--'"
      />
      <grid-item :label="$t('lang.rms.fed.textLogicArea')" :value="detailData.logicId || '--'" />
      <grid-item :label="$t('lang.rms.fed.textLength')" :value="detailData.length || '--'" />
      <grid-item :label="$t('lang.rms.fed.textWidth')" :value="detailData.width || '--'" />
      <grid-item
        :label="$t('lang.rms.web.monitor.robot.workStationId')"
        :value="detailData.stationId !== -1 ? detailData.stationId : '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.chargerId')"
        :value="detailData.chargerId !== -1 ? detailData.chargerId : '--'"
      />
      <grid-item :label="$t('lang.rms.fed.textLockStatus')" :value="detailData.cellFlag || '--'" />
      <grid-item
        :label="$t('lang.rms.fed.textOriginNode')"
        :value="detailData.startBounds || '--'"
      />
      <grid-item :label="$t('lang.rms.fed.laneId')" :value="detailData.laneId || '--'" />
      <grid-item :label="$t('lang.rms.fed.rsLogicId')" :value="detailData.rsLogicId || '--'" />
    </order-group-grid>
  </el-card>
</template>

<script>
import OrderGroupGrid from "../common/order-group-grid.vue";
import GridItem from "../common/order-group-grid-item.vue";

export default {
  name: "ShelfDetail",
  components: { OrderGroupGrid, GridItem },
  props: {
    detailData: {
      type: Object,
      require: true,
    },
    detailTitle: {
      type: String,
      require: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.component-operate-detail {
  background: #fbfbfb;

  ::v-deep tr > td {
    padding-bottom: 0;
  }
}
</style>
