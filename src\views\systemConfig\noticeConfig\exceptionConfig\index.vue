<template>
  <section style="padding-top: 10px">
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      @row-edit="rowEdit"
      style="margin-top: 10px"
    />

    <editDialog ref="editDialog" @updateTableList="getTableList" />
  </section>
</template>
<script>
import editDialog from "./components/editDialog";

export default {
  name: "FaultConfiguration",
  components: { editDialog },
  data() {
    const isGuest = this.isRoleGuest();
    return {
      form: {
        systemCode: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          systemCode: {
            label: "lang.rms.fault.exception.code",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        columns: [
          {
            label: "lang.rms.fault.exception.code",
            align: "center",
            prop: "systemCode",
            width: "100",
          },
          {
            label: "lang.rms.web.monitor.exception.info",
            prop: "exception",
            formatter: (row, column, cellValue, index) => {
              return this.$t(cellValue);
            },
          },
          {
            label: "lang.rms.fed.chargerSolution",
            prop: "solution",
            formatter: (row, column, cellValue, index) => {
              return this.$t(cellValue);
            },
          },
          {
            label: "lang.rms.fed.showEnableOrDisable",
            prop: "status",
            width: "80",
            formatter: (row, column, cellValue, index) => {
              if (cellValue == 1) return this.$t("lang.rms.fed.chargerEnable");
              else return this.$t("lang.venus.common.dict.disable");
            },
          },
          {
            label: "lang.rms.fed.callbackEnableOrDisable",
            prop: "callbackDisable",
            width: "80",
            formatter: (row, column, cellValue, index) => {
              if (cellValue) return this.$t("lang.rms.fed.chargerEnable");
              else return this.$t("lang.venus.common.dict.disable");
            },
          },
          {
            label: "lang.rms.fed.callbackDuration",
            prop: "callbackDuration",
            width: "70",
          },
          {
            label: "lang.rms.fed.exceptionDuration",
            prop: "exceptionDuration",
            width: "70",
          },
          {
            label: "lang.rms.backlog.fault.isBacklog",
            prop: "messageGroup",
            width: "60",
            formatter: (row, column, cellValue, index) => {
              if (cellValue == 2) return this.$t("lang.rms.fed.yes");
              else return this.$t("lang.rms.fed.no");
            },
          },
          {
            label: "lang.rms.backlog.fault.isEmailNotice",
            prop: "isSendEmail",
            width: "60",
            formatter: (row, column, cellValue, index) => {
              if (cellValue) return this.$t("lang.rms.fed.yes");
              else return this.$t("lang.rms.fed.no");
            },
          },
          {
            label: "lang.rms.backlog.fault.isMaintenance",
            prop: "isMaintenance",
            width: "60",
            formatter: (row, column, cellValue, index) => {
              if (cellValue) return this.$t("lang.rms.fed.yes");
              else return this.$t("lang.rms.fed.no");
            },
          },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                width: "100",
                fixed: "right",
                operations: [
                  {
                    label: "lang.rms.fed.edit",
                    handler: "row-edit",
                  },
                ],
              },
        ),
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    // 编辑
    rowEdit(rowData) {
      this.$refs.editDialog.open(rowData);
    },

    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },

    // 重置搜索参数
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    // 查询
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    getTableList() {
      const params = Object.assign({}, this.form, {
        currentPage: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      });
      $req.get("/athena/fault/message/getFaultMessages", params).then(res => {
        let result = res.data;
        if (!result) return;
        this.tableData = result?.recordList || [];

        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result?.currentPage || 1,
          pageCount: result?.pageCount || 0,
        });
      });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less" scope>
.config-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
