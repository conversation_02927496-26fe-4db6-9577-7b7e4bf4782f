import images from '../asset/images'
import {Loader, Sprite, Texture} from 'pixi.js'
import {getGlobalApp} from "../global"
class Resource {
  static textureList = new Map()
  static loader = null
  static async init() {
    this.loader = new Loader();
    const p = new Promise((resolve,reject) => {
      const {loader} = this
      for(const name in images) {
        loader.add(name, images[name]);
      }
      loader.load((loader, resources) => {

      })
      loader.onComplete.add(res => {
        console.log("完成所有资源加载",loader.resources)
        resolve()
      });
    })
    return p
    // this.loader = new Loader();
    // const {loader} = this
    // for(const name in images) {
    //   loader.add(name, images[name]);
    // }
    // loader.load((loader, resources) => {
    //
    // })
    // loader.onComplete.add(res => {
    //   console.log("完成所有资源加载",loader.resources)
    //   // this.generateTexture()
    // });
  }
  //生成特定贴图
  static generateTexture(){
    const app = getGlobalApp()
    const resources = Resource.loader.resources
    const texture = resources.charge.texture
    //
    // //生成背景
    // const $el = Sprite.from('/public/eggHead.png');
    const $el = new Sprite(Texture.WHITE)
    $el.tint = Math.random() * 0x808080
    $el.width = 48;
    $el.height = 48;
    $el.position.set(0, 0);
    // console.log($el)
    // //生成图标
    // const $icon = new Sprite(Texture.WHITE);
    // $icon.tint = Math.random() * 0x808080
    // $icon.width = 48;
    // $icon.height = 48;
    // $icon.scale.set(0.1)
    // $icon.x = 1
    // $icon.y = 1
    // // $icon.position.set(-24, -24);
    // // $icon.anchor.set(0.5, 0.5);
    // $el.addChild($icon)
    const gt = app.renderer.generateTexture($el)
    console.log(gt)
    this.textureList.set('test',gt)
  }
  static destroy() {
    this.loader = null
  }
}
export default Resource
