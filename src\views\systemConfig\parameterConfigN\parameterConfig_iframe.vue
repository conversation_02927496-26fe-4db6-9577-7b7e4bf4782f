<template>
  <div class="iframeDiv">
    <div class="selectBox">
      <!-- <el-select
        v-model="selectValue"
        :popper-append-to-body="true"
        class="selSerTypeSty"
        size="mini"
      >
        <el-option
          v-for="item in searchOptions"
          :key="item.value"
          :label="$t(item.label)"
          :value="item.value"
        />
      </el-select> -->
      <input
        v-model="searchVal"
        :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.fed.configs.search.param')}`"
        class="searchIpt"
        suffix-icon="el-icon-search"
      />
      <!-- <img src="../../../imgs/parameterConfig/search.png" class="searchImg" /> -->
      <div class="btns">
        <el-button type="primary" @click="onSearch"> {{ $t("lang.rms.fed.query") }}</el-button>
        <el-button @click="reset"> {{ $t("lang.rms.fed.reset") }}</el-button>
      </div>
    </div>
    <div class="line">
      <restartAlert v-if="alertIsShow" @parameterIsShow="parameterIsShow"></restartAlert>
    </div>
    <div class="contentBox">
      <el-tabs v-model="activeName" tab-position="left" :before-leave="beforeLeave">
        <el-tab-pane
          v-for="item in modules"
          :key="item.id"
          :label="item.label"
          :name="item.code"
        ></el-tab-pane>
      </el-tabs>
      <parameter-content
        ref="parameterContent"
        :show="show"
        :alert-tip-show="alertIsShow"
        :current-name="activeName"
        :modules="tabModules"
        :modules-list="getModules"
        @changeData="change"
        @alertShow="alertShow"
        @applySuccess="applySuccess"
      />
    </div>
    <dialogParamsChange
      v-if="dialog"
      @apply="apply"
      @close="close"
      @giveUp="giveUp"
    ></dialogParamsChange>
  </div>
</template>

<script>
import {
  searchOptions,
  // modules,
  // mapModules,
  // taskModules,
  // pathModules,
  // systemModules,
  // frontEndModules,
} from "./config";
import parameterContent from "./components/parameterContent";
import restartAlert from "./components/restartAlert";
import dialogParamsChange from "./components/dialogParamsChange";

export default {
  components: {
    parameterContent,
    restartAlert,
    dialogParamsChange,
  },
  props: {
    // list: {
    //   type: Object,
    //   default() {
    //     return {};
    //   },
    // },
    // paramsConfigList: {
    //   type: Array,
    //   default() {
    //     return [];
    //   },
    // },
  },
  data() {
    return {
      limitShowObj: {},
      limitKeyObj: {},
      parameterList: {},
      paramsConfigList: [],
      paramsObj: {},
      language: "",
      iframeUrl: "",
      isCollapse: true,
      selectValue: "",
      searchVal: "",
      searchOptions,
      modules: [],
      mapModules: [],
      taskModules: [],
      pathModules: [],
      systemModules: [],
      frontEndModules: [],
      tabModules: [],
      params: {},
      showStatus: true,
      activeName: "",
      getModules: {},
      tabIndex: "0",
      dialog: false,
      changeData: false,
      show: true,
      alertIsShow: false,
      nextTabIndex: -1,
    };
  },
  watch: {
    activeName(val) {
      switch (val) {
        case "map":
          this.tabModules = this.mapModules;
          this.getModules = this.parameterList.map;
          break;
        case "path":
          this.tabModules = this.pathModules;
          this.getModules = this.parameterList.path;
          break;
        case "task":
          this.tabModules = this.taskModules;
          this.getModules = this.parameterList.task;
          break;
        case "system":
          this.tabModules = this.systemModules;
          this.getModules = this.parameterList.system;
          break;
        case "fesystem":
          this.tabModules = this.frontEndModules;
          this.getModules = this.parameterList.fesystem;
          break;
      }
    },
  },
  activated() {
    // http://dev-5259.local.k8s.ops.geekplus.cc
    // this.iframeUrl = "/athena/configs/configs.html?lang=" + $utils.Data.getLocalLang();
  },
  async created() {
    // 默认选择
    this.selectValue = this.searchOptions[0].value;
    // this.tabModules = this.mapModules;
    // this.getModules = this.list.map;
    await this.getConfigItems();
    // this.getModules = this.parameterList.map;
    // this.tabModules = this.mapModules;
  },
  methods: {
    async getConfigItems() {
      const res = await $req.get("/athena/configs/configItems", {}, { intercept: false });
      const reg = new RegExp("/", "g");
      this.limitKeyObj = {};
      res.forEach(item => {
        // item.value = item.value === '' ? null : item.value
        item.isShow = true;
        item.errorTipShow = false;
        item.errorTipText = "";
        item.paramsName = this.$t(`lang.configs.${item.code}.label`);
        item.sectionLabel = null;
        if (item.path && item.section) {
          const newPath = item.path.replace(reg, ".");
          item.sectionLabel = this.$t(`lang.configs${newPath}.${item.section}.label`);
        }
        if (item.code) {
          const restrictions = item.restrictions ? JSON.parse(item.restrictions) : null;
          if (restrictions && restrictions.enabled) {
            let limitArr = restrictions.enabled.split("==");
            let val = limitArr[1].replace(/\'/g, "");
            this.limitKeyObj[limitArr[0]] = val;
            item["limitKey"] = {
              [limitArr[0]]: val,
            };
            item.showCondition = restrictions.enabled;
          }
        }
        if (item.immediate == false && item.value !== item.currentUpdateValue) {
          this.alertIsShow = true;
        }
      });
      res.forEach(item => {
        if (this.limitKeyObj[item.code]) {
          let currentUpdateValue =
            item.currentUpdateValue != null ? item.currentUpdateValue : item.value;
          this.limitShowObj[item.code] = item.immediate ? item.value : currentUpdateValue;
        }
      });
      this.paramsConfigList = res;
      const obj = this.jsonFormat(res);
      this.parameterList = Object.assign({}, obj);
      // console.log(this.parameterList)
    },
    jsonFormat(data) {
      // console.log(this.limitShowObj, 12314);
      const dataObj = {};
      let mapObj = {};
      let taskObj = {};
      let pathObj = {};
      let systemObj = {};
      let fesystemObj = {};
      // 二级菜单
      let mapModulesObj = {};
      let taskModulesObj = {};
      let pathModulesObj = {};
      let systemModulesObj = {};
      let frontEndModulesObj = {};
      this.mapModules = [];
      this.taskModules = [];
      this.pathModules = [];
      this.systemModules = [];
      this.frontEndModules = [];
      data.forEach(item => {
        const path = item.path;
        if (path) {
          const pathArr = path.split("/");
          pathArr.shift();
          let firstKey = pathArr[0];
          let secondKey = pathArr[1];
          dataObj[firstKey] = null;
          let childObj = {};
          if (item.code) {
            item.errorTipShow = false;
            const restrictions = item.restrictions ? JSON.parse(item.restrictions) : null;
            // console.log(item.id)
            if (item.limitKey) {
              const keysArr = Object.keys(item.limitKey);
              item.isShow = this.limitShowObj[keysArr[0]] == item.limitKey[keysArr[0]];
            }
            let currentUpdateValue =
              item.currentUpdateValue != null ? item.currentUpdateValue : item.value;

            let val = item.immediate ? item.value : currentUpdateValue;
            if (item.widgetType === "timeslot" && val) {
              val = JSON.parse(val);
            }
            childObj[item.code] = {
              ...item,
              i18nCode: item.code,
              label: item.paramsName,
              type: item.widgetType,
              options: {
                componentName: item.widgetType,
                selectList:
                  item.widgetType === "select" && restrictions ? restrictions["options"] : [],
                defVal: val,
              },
              restrictions,
            };
            if (item.validValueRange) {
              let limitArr = item.validValueRange.split("~");
              childObj[item.code].options.limitMax = limitArr[1];
              childObj[item.code].options.limitMin = limitArr[0];
            }
          }
          switch (firstKey) {
            case "map":
              mapObj[secondKey] = Object.assign({}, mapObj[secondKey], childObj);
              mapModulesObj[item.path] = item.path;
              break;
            case "task":
              taskObj[secondKey] = Object.assign({}, taskObj[secondKey], childObj);
              taskModulesObj[item.path] = item.path;
              break;
            case "path":
              pathObj[secondKey] = Object.assign({}, pathObj[secondKey], childObj);
              pathModulesObj[item.path] = item.path;
              break;
            case "system":
              systemObj[secondKey] = Object.assign({}, systemObj[secondKey], childObj);
              systemModulesObj[item.path] = item.path;
              break;
            case "fesystem":
              fesystemObj[secondKey] = Object.assign({}, fesystemObj[secondKey], childObj);
              frontEndModulesObj[item.path] = item.path;
              break;
          }
        }
      });
      const obj = {
        // map: mapObj,
        // task: taskObj,
        // path: pathObj,
        // system: systemObj,
        // fesystem: fesystemObj,
      };
      if (this.isNull(mapObj)) {
        for (let key in mapObj) {
          const sections = this.groupBy(Object.values(mapObj[key]), "section");
          mapObj[key] = sections;
        }
        obj.map = mapObj;
      }
      if (this.isNull(pathObj)) {
        for (let key in pathObj) {
          const sections = this.groupBy(Object.values(pathObj[key]), "section");
          pathObj[key] = sections;
        }
        obj.path = pathObj;
      }
      if (this.isNull(taskObj)) {
        for (let key in taskObj) {
          const sections = this.groupBy(Object.values(taskObj[key]), "section");
          taskObj[key] = sections;
        }
        obj.task = taskObj;
      }
      if (this.isNull(systemObj)) {
        for (let key in systemObj) {
          const sections = this.groupBy(Object.values(systemObj[key]), "section");
          systemObj[key] = sections;
        }
        obj.system = systemObj;
      }
      if (this.isNull(fesystemObj)) {
        for (let key in fesystemObj) {
          const sections = this.groupBy(Object.values(fesystemObj[key]), "section");
          fesystemObj[key] = sections;
        }
        obj.fesystem = fesystemObj;
      }
      this.modules = [];
      for (let key in dataObj) {
        switch (key) {
          case "map":
            this.modules.push({ label: this.$t(`lang.configs.${key}.label`), id: 1, code: "map" });
            break;
          case "path":
            this.modules.push({ label: this.$t(`lang.configs.${key}.label`), id: 2, code: "path" });
            break;
          case "task":
            this.modules.push({ label: this.$t(`lang.configs.${key}.label`), id: 3, code: "task" });
            break;
          case "system":
            this.modules.push({ label: this.$t(`lang.configs.${key}.label`), id: 4, code: "system" });
            break;
          case "fesystem":
            this.modules.push({ label: this.$t(`lang.configs.${key}.label`), id: 5, code: "fesystem" });
            break;
        }
      }
      for (let key in mapModulesObj) {
        switch (key) {
          case "/map/resolver":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/resolver"),
              id: 1,
              path: "/map/resolver",
            });
            break;
          case "/map/base":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/base"),
              id: 2,
              path: "/map/base",
            });
            break;
          case "/map/station":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/station"),
              id: 3,
              path: "/map/station",
            });
            break;
          case "/map/area":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/area"),
              id: 4,
              path: "/map/area",
            });
            break;
        }
      }
      for (let key in taskModulesObj) {
        switch (key) {
          case "/task/shelfTask":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/shelfTask"),
              id: 1,
              path: "/task/shelfTask",
            });
            break;
          case "/task/box":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/box"),
              id: 2,
              path: "/task/box",
            });
            break;
          case "/task/rsp":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/rsp"),
              id: 3,
              path: "/task/rsp",
            });
            break;
          case "/task/sorting":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/sorting"),
              id: 4,
              path: "/task/sorting",
            });
            break;
          case "/task/charging":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/charging"),
              id: 5,
              path: "/task/charging",
            });
            break;
          case "/task/GoRest":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/GoRest"),
              id: 6,
              path: "/task/GoRest",
            });
            break;
          case "/task/click":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/click"),
              id: 7,
              path: "/task/click",
            });
            break;
          case "/task/inspection":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/inspection"),
              id: 8,
              path: "/task/inspection",
            });
            break;
          case "/task/TaskStrategy":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/TaskStrategy"),
              id: 9,
              path: "/task/TaskStrategy",
            });
            break;
          case "/task/allocationStrategy":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/allocationStrategy"),
              id: 10,
              path: "/task/allocationStrategy",
            });
            break;
        }
      }
      for (let key in pathModulesObj) {
        switch (key) {
          case "/path/dispatching":
            this.pathModules.push({
              label: this.secondTabLabelFun("/path/dispatching"),
              id: 1,
              path: "/path/dispatching",
            });
            break;
          case "/path/planning":
            this.pathModules.push({
              label: this.secondTabLabelFun("/path/planning"),
              id: 2,
              path: "/path/planning",
            });
            break;
          case "/path/system":
            this.pathModules.push({
              label: this.secondTabLabelFun("/path/system"),
              id: 3,
              path: "/path/system",
            });
            break;
        }
      }
      for (let key in systemModulesObj) {
        switch (key) {
          case "/system/rms":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/rms"),
              id: 1,
              path: "/system/rms",
            });
            break;
          case "/system/api":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/api"),
              id: 2,
              path: "/system/api",
            });
            break;
          case "/system/robot":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/robot"),
              id: 3,
              path: "/system/robot",
            });
            break;
          case "/system/stop":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/stop"),
              id: 4,
              path: "/system/stop",
            });
            break;
          case "/system/monitor":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/monitor"),
              id: 5,
              path: "/system/monitor",
            });
            break;
          case "/system/URL":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/URL"),
              id: 6,
              path: "/system/URL",
            });
            break;
        }
      }
      for (let key in frontEndModulesObj) {
        switch (key) {
          case "/fesystem/hide":
            this.frontEndModules.push({
              label: this.secondTabLabelFun("/fesystem/hide"),
              id: 1,
              path: "/fesystem/hide",
            });
            break;
          case "/fesystem/base":
            this.frontEndModules.push({
              label: this.secondTabLabelFun("/fesystem/base"),
              id: 2,
              path: "/fesystem/base",
            });
            break;
        }
      }
      if (this.modules.length) {
        const key = this.modules[0].code;
        this.getModules = obj[key];
        switch (key) {
          case "map":
            this.tabModules = this.mapModules;
            break;
          case "path":
            this.tabModules = this.pathModules;
            break;
          case "task":
            this.tabModules = this.taskModules;
            break;
          case "system":
            this.tabModules = this.systemModules;
            break;
          case "fesystem":
            this.tabModules = this.frontEndModules;
            break;
        }
        this.activeName = key;
      }
      return obj;
    },
    secondTabLabelFun(path) {
      let secondTabLabel = "";
      const reg = new RegExp("/", "g");
      const newPath = path.replace(reg, ".");
      secondTabLabel = this.$t(`lang.configs${newPath}.label`);
      return secondTabLabel;
    },
    isNull(data) {
      if (Object.prototype.toString.call(data) === "[object Object]") {
        return Object.keys(data).length;
      }
      return null;
    },
    groupBy(result, group) {
      let obj = {};
      if (Array.isArray(result)) {
        result.forEach(item => {
          if (item[group]) {
            if (obj[item[group]]) {
              obj[item[group]].push(item);
            } else {
              obj[item[group]] = [];
              obj[item[group]].push(item);
            }
          } else {
            if (obj.list) {
              obj.list.push(item);
            } else {
              obj.list = [];
              obj.list.push(item);
            }
          }
        });
      }
      return obj;
    },
    change(n) {
      this.changeData = n;
      // console.log("this.changeData", this.changeData);
    },
    beforeLeave() {
      // console.log(111);
      // if (this.$refs.parameterContent) {
      //   this.$refs.parameterContent.tabContentReset()
      // }
      if (this.changeData === true) {
        this.dialog = true;
        // this.nextTabIndex = index;
        return false;
      }
      return true;
    },

    applySuccess() {
      this.changeData = false;
      // setTimeout(() => {
      //   this.getConfigItems();
      // }, 300);
    },
    // 应用
    apply() {
      const paramsObj = this.$refs.parameterContent.paramsObj;
      // console.log(paramsObj);
      const arr = [];
      for (let key in paramsObj) {
        arr.push(paramsObj[key]);
      }

      let isFlag = false
      arr.forEach(item => {
        if (item.errorTipShow) {
          isFlag = true
        }
      })
      if (isFlag) return
      $req.post("/athena/configs/update", arr, { intercept: false }).then(res => {
        // console.log(res);
        if (res && res.result === "success") {
          this.$message.success(this.$t("lang.common.success"));
          this.dialog = false;
          this.changeData = false;
          // setTimeout(() => {
          //   this.getConfigItems();
          // }, 300);
        }
      });
    },
    // 取消
    close() {
      this.dialog = false;
    },
    // 放弃
    giveUp() {
      this.changeData = false;
      this.dialog = false;
    },
    // 仅展示未生效参数
    parameterIsShow(n) {
      // this.show = n;
      const dataObj = JSON.parse(JSON.stringify(this.paramsConfigList));
      const newArr = dataObj.filter(item => {
        let currentUpdateValue = item.currentUpdateValue || ''
        item.value = item.value === null ? '' : item.value
        if (item.immediate == false && item.value !== currentUpdateValue) {
          return item;
        }
      });
      // console.log(newArr);
      const obj = this.jsonFormat(newArr);
      this.parameterList = Object.assign({}, obj);
      // console.log(this.parameterList);
    },
    onSearch() {
      // console.log(this.searchVal);
      this.getModules = {}
      this.tabModules = []
      const dataObj = JSON.parse(JSON.stringify(this.paramsConfigList));
      const newArr = dataObj.filter(item => {
        if (item.paramsName.includes(this.searchVal) || item.code.includes(this.searchVal)) {
          return item;
        }
      });
      // console.log(newArr);
      let obj = this.jsonFormat(newArr);
      
      this.parameterList = Object.assign({}, obj);
      console.log(this.parameterList);
    },
    reset() {
      this.selectValue = this.searchOptions[0].value;
      this.searchVal = "";
      const obj = this.jsonFormat(this.paramsConfigList);
      this.parameterList = Object.assign({}, obj);
    },
    alertShow(n) {
      this.alertIsShow = n;
    },
  },
};
</script>

<style lang="less" scoped>
.selectBox {
  display: flex;
  position: relative;
  // padding-left: 15px;
  // padding-top: 15px;
  padding: 10px 15px;
  background: #fff;
}
.btns {
  margin-left: 17px;
}
.selectBox /deep/.el-input__inner {
  width: 68px;
  height: 32px;
  background: #eeeeee;
  padding-right: 4px;
  font-size: 14px;
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  color: #313234;
  border-width: 1px 0px 1px 1px;
  border-color: #d8d8d8;
  border-radius: 6px 0px 0px 6px;
}

.searchIpt {
  font-weight: 400;
  margin-right: 10px;
  padding-left: 10px;
  width: 248px;
  height: 32px;
  font-size: 14px;
  line-height: 32px;
  border: 1px solid #d8d8d8;
  border-radius: 6px;
  color: #999ea5;
  position: relative;
}
/deep/.el-input__suffix {
  margin-right: -7px;
}
.searchImg {
  width: 16px;
  height: 16px;
  position: absolute;
  top: 23px;
  left: 305px;
}
/deep/.el-tabs__item.is-active {
  background: #f7f8fa;
}
/deep/.el-tabs__header {
  margin-top: -9px;
}
.contentBox {
  padding-top: 16px;
  padding-left: -15px;
  display: flex;
  width: 100%;
  height: 100%;
  background: #fff;
}
.iframeDiv {
  width: 100%;
  // margin-top: 12px;
  border-top: 8px solid #f7f8fa;
  border-bottom: 8px solid #f7f8fa;
  background: #f7f8fa;
}
.iframeDiv {
  height: 100%;
}
</style>
