/* ! <AUTHOR> at 2021/01 */

import Vue from "vue";
import VueI18n from "vue-i18n";
import langType from "./_lang-type-data";

Vue.use(VueI18n);

function getMessage() {
  const langKeys = Object.keys(langType);
  let messages = {};
  langKeys.forEach(key => {
    messages[key] = {};
  });
  return messages;
}

const locale = $utils.Data.getLocalLang();
const i18n = new VueI18n({
  locale, // 语言标识
  messages: getMessage(),
  silentTranslationWarn: true, // 是否取消本地化失败时输出的警告
});

if (locale) {
  let langApiUrl = "";
  if (langType[locale] && langType[locale].apiUrl) {
    langApiUrl = langType[locale].apiUrl;
  }
  $utils.Data.setI18nMessage(i18n, langApiUrl);
}

window.__geek__$i18n = i18n;
export default i18n;
