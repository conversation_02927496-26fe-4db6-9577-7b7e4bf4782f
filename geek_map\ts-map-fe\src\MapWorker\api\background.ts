/* ! <AUTHOR> at 2022/08/30 */

// 目前这个文件都是弃用的
import axios from "axios";
let service = axios.create({
  baseURL: "/",
  // 是否携带cookie信息
  withCredentials: true,
  // 超时时间，单位ms
  timeout: 60000,
});

const getBackground = (): Promise<any> => {
  const url = "/athena/map/draw/getMapBackground";
  return new Promise((resolve, reject) => {
    service({ method: "get", url })
      .then(xhr => {
        return xhr.data;
      })
      .then(res => {
        if (res.code === 0) resolve(res);
        else reject(res);
      })
      .catch(error => {
        console.error(error);
      });
  });
};

export { getBackground };
