<template>
  <geek-main-structure>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />

    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @selectionChange="handleSelection"
      @enableCharger="changChargerStatus(1)"
      @disableCharger="changChargerStatus(0)"
      @restartCharger="restartCharger"
      @page-change="pageChange"
      style="margin-top: 10px"
    >
      <template #manageStatus="{ row }">
        <el-tag size="mini" :type="row.manageStatus == 1 ? 'success' : 'danger'">{{ $t(row.manageStatusI18n) }}</el-tag>
      </template>
    </geek-customize-table>
  </geek-main-structure>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      form: {
        chargerId: "",
        hostCode: "",
        type: "",
        interactiveModel: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          chargerId: {
            label: "lang.rms.web.charger.chargerId",
            default: "",
            tag: "input",
          },
          type: {
            label: "lang.rms.web.charger.type",
            default: "",
            tag: "input",
          },
          hostCode: {
            label: "lang.rms.web.charger.hostCode",
            default: "",
            tag: "input",
          },
          interactiveModel: {
            label: "lang.rms.web.charger.interactive",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {
          selection: true,
        },
        actions: [
          {
            label: "lang.rms.fed.enable",
            type: "primary",
            handler: "enableCharger",
          },
          {
            label: "lang.rms.fed.stopStatus",
            type: "primary",
            handler: "disableCharger",
          },
          {
            label: "lang.rms.fed.restartRobot",
            type: "primary",
            handler: "restartCharger",
            disabled: true,
          },
        ],
        columns: [
          { label: "lang.rms.web.charger.chargerId", prop: "chargerId", width: "80" },
          {
            label: "lang.rms.web.charger.status",
            prop: "manageStatus",
            slotName: "manageStatus",
            width: "90",
            align: "center",
          },
          {
            label: "lang.rms.web.charger.workStatus",
            prop: "workStatusDesc",
            width: "100",
            formatter: (row, column, cellValue, index) => {
              if (row.workStatus == 2) {
                return row.robotId + this.$t(cellValue);
              } else {
                return this.$t(cellValue);
              }
            },
          },
          {
            label: "lang.rms.fed.connectState",
            prop: "connectionStatusI18n",
            width: "100",
            formatter: (row, column, cellValue, index) => {
              return cellValue ? this.$t(cellValue) : "--";
            },
          },
          {
            label: "lang.rms.fed.errorState",
            prop: "errorStatusI18n",
            width: "100",
            formatter: (row, column, cellValue, index) => {
              return cellValue ? this.$t(cellValue) : "--";
            },
          },
          { label: "lang.rms.web.station.cellCode", prop: "cellCode" },
          {
            label: "lang.rms.web.charger.interactive",
            prop: "interactiveModelDesc",
            formatter: (row, column, cellValue, index) => {
              return cellValue ? this.$t(cellValue) : "--";
            },
          },
          { label: "lang.rms.web.charger.type", prop: "type" },
          { label: "lang.rms.fed.chargerRobotID", prop: "robotId" },
          { label: "lang.rms.fed.preAllocatedRobotId", prop: "preAllocatedRobotId" },
          { label: "lang.rms.fed.hostCode", prop: "hostCode", width: "80" },
        ],
      },

      selectionIds: [],
    };
  },
  activated() {
    this.getInteractiveModels();
    this.getTableList();
  },
  methods: {
    restartCharger() {
      const selectionIds = this.selectionIds;
      if (!selectionIds.length) {
        this.$warning(this.$t("lang.rms.fed.chooseCharger"));
        return;
      }
      $req.post("/athena/charger/restart", selectionIds).then(res => {
        this.$success();
        this.getTableList();
      });
    },
    changChargerStatus(status) {
      const selectionIds = this.selectionIds;
      if (!selectionIds.length) {
        this.$warning(this.$t("lang.rms.fed.chooseCharger"));
        return;
      }
      if (status == 1) {
        $req.post("/athena/charger/enable", selectionIds).then(res => {
          this.$success();
          this.getTableList();
        });
      } else {
        $req.post("/athena/charger/disable", selectionIds).then(res => {
          this.$success();
          this.getTableList();
        });
      }
    },
    handleSelection(selections) {
      this.selectionIds = selections.map(item => item.chargerId);
      const enableItem = selections.filter(item => item.manageStatus === 1);
      this.tableConfig.actions[2].disabled = !enableItem.length;
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const data = {
        ...this.form,
        pageSize: this.tablePage.pageSize,
        currentPage: this.tablePage.currentPage,
      };
      $req.get("/athena/charger/pageList", data).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList;
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          pageCount: result.pageCount || 0,
        });
      });
    },
    getInteractiveModels() {
      $req.get("/athena/charger/interactiveModel").then(res => {
        const data = res?.data || {};
        let interactiveModels = [];
        for (let key in data) {
          interactiveModels.push({ label: data[key], value: key });
        }
        this.formConfig.configs.interactiveModel.options = interactiveModels;
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
