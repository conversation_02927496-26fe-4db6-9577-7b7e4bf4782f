/**
 * 机器人相撞区域接口数据类型
 * @param areaId 唯一区域ID
 * @param floorId 楼层ID
 * @param 其他 可选
 */
type speedLimitAreaData = {
  areaId: code;
  floorId: code;
  [propName: string]: any;
};

/**
 * 机器人相撞区域地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param type 区域类型 speedLimitArea
 * @param shapeData 宽 0.8
 * @param 其他 可选
 */
type mSpeedLimitArea = {
  code: code;
  type: "speedLimitArea";
  shapeData: number[];
  [propName: string]: any;
};
