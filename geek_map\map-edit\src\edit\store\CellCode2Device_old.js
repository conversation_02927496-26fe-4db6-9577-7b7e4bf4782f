export default class CellCode2Device {
  constructor(props) {
    this.cellCode2Device = {}
  }
  //插入数据
  insert(cellCode,deviceId){
    if(!this.cellCode2Device[cellCode]){
      this.cellCode2Device[cellCode] = []
    }
    if(!this.cellCode2Device[cellCode].includes(deviceId)){
      this.cellCode2Device[cellCode].push(deviceId)
    }
  }
  //删除cellCode和设备的关系
  delete(cellCode,deviceId){
    if(deviceId){
      const arr = this.cellCode2Device[cellCode]
      if(!arr || !arr.length) return
      const index = arr.indexOf(deviceId)
      if(index === -1) return;
      this.cellCode2Device[cellCode].splice(index,1)
      if(!this.cellCode2Device[cellCode].length){
        delete this.cellCode2Device[cellCode]
      }
    }else{
      delete this.cellCode2Device[cellCode]
    }
  }
  //当cellCode发生变更时，更新关系
  update(oldCellCode,newCellCode) {
    if(oldCellCode === newCellCode) return
    const deviceIds = this.cellCode2Device[oldCellCode]
    if(!deviceIds) return
    deviceIds.forEach(id => {
      this.insert(newCellCode,id)
    })
    this.delete(oldCellCode)
  }
  getDevice(cellCode){
    return this.cellCode2Device[cellCode]
  }
  clear() {
    this.cellCode2Device = {}
  }
}
