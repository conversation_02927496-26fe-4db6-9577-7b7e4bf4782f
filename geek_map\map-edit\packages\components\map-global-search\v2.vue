<template>
  <div class="treeselect" ref="treeSelectRef">
    <el-select-v2
      size="mini"
      class="selectV2"
      v-model="selectValue"
      :options="selectV2Options"
      popper-class="globalSelectV2Poper"
      remote
      multiple
      filterable
      collapse-tags
      collapse-tags-tooltip
      :remote-method="remoteMethod"
      @visible-change="visibleChange"
      @change="changeSelect"
    >
    </el-select-v2>
    <el-tree-v2
      ref="treeRef"
      class="selectTreeV2"
      v-show="treeVisible"
      show-checkbox
      :props="treeProp"
      :data="treeOptionI18n"
      :default-checked-keys="selectValue"
      :filter-method="treeFilterMethod"
      @check-change="treeNodeCheck"
    >
      <template #default="{ node }">
        <span class="treeV2Text">{{ $t(node.label) }}</span>
      </template>
    </el-tree-v2>
  </div>
</template>

<script lang="ts" setup>
import { ref, Ref, computed, ComputedRef, onMounted, watch } from "vue";
import { fastSearch } from "@packages/api/map";
import { useAppStore } from "@packages/store/app";
import { useI18n } from "vue-i18n";
import type { TreeNode } from "element-plus/es/components/tree-v2/src/types";

const { t } = useI18n();
const appStore = useAppStore();
// 可筛选项目
const selectValue: Ref<string[]> = ref([]);
// 树数据
const treeOptions: Ref<any[]> = ref([]);
// 扁平化数组, 供给selectV2使用
const delayerList: Ref<
  { label: string; value: string; path: string[]; childKeys: string[]; level: number; id: number }[]
> = ref([]);
// 目前可用选项
const selectV2Options: ComputedRef<{ label: string; value: string }[]> = computed(() => {
  return delayerList.value
    .filter(item => {
      return selectValue.value.includes(item.value);
    })
    .map(item => {
      return {
        label: t(item.label),
        value: item.value,
      };
    });
});

// 树数据
const treeOptionI18n = computed(() => {
  return setI18nRecursion(treeOptions.value);
});

// 请求可查询数据
function loadData() {
  fastSearch({
    floorIds: [appStore.floorId || ""],
    searchTypes: [],
  }).then(({ code, data }) => {
    if (code === 0) {
      const option = parseData(data);
      treeOptions.value = option?.treeData || [];
      delayerList.value = option?.delayerData || [];
    }
  });
}

/**
 * 这里会递归检索数据
 * 得到 delayerList 扁平化数组, { label, value, path, childKeys(表示如果选中了该数据, 展示层应该忽略的内容) }
 * 得到 treeOptions 树数据  { label, value, children, childNodes(表示如果选中了该数据, 应该选中哪些node) }
 * 其中 childKeys 中, 如果此节点是一个父节点, 也会被包含
 * childNodes 只会包含最终节点
 */
function parseData(data: any, _conf: any = {}) {
  if (!data?.length) {
    return;
  }

  // 处理结果 option
  const itemTreeData: any[] = [];
  const itemDelayerData: any[] = _conf.itemDelayerData || [];
  const isType = !!data[0].type;
  const isFunctionalArea = !!data[0].functionalArea;
  const level = _conf.level || 0;

  // 这里判断 如果有type才能够被识别为一个可用的树数据, 没有则不处理
  if (isType) {
    data.forEach((item: any) => {
      const value = item.type;
      const label = item.i18;
      const dataItemOption = { value, label };
      const childKeys = getChildKeys(item.data);
      const childNodes = getChildNodes(item.data);
      const isArea = _conf.isArea || ["WAREHOUSE_AREA"].includes(value);
      if (item.data) {
        const childrenData = parseData(item.data, {
          path: [...(_conf.path || []), value],
          isArea,
          parentType: value,
          itemDelayerData,
          level: level + 1,
        });
        itemTreeData.push({ ...dataItemOption, children: childrenData?.treeData, childNodes });
        itemDelayerData.push({ ...dataItemOption, path: _conf.path || [], childKeys, level });
      } else {
        // 如果没有children 则加入此Node节点为可选节点
        // 不过通常情况下不会走到这里, 因为一般没有children的数据会走到 isFunctionalArea 判断中
        itemTreeData.push({ ...dataItemOption, childNodes });
        itemDelayerData.push({ ...dataItemOption, path: _conf.path || [], childKeys, level });
      }
    });
  }

  // 只有区域处理这个东西
  if (isFunctionalArea && _conf.isArea) {
    data.forEach((item: any) => {
      const value = item.extraProperties?.areaId;
      const label = `${_conf.parentType} ${value}`;
      const path = [...(_conf.path || []), value];
      const dataItemOption = { value, label };
      itemTreeData.push({ ...dataItemOption, childNodes: item });
      itemDelayerData.push({ ...dataItemOption, path, level, id: item.id });
    });
  }

  return {
    treeData: itemTreeData,
    delayerData: itemDelayerData,
  };
}

function getChildKeys(data: any) {
  const keys: string[] = [];

  function getKeys(list: any[]) {
    const isType = !!list[0].type;
    const isFunctionalArea = !!data[0].functionalArea;

    if (isType) {
      list.forEach(listItem => {
        keys.push(listItem.type);
        listItem.data && getKeys(listItem.data);
      });
    }

    if (isFunctionalArea) {
      list.forEach(listItem => {
        const id = listItem.extraProperties?.areaId;
        id && keys.push(id);
      });
    }
  }
  getKeys(data);

  return keys;
}

function getChildNodes(data: any) {
  const nodes: any[] = [];
  function getKeys(list: any[]) {
    list.forEach(listItem => {
      if (listItem.data) {
        getKeys(listItem.data);
      } else {
        nodes.push(listItem);
      }
    });
  }
  getKeys(data);
  return nodes;
}

/**
 * 递归翻译tree v2国际化
 * 后面联动的检索需要用到
 * @param data
 */
function setI18nRecursion(data: any[]) {
  const list: any[] = [];
  data.forEach(({ label, children, ...itemData }) => {
    const option = { label: t(label), ...itemData };
    if (children) {
      option.children = setI18nRecursion(children);
    }
    list.push(option);
  });
  return list;
}

const treeRef = ref();
const treeVisible: Ref<boolean> = ref(false);
const treeProp = { value: "value" };
function visibleChange(visible: boolean) {
  if (visible) {
    treeVisible.value = visible;
  }
  treeRef.value!.filter('');
}

function treeFilterMethod(query: string, node: TreeNode) {
  return node.label!.includes(query);
}

function remoteMethod(keyword: string) {
  treeRef.value!.filter(keyword);
}

/**
 * 点击树菜单
 * @param data
 * @param check
 */
function treeNodeCheck() {
  const selectList = treeRef.value!.getCheckedKeys() || [];

  /**
   * 这里需要处理一下展示的逻辑
   * 因为这里取到的keys是只要选中就会获取, 包括一些父节点
   * 而展示是需要 如果全选则仅展示父节点, 为此需要做一些逻辑操作
   * 1. 标记数据原始index
   * 2. 对数据进行level字段排序
   * 3. 过滤不需要的数据字段
   */

  let list: { value: string; index: number }[] = [];
  // 1. 标记下标, 以供数据处理完成后恢复排序
  selectList
    .map((item: any, index: number) => {
      const dataItem = delayerList.value.find(delayerItem => delayerItem.value === item);
      return { ...dataItem, index };
    })
    // 数据排序
    .sort((itemA: any, itemB: any) => itemB.level - itemA.level)
    // 过滤字段
    .forEach((dataItem: any) => {
      list.push({
        value: dataItem.value,
        index: dataItem.index,
      });

      const childKeys = dataItem.childKeys;
      if (childKeys?.length) {
        list = list.filter(listItem => {
          return !childKeys.includes(listItem.value);
        });
      }
    });

  selectValue.value = list
    .sort((itemA, itemB) => {
      return itemA.index - itemB.index;
    })
    .map(item => item.value);
}

const treeSelectRef = ref();
onMounted(() => {
  document.body.addEventListener("click", event => {
    const path: string[] = (<any>event).path;
    const treeSelEl = treeSelectRef.value;
    if (!path.includes(treeSelEl)) {
      treeVisible.value = false;
    }
  });
});

function changeSelect(list: string[]) {
  treeRef.value!.setCheckedKeys(list);
}

loadData();

watch(selectValue, values => {
  values.forEach(value => {
    const item = delayerList.value.find(item => {
      return item.value === value;
    });

    const selectList = [];
  });
});

defineExpose({
  loadData,
});
</script>

<style scoped lang="scss">
.treeselect {
  position: absolute;
  right: 140px;
  z-index: 6;
  width: 300px;
  top: 0px;

  .selectV2 {
    top: 0px;
    position: absolute;
    width: 100%;
  }

  .selectTreeV2 {
    position: absolute;
    top: 32px;
    left: 0;
    /* height: 600px; */
    width: 100%;
    background: #fff;
    box-shadow: 0px 0px 10px 0px #eee;
    padding: 5px;
    box-sizing: border-box;
    border: 1px solid #eee;
  }

  .treeV2Text {
    font-size: 14px;
  }
}
</style>

<style>
.treeselect .selectV2 .el-select-v2__wrapper {
  border-radius: 0px;
}

.globalSelectV2Poper {
  display: none !important;
}
</style>
