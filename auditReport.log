# npm audit report

d3-color  <3.1.0
Severity: high
d3-color vulnerable to ReDoS - https://github.com/advisories/GHSA-36jr-mh4h-2g58
fix available via `npm audit fix --force`
Will install element-geek@1.0.0, which is a breaking change
node_modules/d3-color
  d3  4.0.0-alpha.1 - 6.7.0
  Depends on vulnerable versions of d3-color
  node_modules/d3
    @antv/g6  2.0.0-alpha - 3.2.10
    Depends on vulnerable versions of @antv/g
    Depends on vulnerable versions of d3
    node_modules/@antv/g6
      element-geek  >=1.0.1
      Depends on vulnerable versions of @antv/g6
      node_modules/element-geek
  d3-interpolate  0.1.3 - 2.0.1
  Depends on vulnerable versions of d3-color
  node_modules/d3-interpolate
    @antv/g  <=3.5.0-beta.6
    Depends on vulnerable versions of d3-interpolate
    node_modules/@antv/g
    d3-brush  0.1.0 - 2.1.0
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-brush
    d3-scale  0.1.5 - 3.3.0
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-scale
    d3-scale-chromatic  0.1.0 - 2.0.0
    Depends on vulnerable versions of d3-color
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-scale-chromatic
    d3-transition  0.0.7 - 2.0.0
    Depends on vulnerable versions of d3-color
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-transition
    d3-zoom  0.0.2 - 2.0.0
    Depends on vulnerable versions of d3-interpolate
    node_modules/d3-zoom

nth-check  <2.0.1
Severity: high
Inefficient Regular Expression Complexity in nth-check - https://github.com/advisories/GHSA-rp65-9cf3-cjxr
fix available via `npm audit fix`
node_modules/nth-check
  css-select  <=3.1.0
  Depends on vulnerable versions of nth-check
  node_modules/css-select
    cheerio  0.19.0 - 1.0.0-rc.3
    Depends on vulnerable versions of css-select
    node_modules/cheerio

semver  <7.5.2
Severity: moderate
semver vulnerable to Regular Expression Denial of Service - https://github.com/advisories/GHSA-c2qf-rxjj-qqgw
No fix available
node_modules/@mapbox/node-pre-gyp/node_modules/semver
node_modules/css-loader/node_modules/semver
node_modules/eslint-plugin-vue/node_modules/semver
node_modules/less/node_modules/semver
node_modules/postcss-loader/node_modules/semver
node_modules/semver
node_modules/vue-eslint-parser/node_modules/semver
  @babel/core  *
  Depends on vulnerable versions of @babel/helper-compilation-targets
  Depends on vulnerable versions of semver
  node_modules/@babel/core
    @babel/helper-compilation-targets  *
    Depends on vulnerable versions of @babel/core
    Depends on vulnerable versions of semver
    node_modules/@babel/helper-compilation-targets
      @babel/plugin-transform-classes  >=7.19.0
      Depends on vulnerable versions of @babel/helper-compilation-targets
      node_modules/@babel/plugin-transform-classes
        @babel/preset-env  *
        Depends on vulnerable versions of @babel/helper-compilation-targets
        Depends on vulnerable versions of @babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression
        Depends on vulnerable versions of @babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining
        Depends on vulnerable versions of @babel/plugin-transform-async-to-generator
        Depends on vulnerable versions of @babel/plugin-transform-classes
        Depends on vulnerable versions of @babel/plugin-transform-function-name
        Depends on vulnerable versions of @babel/plugin-transform-named-capturing-groups-regex
        Depends on vulnerable versions of @babel/plugin-transform-object-rest-spread
        Depends on vulnerable versions of babel-plugin-polyfill-corejs2
        Depends on vulnerable versions of semver
        node_modules/@babel/preset-env
      @babel/plugin-transform-function-name  >=7.16.7
      Depends on vulnerable versions of @babel/helper-compilation-targets
      node_modules/@babel/plugin-transform-function-name
      @babel/plugin-transform-object-rest-spread  *
      Depends on vulnerable versions of @babel/helper-compilation-targets
      node_modules/@babel/plugin-transform-object-rest-spread
    @babel/helper-create-class-features-plugin  *
    Depends on vulnerable versions of @babel/core
    Depends on vulnerable versions of semver
    node_modules/@babel/helper-create-class-features-plugin
      @babel/plugin-transform-class-properties  *
      Depends on vulnerable versions of @babel/helper-create-class-features-plugin
      node_modules/@babel/plugin-transform-class-properties
      @babel/plugin-transform-private-methods  *
      Depends on vulnerable versions of @babel/helper-create-class-features-plugin
      node_modules/@babel/plugin-transform-private-methods
      @babel/plugin-transform-private-property-in-object  *
      Depends on vulnerable versions of @babel/helper-create-class-features-plugin
      node_modules/@babel/plugin-transform-private-property-in-object
    @babel/helper-create-regexp-features-plugin  *
    Depends on vulnerable versions of @babel/core
    Depends on vulnerable versions of semver
    node_modules/@babel/helper-create-regexp-features-plugin
      @babel/plugin-transform-named-capturing-groups-regex  *
      Depends on vulnerable versions of @babel/core
      Depends on vulnerable versions of @babel/helper-create-regexp-features-plugin
      node_modules/@babel/plugin-transform-named-capturing-groups-regex
      @babel/plugin-transform-unicode-property-regex  *
      Depends on vulnerable versions of @babel/helper-create-regexp-features-plugin
      node_modules/@babel/plugin-transform-unicode-property-regex
    @babel/helper-remap-async-to-generator  >=7.18.6
    Depends on vulnerable versions of @babel/core
    node_modules/@babel/helper-remap-async-to-generator
      @babel/plugin-transform-async-generator-functions  *
      Depends on vulnerable versions of @babel/helper-remap-async-to-generator
      node_modules/@babel/plugin-transform-async-generator-functions
      @babel/plugin-transform-async-to-generator  >=7.18.6
      Depends on vulnerable versions of @babel/helper-remap-async-to-generator
      node_modules/@babel/plugin-transform-async-to-generator
    @babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression  *
    Depends on vulnerable versions of @babel/core
    node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression
    @babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining  *
    Depends on vulnerable versions of @babel/core
    node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining
    @babel/plugin-syntax-unicode-sets-regex  *
    Depends on vulnerable versions of @babel/core
    node_modules/@babel/plugin-syntax-unicode-sets-regex
    @babel/plugin-transform-class-static-block  *
    Depends on vulnerable versions of @babel/core
    node_modules/@babel/plugin-transform-class-static-block
    @babel/plugin-transform-unicode-sets-regex  *
    Depends on vulnerable versions of @babel/core
    node_modules/@babel/plugin-transform-unicode-sets-regex
    babel-loader  >=8.0.0-beta.0
    Depends on vulnerable versions of @babel/core
    Depends on vulnerable versions of find-cache-dir
    node_modules/babel-loader
  @babel/helper-define-polyfill-provider  *
  Depends on vulnerable versions of semver
  node_modules/@babel/helper-define-polyfill-provider
    babel-plugin-polyfill-corejs3  >=0.0.1
    Depends on vulnerable versions of @babel/helper-define-polyfill-provider
    node_modules/babel-plugin-polyfill-corejs3
    babel-plugin-polyfill-regenerator  >=0.0.1
    Depends on vulnerable versions of @babel/helper-define-polyfill-provider
    node_modules/babel-plugin-polyfill-regenerator
  @babel/plugin-transform-runtime  >=7.1.0
  Depends on vulnerable versions of babel-plugin-polyfill-corejs2
  Depends on vulnerable versions of semver
  node_modules/@babel/plugin-transform-runtime
  babel-plugin-polyfill-corejs2  >=0.0.1
  Depends on vulnerable versions of semver
  node_modules/babel-plugin-polyfill-corejs2
  make-dir  2.0.0 - 3.1.0
  Depends on vulnerable versions of semver
  node_modules/less/node_modules/make-dir
  node_modules/make-dir
    @mapbox/node-pre-gyp  >=1.0.1
    Depends on vulnerable versions of make-dir
    node_modules/@mapbox/node-pre-gyp
    find-cache-dir  2.1.0 - 3.3.2
    Depends on vulnerable versions of make-dir
    node_modules/find-cache-dir
    less  >=3.11.2
    Depends on vulnerable versions of make-dir
    node_modules/less

46 vulnerabilities (32 moderate, 14 high)

To address issues that do not require attention, run:
  npm audit fix

To address all issues possible (including breaking changes), run:
  npm audit fix --force

Some issues need review, and may require choosing
a different dependency.
