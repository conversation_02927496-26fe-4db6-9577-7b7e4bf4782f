/**
 * historyStore
 * 历史记录
 */
import { defineStore } from "pinia";
import mapVNode from "@packages/hook/useMapVNode";
export interface MapHistory {
  action: string;
  detail: any[]; // 这里可能是各种元素集合
  layerName: string;
}

export const useHistoryStore = defineStore({
  id: "edit",
  state: (): {
    // 可撤销的历史记录
    backList: MapHistory[];
    // 可取消的撤销记录
    forwardList: MapHistory[];
  } => {
    return {
      backList: [],
      forwardList: [],
    };
  },
  actions: {
    setBackList(history: MapHistory[]) {
      this.backList = history;
      const leftPanelRef = mapVNode.useLeftPanels();
      leftPanelRef.setDisabledByName("visRevoke", !history.length);
      // leftPanelRef.setDisabledByName("save", false);
    },
    setForwardList(history: MapHistory[]) {
      this.forwardList = history;
      const leftPanelRef = mapVNode.useLeftPanels();
      leftPanelRef.setDisabledByName("visRevokeCancel", !history.length);
    },
    setSaveState(disabled = true) {
      const leftPanelRef = mapVNode.useLeftPanels();
      leftPanelRef.setDisabledByName("save", disabled);
    }
  },
});
