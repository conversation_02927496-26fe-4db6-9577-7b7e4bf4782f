import SelectPlugin from "../../../behavior/select";
import moveModelPluginInstance from "./moveModelPlugin";

// select插件选中元素并且改变位置 == 对于edit场景比较合适
const selectPluginInstance = new SelectPlugin();
selectPluginInstance.selectCategory = ["station", "conveyor", "cell"];
const handleSelectCell = cell => {
  selectPluginInstance.renderCellSelect(cell);
};

const handleCancelEvent = () => {
  selectPluginInstance.Emitter.off("after:move");
  selectPluginInstance.Emitter.off("illegal:move");
};

const handleSelectModel = (modelData, cell) => {
  const Map3d = selectPluginInstance.Map3d;
  const selectModelData = modelData;
  const selectModel = Map3d.modelInstances[selectModelData.uuid];
  // 渲染选中效果；
  selectPluginInstance.renderModelSelect(selectModel.model);
  moveModelPluginInstance.begin(selectModelData);

  // 关闭监听事件
  handleCancelEvent();
  selectPluginInstance.Emitter.on("after:move", ([newVal]) => {
    selectPluginInstance.triggerSelect(newVal);
  });
  selectPluginInstance.Emitter.on("illegal:move", () => {
    selectPluginInstance.triggerSelect(selectModelData);
  });
};

selectPluginInstance.select(arr => {
  const group = arr.reduce(
    (pre, cur) => {
      const value = pre;
      if (cur.category === "cell") {
        value[0] = cur;
      } else {
        value[1] = cur;
      }
      return value;
    },
    [null, null],
  );
  moveModelPluginInstance.off();
  const [cell, selectModelData] = group;
  cell && handleSelectCell(cell);
  selectModelData && handleSelectModel(selectModelData, cell);
  // 通知选中事件
  selectPluginInstance.Emitter.emit("after:select", JSON.parse(JSON.stringify(arr)));
});

selectPluginInstance.unselect(point => {
  selectPluginInstance.Map3d.transformControl.detach();
  selectPluginInstance.Emitter.emit("after:unselect");
});
export default selectPluginInstance;
