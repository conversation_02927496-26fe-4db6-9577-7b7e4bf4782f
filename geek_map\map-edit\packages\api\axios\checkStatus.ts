import { ElMessage } from "element-plus";
import { useI18n } from "@packages/hook/useI18n";
const error = ElMessage.error!;
export function checkStatus(status: number, msg: string): void {
  const { t } = useI18n();
  switch (status) {
    case 400:
      error(`${msg}`);
      break;
    case 401:
      error(t("接口异常(401)"));
      break;
    case 403:
      error(t("接口异常(403)"));
      break;
    case 404:
      error(t("接口异常(404)"));
      break;
    case 405:
      error(t("接口异常(403)"));
      break;
    case 408:
      error(t("接口异常(408)"));
      break;
    case 500:
      error(t("接口异常(500)"));
      break;
    case 501:
      error(t("接口异常(501)"));
      break;
    case 502:
      error(t("接口异常(502)"));
      break;
    case 503:
      error(t("接口异常(503)"));
      break;
    case 504:
      error(t("接口异常(504)"));
      break;
    case 505:
      error(t("接口异常(505)"));
      break;
    default:
  }
}
