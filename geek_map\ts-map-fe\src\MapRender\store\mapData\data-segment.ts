/* ! <AUTHOR> at 2023/04/23 */

class SegmentsData implements MRender.MapData {
  private mapData: { [propName: floorId]: mSegment[] } = {};

  setData(floorId: floorId, data: mSegment[]) {
    let floorSegments: mSegment[] = this.mapData[floorId];
    if (!floorSegments) floorSegments = [];
    this.mapData[floorId] = floorSegments.concat(data);
  }

  getData(code: code): mSegment {
    return null;
  }

  getByFloorId(floorId: floorId): mSegment[] {
    return this.mapData[floorId] || [];
  }

  getAll(): { [propName: floorId]: mSegment[] } {
    return this.mapData;
  }

  delData(code: code) {}

  uninstall() {
    this.mapData = {};
  }

  destroy() {
    this.uninstall();
  }
}

export default SegmentsData;
