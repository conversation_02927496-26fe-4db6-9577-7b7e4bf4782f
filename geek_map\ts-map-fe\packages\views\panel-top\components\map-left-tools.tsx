/* ! <AUTHOR> at 2022/08/31 */
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Tooltip } from "antd";
import leftToolConfig from "../config/left-tools";
import { getMap2D, getMapDom, $eventBus } from "../../../singleton";

function MapLeftTools() {
  const { t } = useTranslation();
  const [toolbar, setToolbar] = useState(leftToolConfig);

  // 点击toolbar
  const toolbarClick = (index: number) => {
    const item = toolbar[index];
    item.isActive = !item.isActive;

    const map2D = getMap2D();
    switch (item.name) {
      case "full-screen": // 地图Map全屏
        const $map: any = getMapDom();
        if ($map.requestFullscreen) {
          $map.requestFullscreen();
        } else if ($map.mozRequestFullScreen) {
          $map.mozRequestFullScreen();
        } else if ($map.webkitRequestFullscreen) {
          $map.webkitRequestFullscreen();
        } else if ($map.msRequestFullscreen) {
          $map.msRequestFullscreen();
        }
        break;
      case "zoom-in": // Map缩小
        map2D.mapRender.zoom(-0.2);
        break;
      case "zoom-out": // Map放大
        map2D.mapRender.zoom(0.2);
        break;
      case "map-reset": // 重置
        map2D.mapRender.setMapPosition();
        break;
      case "show-help-doc": // 显示帮助pop panel
        $eventBus.emit("dialogHelpShow");
        break;
      case "to-3d": // 去3d页面
        _$utils.sendParentIframe({ type: "to3D", data: item });
        break;
    }
    setToolbar([...toolbar]);
  };

  return (
    <div className="map2d-toolbar-container right-bar">
      {toolbar.map((item, index) => (
        <Tooltip key={index} placement="bottom" title={t(item.lang)}>
          <span
            className={[
              "toolbar-menu monitorMap",
              `monitor-font-${item.name}`,
              item.isActive && item.isSwitch ? "is-active" : "",
            ].join(" ")}
            onClick={() => toolbarClick(index)}
          />
        </Tooltip>
      ))}
    </div>
  );
}

export default MapLeftTools;
