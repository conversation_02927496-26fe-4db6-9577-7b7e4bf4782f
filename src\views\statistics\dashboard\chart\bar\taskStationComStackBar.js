import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 2.1.18按工作站统计已完成任务量
 */
export default class TaskStationComStackBar extends Chart {
  /**
   * 初始化图表 - 2.1.18按工作站统计已完成任务量
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('stackBar', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "按工作站统计已完成任务量";
  }

  async request(params) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stats/query/job/split/count/', {
      date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
      cycle: "60",
      showReceive: true,
      showToStationDone: true,
      showOrgData: false,
      ...params
    })
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const totalDataItem = data.split_stationDoneGroupByStation || {};
    const xAxisData = Object.keys(totalDataItem);
    const serverNamesSet = new Set();
  
    xAxisData.forEach(item => {
      const dataItem = totalDataItem[item];
      Object.keys(dataItem).forEach(item => serverNamesSet.add(item));
    })
  
    serverNamesSet.delete('all_info');
    const serverNameList = [...serverNamesSet];
    serverNameList.sort((a, b) => a - b);
  
    const seriesData = serverNameList.map(serverName => {
      return {
        name: serverName,
        type: 'bar',
        stack: 'stack',
        data: xAxisData.map(x => totalDataItem[x][serverName] || 0)
      }
    });
  
    return parseEchartOption({
      title: { text: this.title || '' },
      xAxis: { type: 'category', data: xAxisData },
      yAxis: { type: 'value' },
      tooltip: { show: true, trigger: 'axis' },
      series: seriesData
    })
  } 
}