/* ! <AUTHOR> at 2023/04/18 */

/** 地图楼层id */
type floorId = number | string;

/** 元素唯一code */
type code = number | string;

/** 16进制数字 */
type color16 = number;

/**
 * 地图位置坐标
 * @param x x坐标位置
 * @param y y坐标位置
 * @param z 楼层（可选）
 */
type location = {
  x: number;
  y: number;
  z?: number;
};

/** mesh位置，length为8的数组number */
type meshPosition = [number, number, number, number, number, number, number, number] | [];

/** hitArea，length为4的数组number */
type hitArea = [number, number, number, number];

/** 货架热度接口数据 */
type shelfHeatApiData = { [propName: code]: number };

/** callback 类型 */
type callback = (...args: any[]) => void;
