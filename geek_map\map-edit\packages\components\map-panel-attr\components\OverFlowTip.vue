<template>
  <div class="overflow-tip" :style="tipStyle">
    <el-tooltip :disabled="disabledTip" :content="$t(text)" placement="left" effect="light">
      <p class="inner-text"
         :style="mixinStyle"
         @mouseover.stop="mot"
         @mouseleave.stop="mlt"
      ><span class="required-symbol" v-show="required">*</span>{{$t(text)}}:</p>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'OverFlowTip',
  props: {
    maxWidthVal: {
      type: String,
      default: '200px'
    },
    required: {
      type: Boolean,
      default: false
    },
    text: {
      type: String,
      default: ''
    },
    customStyle: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    mixinStyle() {
      const { customStyle, addStyle } = this
      return Object.assign({}, customStyle, addStyle)
    },
    tipStyle() {
      return {maxWidth:this.maxWidthVal}
    }
  },
  data() {
    return {
      disabledTip: false,
      addStyle: {}
    }
  },
  methods: {
    mot(e) {
      const dom = e.target
      const clientW = dom.clientWidth
      const scrollW = dom.scrollWidth
      this.disabledTip = scrollW === clientW
      // if (!this.disabledTip) this.addStyle = { cursor: 'pointer' }
      this.addStyle = { cursor: this.disabledTip ? '' : 'pointer' }
    },
    mlt() {
      this.disabledTip = false
    }
  }
}
</script>

<style scoped lang="less">
//超出省略号
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.overflow-tip{
  position: relative;
  //max-width: 140px;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .inner-text{
    margin: 0;
    box-sizing: border-box;
    text-align: right;
    font-size: 12px;
    //padding-right: 8px;
    //font-weight: bold;
    color: #434343;
    white-space: nowrap;
    &:extend(.text-ellipsis);
    .required-symbol {
      color:red;
      margin-right: 2px
    }
  }
  //text-align: right;
  //font-size: 14px;
  //padding-right: 8px;
  //font-weight: bold;
  //color: #434343;
  //&:extend(.text-ellipsis);
}
</style>
