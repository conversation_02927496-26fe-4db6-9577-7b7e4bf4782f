/* ! <AUTHOR> at 2021/03 */
import { fabric } from "fabric";

class wallStraightLine {
  constructor($vm) {
    this.$vm = $vm;

    this.$fabric = null;
    this.options = null;

    this.isDrag = false;
    this.straightLine = null;
    this.point = { x1: 0, y1: 0, x2: 0, y2: 0 };
    this.wallColor = "#000000";
  }

  init() {
    this.$fabric = this.$vm.$fabric;
    let layer = this.$vm.layers["wall"];
    let actionAttr = this.$vm.actionAttr;
    this.options = {
      layer2d: layer.canvas2d,
      image: layer.image,
      centerPointX: layer.width / 2,
      centerPointY: layer.height / 2,
      boundary: actionAttr.wallBoundary,
      rotate: actionAttr.angle,
    };
  }

  start(e) {
    this.isDrag = true;

    const { layer2d, centerPointX, centerPointY, rotate } = this.options;
    const { x, y } = e.absolutePointer;

    this.point = { x1: x, y1: y, x2: x, y2: y };

    // 无轮是如何绘制的 先旋再说
    layer2d.translate(centerPointX, centerPointY);
    layer2d.rotate((-rotate * Math.PI) / 180);
    layer2d.translate(-centerPointX, -centerPointY);

    this.createRepairLine();
  }

  move(e) {
    if (!this.isDrag) return;
    const { x, y } = e.absolutePointer;
    this.point.x2 = x;
    this.point.y2 = y;

    this.updateRepairLine();
  }

  end(e) {
    this.isDrag = false;
    const { layer2d, centerPointX, centerPointY, rotate } = this.options;
    const { x, y } = e.absolutePointer;
    this.point.x2 = x;
    this.point.y2 = y;

    this.saveRepairLine();
    layer2d.closePath();

    // 恢复旋转点
    layer2d.translate(centerPointX, centerPointY);
    layer2d.rotate((rotate * Math.PI) / 180);
    layer2d.translate(-centerPointX, -centerPointY);

    // todo 添加history
  }

  setBoundary(boundary) {
    this.options.boundary = boundary;
  }

  createRepairLine() {
    const { boundary } = this.options;

    const straightLine = new fabric.Line(Object.values(this.point), {
      strokeWidth: boundary,
      stroke: this.wallColor,
      selectable: false,
    });

    this.straightLine = straightLine;
    this.$fabric.add(straightLine);
    this.$fabric.requestRenderAll();
  }

  updateRepairLine() {
    const { boundary } = this.options;
    const { x2, y2 } = this.point;
    this.straightLine.set({ x2, y2, strokeWidth: boundary });
    this.$fabric.requestRenderAll();
  }

  saveRepairLine() {
    const { layer2d, image, boundary } = this.options;
    const { x1, y1, x2, y2 } = this.point;
    this.$fabric.remove(this.straightLine);

    layer2d.lineWidth = boundary;
    layer2d.fillStyle = this.wallColor;
    layer2d.strokeStyle = this.wallColor;

    layer2d.beginPath();
    layer2d.moveTo(x1 + boundary / 2, y1 + boundary / 2);
    layer2d.lineTo(x2 + boundary / 2, y2 + boundary / 2);
    layer2d.stroke();
    layer2d.closePath();

    image.render(layer2d);
    this.$fabric.requestRenderAll();
  }
}

export default wallStraightLine;
