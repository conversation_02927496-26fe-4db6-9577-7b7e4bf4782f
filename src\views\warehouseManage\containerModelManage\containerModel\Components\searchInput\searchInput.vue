<template>
  <el-autocomplete
    v-model="state1"
    class="inline-input"
    :fetch-suggestions="querySearch"
    :placeholder="placeholderStr"
    :trigger-on-focus="false"
    @select="handleSelect"
    @input="inputValueFun"
  />

</template>

<script>
export default {
  props: {
    dataArr: {
      type: Array,
      default: () => []
    },
    placeholderStr: {
      type: String,
      default: '请输入'
    },
    resetFormEvent: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      restaurants: [],
      state1: '',
      state2: ''
    }
  },
  watch: {
    resetFormEvent(val) {
      if (val) this.state1 = null
    }
  },
  methods: {
    querySearch(queryString, cb) {
      var restaurants = this.dataArr
      var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) >= 0)
      }
    },
    handleSelect(item) {
      this.$emit('searchItem', item)
    },
    inputValueFun(data) {
      this.$emit('searchItem', { value: data })
    }
  }
}
</script>

<style>

</style>
