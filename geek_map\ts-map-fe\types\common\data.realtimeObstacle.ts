/* ! <AUTHOR> at 2023/04/19 */

/**
 * 外部障碍物接口数据类型
 * @param tagId 唯一区域ID
 * @param location 位置数据
 * @param coverRadius 区域半径
 * @param lastUpdateTime 最后一次更新时间
 * @param timeout
 * @param 其他 可选
 */
type realtimeObstacleData = {
  tagId: code;
  location: location;
  coverRadius: number;
  lastUpdateTime: number;
  timeout: number;
  [propName: string]: any;
};

/**
 * 外部障碍物地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param type 区域类型 realtimeObstacle
 * @param statusColor 状态颜色
 * @param 其他 可选
 */
type mRealtimeObstacleData = {
  code: code;
  type: "realtimeObstacle";
  statusColor: color16;
  position: location;
  radius: number;
  [propName: string]: any;
};
