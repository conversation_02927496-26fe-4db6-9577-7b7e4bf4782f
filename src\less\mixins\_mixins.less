//flex布局
.g-flex() {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

//flex list布局
.g-flex-list() {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}

//透明度 @opacity:0~1
.g-opacity(@opacity:1) {
  opacity: @opacity;
  @opacity-ie: (@opacity * 100);
  filter: ~"alpha(opacity=@{opacity-ie})";
}

//超出文字省略号
.g-text-overflow() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

//左右对齐
.g-text-justify() {
  text-align: justify;
  word-wrap: break-word;
  word-break: break-all;
}

//超出行文字省略号
.g-text-overflowClamp(@clamp:1) {
  display: -webkit-box;
  word-wrap: break-word;
  word-break: break-all;
  line-clamp: @clamp;
  -webkit-line-clamp: @clamp;
  -webkit-box-orient: vertical;
  text-overflow: -o-ellipsis-lastline;
  text-overflow: ellipsis;
  overflow: hidden;
}

//背景渐变
.g-background-linear(@from:#fff,@to:#fff,@deg:90deg) {
  background-image: linear-gradient(@deg, @from, @to);
}

//占位符相关
.g-placeholder(@color:@g-placeholder-color,@size:.13rem) {
  // Firefox
  &::-moz-placeholder {
    font-family: @g-root-font-family;
    font-size: @size;
    color: @color;
    opacity: 1; // Override Firefox's unusual default opacity;
  }
  &:-ms-input-placeholder {
    font-family: @g-root-font-family;
    font-size: @size;
    color: @color;
  }
  // Internet Safari and Chrome
  &::-webkit-input-placeholder {
    font-family: @g-root-font-family;
    font-size: @size;
    color: @color;
  }
}

/** dom before */
.g-before-absolute() {
  position: relative !important;
  &:before {
    position: absolute;
    display: block;
    content: ' ';
    font-size: 0;
    line-height: 0;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
  }
}

/** dom after */
.g-after-absolute() {
  position: relative !important;
  &:after {
    position: absolute;
    display: block;
    content: ' ';
    font-size: 0;
    line-height: 0;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
  }
}

.g-box-shadow-left(@color:rgb(0 0 0 / 10%)) {
  box-shadow: -5px 0 10px -5px @color;
}

.g-box-shadow-bottom(@color:rgb(0 0 0 / 10%)) {
  box-shadow: 0 5px 10px -5px @color;
}

.g-box-shadow-right(@color:rgb(0 0 0 / 10%)) {
  box-shadow: 5px 0 10px -5px @color;
}

.g-box-shadow-top(@color:rgb(0 0 0 / 10%)) {
  box-shadow: 0px -5px 10px -5px @color;
}

.g-box-shadow-left-top(@color:rgb(0 0 0 / 10%)) {
  box-shadow: -5px -5px 10px  -4px @color;
}

.g-box-shadow-right-top(@color:rgb(0 0 0 / 10%)) {
  box-shadow: 5px -5px 10px -4px @color;
}

.g-box-shadow-left-bottom(@color:rgb(0 0 0 / 10%)) {
  box-shadow: -5px 5px 10px -4px @color;
}

.g-box-shadow-right-bottom(@color:rgb(0 0 0 / 10%)) {
  box-shadow: 5px 5px 10px -4px @color;
}

.g-box-shadow-no-left(@color:rgb(0 0 0 / 10%)) {
  /* .right-bottom,.right-top组合 */
  box-shadow: 5px 5px 10px -4px @color,5px -5px 10px -4px  @color;
}

.g-box-shadow-no-bottom(@color:rgb(0 0 0 / 10%)) {
  /* .left-top,.right-top组合 */
  box-shadow: -5px -5px 10px  -4px @color,5px -5px 10px -4px  @color;
}

.g-box-shadow-no-right(@color:rgb(0 0 0 / 10%)) {
  /* .left-top，.left-bottom组合 */
  box-shadow: -5px -5px 10px  -4px @color, -5px 5px 10px -4px @color;
}

.g-box-shadow-no-top(@color:rgb(0 0 0 / 10%)) {
  /* .left-bottom,,right-bottom组合 */
  box-shadow: -5px 5px 10px -4px @color, 5px 5px 10px -4px @color;
}

/**让出ios留海屏（注释不需要）*/
.g-safe-bottom() {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
