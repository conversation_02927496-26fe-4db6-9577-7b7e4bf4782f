<template>
  <el-menu
    :collapse="sidebarCollapse"
    :collapse-transition="false"
    :default-active="$route.path"
    :background-color="null"
    class="geek-aside-menu"
    :class="{ 'geek-aside-collapse': sidebarCollapse }"
  >
    <template v-for="(item, index) in menuList">
      <el-menu-item v-if="!item.children.length" :key="item.path" :index="item.path">
        <a :href="'#' + item.path" class="menu-icon" :class="`el-icon-${item.icon}`"></a>
        <a slot="title" :href="'#' + item.path" style="color: unset; display: inline-flex; width: 180px">
          <span>{{ $t(item.title) }}</span>
        </a>
      </el-menu-item>
      <el-submenu
        v-else
        :key="`${index}_${item.path}`"
        :index="item.path"
        popper-append-to-body
        :popper-class="`geek-aside-pop-menu submenu-${item.icon}`"
      >
        <template slot="title">
          <i class="menu-icon" :class="`el-icon-${item.icon}`"></i>
          <span slot="title">{{ $t(item.title) }}</span>
        </template>

        <template v-for="subItem in item.children">
          <el-menu-item v-if="!subItem.children" :key="subItem.path" :index="subItem.path">
            <a :href="'#' + subItem.path" class="menu-a-link">
              <span slot="title">{{ $t(subItem.title) }}</span>
            </a>
          </el-menu-item>
          <el-submenu
            v-else
            :key="`${index}_${subItem.path}`"
            :index="subItem.path"
            popper-append-to-body
            popper-class="geek-aside-pop-menu"
          >
            <template slot="title">{{ $t(subItem.title) }}</template>
            <el-menu-item v-for="ccItem in subItem.children" :key="ccItem.path" :index="ccItem.path">
              <a :href="'#' + ccItem.path" class="menu-a-link">
                <span slot="title">{{ $t(ccItem.title) }}</span>
              </a>
            </el-menu-item>
          </el-submenu>
        </template>
      </el-submenu>
    </template>
  </el-menu>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "GeekAsideMenu",
  props: ["sidebarCollapse"],
  computed: {
    ...mapState(["menuList"]),
  },
};
</script>

<style lang="less">
@aside-bg: @g-aside-bg;
@aside-active-bg: @g-aside-active-bg;
@aside-color: @g-aside-color;
@aside-active-color: @g-aside-active-color;
.geek-aside-pop-menu {
  max-height: 100%;
  overflow-y: auto;
  background: @aside-bg;
  margin-left: 5px;
  ul {
    margin: 0;
    padding: 0;
  }

  .el-submenu .el-submenu__title,
  .el-menu-item {
    color: @aside-color;

    &:focus {
      color: unset;
      background-color: unset;
    }

    &:hover {
      background-color: @aside-active-bg;
    }
  }

  .menu-a-link {
    display: block;
    color: unset;
  }
}
</style>
<style lang="less" scoped>
@aside-max-width: @g-slide-max-width; //240px
@aside-min-width: @g-slide-min-width; //36px

@aside-bg: @g-aside-bg;
@aside-active-bg: @g-aside-active-bg;
@aside-color: @g-aside-color;
@aside-active-color: @g-aside-active-color;
.geek-aside-menu {
  width: @aside-max-width;
  border: 0;
  background: inherit;

  :deep(> .el-submenu) {
    li {
      padding: 0 !important;
      .menu-a-link {
        display: block;
        padding: 0 0 0 45px;
      }
      .el-menu {
        background: rgba(26, 51, 79, 0.5);
      }
      .el-menu .menu-a-link {
        padding-left: 60px !important;
        opacity: 0.8;
      }
      .el-submenu__title {
        padding-left: 45px !important;
      }
    }
  }

  .el-submenu span,
  .el-menu-item span,
  :deep(.el-submenu .el-submenu__title) {
    color: @aside-color;
  }

  .el-menu-item.is-active {
    span {
      color: @aside-active-color;
    }
  }
  :deep(.el-submenu .el-menu) {
    background: @g-aside-submenu-bg;

    .el-menu-item:hover {
      background-color: @g-aside-submenu-active-bg;
    }
  }

  :deep(.el-menu-item:focus),
  :deep(.el-submenu .el-submenu__title:focus),
  :deep(.el-submenu .el-submenu .el-submenu__title:focus),
  :deep(.el-submenu .el-menu .el-menu-item:focus) {
    color: unset;
    background-color: unset;
  }

  :deep(.el-menu-item:hover),
  .el-menu-item:hover,
  :deep(.el-submenu .el-submenu__title:hover),
  .el-submenu .el-submenu__title:hover {
    background-color: @aside-active-bg;
  }

  &.geek-aside-collapse {
    width: @aside-min-width;

    :deep(.el-tooltip) {
      padding: 0 !important;
      a {
        display: block;
        width: 100%;
        height: 100%;
      }
    }

    :deep(.el-menu-item),
    :deep(.el-submenu__title) {
      padding: 0 !important;
      text-align: center;
      .menu-icon {
        margin-right: 0 !important;
      }
      > span {
        display: none;
      }
    }
  }
}

// icon
.el-menu-item .menu-icon,
.el-submenu .el-submenu__title .menu-icon {
  height: 24px;
  width: 24px;
  margin-right: 8px;
  font-size: 0;
  vertical-align: middle;
  background-size: 14px;
  background-repeat: no-repeat;
  background-position: 50% 50%;

  &.el-icon-dashboard {
    background-image: url(~@imgs/layout/aside/dashboard.png);
  }

  &.el-icon-location {
    background-image: url(~@imgs/layout/aside/location.png);
  }

  &.el-icon-info {
    background-image: url(~@imgs/layout/aside/info.png);
  }

  &.el-icon-warehouse {
    background-image: url(~@imgs/layout/aside/warehouse.png);
  }

  &.el-icon-component {
    background-image: url(~@imgs/layout/aside/component.png);
  }

  &.el-icon-example {
    background-image: url(~@imgs/layout/aside/example.png);
  }

  &.el-icon-documentation {
    background-image: url(~@imgs/layout/aside/documentation.png);
  }

  &.el-icon-peoples {
    background-image: url(~@imgs/layout/aside/peoples.png);
  }
}
// active icon
.el-menu-item.is-active .menu-icon {
  &.el-icon-dashboard {
    background-image: url(~@imgs/layout/aside/dashboard-active.png);
  }

  &.el-icon-location {
    background-image: url(~@imgs/layout/aside/location-active.png);
  }

  &.el-icon-info {
    background-image: url(~@imgs/layout/aside/info-active.png);
  }

  &.el-icon-warehouse {
    background-image: url(~@imgs/layout/aside/warehouse-active.png);
  }

  &.el-icon-component {
    background-image: url(~@imgs/layout/aside/component-active.png);
  }

  &.el-icon-example {
    background-image: url(~@imgs/layout/aside/example-active.png);
  }

  &.el-icon-documentation {
    background-image: url(~@imgs/layout/aside/documentation-active.png);
  }

  &.el-icon-peoples {
    background-image: url(~@imgs/layout/aside/peoples-active.png);
  }
}
</style>
