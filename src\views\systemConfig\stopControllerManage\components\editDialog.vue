<template>
  <el-dialog
    :title="$t(title)"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="520px"
  >
    <div class="ui-containerBox">
      <el-form ref="dynamicForm" :model="dynamicForm" label-position="right" label-width="150px">
        <el-row>
          <el-col :span="11">
            <el-form-item
              :label="$t('lang.rms.fed.controllerId')"
              prop="deviceId"
              :rules="[
                {
                  required: true,
                  message: $t('lang.rms.fed.pleaseEnterControllerId'),
                  trigger: 'blur',
                },
                { pattern: /^(\-|\+)?\d+$/, message: $t('libsSz.key94') },
                { min: 0, max: 64, message: $t('libsSz.key36', [64]), trigger: 'blur' },
              ]"
            >
              <el-input v-model="dynamicForm.deviceId" />
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item
              :label="$t('lang.rms.web.monitor.robot.serverRobotIpMapIp')"
              prop="ip"
              :rules="[
                {
                  required: true,
                  message: $t('lang.rms.fed.pleaseEnterIPAddress'),
                  trigger: 'blur',
                },
                {
                  pattern:
                    /((?:(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d))\.){3}(?:25[0-5]|2[0-4]\d|(?:1\d{2}|[1-9]?\d)))/,
                  message: $t('libsSz.key37'),
                },
                { min: 0, max: 64, message: $t('libsSz.key36', [64]), trigger: 'blur' },
              ]"
            >
              <el-input v-model="dynamicForm.ip" />
            </el-form-item>
          </el-col>
          <el-col :span="22">
            <el-form-item
              :label="$t('lang.rms.fed.textLogicArea')"
              prop="referBy"
              :rules="[
                { pattern: /^[-,0-9]+$/, message: $t('libsSz.key95') },
                { min: 0, max: 255, message: $t('libsSz.key36', [255]), trigger: 'blur' },
              ]"
            >
              <el-input v-model="dynamicForm.referBy" :placeholder="$t('lang.rms.fed.logiclsTip')" />
            </el-form-item>
          </el-col>
          <div v-for="(item, index) in dynamicForm.dynamicItem" :key="index">
            <el-col :span="11">
              <!-- :prop="form.dynamicItem[index].channelId" -->
              <el-form-item
                :label="$t('lang.rms.fed.channel')"
                :prop="'dynamicItem.' + index + '.channelId'"
                :rules="[{ pattern: /^\d/, message: $t('libsSz.key94') }]"
              >
                <el-input v-model="item.channelId" />
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item
                :label="$t('lang.rms.web.monitor.robot.workStationId')"
                :prop="'dynamicItem.' + index + '.referBy'"
                :rules="[{ pattern: /^[-,0-9]+$/, message: $t('libsSz.key95') }]"
              >
                <el-input v-model="item.referBy" />
              </el-form-item>
            </el-col>
            <el-col v-if="dynamicForm.dynamicItem.length > 1" :span="1" class="p12">
              <el-button class="el-icon-delete" @click="deleteItem(index)" />
            </el-col>
          </div>
        </el-row>
      </el-form>

      <div class="pct100 tc">
        <el-button class="el-icon-plus w180" plain @click="addItem">{{ $t("lang.rms.fed.add") }}</el-button>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" :loading="saveLoading" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
// TODO 后续优化这个弹出框
import { getEditFormData } from "./data";

export default {
  data() {
    return {
      type: "",
      dialogVisible: false,

      extendFormConfig: {
        isNeedBtn: false,
      },
      dynamicForm: {
        deviceId: "",
        ip: "",
        channelId: "",
        referBy: "",
        dynamicItem: [
          {
            channelId: "",
            referBy: "",
          },
        ],
      },
      saveLoading: false,

      rowData: {},
    };
  },
  computed: {
    title() {
      if (this.type == "add") return "auth.rms.robotSoftwareManager.botton.add";
      else return "lang.rms.fed.edit";
    },
    formData() {
      return getEditFormData(this, this.rowData);
    },
  },
  methods: {
    open(type, row) {
      let { dynamicItem } = this.dynamicForm;
      this.type = type;
      this.rowData = row;
      this.dialogVisible = true;
      if (type === "edit") {
        const channels = row.channels || [];
        this.dynamicForm.ip = row.ip;
        this.dynamicForm.deviceId = String(row.deviceId);
        this.dynamicForm.referBy = row.referBy;
        if (channels.length > 0) {
          dynamicItem = [];
          channels.forEach(i => {
            dynamicItem.push({
              id: String(i.id),
              channelId: String(i.channelId),
              referBy: String(i.referBy),
            });
          });
        }
        this.$set(this.dynamicForm, "dynamicItem", dynamicItem);
      } else {
        this.dynamicForm = {}
      }
    },

    addItem() {
      const { dynamicItem } = this.dynamicForm;
      if (dynamicItem.length < 8) {
        this.dynamicForm.dynamicItem.push({
          channelId: "",
          referBy: "",
        });
      } else {
        this.$message.error(this.$t("lang.rms.fed.addUpToEightChannels"));
      }
    },
    deleteItem(index) {
      this.dynamicForm.dynamicItem.splice(index, 1);
    },
    // 保存前
    beforeSave() {
      return new Promise((resolve, reject) => {
        this.$refs.dynamicForm
          .validate()
          .then((vaild, formData2) => {
            const channelIdArr = this.dynamicForm.dynamicItem.map(el => el.channelId);
            if (this.dynamicForm.dynamicItem.length !== Array.from(new Set(channelIdArr)).length) {
              this.$warning("当前控制器下已存在相同通道，保存失败");
              reject();
              return;
            }
            let arr = [];
            let isSave = true;
            if (this.dynamicForm.dynamicItem.length > 1) {
              this.dynamicForm.dynamicItem.forEach(item => {
                if (!item.channelId) {
                  isSave = false;
                } else {
                  arr.push(item);
                }
              });
            } else {
              this.dynamicForm.dynamicItem.forEach(item => {
                if (!item.channelId) {
                  if (item.referBy) {
                    isSave = false;
                  }
                } else {
                  arr.push(item);
                }
              });
            }
            if (!isSave) {
              this.$message.warning(this.$t("lang.rms.api.result.parameter.channelIdCannotBeNull"));
              this.saveLoading = false;
              return;
            }
            const params = {
              deviceId: this.dynamicForm.deviceId || "",
              ip: this.dynamicForm.ip,
              id: this.rowData.id || "",
              channels: arr,
              referBy: this.dynamicForm.referBy,
            };

            $req.post("/athena/baseDevice/saveOrUpDate", params).then(res => {
              if (res.code === 0) {
                this.$message.success(this.$t(res.msg));
                resolve();
              } else {
                this.$message.error(this.$t(res.msg));
                reject()
              }
            });
          })
          .catch(() => reject());
      });
    },
    save() {
      this.saveLoading = true;
      const success = () => {
        this.saveLoading = false;
        this.$emit("updateList");
        this.dialogVisible = false;
      };
      const fail = () => {
        this.saveLoading = false;
      };
      this.beforeSave ? this.beforeSave().then(success).catch(fail) : success();
    },
    // 关闭
    close() {
      this.dialogVisible = false;
    },
  },
};
</script>
<style lang="scss">
.ui-containerBox .el-input--prefix .el-input__inner {
  padding-left: 15px;
}
.p12 {
  padding: 0 12px;
}
</style>
