/* ! <AUTHOR> at 2022/08/26 */
import * as PIXI from "pixi.js";
import LayerCell from "./layer-cell/cell-base"; // 底层单元格
import LayerRoad from "./layer-road/road-base"; // 底层道路
import LayerRobot from "./layer-robot/robot-base"; // 机器人
import LayerShelf from "./layer-shelf/shelf-base"; // 货架层
import LayerRack from "./layer-rack/rack-base"; // 货架层
import LayerStation from "./layer-station/station-base"; // 工作站
import LayerCharger from "./layer-charger/charger-base"; // 充电站
import LayerDevice from "./layer-device/device-base"; // 设备
import LayerAreas from "./layer-area/area-base"; // 区域
import LayerImage from "./layer-image/image-background"; // 图片层

class MapFloor {
  floorId: floorId;
  layerCell: LayerCell;
  layerRoad: LayerRoad;
  layerRobot: LayerRobot;
  layerShelf: LayerShelf;
  layerRack: LayerRack;
  layerStation: LayerStation;
  layerCharger: LayerCharger;
  layerDevice: LayerDevice;
  layerAreas: LayerAreas;
  layerImage: LayerImage;
  private mapCore: MRender.MainCore;
  private layerFloor: PIXI.Container; // 楼层layer
  private layerSelect: PIXI.Container; // 楼层 select layer
  constructor(mapCore: MRender.MainCore, floorData: floorData) {
    this.mapCore = mapCore;
    this.floorId = floorData.floorId;
    this.createLayerFloor();

    this.layerRoad = new LayerRoad(mapCore, this);
    this.layerCell = new LayerCell(mapCore, this);
    this.layerRobot = new LayerRobot(mapCore, this);
    this.layerShelf = new LayerShelf(mapCore, this);
    this.layerRack = new LayerRack(mapCore, this);
    this.layerStation = new LayerStation(mapCore, this);
    this.layerCharger = new LayerCharger(mapCore, this);
    this.layerDevice = new LayerDevice(mapCore, this);
    this.layerAreas = new LayerAreas(mapCore, this);
    this.layerImage = new LayerImage(mapCore, this);

    this.renderFloor(floorData);
  }

  renderFloor(floorData: floorData): void {
    const { cells, segments, background } = floorData;

    this.renderCells(cells, false);
    this.renderRoad(segments);
    this.renderBackground(background);
  }
  /** 渲染 单元格相关 */
  renderCells(cells: Array<cellData>, isUpdate = true): void {
    const layerCell = this.layerCell;

    if (isUpdate) {
      layerCell.layerCellStatus.update(cells);
    } else {
      layerCell.render(cells);
    }
  }
  /** 路线 SLine & Bezier */
  renderRoad(segments: Array<any>) {
    if (!segments.length) return;
    const layerRoad = this.layerRoad;
    layerRoad.render(segments);
  }
  /** 渲染 机器人 */
  renderRobots(robots: Array<robotData>, isUpdate = true) {
    if (!robots?.length) return;
    if (isUpdate) this.layerRobot.update(robots);
    else this.layerRobot.render(robots);
  }
  /** 渲染 货架 */
  renderShelves(shelves: Array<shelfData>, isUpdate = true) {
    const layerShelf = this.layerShelf;
    if (isUpdate) layerShelf.repaint();

    if (!shelves || !shelves.length) return;
    layerShelf.render(shelves);
  }
  /** 渲染固定货架 */
  renderRacks(racks: Array<rackData>, isUpdate = true) {
    const layerRack = this.layerRack;
    if (!racks || !racks.length) return;

    if (isUpdate) layerRack.repaint();
    layerRack.render(racks);
  }
  /** 渲染工作站 */
  renderStations(stations: Array<stationData>, isUpdate = true) {
    if (!stations?.length) return;
    if (isUpdate) this.layerStation.update(stations);
    else this.layerStation.render(stations);
  }
  /** 渲染充电站 */
  renderChargers(chargers: Array<chargerData>, isUpdate = true) {
    if (!chargers?.length) return;
    if (isUpdate) this.layerCharger.update(chargers);
    else this.layerCharger.render(chargers);
  }
  /** 渲染设备 */
  renderDevices(devices: Array<deviceData>, isUpdate = true) {
    if (!devices?.length) return;
    if (isUpdate) this.layerDevice.update(devices);
    else this.layerDevice.render(devices);
  }

  renderDmpDevices(dmpDevices: Array<any>, isUpdate = true) {
    if (isUpdate) this.layerDevice.layerDmpDevice.update(dmpDevices);
    else this.layerDevice.layerDmpDevice.render(dmpDevices);
  }

  /** 渲染 背景图 */
  renderBackground(background: any): void {
    if (!background?.hasBg) return;
    this.layerImage.render(background);
  }

  /** 显隐 地图层 */
  toggleLayer(layerName: MRender.toggleLayerName) {
    const mapConfig: MRender.RenderConfigMain = this.mapCore.mapConfig;

    let isShow: boolean;
    switch (layerName) {
      case "location":
        isShow = mapConfig.getLayerVisible("cellLocation");
        this.layerCell.layerCellLocation.toggle(isShow);
        break;
      case "load":
        isShow = mapConfig.getLayerVisible("mapLoad");
        this.layerCell.layerCellLoad.toggle(isShow);
        this.layerRoad.layerRoadLoad.toggle(isShow);
        break;
      case "unload":
        isShow = mapConfig.getLayerVisible("mapUnload");
        this.layerCell.layerCellUnload.toggle(isShow);
        this.layerRoad.layerRoadUnload.toggle(isShow);
        break;
      case "cellHeat":
        isShow = mapConfig.getLayerVisible("cellHeat");
        this.layerCell.layerCellFeature.renderHeat(isShow);
        break;
      case "realtimeObstacle":
        isShow = mapConfig.getLayerVisible("realtimeObstacle");
        this.layerAreas.toggleRealtimeObstacle(isShow);
        break;
    }
  }

  /** 可否点击 地图层设置 */
  triggerLayers(layerNames: MRender.layerName[]) {
    this.mapCore.mapConfig.data.setDestCell(null);
    this.mapCore.mapEvent.removeDestCircle();
    this.layerRobot.triggerLayer(false); // 机器人
    this.layerShelf.triggerLayer(false); // 货架 shelf
    this.layerCell.triggerLayer(false); // 单元格
    this.layerRack.triggerLayer(false); // 货箱 rack
    this.layerCharger.triggerLayer(false); // 充电站
    this.layerDevice.triggerLayer(false); // 设备
    for (let i = 0, len = layerNames.length; i < len; i++) {
      switch (layerNames[i]) {
        case "robot":
          this.layerRobot.triggerLayer(true);
          break;
        case "shelf":
          this.layerShelf.triggerLayer(true); // 货架
          break;
        case "cell":
          this.layerCell.triggerLayer(true); // 单元格
          break;
        case "rack":
          this.layerRack.triggerLayer(true); // 货箱
          break;
        case "charger":
          this.layerCharger.triggerLayer(true); // 充电站
          break;
        case "device":
          this.layerDevice.triggerLayer(true); //  设备
          break;
      }
    }
  }

  getLayerFloor(type = "floor") {
    switch (type) {
      case "floor":
        return this.layerFloor;
      case "select":
        return this.layerSelect;
    }
  }

  destroy() {
    this.layerCell.destroy();
    this.layerRoad.destroy();
    this.layerRobot.destroy();
    this.layerShelf.destroy();
    this.layerRack.destroy();
    this.layerStation.destroy();
    this.layerCharger.destroy();
    this.layerDevice.destroy();
    this.layerAreas.destroy();
    this.layerImage.destroy();

    this.layerCell = null;
    this.layerRoad = null;
    this.layerRobot = null;
    this.layerShelf = null;
    this.layerRack = null;
    this.layerStation = null;
    this.layerCharger = null;
    this.layerDevice = null;
    this.layerAreas = null;
    this.layerImage = null;

    if (this.layerFloor) {
      this.layerFloor.destroy({ children: true });
      this.layerFloor = null;
      this.layerSelect = null;
    }
    this.floorId = null;
    this.mapCore = null;
  }

  /** floor layer 楼层container */
  private createLayerFloor() {
    const floorId = this.floorId;
    const viewport = this.mapCore.mapView.getViewport();

    let config: any = {};
    let RMSConfig = localStorage.getItem("Geek_RMSConfig");
    if (RMSConfig) config = JSON.parse(RMSConfig);
    const angle = config?.mapRotateAngle || 0;

    let floor: any = new PIXI.Container();
    floor.name = `floor_${floorId}`;
    floor.floorId = floorId;
    floor.sortableChildren = true; // 使zIndex属性有用
    floor.rotation = angle / (180 / Math.PI);
    this.layerFloor = floor;

    let layerSelect: any = new PIXI.Container();
    layerSelect.name = `select_${floorId}`;
    layerSelect.floorId = floorId;
    layerSelect.zIndex = 33;
    layerSelect.alpha = 0.9;
    layerSelect.sortableChildren = true; // 使zIndex属性有用

    floor.addChild(layerSelect);

    viewport.addChild(floor);
    this.layerSelect = layerSelect;
  }
}
export default MapFloor;
