<template>
  <el-dialog
    :title="$t(title)"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="520px"
  >
    <geek-customize-form ref="refForm" :form-config="formConfig" />

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditRobotModalDialog",
  data() {
    return {
      dialogVisible: false,
      operation: "",
      rowData: {},
      chassis: [],
      mechanism: [],
      business: [],
      protocol: [],
    };
  },
  computed: {
    title() {
      switch (this.operation) {
        case "add":
          return "lang.rms.fed.createRobotModel";
        default:
          return "lang.rms.api.result.warehouse.robotOntologyModel";
      }
    },

    formConfig() {
      let disabled = false;
      if (this.operation == "view") disabled = true;
      return {
        attrs: {
          labelWidth: "130px",
          labelPosition: "right",
          disabled,
        },
        configs: {
          product: {
            label: "lang.rms.api.result.warehouse.robotModel",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRobotModel",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseEnterRobotModeAlias"),
                trigger: "blur",
              },
            ],
          },
          displayName: {
            label: "lang.rms.api.result.warehouse.robotModelAlias",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRobotModeAlias",
          },
          chassisModelId: {
            label: "lang.rms.api.result.warehouse.ontologyModel",
            tag: "select",
            default: "",
            required: true,
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.chassis,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.chooseRobotModel"),
                trigger: "blur",
              },
            ],
          },
          mechanismModelId: {
            label: "lang.rms.api.result.warehouse.mechanismModel",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.mechanism,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.chooseRobotModel"),
                trigger: "blur",
              },
            ],
          },
          businessModelId: {
            label: "lang.rms.api.result.warehouse.businessModel",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.business,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.chooseRobotModel"),
                trigger: "blur",
              },
            ],
          },
          protocolModelId: {
            label: "lang.rms.api.result.warehouse.protocolModel",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChooseWithRobot",
            options: this.protocol,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.chooseRobotModel"),
                trigger: "blur",
              },
            ],
          },
        },
      };
    },
  },
  methods: {
    open(type, data) {
      this.getActualList();
      this.getMechanismList();
      this.getBusinessList();
      this.getProtocolList();
      this.operation = type;
      this.rowData = data || {};
      this.dialogVisible = true;

      const params = {
        product: data?.product || "",
        displayName: data?.displayName || "",
        chassisModelId: data?.chassisModelEnt?.chassisModelId || "",
        mechanismModelId: data?.mechanismModelEnt?.mechanismId || "",
        businessModelId: data?.businessModelEnt?.businessModelId || "",
        protocolModelId: data?.protocolModelEnt?.protocolModelId || "",
      };
      this.$nextTick(() => {
        this.$refs.refForm.reset();
        this.$refs.refForm.setData(params);
      });
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      if (this.operation == "view") {
        this.close();
        return;
      }

      this.$refs.refForm.validate().then(data => {
        let formData = Object.assign({}, data);
        if (this.operation == "edit") formData.id = this.rowData.id;

        $req.post("/athena/robot/manage/modelSave", formData).then(res => {
          this.$success();
          this.close();
          this.$emit("updateList");
        });
      });
    },

    getActualList() {
      $req.get("/athena/robot/manage/chassisPageList", { currentPage: 1, pageSize: 1000 }).then(res => {
        if (res?.code !== 0) return;
        const list = res?.data?.recordList || [];

        this.chassis = list.map(item => {
          return {
            label: `${item?.chassisCode || ""}(${item.name})`,
            value: item.chassisModelId,
          };
        });
      });
    },
    getMechanismList() {
      $req
        .post("/athena/robot/manage/mechanismPageList?currentPage=1&pageSize=1000", {
          name: "",
          mechanismCode: "",
          controlAbilities: [],
          actionAbilities: [],
        })
        .then(res => {
          if (res?.code !== 0) return;
          const list = res?.data?.recordList || [];

          this.mechanism = list.map(item => {
            return {
              label: `${item?.mechanismCode || ""}(${item.name})`,
              value: item.mechanismId,
            };
          });
        });
    },
    getBusinessList() {
      $req
        .post("/athena/robot/manage/businessPageList?currentPage=1&pageSize=1000", {
          businessCode: "",
          sizeTypes: [],
        })
        .then(res => {
          if (res?.code !== 0) return;
          const list = res?.data?.recordList || [];

          this.business = list.map(item => {
            return {
              label: `${item?.businessCode || ""}(${item.name})`,
              value: item.businessModelId,
            };
          });
        });
    },
    getProtocolList() {
      $req.get("/athena/robot/manage/findRobotProtocols").then(res => {
        if (res?.code !== 0) return;
        const list = res?.data || [];
        this.protocol = list.map(item => {
          return {
            label: `${item?.protocolCode || ""}(${item.name})`,
            value: item.id,
          };
        });
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
