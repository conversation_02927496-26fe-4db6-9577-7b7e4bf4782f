/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";
import type { Sprite } from "pixi.js";

class LayerStation implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  private fillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  private meshList: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "station";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("station");
    this.container = container;

    this.fillStyle.color = utils.getOriginColor("STATIONS_AREA");
    this.fillStyle.visible = true;
    this.fillStyle.alpha = 0.6;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  render(arr: Array<stationData>): void {
    const _this = this;
    const container = _this.container;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils,
      texture = utils.getResources("station");

    let item, options;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatStation(item);
      let station = _this.drawSprite(options, texture);
      container.addChild(station);
      this.checkRenderStationStatus(options);
      mapData.station.setData(options.code, { element: station, options });
    }

    this.paintStationArea();
  }

  update(arr: Array<stationData>) {
    const _this = this;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      utils = mapCore.utils;

    let item, code, options, station;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatStation(item);
      code = options["code"];
      station = mapData.station.getData(code);
      if (station) {
        mapData.station.setData(code, Object.assign(station, { options }));
        this.checkRenderStationStatus(options);
      }
    }

    this.paintStationArea();
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    // station area
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    console.log("station repaint, mapData会处理");
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.fillStyle = null;
    this.lineStyle = null;
    this.meshList = null;
  }

  private paintStationArea() {
    const _this = this;
    const fillStyle = _this.fillStyle;
    const lineStyle = _this.lineStyle;
    const mapData = _this.mapCore.mapData;

    this.repaint();
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    let sum = 0;

    const stations = mapData.station.getAll();
    for (let key in stations) {
      const options = stations[key].options;
      if (options["stopButtonPressed"]) {
        ++sum;
        let polygon = _this.drawStationArea(options, graphicsGeometry);
        graphicsGeometry.drawShape(polygon, fillStyle, lineStyle);
      }
    }
    graphicsGeometry.BATCHABLE_SIZE = sum;
    const graphics = new PIXI.Graphics(graphicsGeometry);
    _this.container.addChild(graphics);
    _this.meshList.push(graphics);
  }

  private drawSprite(options: mStationData, texture: any) {
    const { code, width, height, position, direction, virtualRacks } = options;

    let sprite: any = new PIXI.Sprite(texture);
    sprite.mapType = "station";
    sprite.name = code;
    sprite.width = width;
    sprite.height = height;
    sprite.interactive = sprite.buttonMode = virtualRacks;
    if (virtualRacks) sprite.tint = 0xe6a23c;
    sprite.anchor.set(0.5, 0.5);
    sprite.position.set(position.x, position.y); // 使图片居中
    sprite.rotation = direction; // 设置方向
    return sprite;
  }

  private checkRenderStationStatus(options: mStationData) {
    const { code, isWorking, width, height, position } = options;
    const parkSpriteMesh = this.container.getChildByName(`${code}-park`) as Sprite;
    if (parkSpriteMesh) return (parkSpriteMesh.visible = !isWorking);
    const texture = this.mapCore.utils.getResources("park");
    const parkSprite: Sprite = new PIXI.Sprite(texture);
    parkSprite.name = `${code}-park`;
    parkSprite.anchor.set(0.5, 0.5);
    parkSprite.width = width * 1.2;
    parkSprite.height = height * 1.2;
    parkSprite.position.set(position.x, position.y); // 使图片居中
    parkSprite.visible = !isWorking;
    this.container.addChild(parkSprite);
  }

  private drawStationArea(options: mStationData, graphicsGeometry: any) {
    const points = options["polygon"];
    if (!points.length) return null;
    return new PIXI.Polygon(points);
  }
}
export default LayerStation;
