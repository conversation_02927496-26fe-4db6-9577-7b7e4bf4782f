//维护点与面的关系
import Tree from './Tree'
//维护node与线的关系
import Node2Line from './Node2Line'
//维护cellCode与设备的关系
import CellCode2Device from './CellCode2Device'
export default class Store {
  //四叉树实例
  static tree = new Tree()
  static node2Line = new Node2Line()
  static cellCode2Device = new CellCode2Device()
  static destroy() {
    this.tree.clear()
    this.node2Line.clear()
    this.cellCode2Device.clear()
  }
  // static getTreeNodes({x,y,width,height}) {
  //   return this.tree.getTreeNodes({x,y,width,height})
  // }
}

