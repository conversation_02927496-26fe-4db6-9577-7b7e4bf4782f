/* ! <AUTHOR> at 2022/08/27 */

class SelectData {
  /** 选中 渲染的元素 */
  private selects: { [propName: code]: Array<any> } = {};
  private placements: { [propName: code]: Array<any> } = {};

  setSelect(code: code, element: any) {
    this.selects[code] = element;
  }

  setPlacement(code: code, element: any) {
    this.placements[code] = element;
  }

  getSelect(code: code) {
    return this.selects[code];
  }

  /** type: all | cell */
  removeSelects(layerName: MRender.layerName | "all" = "all", codes?: Array<any>) {
    const selects: any = this.selects;
    const placements: any = this.placements;

    if (layerName === "all") {
      let element;
      for (let key in selects) {
        element = selects[key];
        if (!element) continue;
        element.parent && element.parent.removeChild(element);
        element.destroy();
      }

      for (let key in placements) {
        element = placements[key];
        if (!element) continue;
        element.parent && element.parent.removeChild(element);
        element.destroy();
      }
      this.selects = {};
      this.placements = {};
      return;
    }

    if (codes && codes.length) {
      const prefix = `${layerName}__`;

      let element, placement;
      codes.forEach(code => {
        const key = prefix + code;
        element = selects[key];
        if (!element) return;
        element.parent && element.parent.removeChild(element);
        element.destroy();
        delete this.selects[key];

        if (layerName === "shelf") {
          placement = placements[key];
          if (!placement) return;
          placement.parent && placement.parent.removeChild(placement);
          placement.destroy();
          delete this.placements[key];
        }
      });
      return;
    }

    const prefix = `${layerName}__`;
    let element;
    for (let key in selects) {
      if (key.indexOf(prefix) === -1) continue;
      element = selects[key];
      if (!element) continue;
      element.parent && element.parent.removeChild(element);
      element.destroy();
      delete this.selects[key];
    }

    if (layerName === "shelf") {
      for (let key in placements) {
        if (key.indexOf(prefix) === -1) continue;
        element = placements[key];
        if (!element) continue;
        element.parent && element.parent.removeChild(element);
        element.destroy();
        delete this.placements[key];
      }
    }
  }

  getSelectsByType(type: "all" | "cell" | "robot") {
    const prefix = `${type}__`;

    const selects: any = this.selects;
    const codes = Object.keys(selects);
    const prefixLen = prefix.length;
    return codes.map(code => {
      if (type === "all") return code.split("__")[1];
      else if (code.indexOf(prefix) !== -1) return code.substring(prefixLen);
    });
  }

  uninstall() {
    this.selects = {};
    this.placements = {};
  }

  destroy() {
    this.uninstall();
    this.selects = null;
    this.placements = null;
  }
}

export default SelectData;
