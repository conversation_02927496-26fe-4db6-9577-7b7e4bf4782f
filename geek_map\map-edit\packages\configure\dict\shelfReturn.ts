export const STATION_DIRECTIONBYWARE_DICT_EAST = 'EAST';
export const STATION_DIRECTIONBYWARE_DICT_SOUTH = 'SOUTH';
export const STATION_DIRECTIONBYWARE_DICT_WEST = 'WEST';
export const STATION_DIRECTIONBYWARE_DICT_NORTH = 'NORTH';
export const STATION_DIRECTIONBYWARE_DICT = [
  { label: "EAST", value: STATION_DIRECTIONBYWARE_DICT_EAST }, // 工位在仓库东侧
  { label: "SOUTH", value: STATION_DIRECTIONBYWARE_DICT_SOUTH }, //工位在仓库南侧
  { label: "WEST", value: STATION_DIRECTIONBYWARE_DICT_WEST }, // 工位在仓库西侧
  { label: "NORTH", value: STATION_DIRECTIONBYWARE_DICT_NORTH }, // 工位在仓库北侧
];

export const STATION_DIRECTION_DICT_EAST = "east";
export const STATION_DIRECTION_DICT_SOUTH = "south";
export const STATION_DIRECTION_DICT_WEST = "west";
export const STATION_DIRECTION_DICT_NORTH = "north";
export const STATION_DIRECTION_DICT = [
  { label: "lang.rms.fed.east", value: STATION_DIRECTION_DICT_EAST }, // 东
  { label: "lang.rms.fed.south", value: STATION_DIRECTION_DICT_SOUTH }, // 南
  { label: "lang.rms.fed.west", value: STATION_DIRECTION_DICT_WEST }, // 西
  { label: "lang.rms.fed.north", value: STATION_DIRECTION_DICT_NORTH }, // 北
];

export const SHELF_SURFACE_DICT_R = "R";
export const SHELF_SURFACE_DICT_B = "B";
export const SHELF_SURFACE_DICT_L = "L";
export const SHELF_SURFACE_DICT_F = "F";
export const SHELF_SURFACE_DICT = [
  { label: "R", value: SHELF_SURFACE_DICT_R },
  { label: "B", value: SHELF_SURFACE_DICT_B },
  { label: "L", value: SHELF_SURFACE_DICT_L },
  { label: "F", value: SHELF_SURFACE_DICT_F },
];

/**
 * 与 STATION_DIRECTION_DICT_xxx 使用的地方不一样, key也不同
 */
export const FUNC_DIRECTION_DICT_EAST = 0;
export const FUNC_DIRECTION_DICT_SOUTH = 1;
export const FUNC_DIRECTION_DICT_WEST = 2;
export const FUNC_DIRECTION_DICT_NORTH = 3;
export const FUNC_DIRECTION_DICT = [
  { label: "lang.rms.fed.east", value: FUNC_DIRECTION_DICT_EAST }, // 东
  { label: "lang.rms.fed.south", value: FUNC_DIRECTION_DICT_SOUTH }, // 南
  { label: "lang.rms.fed.west", value: FUNC_DIRECTION_DICT_WEST }, // 西
  { label: "lang.rms.fed.north", value: FUNC_DIRECTION_DICT_NORTH }, // 北
];
