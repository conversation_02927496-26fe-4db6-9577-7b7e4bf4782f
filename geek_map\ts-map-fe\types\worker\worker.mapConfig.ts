/* ! <AUTHOR> at 2023/04/20 */

namespace MWorker {
  /**
   * 地图数据的一些全局config数据
   * @param systemState 系统急停状态
   * @param stopAreaCount 急停区域数量
   * @param runningAreaCount 任务区域数量
   * @param taskRunning
   * @param systemRunning
   * @param gatherRunning
   * @param sleepRobotsRunning
   * @param awakenRobotsRunning
   * @param shutdownRunning
   * @param shelfTurningRunning
   * @param shelfReturnRunning
   * @param scanRunning
   */
  export type mapConfig = {
    systemState?: string;
    stopAreaCount?: number;
    runningAreaCount?: number;
    taskRunning?: boolean;
    systemRunning?: boolean;
    gatherRunning?: boolean;
    sleepRobotsRunning?: boolean;
    awakenRobotsRunning?: boolean;
    shutdownRunning?: boolean;
    shelfTurningRunning?: boolean;
    shelfReturnRunning?: boolean;
    scanRunning?: boolean;
    [propName: string]: any;
  };
}
