/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import OrderGrid from "../common/order-grid";

type PropsOrderData = {
  cell: cellData;
};
function CellDetail(props: PropsOrderData) {
  const { t } = useTranslation();
  const [data, setData] = useState(null);

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.cell) {
      setData(null);
      return;
    }
    setData(props.cell);
  }, [props.cell]);

  return (
    data && (
      <OrderGrid
        items={[
          {
            label: t("lang.rms.fed.textNodeCode"),
            value: data?.cellCode || "--",
          },
          {
            label: t("lang.rms.fed.hostCode"),
            value: data?.hostCellCode || "--",
          },
          {
            label: t("lang.rms.fed.qrCodeValue"),
            value: data?.qrCode || "--",
          },
          {
            label: t("lang.rms.fed.textIndexCoordinates"),
            value: data?.index || "--",
          },
          {
            label: t("lang.rms.fed.textAbsoluteCoordinate"),
            value: data?.location || "--",
          },
          {
            label: t("lang.rms.fed.textNodeType"),
            value: data?.cellType || "--",
          },
          {
            label: t("lang.rms.fed.textNodeStatus"),
            value: data?.cellStatus || "--",
          },
          {
            label: t("lang.rms.fed.textLoadDirectionMatrix"),
            value: data?.loadDirs || "--",
          },
          {
            label: t("lang.rms.fed.textUnloadDirectionMatrix"),
            value: data?.unloadDirs || "--",
          },
          {
            label: t("lang.rms.fed.textLoadDirectionNode"),
            value: data?.unloadAdjacentCells || "--",
          },
          {
            label: t("lang.rms.fed.textAllocatedRobotID"),
            value: data?.allocatedRobotId !== -1 ? data.allocatedRobotId : "--",
          },
          {
            label: t("lang.rms.fed.textOccupiedRobotID"),
            value: data?.occupyRobotId !== -1 ? data.occupyRobotId : "--",
          },
          {
            label: t("lang.rms.web.monitor.cell.fieldPrefix.occupiedShelfCode"),
            value: data?.occupiedShelfCode || "--",
          },
          {
            label: t("lang.rms.fed.textLogicArea"),
            value: data?.logicId || "--",
          },
          {
            label: t("lang.rms.fed.textLength"),
            value: data?.length || "--",
          },
          {
            label: t("lang.rms.fed.textWidth"),
            value: data?.width || "--",
          },
          {
            label: t("lang.rms.web.monitor.robot.workStationId"),
            value: data?.stationId !== -1 ? data.stationId : "--",
          },
          {
            label: t("lang.rms.fed.chargerId"),
            value: data?.chargerId !== -1 ? data.chargerId : "--",
          },
          {
            label: t("lang.rms.fed.textLockStatus"),
            value: data?.cellFlag || "--",
          },
          {
            label: t("lang.rms.fed.textOriginNode"),
            value: data?.startBounds || "--",
          },
          {
            label: t("lang.rms.fed.laneId"),
            value: data?.laneId || "--",
          },
          {
            label: t("lang.rms.fed.rsLogicId"),
            value: data?.rsLogicId || "--",
          },
          {
            label: t("lang.rms.fed.cellSizeType"),
            value: data?.sizeType || "--",
          },
        ]}
      />
    )
  );
}

export default CellDetail;
