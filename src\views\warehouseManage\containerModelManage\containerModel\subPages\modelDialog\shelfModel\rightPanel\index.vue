<template>
  <div class="rightModel">
    <!-- 编辑任务 -->
    <TaskEdit v-show="editShelfModelVisible" ref="edit" :view-disabled="viewDisabled" />
  </div>
</template>

<script>
import TaskEdit from "./edit";

export default {
  components: {
    TaskEdit,
  },
  props: {
    viewDisabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      editShelfModelVisible: true,
    };
  },
};
</script>

<style scoped lang="less">
.rightModel {
  position: relative;
  width: 300px;
  height: 100%;
  border-left: 1px solid #eee;
  padding: 0 10px;
  overflow-y: auto;
}
</style>
