//
import { Emitter } from "mitt";
import { MapControls, OrbitControls } from "three/examples/jsm/controls/OrbitControls";
export declare type Color = number;
// -----------
// ------------  var --------------------
export declare enum InsetShape {
  STATION_6 = "station6",
  STATION_2 = "station2",
}
export declare enum ControlMode {
  MAP = "Map",
  ORBIT = "Orbit",
}
export declare enum CommandActions {
  UPDATE = "update",
  ADD = "add",
  DEL = "del",
}

// declare type PluginExtends<T> = T extends PluginBase ? T : PluginBase;

export declare type mouseEvent = (event: Event) => void;

export declare type Events = {
  ["after:initMap"]: any;
  ["after:add"]?: any[] | ModelItemData[];
  ["after:move"]?: any;
  ["illegal:move"]?: any;
};
// ------------ interface ----------------------
export declare interface coordinate {
  x: number;
  y: number;
}
export declare interface Floor {
  floor: any; // 基础信息
  cells: any[]; // 单元格渲染
}
export declare interface MapDataInterface {
  [floorId: string]: Floor;
}
export declare interface ModelItemData {
  uuid: string; // 唯一ID
  category: string; // 类别
  useModelName: string; // 指定使用得模型name;
}
export declare interface MapModelData {
  [category: string]: ModelItemData[];
}
export declare interface MouseHook {
  addHandle: mouseEvent; // 初始化
  downHandle: mouseEvent; // 按下
  moveHandle: mouseEvent; // 移动
  upHandle: mouseEvent; // 抬起
  clickHandle: mouseEvent; // 点击操作
  keydownHandle: mouseEvent; // 键盘按下
  keyupHandle: mouseEvent; // 键盘抬起；
  offHandle: mouseEvent; // 结束；
  dragoverHandle: mouseEvent; // 拖拽hover;
  dropHandle: mouseEvent; // 拖拽结束;
}

// 只支持部分属性调节，如果需要定制，自己写内置模型，这块暂未开放；
export declare interface Map3dModelMapItem {
  path: string;
  scale?: number; //模型放大尺寸
  insetShape: keyof InsetShape; // 内置组合模型
  isAdsorb: boolean;
  setAttributes: (model: THREE.Object3D) => THREE.Object3D;
}

// 主题配色方案
export declare interface MapTheme {
  SCENE_COLOR?: Color;
  BG_Color?: Color; // 地图背景色
  DEFAULT_CELL?: Color; // 默认cellColor
  CHARGER_CELL?: Color; // 充电站点
  STATION_CELL?: Color; // 工作站
  OMNI_DIR_CELL?: Color; // 道路节点
  DROP_CELL?: Color; // 投递点
  QUEUE_CELL?: Color; // 排队点
  SHELF_CELL?: Color; // 货架点
  BOX_RACK_CELL?: Color; // 固定货架
  TURN_CELL?: Color; // 转面点
  CHARGER_PI_CELL?: Color; // 充电桩点
  BLOCKED_CELL?: Color; // 障碍点
  ELEVATOR_CELL?: Color; // 电梯点
  PALLET_RACK_CELL?: Color; // 托盘位点
  S_LINE?: Color; // 路线直线
  BEZIER?: Color; // 路线曲线
  AREA?: Color; // 区域
  ROBOT_PATH?: Color; // 机器人路径
  SHELF?: Color; // 货架
  SELECTED?: Color; // Cell、货架、机器人选中颜色
  LOCKED?: Color; // Cell锁定颜色
  STOPPED?: Color; // Cell停止(暂停)颜色
  CHARGER_NORMAL?: Color; // 充电站正常颜色
  CHARGER_WORK?: Color; // 充电站工作状态颜色
  CHARGER_OFFLINE?: Color; // 充电站离线颜色
  CHARGER_ERROR?: Color; // 充电站错误颜色
  DEVICES_NORMAL?: Color; // 设备-正常
  DEVICES_ABNORMAL?: Color; // 设备-异常
  DEVICES_UNUSUAL?: Color; // 设备-配置异常
  LOAD_CELL_DIR?: Color; // cell负载箭头颜色
  UNLOAD_CELL_DIR?: Color; // cell空载箭头颜色
  LOAD_LINE_DIR?: Color; // 线段负载箭头颜色
  UNLOAD_LINE_DIR?: Color; // 线段空载箭头颜色
}

// useModelName 来源这边自定义；
export declare interface Map3DOptonsConfig {
  modelMap?: { [modelName: string]: Map3dModelMapItem };
  controlMode: ControlMode;
  constants: { theme?: MapTheme };
}
export declare interface Map3DOptions {
  dom: HTMLElement;
  baseUrl: string; // 静态资源目录baseUrl;
  config: Map3DOptonsConfig;
}

export declare interface GeoModelOptions {
  object3d: THREE.Object3D;
  data: any;
  category: string;
  isAdsorb: boolean;
  uuid: string;
}

export declare interface commandItem {
  action: CommandActions;
  config: any;
}

export declare interface initModelConfig {
  category: string;
  useModelName: string | ((item: ModelItemData) => string); // 使用那个模型
}

// ------------- Plugin ----------------------------
export declare class PointerLock extends PluginBase {
  constructor();
  public effectPlugins: string[];
  public autoFollow: Boolean; // todo: 跟随视角
}
// ------------- 基础class --------------------------
// 3D地图基础类
export declare class Map3D extends Plugin {
  constructor(options: Map3DOptions);
  private config: Map3DOptonsConfig;
  private $dom: HTMLElement;
  private baseUrl: string;
  private scene: THREE.Scene;
  private camera: THREE.Camera;
  private renderer: THREE.WebGLRenderer;
  public OrbitControls: OrbitControls | MapControls;
  public modelInstances: { [modelUuid: string]: GeoModel };
  public command: Command;
  public ticker: Ticker;
  initMap(data: MapDataInterface): void; // 加载二维地图
  initModel(modelArr: ModelItemData[], config: initModelConfig): void;
  addModelAction(modelCategory: string, formatter: ModelItemData): void; // 注册添加动作；//modelItemData偷了个懒，正确支持不同得模型有不同得属性，用泛型比较好；
  updateModel(value: ModelItemData): void;
  getModelData(category: string): ModelItemData[];
  deleteModel(value: ModelItemData): void;
  deleteBatchModels(value: ModelItemData[]): void;
  /** 删除实例，不用的时候强制需要调用 */
  desotry(): void; //删除实例
  registerModel(config: { [modelName: string]: Map3dModelMapItem }): void;
  resetCamera(): void;
}

// Plugin基础类
export declare class Plugin {
  constructor(options: { dom: HTMLElement });
  public Emitter: Emitter<Events>;
  getPlugin(pluginName: string): any;
  enablePlugin(pluginName: string | string[]): void;
  disabledPlugin(pluginName: string | string[]): void;
  registerPlugin(pluginObj: any[]): void;
  removePlugins(pluginName: string | string[]): void;
}

// Store基础类；
export declare class MapData {
  constructor();
  private _mapData: MapDataInterface;
  private _mapModelData: MapModelData;
  public mapData: MapDataInterface;
  addModelData(category: string, value: ModelItemData): void;
  getModelData(category: string): ModelItemData | string;
  delModelData(category: string, value: ModelItemData): void;
  updateModelData(category: string, value: ModelItemData): void;
  findCellByCellCode(cellCode: string): any;
  findModelByCellCode(category: string, cellCode: string): ModelItemData;
  findModelByUuid(category: string, uuid: string): ModelItemData;
  findSelectCell(point: coordinate): ModelItemData;
}

// 注册插件base类；
export declare class PluginBase {
  constructor();
  public Map3d: Map3D;
  public Store: MapData;
  public EventInstance: EventBase;
  public $dom: HTMLElement;
  public Emitter: Emitter<Events>;
  activated(): void; // enablePlugin激活可以拿到Map3d对象；
  deactivated(): void; // disabledPlugin激活；
  destroyed(): void; // removePlugins激活；
  static getInstance(): PluginBase;
}

// 手表操作基础 EventBase
export declare class EventBase {
  constructor();
  private $dom: HTMLElement;
  private componentList: any;
  private exclusiveName: string;
  public isPause: boolean;
  protected init(dom: HTMLElement): void;
  add(name: string, componentHook: MouseHook): void;
  off(name?: string): void; // 传name关闭单个/不传name全部关闭
  exclusive(name: string): void; //排除其他事件回调，只执行名为exclusiveName的回调，传空为全部执行;
  pause(): void; // 暂停事件；
}

// 模型-基础类
export declare class baseModel {
  constructor(options: GeoModelOptions);
  public uuid: string;
  fit(object: THREE.Object3D, params: { width: number; length: number }): number;
}
// 模型-通用类
export declare class GeoModel extends baseModel {
  constructor(options: GeoModelOptions);
  public model: THREE.Object3D;
  update(value: ModelItemData): void;
}

// command
export declare class Command {
  constructor(options: { Map3d: Map3D });
  private undoStash: commandItem[];
  exec(action: CommandActions, config: any, success: () => void): void;
  destory(): void;
}

export declare class Ticker {
  constructor(parameters);
  public deltaTime: number;
  public maxFPS: number;
  add(fn: (...params: any[]) => any): void;
  addOnce(fn: (...params: any[]) => any): void;
  remove(fn: (...params: any[]) => any): void;
  start(): void; // todo
  stop(): void; // todo
  destroy(): void;
}

export default Map3D;
export declare const BasePlugin: PluginBase;

declare module "map-edit-3d" {
  export default Map3D;
  export const BasePlugin: PluginBase;
}
