<template>
  <el-dialog
    :title="$t('lang.rms.fed.edit')"
    :visible.sync="showDialog"
    :before-close="closeDialog"
    :close-on-click-modal="false"
    width="540"
    center
  >
    <el-form ref="editForm" :rules="rules" :model="formData" label-width="160px">
      <el-form-item :label="$t('lang.rms.fault.exception.code') + ':'" prop="systemCode">{{
        formData.systemCode
      }}</el-form-item>
      <el-form-item :label="$t('lang.rms.web.monitor.exception.info') + ':'" prop="exception">
        <span>{{ $t(formData.exception) }}</span>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.chargerSolution') + ':'" prop="solution">
        <span>{{ $t(formData.solution) }}</span>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.backlog.fault.maintenanceTask') + ':'" prop="maintenance">
        <span>{{ $t(formData.maintenance) }}</span>
      </el-form-item>
      <!--延迟发送邮件-->
      <el-form-item :label="$t('lang.rms.email.config.delaySendInterval') + ':'" prop="delaySendEmailInterval">
        <el-input
          type="number"
          v-model="formData.delaySendEmailInterval"
          :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.email.config.delaySendInterval')}`"
          style="width: 200px"
        >
          <template slot="append">s</template>
        </el-input>
      </el-form-item>
      <el-form-item :label="`${$t('lang.rms.fed.showEnableOrDisable')}:`" prop="status">
        <el-switch :active-value="1" :inactive-value="0" v-model="formData.status"> </el-switch>
        <span>{{
          formData.status === 1 ? $t("lang.rms.fed.chargerEnable") : $t("lang.venus.common.dict.disable")
        }}</span>
      </el-form-item>
      <el-form-item :label="`${$t('lang.rms.fed.callbackEnableOrDisable')}:`" prop="callbackDisable">
        <el-switch :active-value="true" :inactive-value="false" v-model="formData.callbackDisable"> </el-switch>
        <span>{{
          formData.callbackDisable ? $t("lang.rms.fed.chargerEnable") : $t("lang.venus.common.dict.disable")
        }}</span>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.callbackDuration') + ':'" prop="callbackDuration">
        <el-input
          type="number"
          v-model="formData.callbackDuration"
          :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.email.config.delaySendInterval')}`"
          style="width: 200px"
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.exceptionDuration') + ':'" prop="exceptionDuration">
        <el-input
          type="number"
          v-model="formData.exceptionDuration"
          :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.email.config.delaySendInterval')}`"
          style="width: 200px"
        >
        </el-input>
      </el-form-item>
      <el-form-item :label="`${$t('lang.rms.backlog.fault.isBacklog')}:`" prop="messageGroup">
        <el-switch :active-value="2" :inactive-value="1" v-model="formData.messageGroup"> </el-switch>
        <span>{{ formData.messageGroup === 2 ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}</span>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.backlog.fault.isEmailNotice') + ':'" prop="isSendEmail">
        <el-switch v-model="formData.isSendEmail"> </el-switch>
        <span>{{ formData.isSendEmail ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}</span>
      </el-form-item>
      <el-form-item :label="$t('lang.rms.backlog.fault.isMaintenance') + ':'" prop="isMaintenance">
        <el-switch v-model="formData.isMaintenance"> </el-switch>
        <span>{{ formData.isMaintenance ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}</span>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="closeDialog">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: "editMaxQueueNum",
  data() {
    //通知频率校验
    const delaySendEmailIntervalValidator = (rule, value, callback) => {
      if (value >= 0) {
        callback();
      } else {
        callback(new Error("请输入大于等于0的数字"));
      }
    };
    return {
      showDialog: false,
      formData: {},
      rules: {
        delaySendEmailInterval: [
          { type: "number", required: true, trigger: "blur", validator: delaySendEmailIntervalValidator },
        ],
      },
    };
  },
  methods: {
    open(rowData) {
      this.formData = Object.assign({}, rowData);
      this.showDialog = true;
    },

    closeDialog() {
      this.formData = {};
      this.showDialog = false;
    },
    // 保存
    save() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          const editData = this.formData;
          const newData = {
            systemCode: editData.systemCode,
            isSendEmail: editData.isSendEmail,
            isMaintenance: editData.isMaintenance,
            messageGroup: editData.messageGroup,
            delaySendEmailInterval: Number(editData.delaySendEmailInterval),
            callbackDuration: Number(editData.callbackDuration),
            exceptionDuration: Number(editData.exceptionDuration),
            status: editData.status,
            callbackDisable: editData.callbackDisable,
          };
          $req.post("/athena/fault/message/updateFaultMessage", newData).then(res => {
            if (res.code === 0) {
              this.$success(this.$t(res.msg));
              this.closeDialog();
              this.$emit("updateTableList");
            }
          });
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style scoped></style>
