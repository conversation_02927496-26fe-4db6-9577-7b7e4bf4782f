/* ! <AUTHOR> at 2022/08/27 */
import * as PIXI from "pixi.js";

class LayerCellLoad implements MRender.Layer {
  private floorId: floorId;
  private mapCore: any;
  private fillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  private meshList: Array<any> = [];
  private container: PIXI.Container;
  private lineWidth: number;
  private rendered: boolean = false;
  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    const fillStyle = this.fillStyle;
    fillStyle.color = utils.getOriginColor("LOAD_CELL_DIR");
    fillStyle.visible = true;
    fillStyle.alpha = 0.8;
    this.lineStyle.visible = false;

    let container = new PIXI.Container();
    container.name = "load";
    container.zIndex = utils.getLayerZIndex("load");
    container.interactiveChildren = false;
    container.visible = true;
    this.container = container;

    this.lineWidth = utils.getSettings("lineWidth");
    this.mapCore = mapCore;
    this.floorId = floorId;
  }

  render(): void {
    const mapCore = this.mapCore;
    const cells: mCellData[] = mapCore.mapData.cell.getByFloorId(this.floorId);
    if (!cells.length) return;

    const fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    for (let i = 0, len = Math.ceil(cells.length / fragment); i < len; i++) {
      const arr = cells.slice(i * fragment, i * fragment + fragment);

      const graphicsGeometry = this.resolveLoad(arr);

      if (!graphicsGeometry) continue;

      const graphics = new PIXI.Graphics(graphicsGeometry);
      this.meshList.push(graphics);
      this.container.addChild(graphics);
    }
    this.rendered = true;
  }

  toggle(isShow: boolean): void {
    if (isShow && !this.rendered) {
      this.render();
    }
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];
    this.rendered = false;
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.fillStyle = null;
    this.lineStyle = null;
    this.meshList = null;
    this.mapCore = null;
    this.lineWidth = null;
    this.rendered = null;
    this.floorId = undefined;
  }

  private resolveLoad(cells: mCellData[]): any {
    const _this = this;
    const fillStyle = _this.fillStyle,
      lineStyle = _this.lineStyle,
      lineWidth = _this.lineWidth / 100;

    const size = Number((lineWidth / 2).toFixed(3)),
      space = Number((size * 10).toFixed(3)),
      lineW = Number((size * 2).toFixed(3)),
      arrowW = Number((size * 4).toFixed(3));

    let sum = 0;
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    for (let i = 0, len = cells.length; i < len; i++) {
      const options = cells[i];
      const dirs = options?.loadDirs || [];
      if (dirs.length <= 0) continue;

      const { x: cx, y: cy } = options["location"];
      const [x1, y1, x2, y2] = options["hitArea"];
      dirs.forEach((dir: number, dirIndex: number) => {
        if (dir !== 0) return;
        ++sum;
        let rect, polygon;
        switch (dirIndex) {
          case 0: // right
            rect = [cx, -cy - size, x2 - cx - space, lineW];
            polygon = [x2 - space, -cy - arrowW, x2, -cy, x2 - space, -cy + arrowW];
            break;
          case 1: // down
            rect = [cx - size, -cy, lineW, y2 + cy - space];
            polygon = [cx - arrowW, y2 - space, cx + arrowW, y2 - space, cx, y2];
            break;
          case 2: // left
            rect = [x1 + space, -cy - size, x2 - cx - space, lineW];
            polygon = [x1, -cy, x1 + space, -cy - arrowW, x1 + space, -cy + arrowW];
            break;
          case 3: // up
            rect = [cx - size, y1 + space, lineW, y2 + cy - space];
            polygon = [cx, y1, cx - arrowW, y1 + space, cx + arrowW, y1 + space];
            break;
        }

        graphicsGeometry.drawShape(new PIXI.Rectangle(...rect), fillStyle, lineStyle);
        graphicsGeometry.drawShape(new PIXI.Polygon(polygon), fillStyle, lineStyle);
      });
    }

    if (sum <= 0) return null;
    else {
      graphicsGeometry.BATCHABLE_SIZE = sum;
      return graphicsGeometry;
    }
  }
}
export default LayerCellLoad;
