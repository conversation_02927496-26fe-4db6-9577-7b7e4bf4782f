interface MapAreaDto {
  id?: number;
  areaCode: string;
  areaId: number;
  areaName: string;
  areaType: string;
  cellCodes: string[];
  color?: string | null; //颜色
  controlPoints: { x: number; y: number }[];
  functions?: {
    cellCode: string;
    funcType: string;
    [key: string]: string;
  }[];

  // 补充字段
  [key: string]: any;
}

export interface MoveOperationDtos {
  angle?: number;
  baseX?: number;
  baseY?: number;
  moveX?: number;
  moveY?: number;
  opType?: number;
}

export interface MapFloorDto {
  mapId: number;
  floorId: number;
  mapAreaList: MapAreaDto[];
  mapChargerDtoList: any[];
  mapDeviceItemDtoList: any[];
  mapFloorMaskItemDtoList: any[];
  mapMarkerList: any[];
  mapNodeDtoList: any[];
  mapSegmentDtoList: any[];
  mapStationDtoList: any[];
  mapElevatorDtoList: any[];
  dmpDeviceDtoList: any[];
  deleteData: any
}

export interface ValidateMapParams {
  mapId: string | number;
  mapFloorDto: MapFloorDto;
  moveOperationDtos?: MoveOperationDtos[];
}

export interface ValidateMapResult {
  node: string[];
}
