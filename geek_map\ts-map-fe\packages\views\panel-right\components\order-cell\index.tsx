/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseCircleFilled, ExclamationCircleOutlined } from "@ant-design/icons";
import { Modal, Input, Radio, Button, message } from "antd";
import { getMap2D, $eventBus, checkBtn } from "../../../../singleton";

import CellDetail from "./cell-detail";

const { Search, TextArea } = Input;
const { confirm } = Modal;
type PropsOrderData = {
  isCurrent: boolean;
};
function OrderCell(props: PropsOrderData) {
  const { t } = useTranslation();
  const [operate, setOperate] = useState<"common" | "multi" | "rect">("common");
  const [cellCodes, setCellCodes] = useState<Array<code>>([]);
  const [cellCode, setCellCode] = useState<code>("");
  const [cellData, setCellData] = useState<cellData>(null);

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.isCurrent) return;

    $eventBus.on("wsDataQueryRightTab", wsData => {
      if (!props.isCurrent || !wsData) return;
      const { params, data } = wsData;
      if (data && operate === "common" && params?.layer === "cell") setCellData(data);
      else setCellData(null);
    });

    return () => {
      $eventBus.off("wsDataQueryRightTab");
    };
  }, [props.isCurrent, operate]);

  // 地图点击 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;
    const map2D = getMap2D();
    map2D.mapRender.triggerLayers(["cell"]);
    $eventBus.on("mapClick", (data: MRender.clickParams) => {
      const layer = data?.layer;
      if (layer !== "cell") return;
      if (data.multi) {
        setCellCodes(map2D.mapRender.getSelectCodes(layer));
      } else {
        setCellCode(data.code || "");
        setCellCodes([data.code]);
        map2D.mapWorker.reqQuery({ layer, code: data.code });
      }
    });

    return () => {
      $eventBus.off("mapClick");
      clearCell();
    };
  }, [props.isCurrent]);

  // 操作切换
  useEffect(() => {
    if (!props.isCurrent || !operate) return;
    clearCell(false);

    const map2D = getMap2D();
    switch (operate) {
      case "common":
        map2D.mapRender.trigger("stop");
        map2D.mapRender.triggerLayers(["cell"]);
        map2D.mapRender.enableMultiClick(false);
        break;
      case "multi":
        map2D.mapRender.triggerLayers(["cell"]);
        map2D.mapRender.enableMultiClick(true);
        break;
      case "rect":
        map2D.mapRender.triggerLayers([]);
        map2D.mapRender.enableMultiClick(true);
        map2D.mapRender.trigger("rect", data => {
          if (data.code !== 0) {
            if (data.code === -1 || data.code === -2) {
              message.error(t("lang.rms.api.result.task.startOrDestOutOfMap"));
            }
            return;
          }
          _$utils
            .reqPost("/athena/warehouse/cell/queryByLocationRange", {
              startLocation: data["points"][0],
              stopLocation: data["points"][1],
              type: null,
            })
            .then(res => {
              if (res.code !== 0) return;
              const data = res.data || [];
              const codes = data.map((item: any) => item.cellCode);
              setCellCodes(codes);
              map2D.mapRender.trigger("click", { cell: codes });
            });
          setOperate("multi");
        });
        break;
    }
  }, [operate]);

  // 单元格搜索
  const cellSearch = (value: string) => {
    if (!value) return;

    const map2D = getMap2D();
    let params = { layer: "cell", code: value };
    const data = map2D.mapWorker.getQueryData(params);
    // some wrong,  invalid cellCode request ????
    map2D.mapWorker.reqQuery(data ? Object.assign(params, { code: data.cellCode }) : params);
    if (data) {
      map2D.mapRender.trigger("click", { cell: [data.cellCode] });
      map2D.mapRender.setEleCenter({ layer: "cell", code: data.cellCode });
    }
  };

  const clearCell = (clearOperate = true) => {
    setCellCode("");
    setCellCodes([]);
    if (clearOperate) setOperate("common");
    setCellData(null);
    const map2D = getMap2D();
    map2D.mapRender.trigger("stop");
    map2D.mapWorker.stopQuery();
    map2D.mapRender.clearSelects();
    map2D.mapRender.enableMultiClick(false);
  };

  const controlHandler = (cmd: string) => {
    if (!cellData?.cellCode && !cellCodes.length) {
      const msg = t("lang.rms.fed.enterTheCellCode"); // 单元格编号不能为空
      message.error(msg);
      return;
    }

    const map2D = getMap2D();
    let instruction = "";
    switch (cmd) {
      case "lock":
        instruction = "LOCK_CELL";
        break;
      case "stop":
        instruction = "STOP_CELL";
        break;
      case "unlock":
        instruction = "UNLOCK_CELL";
        break;
    }

    let warningText = t("lang.rms.fed.confirmTheOperation");
    confirm({
      icon: <ExclamationCircleOutlined />,
      content: warningText,
      onOk() {
        const reqMsg = "WarehouseInstructionRequestMsg";
        const resMsg = "WarehouseInstructionResponseMsg";
        map2D.mapWorker.reqSocket(reqMsg, { instruction, cellCodes }).then(res => {
          if (res.msgType !== resMsg) return;
          clearCell();
          _$utils.wsCmdResponse(res?.body || {});
        });
      },
    });
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component"
    >
      <Search
        value={cellCode}
        placeholder={t("lang.rms.fed.textNodeCode")}
        onSearch={cellSearch}
        enterButton
        allowClear={{ clearIcon: <CloseCircleFilled onClick={() => clearCell()} /> }}
        onChange={e => setCellCode(e.target.value)}
        style={operate !== "common" ? { display: "none" } : null}
      />

      <TextArea
        placeholder={t("lang.rms.fed.chooseAPoint")}
        rows={1}
        autoSize={{ maxRows: 8 }}
        value={cellCodes.join(",")}
        readOnly={true}
        style={{ display: operate === "common" ? "none" : "block" }}
      />

      <Radio.Group
        onChange={({ target: { value } }) => setOperate(value)}
        value={operate}
        optionType="button"
        className="cell-radio"
      >
        <Radio value="common">{t("lang.rms.fed.tabCommonlyUsed")}</Radio>
        <Radio value="multi">{t("lang.rms.fed.multiSelection")}</Radio>
        <Radio value="rect">{t("lang.rms.fed.rectSelection")}</Radio>
      </Radio.Group>

      <div className="component-btn-group">
        {checkBtn("MonitorCellLock") && (
          <Button type="primary" block onClick={() => controlHandler("lock")}>
            {t("lang.rms.fed.buttonLock")}
          </Button>
        )}
        {checkBtn("MonitorCellStop") && (
          <Button type="primary" block onClick={() => controlHandler("stop")}>
            {t("lang.rms.fed.buttonPause")}
          </Button>
        )}
        {checkBtn("MonitorCellUnlock") && (
          <Button type="primary" block onClick={() => controlHandler("unlock")}>
            {t("lang.rms.fed.buttonUnlock")}
          </Button>
        )}
      </div>

      <CellDetail cell={cellData} />
    </div>
  );
}

export default OrderCell;
