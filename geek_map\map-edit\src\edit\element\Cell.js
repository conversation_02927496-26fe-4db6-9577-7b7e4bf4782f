import Resource from "../resource/Resource";
import {Sprite,Texture,Graphics} from "pixi.js";
import CellArrow from "./baseElement/CellArrow";
import {cad2pixi, pixi2cad, toFixed} from '../utils/utils'
import ElementEvent from "../event/ElementEvent";
import LayerManager from "../layerManager/LayerManager";
import EventBus from '../eventBus/EventBus'
import Event from '../event/Event'
import Control from '../control/Control'
import {elementColor,defaultConfig,cellRatio,COEFFICIENT} from '../config'
const {UNLOAD_DIR,LOAD_DIR,BOTH_DIR} = elementColor
const {CELL:{width:defaultW,length:defaultL}} = defaultConfig
export default class Cell {
  static showDirType = 'both'
  static add(data) {
    const {location,startBounds,cellType,cellCode,width = defaultW,length = defaultL,nodeId,loadDirs,unloadDirs,isQrNode} = data
    //需要绑定在元素上的属性
    const elAttr = {cellCode,cellType,nodeId}
    const pixiLocation = cad2pixi(location)
    const $el = new Sprite(Texture.WHITE);
    const {x,y} = pixiLocation
    $el.tint = elementColor[cellType];
    //FIXME:不知道为啥，NULL_CELL存在对象里，不能动态取
    if(cellType === 'NULL_CELL') $el.tint = 0x3f9cfb
    $el.buttonMode = true;
    $el.interactive = true;
    $el.x = x;
    $el.y = y;
    $el.width = toFixed(length * cellRatio / COEFFICIENT);
    $el.height = toFixed(width * cellRatio / COEFFICIENT);
    //计算location与startBounds的距离，从而使单元格偏移量
    const {x:lx,y:ly} = location
    const {x:sx,y:sy} = startBounds
    const rx = (lx - sx) / length
    const ry = 1 - (ly - sy) / width
    // $el.anchor.set(0.5, 0.5);
    $el.anchor.set(rx, ry);
    $el.zIndex = 1
    $el.type = 'element'
    $el.name = 'element'
    Object.assign($el,elAttr)
    this.renderDir($el,data)
    //渲染二维码图标
    if(isQrNode) this.renderQrcode($el)
    //绑定事件
    this.bindEvent($el)
    return $el
  }
  //更新dir,原先版
  static renderDir_old($el,data) {
    const {loadDirs,unloadDirs} = data
    const dirType = Control.dirType
    //删除原来的箭头对象
    const $sprite = $el.getChildByName('dir')
    if($sprite) $el.removeChild($sprite)
    //都不存在时
    if(!loadDirs && !unloadDirs) return
    //判断是否渲染箭头
    const $dir = new Graphics()
    $dir.scale.set(0.3)
    $dir.name = 'dir'
    if(loadDirs && dirType !== 'unload'){
      loadDirs.forEach((num,index) => {
        let color,isVisible = true;
        //空负载都有
        const bothDir =!num && unloadDirs && !unloadDirs[index] && dirType === 'both'
        //只有负载
        const isLoad = !num && dirType !== 'unload'
        //只有空载
        const isUnload = num && unloadDirs && !unloadDirs[index] && dirType !== 'load'
        if(bothDir){
          color = BOTH_DIR
        }else{
          if(isLoad){
            color = LOAD_DIR
          }else{
            if(isUnload){
              color = UNLOAD_DIR
            }else{
              isVisible = false
              color = BOTH_DIR
            }
          }
        }
        const dirOps = {
          radian: Math.PI / 2 * index,
          isVisible,
          color
        }
        CellArrow.render($dir,dirOps)
      })
    }else if(unloadDirs && dirType !== 'load'){
      unloadDirs.forEach((num,index) => {
        const dirOps = {
          radian: Math.PI / 2 * index,
          isVisible:!num,
          color:UNLOAD_DIR
        }
        CellArrow.render($dir,dirOps)
      })
    }
    $el.addChild($dir)
  }
  //更新dir(优化方案，还在测试)
  static renderDir($el,data) {
    const {loadDirs,unloadDirs} = data
    const dirType = Control.dirType
    //删除原来的箭头对象
    let $dir = $el.getChildByName('dir')
    if($dir){
      $dir.clear()
    }else{
      $dir = new Graphics()
    }
    //都不存在时
    if(!loadDirs && !unloadDirs){
      $el.removeChild($dir)
      return null
    }
    //都没有方向的时候
    if(JSON.stringify([1,1,1,1]) === loadDirs && JSON.stringify([1,1,1,1]) === unloadDirs){
      $el.removeChild($dir)
      return null
    }
    //判断是否渲染箭头
    // const $dir = new Graphics()
    $dir.scale.set(0.3)
    $dir.name = 'dir'
    if(loadDirs && dirType !== 'unload'){
      loadDirs.forEach((num,index) => {
        let color,isVisible = true;
        //空负载都有
        const bothDir =!num && unloadDirs && !unloadDirs[index] && dirType === 'both'
        //只有负载
        const isLoad = !num && dirType !== 'unload'
        //只有空载
        const isUnload = num && unloadDirs && !unloadDirs[index] && dirType !== 'load'
        if(bothDir){
          color = BOTH_DIR
        }else{
          if(isLoad){
            color = LOAD_DIR
          }else{
            if(isUnload){
              color = UNLOAD_DIR
            }else{
              isVisible = false
              color = BOTH_DIR
            }
          }
        }
        const dirOps = {
          radian: Math.PI / 2 * index,
          isVisible,
          color
        }
        CellArrow.render($dir,dirOps)
      })
    }else if(unloadDirs && dirType !== 'load'){
      unloadDirs.forEach((num,index) => {
        const dirOps = {
          radian: Math.PI / 2 * index,
          isVisible:!num,
          color:UNLOAD_DIR
        }
        CellArrow.render($dir,dirOps)
      })
    }
    $el.addChild($dir)
  }
  //渲染二维码
  static renderQrcode($el) {
    const defaultW = 3
    const resources = Resource.loader.resources
    const texture = resources.qrcode.texture
    const $qrcode = new Sprite(texture)
    $qrcode.name = 'qrcode'
    $qrcode.width = defaultW
    $qrcode.height = defaultW
    $qrcode.anchor.set(0.5, 0.5);
    // const $qrcode = Resource.qrTest.clone()
    $el.addChild($qrcode)
  }
  static update($el,data) {
    const {location,cellType,cellCode,width = defaultW,length = defaultL,isQrNode} = data
    const pixiLocation = cad2pixi(location)
    const $qrcode = $el.getChildByName('qrcode')
    const elAttr = {
      cellType,
      cellCode,
      width: length * cellRatio / COEFFICIENT,
      height: width * cellRatio / COEFFICIENT,
      tint:elementColor[cellType],
      x:pixiLocation.x,
      y:pixiLocation.y
    }
    Object.assign($el,elAttr)
    //更新是否是二维码单元格类型
    if(isQrNode && !$qrcode){
      this.renderQrcode($el)
    }
    if(!isQrNode && $qrcode){
      $el.removeChild($qrcode)
    }
    //更新单元格箭头
    this.renderDir($el,data)
  }
  //给元素绑定事件
  static bindEvent($el){
    const {onDragMove,onDragStart,onDragEnd} = ElementEvent
    //更新cell数据
    const updateCell = ($el,e) => {
      if(!$el.selected || !$el.dragging){
        Control.enableDrag(true)
        return
      }
      onDragEnd($el,e)
      const {nodeId,width,height} = $el
      const {x,y} = e.data.getLocalPosition($el.parent)
      //将pixi坐标转为cad坐标
      const location = pixi2cad({x:toFixed(x),y:toFixed(y)})
      //计算startBounds
      const realW = width / cellRatio * COEFFICIENT
      const realH = height / cellRatio * COEFFICIENT
      const startBounds = {x:toFixed(location.x - realW / 2),y:toFixed(location.y - realH / 2)}
      const updateOp = {
        id:'CELL',
        data:[{nodeId,location,startBounds}]
      }
      LayerManager.updateElements(updateOp)
    }
    $el
      .on('pointerdown', function(e){
        onDragStart(this,e)
      })
      .on('pointerup', function(e){
        updateCell(this,e)
      })
      .on('pointerupoutside', function(e){
        updateCell(this,e)
      })
      .on('pointermove', function(e){
        const {selected,data} = this
        if(selected && data){
          onDragMove(this,e)
          EventBus.$emit('dragging',[this])
        }
      })
  }
}
