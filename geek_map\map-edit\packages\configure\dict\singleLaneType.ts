// 单行道模式

/**
 * 单行道模式 - 断头路
 */
export const SINGLE_LANG_GUILLOTIONEROAD = 1;
/**
 * 单行道模式 - 独木桥
 */
export const SINGLE_LANG_SINGLEBRIDGE = 2;

export const SINGLE_LANG_TYPE = [
  { label: "lang.rms.fed.guillotineRoad", value: SINGLE_LANG_GUILLOTIONEROAD },
  { label: "lang.rms.fed.singleBridge", value: SINGLE_LANG_SINGLEBRIDGE },
];

/**
 * 空负载模式 - 空载可用
 */
export const SINGLE_LANG_NOLOADAVAILABLE = 0;

/**
 * 空负载模式 - 负载可用
 */
export const SINGLE_LANG_LOADAVAILABLE = 1;

/**
 * 空负载模式 - 空负载可用
 */
export const SINGLE_LANG_EMPTYLOADAVAILABLE = 2;

// 单行道空负载模式
export const SINGLE_LANG_LOADTYPE = [
  { label: "lang.rms.fed.noLoadAvailable", value: SINGLE_LANG_NOLOADAVAILABLE },
  { label: "lang.rms.fed.loadAvailable", value: SINGLE_LANG_LOADAVAILABLE },
  { label: "lang.rms.fed.emptyLoadAvailable", value: SINGLE_LANG_EMPTYLOADAVAILABLE },
];
//路障空负载模式
export const OBSTACLE_AREA_LOAD_MODE = [
  { label: "lang.rms.fed.noLoadAvailable", value: SINGLE_LANG_NOLOADAVAILABLE },
  { label: "lang.rms.fed.loadAvailable", value: SINGLE_LANG_LOADAVAILABLE },
]
