/* ! <AUTHOR> at 2022/09/08 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseOutlined } from "@ant-design/icons";
import { Card, Select, Button } from "antd";
import { getMap2D } from "../../../../singleton";

import OrderPanelGrid from "../common/order-panel-grid";
const { Option } = Select;
type deadRobot = { code: code; errorCodes: Array<any>; taskId: any };
type PropsOrderData = {
  visible: boolean;
  currentRobot: deadRobot;
  cellCode: code;
  onCancel: () => void;
};
function DeadRobotGoTo(props: PropsOrderData) {
  const { t } = useTranslation();
  const [cellCode, setCellCode] = useState<string>("");
  const [cellCodeList, setCellCodeList] = useState([]);

  useEffect(() => {
    if (!props.visible) return;
    const map2D = getMap2D();
    map2D.mapRender.enableMultiClick(true);
    map2D.mapRender.triggerLayers(["cell"]);
    map2D.mapWorker
      .reqSocket("QueryInstructionRequestMsg", {
        instruction: "ADJACENT_FREE_CELL",
        robotId: props.currentRobot?.code,
      })
      .then(res => {
        if (!["QueryInstructionResponseMsg"].includes(res.msgType)) return;
        if (res?.body?.code === 0) {
          const codes = res?.body?.data || [];
          setCellCodeList(codes);
          map2D.mapRender.renderFeatureColor({ type: "cell", codes, color: 0x2bef69 });
        }
      });

    return () => {
      setCellCode("");
      map2D.mapRender.renderFeatureColor({ type: "cell", clear: true });
      map2D.mapRender.enableMultiClick(false);
      map2D.mapRender.triggerLayers(["robot"]);
    };
  }, [props.visible]);

  // cellCode 变化
  useEffect(() => {
    const pCellCode = props.cellCode;
    if (pCellCode !== cellCode) {
      const map2D = getMap2D();
      map2D.mapRender.clearSelects("cell", [cellCode]);
      if (pCellCode) {
        setCellCode(pCellCode.toString());
      } else {
        setCellCode("");
      }
    }
  }, [props.cellCode]);

  const controlHandler = () => {
    const reqMsg = "ForcedTaskRequestMsg";
    const resMsg = "ForcedTaskResponseMsg";
    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket(reqMsg, {
        robotId: props.currentRobot?.code,
        instruction: "FORCE_MOVE_ONE_CELL",
        nextCellCode: cellCode,
      })
      .then(res => {
        if (res.msgType !== resMsg) return;
        _$utils.wsCmdResponse(res?.body || {});
      });

    props.onCancel();
  };

  const cellSearch = (value: string) => {
    setCellCode(value);
    const map2D = getMap2D();
    map2D.mapRender.clearSelects("cell", [cellCode]);

    map2D.mapWorker.reqQuery({ layer: "cell", code: value });
    map2D.mapRender.trigger("click", { cell: [value] });
    map2D.mapRender.setEleCenter({ layer: "cell", code: value });
  };

  return (
    props.visible && (
      <Card
        size="small"
        type="inner"
        title={t("lang.rms.fed.forceMove")}
        extra={<CloseOutlined onClick={props.onCancel} />}
        actions={[
          <Button
            type="primary"
            onClick={controlHandler}
            size="small"
            style={{ float: "right", marginRight: "8px" }}
          >
            {t("lang.rms.fed.confirm")}
          </Button>,
        ]}
        className="component-operate-detail"
      >
        <OrderPanelGrid
          style={{ borderLeft: 0, borderRight: 0, marginBottom: 0 }}
          items={[
            {
              label: t("lang.rms.fed.availableIdlePoints"),
              node: (
                <Select
                  value={cellCode}
                  style={{ width: "100%" }}
                  onChange={value => cellSearch(value)}
                  placeholder={t("lang.rms.fed.availableIdlePoints")}
                >
                  {cellCodeList.map(item => (
                    <Option key={item} value={item}>
                      {item}
                    </Option>
                  ))}
                </Select>
              ),
            },
          ]}
        />
      </Card>
    )
  );
}

export default DeadRobotGoTo;
