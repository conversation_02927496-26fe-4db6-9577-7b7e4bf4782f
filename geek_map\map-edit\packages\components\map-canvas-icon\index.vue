<template>
  <div class="icons">
    <template v-for="item in modelValue" :key="item.name">
      <span
        v-if="item?.innerType === 'none'"
        class="mapFont icon"
        :class="item.icon"
        :style="{ fontSize: `${item.size || 14}px`, color: item.color || '#666' }"
        @click="clickItem(item)"
      ></span>
      <el-popover v-else width="auto" placement="bottom-end" :trigger="item.type || 'hover'">
        <template #reference>
          <span
            class="mapFont icon"
            :class="item.icon"
            :style="{ fontSize: `${item.size || 14}px`, color: item.color || '#666' }"
            @click="clickItem(item)"
          ></span>
        </template>

        <div class="descriptions" v-if="item.innerType === 'descriptions'">
          <div class="labels">
            <span class="labelItem" v-for="(dataItem, index) in item.innerData" :key="index">
              {{ $t(dataItem.label) }}
            </span>
          </div>
          <div class="values">
            <span class="valueItem" v-for="(dataItem, index) in item.innerData" :key="index">
              {{ $t(templateStore.parseContent(dataItem.value, "-")) }}
            </span>
          </div>
        </div>
      </el-popover>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { useTemplateStore } from "@packages/store/template";
const templateStore = useTemplateStore();

interface IconItem {
  icon: string;
  name: string;
  size?: number;
  eventName?: string;
  type?: "hover" | "click" | "focus" | "contextmenu"; // 触发条件, 点击或移入
  innerType?: "descriptions" | "none"; // 目前仅支持了 descriptions
  innerData?: {
    label: string;
    value: string;
  }[];
  color?: string;
}

defineProps<{
  modelValue: IconItem[];
}>();

const emits = defineEmits<{
  (event: string, data: IconItem): void;
}>();

const clickItem = (item: IconItem) => {
  if (item.eventName) {
    emits(item.eventName, item);
  }
};
</script>

<style scoped lang="scss">
.icons {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 5;
  text-align: center;
  padding: 5px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 5px;
  padding-left: 10px;
  .icon {
    float: left;
    margin-right: 5px;
  }
}

.descriptions {
  display: flex;
  flex-direction: row;

  .labels,
  .values {
    .labelItem,
    .valueItem {
      text-overflow: ellipsis;
      display: block;
    }
    white-space: nowrap;
    padding: 5px;
  }
}
</style>
