import Selected from "./Selected";
import EventBus from "../eventBus/EventBus";
import {getId} from '../utils/utils'
export default class SingleSelected {
  constructor() {

  }
  //选中触发
  selected(e){
    const target = e.event.target
    if(target && target.name !== 'viewport'){
      const $el = (target.isSprite || target.isSingleLane) ? target : target.parent
      // const {nodeId,segmentId,id} = $el
      // const selectedId = nodeId || segmentId || id
      //特殊处理了，面有两种形态，1.选中 2.给边增点
      // if($el.type === 'area'){
      //   const clickCount = $el.clickCount
      //   if([0,2].includes(clickCount)){
      //     const selectedId = getId($el)
      //     Selected.renderSelected(selectedId,$el)
      //     $el.clickCount = clickCount === 0 ? 1 : 0
      //   }else if(clickCount === 1){
      //     $el.clickCount = 2
      //   }
      //   return
      // }
      const selectedId = getId($el)
      Selected.renderSelected(selectedId,$el)
    }else{
      Selected.resetAllSelected()
      //点击空白回调
      EventBus.$emit('selected',null)
    }
  }
}
