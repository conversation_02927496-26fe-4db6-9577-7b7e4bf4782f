/* ! <AUTHOR> at 2021/08 */
import * as PIXI from "pixi.js";

class EventRanging {
  private mapCore: MRender.MainCore;
  private viewport: any;
  private Line: any = null;
  private LineText: any = null;

  private isRanging = false;
  private isDrag = false;
  private startFloorId: floorId = null;
  private startPoint: location = null;
  private endPoint: location = null;
  private cb: callback = null;
  constructor(mapCore: MRender.MainCore) {
    this.mapCore = mapCore;
    this.Line = null;
    this.LineText = null;

    const viewport = mapCore.mapView.getViewport();
    viewport.on("pointerdown", this.start.bind(this));
    viewport.on("pointermove", this.move.bind(this));
    viewport.on("pointerup", this.end.bind(this));
    document.addEventListener("keydown", e => {
      // 按 ESC
      if (e.keyCode !== 27) return;
      this.remove();
    });
    this.viewport = viewport;
  }

  init(cb: callback) {
    const viewport = this.viewport;
    this.isRanging = true;
    this.startPoint = { x: 0, y: 0 };
    this.endPoint = { x: 0, y: 0 };
    this.startFloorId = null;
    this.cb = cb;

    let Line = new PIXI.Graphics();
    Line.zIndex = 999;
    viewport.addChild(Line);
    this.Line = Line;
  }

  getTriggerStatus() {
    return this.isRanging;
  }

  start(e: any) {
    if (!this.isRanging) return;
    this.isDrag = true;
    const viewport = this.viewport;
    viewport.drag({ pressDrag: false });

    const eData = e.data;
    if (!eData) throw new Error("rect 框选 start, 没有e.data, e 出错了?");
    const { x, y } = eData.getLocalPosition(viewport);
    if (!this.calPosition(eData, x, y)) {
      this.cb && this.cb({ code: -1 }); // 超出楼层范围
      this.remove();
      return;
    }

    this.startPoint = { x, y };
    this.endPoint = { x, y };
  }

  move(e: any) {
    if (!this.isRanging || !this.isDrag) return;

    const eData = e.data;
    if (!eData) throw new Error("rect 框选 move, 没有e.data, e 出错了?");
    const { x, y } = eData.getLocalPosition(this.viewport);

    this.endPoint = { x, y };
    this.drawLine();
  }

  end(e: any) {
    if (!this.isRanging) return;

    const eData = e.data;
    if (!eData) throw new Error("rect 框选 end, 没有e.data, e 出错了?");
    const { x, y } = eData.getLocalPosition(this.viewport);
    this.endPoint = { x, y };
    if (!this.calPosition(eData, x, y)) {
      this.cb && this.cb({ code: -2 }); // 不能跨楼层
      this.remove();
      return;
    }

    this.drawLine();
    this.drawLineText();
    this.remove(false);
    this.cb && this.cb({ code: 0 });
  }

  remove(isClear = true) {
    this.isRanging = false;
    this.isDrag = false;
    this.startFloorId = null;
    this.startPoint = null;
    this.endPoint = null;
    this.cb = null;

    const viewport = this.viewport;
    if (isClear) {
      if (this.Line) {
        viewport.removeChild(this.Line);
        this.Line.destroy();
        this.Line = null;
      }

      if (this.LineText) {
        viewport.removeChild(this.LineText);
        this.LineText.destroy();
        this.LineText = null;
      }
    }

    viewport.drag({ pressDrag: true });
    this.mapCore.mapView.renderAll();
  }

  destroy() {
    this.mapCore = null;
    this.viewport = null;
    this.Line = null;
    this.LineText = null;
    this.isRanging = false;
    this.isDrag = false;
    this.startFloorId = null;
    this.startPoint = null;
    this.endPoint = null;
    this.cb = null;
  }

  private drawLine() {
    const start = this.startPoint;
    const end = this.endPoint;

    let Line = this.Line;
    Line.clear();
    Line.lineStyle({ color: 0xf36d68, width: 0.05 });
    Line.moveTo(start.x, start.y);
    Line.lineTo(end.x, end.y);

    this.mapCore.mapView.renderAll();
  }

  private drawLineText() {
    const start = this.startPoint;
    const end = this.endPoint;
    const style = {
      fontFamily: "Arial",
      fontSize: 18,
      fill: 0xf36d68,
    };

    const x = end.x - start.x;
    const y = end.y - start.y;
    const angle = (Math.atan2(Math.abs(x), Math.abs(y)) / Math.PI) * 180;
    const dis = Math.pow(x ** 2 + y ** 2, 0.5);

    let LineText = new PIXI.Text(
      `总长度：${dis.toFixed(3)}m\nY轴正方向夹角: ${Math.round(angle + 360) % 360}°`,
      style,
    );
    LineText.x = end.x;
    LineText.y = end.y;
    LineText.width = 4;
    LineText.height = 1;

    this.viewport.addChild(LineText);
    this.LineText = LineText;
  }

  private calPosition(eData: any, x: number, y: number): boolean {
    const mapFloors = this.mapCore.mapFloors;
    let floorLayer: any, bounds;
    for (let floorId in mapFloors) {
      floorLayer = mapFloors[floorId].getLayerFloor();
      const { x, y } = eData.getLocalPosition(floorLayer);
      bounds = floorLayer.getLocalBounds();
      if (
        x > bounds.x &&
        x < bounds.x + bounds.width &&
        y > bounds.y &&
        y < bounds.y + bounds.height
      ) {
        if (!this.startFloorId) this.startFloorId = floorId;
        else if (this.startFloorId !== floorId) return false;
        return true;
      }
    }

    return false;
  }
}

export default EventRanging;
