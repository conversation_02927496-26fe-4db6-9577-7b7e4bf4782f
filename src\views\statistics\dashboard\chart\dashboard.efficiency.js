import RmsJobReceiveToRsStartMoveBar from "./bar/rmsJobReceiveToRsStartMoveBar";
import P40ToStationJobStartMoveToStationBar from "./bar/p40ToStationJobStartMoveToStationBar";
import RmsJobReceiveToP40StartMove from "./bar/rmsJobReceiveToP40StartMoveBar";
import JobDoneCostBar from "./bar/JobDoneCostBar";
import RsJobStartMoveToDestLatticeCostHighBar from "./bar/rsJobStartMoveToDestLatticeCostHighBar";
import RmsJobBoxArriveTransferToP40StartMoveBar from "./bar/rmsJobBoxArriveTransferToP40StartMoveBar";
import P40StayStationCostBar from "./bar/p40StayStationCostBar";
import P40StationBackStartMoveToDestLatticeBar from "./bar/p40StationBackStartMoveToDestLatticeBar";
import LocStatusBar from "./bar/locStatusBar";
export default [
  new RmsJobReceiveToRsStartMoveBar({ width: '50%', height: '300px' }),
  new P40ToStationJobStartMoveToStationBar({ width: '50%', height: '300px' }),
  new RmsJobReceiveToP40StartMove({ width: '50%', height: '300px' }),
  new JobDoneCostBar({ width: '50%', height: '300px' }),
  new RsJobStartMoveToDestLatticeCostHighBar({ width: '50%', height: '300px' }),
  new RmsJobBoxArriveTransferToP40StartMoveBar({ width: '50%', height: '300px' }),
  new P40StayStationCostBar({ width: '50%', height: '300px' }),
  new P40StationBackStartMoveToDestLatticeBar({ width: '50%', height: '300px' }),
  new LocStatusBar({ width: '50%', height: '300px' }),
]