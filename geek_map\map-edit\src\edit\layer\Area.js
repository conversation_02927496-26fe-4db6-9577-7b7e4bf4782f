import AreaElement from "../element/Area";
import SingleLane from "../element/SingleLane";
import Base from "./Base";
import EventBus from "../eventBus/EventBus";
import { getGlobalViewport } from "../global";
import { Container } from "pixi.js";
class Area extends Base {
  constructor(props) {
    super(props);
    this.layerName = "AREA";
  }
  initLayer(op) {
    const { id, zIndex } = op;
    this.id = id;
    this.viewport = getGlobalViewport();
    this.container = new Container();
    this.container.id = id;
    this.container.name = id;
    this.container.zIndex = zIndex;
    this.container.interactive = true;
    this.container.interactiveChildren = true;
    // this.container.sortableChildren = true
    this.viewport.addChild(this.container);
  }
  //初始化
  initElements(data = []) {
    data.forEach(item => {
      const { id, areaType } = item;
      const $el = areaType === "SINGLE_LANE" ? SingleLane.add(item) : AreaElement.add(item);
      if ($el) {
        $el.layerName = this.layerName;
        $el.areaType = areaType;
        // $el.zIndex = this. _renderIndex()
        this.container.addChild($el);
        this.setProperties(id, $el, item);
      }
    });
  }
  //添加元素
  addElements(data = []) {
    const { layerName } = this;
    //历史数据
    const historyData = [];
    const addedData = [];
    data.forEach(item => {
      const { id, areaType } = item;
      const $el = areaType === "SINGLE_LANE" ? SingleLane.add(item) : AreaElement.add(item);
      if ($el) {
        $el.layerName = layerName;
        $el.areaType = areaType;
        // $el.zIndex = this. _renderIndex()
        this.container.addChild($el);
        this.setProperties(id, $el, item);
        historyData.push(item);
        addedData.push(this.getProperties(id));
      }
    });
    //添加历史记录
    const historyDetail = { action: "add", detail: historyData, layerName };
    EventBus.$emit("added", addedData);
    return { historyDetail };
  }
  updateElements(data = [], isCoverProperties = false) {
    //历史数据
    const historyData = [];
    const updateData = [];
    data.forEach(item => {
      const { id } = item;
      //旧数据存储
      const { properties: oldProperties } = this.getProperties(id);
      historyData.push({ ...oldProperties });
      const $el = this.id2$el.get(id);
      this.setProperties(id, $el, item, isCoverProperties);
      const updateInfo = this.getProperties(id);
      const { properties } = updateInfo;
      AreaElement.update($el, properties);
      updateData.push({ ...updateInfo });
    });
    //添加历史记录
    const historyDetail = { action: "update", detail: historyData, layerName: this.layerName };
    EventBus.$emit("updated", updateData);
    return { historyDetail };
  } //删除元素
  deleteElements(ids = []) {
    //历史数据
    const historyData = [];
    const deleteData = [];
    ids.forEach(id => {
      const { $el, properties } = this.getProperties(id);
      historyData.push({ ...properties });
      deleteData.push({ $el: null, properties: { ...properties } });
      this.id2$el.delete(id);
      this.container.removeChild($el);
    });
    const historyDetail = { action: "delete", detail: historyData, layerName: this.layerName };
    EventBus.$emit("deleted", deleteData);
    return { historyDetail };
  }
  getAreaInfoByType(type) {
    const id2$elArr = [...this.id2$el];
    const areaInfo = [];
    id2$elArr.forEach(arr => {
      const [id, $el] = arr;
      const { areaType } = $el;
      if (areaType === type) {
        areaInfo.push(this.getProperties(id));
      }
    });
    return areaInfo;
  }
  //为了控制图层的上下关系
  _renderIndex() {
    return this.id2$el.size + 1;
  }
  //是否可以被点击
  triggerLayer(isTrigger) {
    this.container.interactiveChildren = isTrigger;
  }
}
export default Area;
