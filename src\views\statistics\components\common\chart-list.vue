<template>
  <div class="chart-list">
    <div
      v-for="(item, index) in chartList"
      :key="index"
      @click="$emit('chooseChart', item)"
      class="chart-item"
    >
      <div class="item-box">
        <h6>{{ item.title }}</h6>
        <img v-if="item.chartType == 'line'" src="@imgs/statistics/line-chart.png" />
        <img v-if="item.chartType == 'point'" src="@imgs/statistics/point-chart.png" />
        <!-- TODO: -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "statisticsChartList",
  props: {
    chartList: {
      type: Array,
      default: () => [],
    },
  },
};
</script>

<style lang="less" scoped>
.chart-list {
  .g-flex();
  width: 100%;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 0 8px;
  max-height: 100%;
  overflow-y: auto;
}
.chart-item {
  padding: 5px;
  width: 25%;

  .item-box {
    background: #fff;
    border: 1px solid #eee;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    cursor: pointer;
    > h6 {
      padding: 20px 12px;
      font-weight: 600;
      color: #666;
      text-align: center;
      font-size: 16px;
      border-bottom: 2px dashed #ccc;
    }
    > img {
      width: 100%;
      height: 160px;
      padding: 8px 0 0;
      display: block;
      text-align: center;
    }
  }
}
</style>
