//查找元素
import LayerManager from "../layerManager/LayerManager";
import Selected from "../selected/Selected";
import { getGlobalEditMap, getGlobalViewport } from "../global";
const search = function (ops) {
  const EditMap = getGlobalEditMap();
  const viewport = getGlobalViewport();
  const padding = 500;
  const { layerName, id } = ops;
  const layerInstance = LayerManager.get(layerName);
  const $el = layerInstance.id2$el.get(id);
  //sprite类型特殊处理
  let h, w, centerX, centerY;
  if ($el.isSprite) {
    const { height, width, x, y } = $el;
    (h = height), (w = width), (centerX = x), (centerY = y);
  } else {
    const { height, width, x, y } = $el.getLocalBounds();
    (h = height), (w = width), (centerX = x + w / 2), (centerY = y + h / 2);
  }

  const { screenWidth, screenHeight } = viewport;
  const scale = Math.min((screenWidth - padding) / w, (screenHeight - padding) / h);
  const center = [centerX, centerY];
  viewport.moveCenter(...center);
  viewport.setZoom(scale, true);
  Selected.resetAllSelected();
  EditMap.setSelected({ layerName, id });
};

const setEleCenter = ({ centerX, centerY, w, h }) => {
  const viewport = getGlobalViewport();
  const padding = 500;
  const { screenWidth, screenHeight } = viewport;
  const scale = Math.min((screenWidth - padding) / w, (screenHeight - padding) / h);
  const center = [centerX, centerY];

  viewport.moveCenter(...center);
  viewport.setZoom(scale, true);
};

//获取坐标 和 长宽
const getSinglePosition = ops => {
  const { layerName, id } = ops;
  const layerInstance = LayerManager.get(layerName);
  const $el = layerInstance.id2$el.get(id);
  //sprite类型特殊处理
  let h, w, centerX, centerY;
  if ($el.isSprite) {
    const { height, width, x, y } = $el;
    (h = height), (w = width), (centerX = x), (centerY = y);
  } else {
    const { height, width, x, y } = $el.getLocalBounds();
    (h = height), (w = width), (centerX = x + w / 2), (centerY = y + h / 2);
  }
  return {
    centerX,
    centerY,
    w,
    h,
  };
};

const setEleSelected = ops => {
  const EditMap = getGlobalEditMap();
  const { layerName, id } = ops;
  Selected.resetAllSelected();
  EditMap.setSelected({ layerName, id });
};

//TODO api修改
const multiSearch = function (ops = []) {
  Selected.resetAllSelected();
  if (ops && ops.length) {
    this.setMultiSelectedStatus(true);
    /***
     * TODO 走两套 只有一个点 length =1
     * 有多个点 可能需要个算法 要把 所有点 汇聚在一起 计算出 一个范围 居中
     */

    if (ops.length === 1 && ops[0].ids && ops[0].ids.length === 1) {
      const singleOps = ops[0];
      const { layerName, ids } = singleOps;
      const { centerX, centerY, w, h } = getSinglePosition({ layerName, id: ids[0] });
      setEleCenter({ centerX, centerY, w, h });
      setEleSelected({ layerName, id: ids[0] });
    } else {
      const postionArr = [];
      let minX, minY, maxX, maxY, rectH, rectW, rectCenterX, rectCenterY;
      ops.forEach(op => {
        const { layerName, ids } = op;
        ids.forEach((id, index) => {
          const { centerX, centerY, w, h } = getSinglePosition({ layerName, id });

          if (index === 0) {
            minX = centerX;
            minY = centerY;
            maxX = centerX;
            maxY = centerY;
            rectH = h;
            rectW = w;
          } else {
            if (centerX < minX) minX = centerX;
            if (centerY < minY) minY = centerY;
            if (centerX > maxX) maxX = centerX;
            if (centerY > maxY) maxY = centerY;
            rectH = maxY - minY;
            rectW = maxX - minX;
          }
          rectCenterX = (minX + maxX) / 2;
          rectCenterY = (minY + minY) / 2;
          this.setSelected({ layerName, id });
        });
      });
      setEleCenter({ centerX: rectCenterX, centerY: rectCenterY, w: rectW, h: rectH });
    }
  } else {
    this.setMultiSelectedStatus(false);
  }
};
export { search, multiSearch };
