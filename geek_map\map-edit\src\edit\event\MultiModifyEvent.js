import Selected from "../selected/Selected";
import Event from "../event/Event";
import Mode from '../Mode'
import {getGlobalViewport} from "../global";
//多选
import MultiSelectedModify from '../selected/MultiSelectedModify'
import EventBus from "../eventBus/EventBus";
export default class DefaultEvent {
  static multiSelectedModify = new MultiSelectedModify()
  //判断是否为右键点击
  static addEvents(options) {
    const {selectedData} = options
    if(selectedData.length !== 1 || !selectedData[0].nodeId){
      EventBus.$emit('message',{type:'warning',text:'请选择一个批量修改对象'})
      Mode.resetMode()
      return
    }
    //设置批量修改目标
    this.multiSelectedModify.setAim(selectedData[0])
    // EventBus.$emit('message',{type:'info',text:'请选择一个批量复制对象'})
    //重置选中模式为单选
    Selected.isMultipleSelected = false
    const vp = getGlobalViewport()
    let timestamp = null
    const events =  {
      clicked:e => {
        this.multiSelectedModify.clickModify(e)
      },
      mousemove:e => {
        if(Event.activeKey === 'Shift') {
          const movePos = e.data.getLocalPosition(vp);
          this.multiSelectedModify.render(movePos)
        }
      },
      mousedown: e => {
        timestamp = Date.now()
        if(Event.activeKey === 'Shift') {
          const startPos = e.data.getLocalPosition(vp);
          this.multiSelectedModify.start(startPos)
        }
      },
      mouseup: e => {
        if(Date.now() - timestamp < 300) return
        this.multiSelectedModify.end()
      },
      keydown:e => {
        const {key} = e
        if(key === 'Escape'){
          this.multiSelectedModify.destroy()
          EventBus.$emit('keydown:Escape')
        }
        Event.activeKey = key
      },
      keyup: (e) => {
        Event.activeKey = null
      },
    }
    return events
  }
}
