<template>
  <div class="chartGroupStyle">
    <p class="title" v-if="title">{{ $t(title) }}</p>
    <div class="content">
      <div class="chartGroupItemStyle" v-for="item, index in groupList">
        <component v-if="item.type" :key="index" :is="map[item.type] || 'ChartItem'" :option="item" />
      </div>
    </div>
  </div>
</template>

<script>
import CredItem from "./number-item.vue";
import ChartItem from "./chart-item.vue";
import TableItem from "./table-item.vue";
import AnnularItem from "./chart-annular.vue"
export default {
  name: "statisticsNumberItemGroup",
  components: { CredItem, ChartItem, TableItem, AnnularItem },
  props: {
    option: {
      type: Object,
    },
  },
  data() {
    return {
      map: {
        cred: CredItem,
        table: TableItem,
        Annular: AnnularItem,
      }
    }
  },
  computed: {
    groupList() {
      return this.option?.group || [];
    },
    title() {
      return this.option?.title || ""
    }
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.chartGroupStyle {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 5px;
  border: 1px solid #eee;

  .title {
    color: #666;
    font-size: 18px;
    font-weight: 900;
    text-align: center;
  }
  
  .content {
    flex: 1;
    display: flex;
    flex-direction: row;
  }
  
  .chartGroupItemStyle {
    flex: 1;
    margin: 5px;
    overflow: hidden;
  }
}
</style>
