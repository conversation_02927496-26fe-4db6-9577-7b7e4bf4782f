<template>
  <geek-main-structure>
    <el-tabs v-model="defaultActive" class="tab-class">
      <el-tab-pane v-for="item in componentMap" :key="item.componentName" :label="$t(item.label)" :name="item.name">
        <component :is="item.componentName" />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import container from "./components/container.vue";
import tray from "./components/tray.vue";
import shelf from "./components/shelf.vue";
import robot from "./components/robot.vue";
import device from "./components/device.vue";
export default {
  components: { container, tray, shelf, robot, device },
  data() {
    return {
      defaultActive: "container",
    };
  },
  computed: {
    componentMap() {
      return [
        { name: 'container', label: 'lang.rms.fed.box', componentName: 'container' },
        { name: 'tray', label: `${this.$t('lang.rms.fed.pallet')}/${this.$t('托盘底座')}`, componentName: 'tray' },
        { name: 'shelf', label: 'lang.rms.config.group.shelf', componentName: 'shelf' },
        { name: 'robot', label: 'lang.rms.config.group.robot', componentName: 'robot' },
        { name: 'device', label: 'lang.rms.config.group.dmp', componentName: 'device' },
      ]
    }
  },
  mounted() {

  },
};
</script>
<style lang="less" scoped>
:deep(.el-tab-pane) {
  position: relative;
}
</style>
