/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerDeviceDmp implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  private dmpDevices: { [propName: code]: any } = {};
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  render(arr: Array<any>): void {
    this.repaint();
    if (!arr) return;

    const _this = this;
    const container = _this.container;
    const mapCore = _this.mapCore,
      utils = mapCore.utils;

    let item, options, device;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatDevice(item);
      device = _this.drawSprite(options);

      _this.dmpDevices[options["code"]] = device;
      container.addChild(device);
    }
  }

  update(arr: Array<any>) {
    this.render(arr);
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }
  repaint(): void {
    let dmpDevices = this.dmpDevices;
    let element;
    for (let code in dmpDevices) {
      element = dmpDevices[code];
      element && element.destroy();
    }
    this.dmpDevices = {};
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.dmpDevices = null;
  }

  init(): void {
    const utils = this.mapCore.utils;

    let container = new PIXI.Container();
    container.name = "dmpDevice";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("device");
    this.container = container;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  private drawSprite(options: mDeviceData) {
    const { code, width, height, position, iconTexture } = options;
    let sprite: any = new PIXI.Sprite(iconTexture);
    sprite.mapType = "dmpDevice";
    sprite.name = code;
    sprite.width = width;
    sprite.height = height;
    sprite.interactive = sprite.buttonMode = false;
    sprite.anchor.set(0.5, 0.5);
    sprite.position.set(position.x, position.y); // 使图片居中
    return sprite;
  }
}
export default LayerDeviceDmp;
