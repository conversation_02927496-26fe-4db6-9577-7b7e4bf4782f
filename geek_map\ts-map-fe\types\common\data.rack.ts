/* ! <AUTHOR> at 2023/04/19 */

/**
 * 固定货架接口数据类型
 * @param rackCode 唯一货架code
 * @param rackType 类型，1-单深；2-双深
 * @param lattices 货位货箱数据
 * @param radAngle 角度，0-up；90-right；180-down；270-left
 * @param location 位置数据
 * @param boxNum 货箱数量
 * @param layers 总层数
 * @param width 宽
 * @param length 长
 * @param 其他 可选
 */
type rackData = {
  rackCode: code;
  rackType: 1 | 2;
  degAngle: number;
  location: location;
  boxNum?: number;
  lattices?: any[];
  layers?: number;
  width?: number;
  length?: number;
  [propName: string]: any;
};
/**
 * 固定货架地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param floorId 楼层
 * @param rackType 类型，1-单深；2-双深
 * @param position mesh位置，length为8的数组number
 * @param hitArea xy的四个最大最小值，length为4的数组number
 * @param boxNum 货箱数量
 * @param 其他 可选
 */
type mRackData = {
  code: code;
  floorId: floorId;
  rackType: 1 | 2;
  position: meshPosition;
  hitArea: hitArea;
  boxNum?: number;
  [propName: string]: any;
};
