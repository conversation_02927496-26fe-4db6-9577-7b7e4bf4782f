/* ! <AUTHOR> at 2022/09/02 */
import * as PIX<PERSON> from "pixi.js";

class LayerRobotBox implements MRender.Layer {
  private utils: any;
  private container: PIXI.Container;
  private vColor: any;
  private vConfirmColor: any;
  private shader: any;
  private fragment: number;
  private geometries: Array<any> = [];
  private meshList: any = [];
  init(mapCore: MRender.MainCore): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "robotBox";
    container.zIndex = utils.getLayerZIndex("robot");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.vColor = utils.getShaderRGB(0xe4b370); // box vColor
    this.vConfirmColor = utils.getShaderRGB(0xef5355); // 货架vColor
    this.shader = utils.getShader("iconColor", utils.getResources("robot_box"));
    this.utils = utils;
  }

  render(): void {
    const _this = this;
    const geometries = _this.geometries;
    if (!geometries.length) return;

    const utils = _this.utils;
    const fragment = _this.fragment;
    const shader = _this.shader;

    for (let i = 0, len = Math.ceil(geometries.length / fragment); i < len; i++) {
      const arr = geometries.slice(i * fragment, i * fragment + fragment);

      let mesh = utils.createMesh(arr, shader);
      mesh.name = "robotBox";
      mesh.mapType = "robotBox";
      mesh.interactive = mesh.buttonMode = false;

      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }
    this.geometries = [];
  }

  drawGeometry(options: mRobotData): void {
    const _this = this;
    let vColor = _this.vColor;
    if (options.boxConfirm) {
      vColor = _this.vConfirmColor;
    }
    const geometry = _this.utils.drawGeometry("iconColor", options["boxPosition"], vColor);
    _this.geometries.push(geometry);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.geometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.geometries = null;
    this.utils = null;
    this.vColor = null;
    this.vConfirmColor = null;
    this.shader = null;
    this.meshList = null;
  }
}
export default LayerRobotBox;
