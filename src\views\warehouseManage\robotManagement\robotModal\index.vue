<template>
  <section>
    <geek-customize-form
      :form-config="formConfig"
      @on-query="onQuery"
      @on-reset="onReset"
      class="instance-search-form"
    />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      @row-view="rowView"
      @row-add="rowAdd"
      @row-copy="rowCopy"
      @row-edit="rowEdit"
      @row-del="rowDel"
    >
    </geek-customize-table>

    <EditDialog ref="editDialog" @updateList="getTableList" />
  </section>
</template>

<script>
import EditDialog from "./components/editDialog";
export default {
  name: "RobotModal",
  components: { EditDialog },
  data() {
    const permission = !this.isRoleGuest();
    return {
      form: {
        product: "",
        displayName: "",
        chassisModelEntName: "",
        businessModelEntName: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "150px",
          inline: true,
        },
        configs: {
          product: {
            label: "lang.rms.api.result.warehouse.robotModel",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRobotModel",
          },
          displayName: {
            label: "lang.rms.api.result.warehouse.robotModelAlias",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterRobotModeAlias",
          },
          chassisModelEntName: {
            label: "lang.rms.api.result.warehouse.ontologyModel",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterOntologyModel",
          },
          businessModelEntName: {
            label: "lang.rms.api.result.warehouse.businessModel",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterBusinessCharacteristics",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {},
        actions: [
          {
            label: "lang.rms.fed.createRobotModel",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.api.result.warehouse.robotModel", prop: "product", width: "100" },
          {
            label: "lang.rms.api.result.warehouse.robotModelAlias",
            prop: "displayName",
          },
          {
            label: "lang.rms.api.result.warehouse.ontologyModel",
            prop: "chassisModelEnt",
            formatter: (row, column, cellValue, index) => {
              if (cellValue) {
                return `${cellValue?.chassisCode || ""}(${cellValue?.name || ""})`;
              }
              return "--";
            },
          },
          {
            label: "lang.rms.api.result.warehouse.mechanismModel",
            prop: "mechanismModelEnt",
            formatter: (row, column, cellValue, index) => {
              if (cellValue) {
                return `${cellValue?.mechanismCode || ""}(${cellValue?.name || ""})`;
              }
              return "--";
            },
          },
          {
            label: "lang.rms.api.result.warehouse.businessModel",
            prop: "businessModelEnt",
            width: "160",
            formatter: (row, column, cellValue, index) => {
              if (cellValue) {
                return `${cellValue?.businessCode || ""}(${cellValue?.name || ""})`;
              }
              return "--";
            },
          },
          {
            label: "lang.rms.api.result.warehouse.protocolModel",
            prop: "protocolModelEnt",
            formatter: (row, column, cellValue, index) => {
              if (cellValue) {
                return `${cellValue?.version || ""}(${cellValue?.contentType || ""})`;
              }
              return "--";
            },
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "180",
            align: "center",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.buttonView",
                handler: "row-view",
              },
              {
                label: "lang.rms.web.map.version.copy",
                permission,
                handler: "row-copy",
              },
              {
                label: "lang.rms.fed.buttonEdit",
                permission,
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.delete",
                permission,
                handler: "row-del",
              },
            ],
          },
        ],
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      let row = {
        // name 500TurnLift
        mechanismModelEnt: {
          mechanismId: 100
        },
        // name P500L
        businessModelEnt: {
          businessModelId: 1
        },
        protocolModelEnt: {
          protocolModelId: 1
        }
      }
      this.$refs.editDialog.open("add", row);
    },
    rowView(row) {
      this.$refs.editDialog.open("view", row);
    },
    rowCopy(row) {
      this.$refs.editDialog.open("copy", row);
    },
    rowEdit(row) {
      this.$refs.editDialog.open("edit", row);
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.api.result.warehouse.willDeleteToContinue")).then(() => {
        $req.get("/athena/robot/manage/robotModelDelete", { id: row.id }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t("lang.venus.web.common.successfullyDeleted"));
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { pageSize, currentPage } = this.tablePage;
      const url = `/athena/robot/manage/modelPageList?pageSize=${pageSize}&currentPage=${currentPage}`;
      const searchData = this.form;
      const data = {
        product: searchData.product,
        displayName: searchData.displayName,
        chassisModelEnt: { name: searchData.chassisModelEntName },
        businessModelEnt: { name: searchData.businessModelEntName },
      };

      $req.post(url, data).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          pageCount: result.pageCount || 0,
          currentPage: result.currentPage || 1,
        });
      });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less" scoped>
.instance-search-form {
  padding: 5px 0;
}
</style>
