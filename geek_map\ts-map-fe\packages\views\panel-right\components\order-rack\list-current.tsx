/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { LockFilled } from "@ant-design/icons";

type PropsOrderData = {
  rackData: rackData;
  searchData: { type: "container" | "lattice" | "box"; value: string; subCode?: code };
  currentSelect: currentSelectType;
  setCurrentSelect: (data: currentSelectType) => void;
};
type currentSelectType = { type: "box" | "lattice"; latticeCodes: Array<code>; boxCode: "" } | null;

function OrderRackCurrent(props: PropsOrderData) {
  const { t } = useTranslation();
  const [lattices, setLattices] = useState<Array<any>>([]);

  const [selectData, setSelectData] = useState<currentSelectType>(null);

  // 处理 rackData
  useEffect(() => {
    const rackData = props.rackData;
    const lattices = rackData?.lattices || [];
    if (!lattices.length) {
      setLattices([]);
      setSelectData(null);
      props.setCurrentSelect(null);
      return;
    }
    formatData(rackData);
  }, [props.rackData]);

  useEffect(() => {
    if (!props.currentSelect) setSelectData(null);
  }, [props.currentSelect]);

  // 点击货格列表
  const clickSelect = (lattice: any) => {
    if (!lattice || !lattice.latticeCode) return;
    if (lattice.relateBox?.boxCode) {
      clickBox(lattice);
    } else {
      clickLattice(lattice);
    }
  };
  // 点击货位
  const clickLattice = (lattice: any) => {
    const latticeCode: code = lattice.latticeCode;

    let latticeCodes: Array<code> = selectData?.latticeCodes || [];
    if (selectData?.type !== "lattice") latticeCodes = [];

    if (latticeCodes.includes(latticeCode)) {
      latticeCodes = latticeCodes.filter(code => code !== latticeCode);
    } else {
      latticeCodes.push(latticeCode);
    }

    let data: currentSelectType = {
      type: "lattice",
      latticeCodes,
      boxCode: "",
    };
    if (!latticeCodes.length) data = null;
    setSelectData(data);
    props.setCurrentSelect(data);
  };
  // 点击货箱
  const clickBox = (lattice: any) => {
    const data: currentSelectType = {
      type: "box",
      latticeCodes: [lattice.latticeCode],
      boxCode: lattice.relateBoxCode,
    };
    setSelectData(data);
    props.setCurrentSelect(data);
  };

  const formatData = (data: rackData) => {
    const rackType = data?.rackType;
    if (![1, 2].includes(rackType)) {
      const { warn } = console;
      warn("rackType:", rackType, "。请检查数据是否正确, rackType只能是 1 或 2");
      return;
    }
    const { type, value, subCode } = props.searchData || {};
    const list = data.lattices;
    const rackCode = data.rackCode;
    const degAngle = data.degAngle;

    let _lattices: Array<any> = [];
    let item, isOuter, rowIndex, colIndex;
    for (let i = 0, len = list.length; i < len; i++) {
      item = list[i];
      isOuter = item.isOuter; // 1 代表外深（地图中占用深色格子），0 代表内深（地图中占用浅色格子）

      rowIndex = item.layer;
      if (rackType == 1) {
        colIndex = 0;
        if (!_lattices[rowIndex]) _lattices[rowIndex] = [null];
      } else {
        if (degAngle == 0 || degAngle == 270) colIndex = isOuter;
        else colIndex = isOuter ? 0 : 1;
        if (!_lattices[rowIndex]) _lattices[rowIndex] = [null, null];
      }

      _lattices[rowIndex][colIndex] = item;
      if (value !== rackCode) continue;
      switch (type) {
        case "lattice":
          if (item?.latticeCode === subCode) {
            clickSelect(item);
          }
          break;
        case "box":
          if (item?.relateBoxCode === subCode) {
            clickSelect(item);
          }
          break;
      }
    }

    setLattices(_lattices);
  };

  const getRenderList = () => {
    const layers = lattices.length;

    let nodes: any = [];
    let items: Array<any>, itemsNode: any;
    for (let i = layers - 1; i >= 0; i--) {
      items = lattices[i];
      if (!items?.length) continue;
      itemsNode = (
        <div key={i} className="rack-list-item">
          {items.map((item: any, j: number) => {
            if (!item) return <span key={j} className="no-data" />;

            return (
              <span
                key={j}
                className={[
                  item?.isOuter ? "is-outer" : "",
                  item?.relateBoxCode ? "has-box" : "",
                  selectData?.type === "lattice" && selectData.latticeCodes.includes(item.latticeCode) ? "active" : "",
                  selectData?.type === "box" && selectData.boxCode === item.relateBoxCode ? "active" : "",
                  item?.latticeFlag == "LOCKED" || item?.relateBox?.lockState === 1 ? "locked" : "",
                  item?.latticeStatus == "ALLOCATED" ? "occupy-box" : "",
                ].join(" ")}
                title={item.latticeCode || ""}
                onClick={() => clickSelect(item)}
              >
                <i className="icon-status">
                  {(item?.latticeFlag == "LOCKED" || item?.relateBox?.lockState === 1) && <LockFilled />}
                </i>
                {item?.relateBoxCode && <i className="icon-box" />}
                {item?.relateBoxCode ? item.relateBoxCode : item.latticeCode}
              </span>
            );
          })}
        </div>
      );
      nodes.push(itemsNode);
    }

    return nodes;
  };

  return (
    props.rackData?.rackCode && (
      <>
        <h3 className="rack-title">
          <span className="title-code">
            {t("lang.rms.fed.rackCode")}:<strong>{props.rackData?.rackCode || ""}</strong>
          </span>
        </h3>
        <div className="map2d-rack-list">{getRenderList()}</div>
      </>
    )
  );
}

export default OrderRackCurrent;
