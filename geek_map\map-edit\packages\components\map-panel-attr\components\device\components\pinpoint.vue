<template>
  <div class="pinpointSlotStyle">
    <el-divider />
    <div class="title">
      精准点位标注
      <el-tooltip
        effect="dark"
        :enterable="false"
        :content="$t('精准点位标注需要选择的车辆进行定位，定位完毕后获取车辆坐标实现精准定位')"
        placement="bottom"
      >
        <span class="mapFont map-font-bangzhu1 describeIcon"></span>
      </el-tooltip>
    </div>
    <!--  -->
    <el-form-item label-width="100px" label="选择在线车辆">
      <el-select-v2
        v-model="onlineVehicle"
        :options="options"
        :placeholder="$t('请选择')"
        @visible-change="selectVisibleChange"
      />
    </el-form-item>
    <div class="updateLocationCont">
      <el-button
        class="updateLocationBtn"
        type="primary"
        :disabled="!onlineVehicle"
        @click="updateLocation()"
      >
        {{ firstTimeReq ? $t("检查") : $t("重新检查") }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, Ref } from "vue";
import { storeToRefs } from "pinia";
import { getReflectorInfo, queryRobot } from "@packages/api/map";
import { useAppStore } from "@packages/store/app";
const onlineVehicle: Ref<string> = ref("");
const options: Ref<{ label: string; value: string }[]> = ref([]);
const timer: Ref<NodeJS.Timer | null> = ref(null);
const appStore = storeToRefs(useAppStore());
const firstTimeReq: Ref<boolean> = ref(true);

const selectVisibleChange = (visible: boolean) => {
  if (visible) {
    startQueryRobot();
  } else {
    stopQueryRobot();
  }
};

function getQueryRobot() {
  queryRobot({
    floorId: String(appStore.floorId?.value),
  }).then(({ data }) => {
    if (data) {
      options.value = Object.values(data).map((item: any) => {
        return {
          label: item.id,
          value: item.id,
        };
      });
    }
  });
}

function startQueryRobot() {
  timer.value = setInterval(() => {
    getQueryRobot();
  }, 1000);
}

function stopQueryRobot() {
  timer.value && clearInterval(timer.value);
}

/**
 * 反光柱检查
 */
function updateLocation(frequency: number = 10) {
  if (frequency > 0 && onlineVehicle.value) {
    getReflectorInfo({
      robotId: onlineVehicle.value,
    }).then(({ code, data }) => {
      // TOCO: 这块的逻辑我也不知道是啥...
    });

    setTimeout(() => {
      updateLocation(frequency - 1);
    }, 1000);
  } else {
    firstTimeReq.value = false;
  }
}
</script>

<style scoped lang="scss">
.title {
  font-size: 14px;
  font-weight: 900;
}

.updateLocationCont {
  text-align: center;
  padding-top: 20px;
}

.describeIcon {
  margin-right: 3px;
  color: #409eff;
}
</style>
