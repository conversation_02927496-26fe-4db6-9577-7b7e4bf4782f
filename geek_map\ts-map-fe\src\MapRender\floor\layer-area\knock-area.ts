/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerKnockArea implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  private fillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  private meshList: Array<any> = [];
  private geometries: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "knockArea";
    container.interactiveChildren = true;
    container.visible = true;
    container.alpha = 0.4;
    container.zIndex = utils.getLayerZIndex("knockArea");
    this.container = container;

    this.fillStyle.visible = true;
    this.fillStyle.color = utils.getOriginColor("KNOCK_AREA");

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  render(arr: Array<knockAreaData>): void {
    const _this = this;
    const mapCore = _this.mapCore,
      utils = mapCore.utils,
      lineStyle = _this.lineStyle,
      fillStyle = _this.fillStyle;

    const len = arr.length;
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    graphicsGeometry.BATCHABLE_SIZE = len;

    let item, options, shapeData;
    for (let i = 0; i < len; i++) {
      item = arr[i];
      options = utils.formatKnockArea(item);
      shapeData = options["shapeData"];

      const polygon = new PIXI.Polygon(shapeData);
      graphicsGeometry.drawShape(polygon, fillStyle, lineStyle);
      _this.geometries.push({ polygon, options });
    }

    const graphics: any = new PIXI.Graphics(graphicsGeometry);
    graphics.name = "knockArea";
    graphics.floorId = this.floorId;
    graphics.mapType = "knockArea";
    graphics.interactive = graphics.buttonMode = true;
    this.meshList.push(graphics);
    this.container.addChild(graphics);
  }

  update(arr: Array<knockAreaData>) {
    this.repaint();
    if (arr.length) this.render(arr);
  }

  getAreaCode(x: number, y: number) {
    const geometries = this.geometries;
    let item;
    for (let i = 0, len = geometries.length; i < len; i++) {
      item = geometries[i];
      if (item.polygon.contains(x, y)) {
        return item.options.code;
      }
    }
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.geometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.fillStyle = null;
    this.lineStyle = null;
    this.meshList = null;
    this.geometries = null;
  }
}
export default LayerKnockArea;
