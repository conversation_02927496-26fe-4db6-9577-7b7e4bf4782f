/* ! <AUTHOR> at 2023/06/09 */

export default {
  async rmsInitConfig() {
    const RMSConfigRes = await $req.reqRMSConfig();
    const RMSConfig = RMSConfigRes.data;

    if (RMSConfig.openPermissionSystem !== $utils.Data.getRMSPermission()) {
      $utils.Data.removeAllStorage();
    }
    $utils.Data.setRMSPermission(RMSConfig.openPermissionSystem);
    $utils.Data.setRMSConfig(JSON.stringify(RMSConfig));

    const feStaticConfig = await $req.reqStaticConfig();
    $utils._staticConfig = feStaticConfig;
    $utils.Tools.setFavIcon();
  },
};
