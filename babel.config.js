module.exports = {
  presets: [
    [
      "@babel/preset-env", // 会根据浏览器兼容设置自动引入babel的一些常用或必备插件
      {
        modules: false,
        useBuiltIns: "entry",
        corejs: 3,
      },
    ],
  ],
  plugins: [
    "@babel/plugin-transform-runtime", // 辅助@babel/runtime, 提供统一的模块化的helper
    "@babel/plugin-syntax-dynamic-import", // 解析识别import()动态导入语法
    [
      "component",
      {
        libraryName: "element-ui",
        styleLibraryName: "theme-chalk",
      },
    ],
  ],
};
