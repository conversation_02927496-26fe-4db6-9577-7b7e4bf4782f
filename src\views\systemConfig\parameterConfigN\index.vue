<template>
  <geek-main-structure class="parameterConfig">
    <el-tabs v-model="activeName" class="tab-class">
      <el-tab-pane
        v-if="tabNamePerssion['params']"
        :label="$t('lang.rms.fed.ParameterConfiguration')"
        name="params"
      >
        <parameter-config-iframe />
      </el-tab-pane>
      <el-tab-pane
        v-if="tabNamePerssion['library']"
        :label="$t('lang.rms.fed.configurationLibrary')"
        name="library"
      >
        <parameter-library />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import parameterConfigIframe from "./parameterConfig_iframe";
import parameterLibrary from "./parameterLibrary";
// import parameterList from "./config.js";
export default {
  components: {
    // parameterConfig,
    parameterLibrary,
    parameterConfigIframe,
  },
  data() {
    return {
      tabNamePerssion: {
        params: this.getTabPermission("TabParamsConfigureParamPage", "paramsConfigure"),
        library: this.getTabPermission("TabParamsConfigureDemoPage", "paramsConfigure"),
      },
      defaultActive: "params",
      // parameterList: {},
      // paramsConfigList: [],
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  created() {
    // console.log("parameterList", this.parameterList);
    // this.getConfigItems();
  },
  mounted() {
    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
  },
  methods: {
    getConfigItems() {
      $req.get("/athena/configs/configItems", {}, { intercept: false }).then(res => {
        // console.log(res, 3123);
        this.paramsConfigList = res;
        const obj = this.jsonFormat(res);
        this.parameterList = Object.assign({}, obj);
      });
    },
    jsonFormat(data) {
      const dataObj = {};
      let mapObj = {};
      let taskObj = {};
      let pathObj = {};
      let systemObj = {};
      let fesystemObj = {};
      data.forEach(item => {
        const path = item.path;
        if (path) {
          const pathArr = path.split("/");
          pathArr.shift();
          let firstKey = pathArr[0];
          let secondKey = pathArr[1];
          dataObj[firstKey] = null;
          let childObj = {};
          if (item.code) {
            const restrictions = item.restrictions ? JSON.parse(item.restrictions) : null;
            // console.log(item.id)
            childObj[item.code] = {
              i18nCode: item.code,
              type: item.widgetType,
              options: {
                componentName: item.widgetType,
                selectList:
                  item.widgetType === "select" && restrictions ? restrictions["options"] : [],
                defVal: item.value,
              },
              ...item,
            };
          }
          switch (firstKey) {
            case "map":
              mapObj[secondKey] = Object.assign({}, mapObj[secondKey], childObj);
              break;
            case "task":
              taskObj[secondKey] = Object.assign({}, taskObj[secondKey], childObj);
              break;
            case "path":
              pathObj[secondKey] = Object.assign({}, pathObj[secondKey], childObj);
              break;
            case "system":
              systemObj[secondKey] = Object.assign({}, systemObj[secondKey], childObj);
              break;
            case "fesystem":
              fesystemObj[secondKey] = Object.assign({}, fesystemObj[secondKey], childObj);
              break;
          }
        }
      });
      const obj = {
        map: mapObj,
        task: taskObj,
        path: pathObj,
        system: systemObj,
        fesystem: fesystemObj,
      };
      console.log(dataObj);
      return obj;
    },
  },
};
</script>
<style lang="less" scoped>
.tab-class {
  width: 100%;
  height: 100%;
}

.tab-class :deep(.el-tabs__content),
.el-tab-pane {
  width: 100%;
  height: calc(100% - 45px);
}
/deep/.el-tabs__header {
  padding-left: 15px;
  padding-top: 9px;
}
.parameterConfig {
  padding: 0px;
  overflow: hidden;
  border: 0px;
}
/deep/.el-tabs__content {
  overflow: hidden;
  position: relative;
  top: -15px;
}
</style>
