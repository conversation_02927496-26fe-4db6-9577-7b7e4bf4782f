<template>
  <geek-main-structure>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <geek-customize-table :table-config="tableConfig" :data="tableData" @row-add="rowAdd" style="margin-top: 10px">
      <template #operations="{ row }" v-if="!isRoleGuest()">
        <el-button type="text" size="small" @click="rowEdit(row)">
          {{ $t("lang.rms.fed.buttonEdit") }}
        </el-button>
        <el-popover v-model="row.visible" placement="top">
          <p style="padding: 5px 10px">
            <i class="el-icon-warning" style="color: #e6a23c" />
            {{ $t("lang.rms.fed.pleaseConfirmDeleteController") }}?
          </p>
          <div style="text-align: right; margin: 0">
            <el-button size="mini" @click="row.visible = false">
              {{ $t("lang.rms.fed.cancel") }}
            </el-button>
            <el-button type="primary" size="mini" @click="rowDel(row)">
              {{ $t("lang.rms.fed.confirm") }}
            </el-button>
          </div>
          <el-button slot="reference" type="text" @click.stop>
            {{ $t("lang.rms.fed.delete") }}
          </el-button>
        </el-popover>
      </template>
    </geek-customize-table>

    <edit-dialog ref="editDialog" @updateList="getTableList" />
  </geek-main-structure>
</template>
<script>
import editDialog from "./components/editDialog";

export default {
  name: "StopControllerManage",
  components: { editDialog },
  data() {
    return {
      form: {
        deviceId: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          deviceId: {
            label: "lang.rms.fed.controllerId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterBusinessNo",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tableConfig: {
        attrs: { index: true },
        actions: [
          {
            label: "lang.rms.fed.addController",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.fed.controllerId", prop: "deviceId" },
          { label: "lang.rms.fed.IPAdress", prop: "ip" },
          {
            label: "lang.rms.fed.boundLogicIdArea",
            prop: "referBy",
            formatter: (row, column, cellValue, index) => {
              if (!cellValue === "null") return "";
              return cellValue;
            },
          },
          { label: "lang.rms.fed.channelAndWorkstation", prop: "channelsStr" },
          {
            label: "lang.rms.fed.listOperation",
            width: "170",
            fixed: "right",
            align: "center",
            slotName: "operations",
          },
        ],
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.$refs.editDialog.open("add", {});
    },
    rowEdit(row) {
      this.$refs.editDialog.open("edit", row);
    },
    rowDel(row) {
      $req.get("/athena/baseDevice/delete", { id: row.id }).then(res => {
        if (res?.code != 0) return;
        this.getTableList();
      });
    },

    onQuery(val) {
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.form = Object.assign({}, val);
      this.getTableList();
    },

    getTableList() {
      $req.get("/athena/baseDevice/findAll", this.form).then(res => {
        if (res?.code != 0) return;
        this.tableData = res?.data || [];
      });
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>

<style lang="less" scoped></style>
