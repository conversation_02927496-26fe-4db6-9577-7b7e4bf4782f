/* ! <AUTHOR> at 2021/03 */
import { fabric } from "fabric";
class EventsDrag {
  constructor($vm) {
    this.$vm = $vm;

    this.$fabric = null;
    this.isDrag = false;
  }

  start(e) {
    if (this.$vm.actionType !== "movement") return;
    this.isDrag = true;
    this.$fabric = this.$vm.$fabric;
  }

  move(e) {
    if (this.$vm.actionType !== "movement") return;
    if (!this.isDrag) return;

    const { movementX, movementY } = e.e;
    const delta = new fabric.Point(movementX, movementY);
    this.$fabric.relativePan(delta);
  }

  end(e) {
    if (this.$vm.actionType !== "movement") return;
    this.isDrag = false;
  }
}

export default EventsDrag;
