/* ! <AUTHOR> at 2023/04/19 */

/**
 * 工作站接口数据类型
 * @param stationId 唯一工作站ID
 * @param isWorking 是否为工作状态
 * @param location 位置数据
 * @param placeDir 方向
 * @param polygon 工作站区域，如：[{x: 23.327, y: 2.549, z: 1}, {x: 23.327, y: 1.549, z: 1}, {x: 24.327, y: 1.549, z: 1},…]
 * @param stopButtonPressed 工作站急停按钮状态，true则当前工作站区域变成红色
 * @param virtualRacks 是否有虚拟货箱架
 * @param 其他 可选
 */
type stationData = {
  stationId: code;
  isWorking: boolean;
  location: location;
  placeDir: "EAST" | "WEST" | "SOUTH" | "NORTH";
  polygon?: location[];
  stopButtonPressed?: boolean;
  virtualRacks: any;
  [propName: string]: any;
};
/**
 * 工作站地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param floorId 楼层
 * @param isWorking 是否为工作状态
 * @param direction placeDir转换的角度
 * @param width 宽
 * @param height 高
 * @param position 地图位置数据
 * @param polygon 地图工作站区域
 * @param stopButtonPressed 工作站急停按钮状态，true则当前工作站区域变成红色
 * @param virtualRacks 是否有虚拟货箱架
 * @param 其他 可选
 */
type mStationData = {
  code: code;
  floorId: floorId;
  isWorking: boolean;
  direction: number;
  width: number;
  height: number;
  position: location;
  polygon: location[];
  stopButtonPressed: boolean;
  virtualRacks?: boolean;
  [propName: string]: any;
};
