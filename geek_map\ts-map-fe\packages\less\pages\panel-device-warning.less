@charset "utf-8";
@warning-bg: rgba(34, 69, 118, 0.8);
#root .map2d-left-warning-panel {
  position: absolute;
  width: 258px;
  padding: 0;
  left: 10px;
  top: 48px;
  background: @warning-bg;
  box-shadow: rgb(0, 100, 255) 0 0 10 inset;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  color: #fff;

  .map2d-robot-info {
    width: 100%;
    height: 44px;
    padding: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    &.robot-task li{
      flex: 0 0 33.333%;
      &:first-child span{
        color: inherit;
      }
    }
    li {
      flex: 0 0 16.666%;
      padding: 6px 0;
      text-align: center;
      font-size: 12px;
      position: relative;
      overflow: hidden;

      &:after {
        position: absolute;
        display: block;
        content: " ";
        font-size: 0;
        line-height: 0;
        top: 15%;
        left: 0;
        width: 1px;
        height: 70%;
        background: #8c8c8c;
      }

      label {
        display: block;
        line-height: 1.8;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      span {
        display: block;
        padding-top: 3px;
        line-height: 1;
      }

      &:first-child {
        &:after {
          width: 0;
        }

        span {
          color: #ff5e62;
        }
      }

      &:nth-child(4) {
        span {
          color: #409eff;
        }
      }
    }
  }

  .map2d-device-warning-panel {
    width: 100%;
    max-height: 120px;
    padding: 3px 8px;
    border-top: 1px solid #8c8c8c;
    .item-error {
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      color: #ff5e62;
      font-size: 13px;
      font-weight: bold;
      cursor: pointer;
      line-height: 22px;
    }
  }

  .map2d-robot-warning-panel {
    width: 100%;
    padding: 0;
    overflow: hidden;
    .ant-spin-container,
    .ant-table-body,
    .ant-table-cell,
    .ant-table.ant-table-small,
    .ant-table-header > table,
    .ant-table-header > table > thead > tr > th,
    .ant-table-body > table > tbody > tr > td > .ant-table-expanded-row-fixed:after {
      background: unset;
      border-color: #8c8c8c !important;
      box-shadow: unset;
    }
    .ant-table.ant-table-bordered > .ant-table-container {
      border: 0;
      border-radius: 0;
    }
    .ant-empty-description,
    .ant-table {
      color: #fff;
    }
    .ant-table-cell-scrollbar:not([rowspan]) {
      box-shadow: unset;
    }
    .robot-list {
      width: "100%";
      overflow: hidden;
      .ant-table-thead {
        font-size: 12px;
      }
      .ant-table-cell {
        color: #fff;
        text-align: center;
        &:nth-child(2) {
          font-size: 12px;
        }
        &.ant-table-cell-scrollbar {
          border-right: 0;
        }
      }
      .ant-table-placeholder {
        .ant-table-cell {
          border-right: 1px solid #8c8c8c;
        }
        .ant-table-expanded-row-fixed {
          &::after {
            border: 0;
          }
        }
      }

      .robot-list-row {
        .warning {
          color: rgb(255, 166, 0);
        }
        .error {
          color: red;
        }
      }
    }
  }
}
