@charset "utf-8";
@list-border-color: #ccc;
@noBox-border-color: #ddd;
@box-border-color: #eee;

.map2d-rack-drawer {
  .ant-drawer-header {
    padding: 5px;
    border-color: #ccc;
    .ant-drawer-header-title {
      justify-content: flex-end;
    }
  }
  .ant-drawer-body {
    padding: 5px 5px 10px;
  }

  .rack-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid @list-border-color;
    border-bottom: 0;
    background: rgb(64 103 151 / 90%);
    padding: 3px 3px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    color: #fff;
    .title-code {
      font-size: 13px;
      padding: 5px 0 3px 5px;
    }
    strong {
      font-weight: 600;
      padding-left: 5px;
    }
  }
}

.map2d-rack-drawer .map2d-rack-list {
  border: 1px solid @list-border-color;

  .rack-list-item {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: center;
    padding: 0 2px 3px;
    background: #fff;

    &:first-child {
      padding-top: 3px;
    }

    > span {
      position: relative;
      flex: 1;
      justify-content: center;
      align-items: center;
      border: 1px solid #ddd;
      cursor: pointer;
      margin: 0 1px;
      padding: 0 3px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 30px;
      font-size: 12px;
      line-height: 30px;
      background: #fff;
      &.is-outer {
        border-color: #42b983;
      }

      &::after {
        position: absolute;
        content: " ";
        width: 0;
        height: 0;
        line-height: 0;
        font-size: 0;
        right: 0;
        bottom: 0;
        border: 6px solid transparent;
        border-right-color: #049252;
        border-bottom-color: #049252;
        display: none;
        z-index: 999;
      }

      &.no-data {
        cursor: default !important;
        background: none !important;
        border: 0;
      }

      &.has-box {
        background: #d0f9e6;
        color: #198353;
        text-align: center;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .icon-box {
          display: inline-flex;
          width: 11px;
          height: 12px;
          background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA2xJREFUWEfFlj1sW1UYht/3XgcqFmIbFn6EOoDEgKCCCjHxIxhQGztBiKpQ4wV1gCX1dcVPCS1dAOFrMgSQIiGB3JbSSjS+LQMCiQZlQu2YMrAgoXaBOOna+p4XHZOb2q7tHhspueM97/e9z/edX2KLP26xP0YCuHr24IOxVIS0578CeFr0j2dznywPW9BQAFcXyk/GRBEwRYB39DD7VsI32cnwR1cQJ4BGFLwIoAhhveL+6UVcBDCfzYXzLhADARr14DUCRQEv3CpZL+O1qPxcbDQ2qCM3ATROvX0ntzVftxVLeHwU40a9vAvSIRBP2XgL54NfjucqX3Xn6wBYOVM6Qo/WfLuLsQfNpXPVrxPt2kLwsshDgh7rFW9BaFjNTFZOJOMbAOvmh12MQX2anah+l2hXo6AA4X0BD90qvtURow+zU9Ujrf2TBKxEwQUOaLmEix51NJ2vRhsVR8F+I84Aus/FONHYTmRz4RMdAI16oD5Jljzg6Hg+/OlGxeVpQDMSMsMYt2sz+bBV/EYH+gEkQituRMF7AGcgbRvVOIkbGsB1jfQBu0JguX07by4ANRErtegrXgExZiE3DYDAr+l8+LQ1/ftM+Rnf0y9DAdhj1TOYj6EJehy8TYnzirXYqePeTL5yct18P4C9wwK0ppXAzwKeH7T4ZHQgO1WdbdSDOQBvWW1s+OzdU5Xz7dWPBOCy6mni7emp2T9X66UdgrcEqHVj2vneDIBzsWFoq+3e0psCQOB00/CLLQNon+9eHWhEpd0Qzw59ELnMfaJJFtzNpyoXScDIfA5wJ4GDvc6BywDuGcawW9sLQMBJwpsDzaMU3hCwA8CVTD68t+MuWI3Kn0ma/r8AKV+PGPsYAY779Obi2OTpcxrCA0lukrPpXOVAB8D6ZfMRhZKA20YBkfSbR56AGavHvP6qvb4B+hvGwDUR1UwufLftX6fVP99PP+yl/H0QCgDudwRZNsAHt8dcuu7pMIg3u+L+AlEzzfjYXS/N/t4+1vdRunbunbSazYKhKRBsPR76fST/gMwlgfl2jaALnrwaU6na+O6PV3vFOz3LVxZKe0jajuxy7MgPkmrZyRvPtr7wjglbMnucpnzsg1ToXicEroGsNWMcs4eRa16nDnQna1snr7TGiFO95tcFYiQAl8Sumi0H+BdJ07QwEhkAqwAAAABJRU5ErkJggg==)
            0 100% no-repeat;
          background-size: 10px;
        }
      }

      &.occupy-box {
        background: rgba(230, 162, 60, 0.3);
      }

      &.locked {
        background: #dddddd;
        color: #666;
      }

      .icon-status {
        position: absolute;
        right: 0;
        top: 0;
        font-size: 10px;
        line-height: 10px;
        color: #666 !important;
        .anticon-inbox {
          color: #198353;
        }
      }

      &.active {
        border-color: #049252 !important;
        &::after {
          display: inline-block;
        }
      }
    }
  }
}
