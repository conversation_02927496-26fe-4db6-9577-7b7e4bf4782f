<template>
  <div class="paramsChange">
    <el-dialog
      :title="$t('lang.page.rms.menu.systemParamConfig')"
      :visible.sync="dialogVisible"
      width="30%"
      @close="cancel"
    >
      <!-- <el-alert
        :title="$t('lang.rms.fed.msgNotification')"
        type="warning"
        show-icon
        :closable="false"
      >
      </el-alert> -->
      <p class="content">{{ $t("lang.rms.fed.confirm.application") }}</p>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="apply">{{ $t("lang.rms.fed.application") }}</el-button>
        <el-button @click="cancel">{{ $t("lang.common.cancel") }}</el-button>
        <el-button @click="giveUp">{{ $t("lang.rms.fed.giveUp") }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "DialogParamsChange",
  data() {
    return {
      dialogVisible: true,
    };
  },

  mounted() {},

  methods: {
    apply() {
      this.$emit("apply");
    },
    cancel() {
      this.$emit("close");
    },
    giveUp() {
      this.$emit("giveUp");
    },
  },
};
</script>

<style lang="less" scoped>
.paramsChange /deep/.el-dialog__body {
  padding: 0px;
  margin-top: 10px;
  overflow: hidden;
}
.paramsChange /deep/ .el-dialog {
  border-radius: 12px;
}
/deep/.el-dialog__title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #323334;
}
/deep/.el-alert__title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #313234;
}
/deep/.el-alert {
  background: #fffbe6;
}
.content {
  margin: 26px 26px;
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #323334;
}
</style>
