<template>
  <div class="ui-monitor">
    <map-three-monitor
      :base-url="baseUrl"
      :model-path="modelPath"
      :token="token"
      :theme="theme"
      @redirect2D="handlerRedirect2D"
    ></map-three-monitor>
  </div>
</template>
<script>
import MapEdit3d from "@geek_map/map-edit-3d/dist/index.min.js";
import Vue from "vue";
Vue.use(MapEdit3d);

const baseUrl = process.env.NODE_ENV === "production" ? "./static/" : "/";
let modelPath = window.GeekEditor3DMapConfig;
let theme = window.GeekEditor3dGlobalConfig;

export default {
  name: "MapMonitor3D",
  data() {
    return {
      baseUrl: baseUrl,
      modelPath: modelPath,
      token: {},
      theme: theme,
    };
  },
  created() {
    this.token = $utils.Data.getToken();
  },
  methods: {
    handlerRedirect2D() {
      this.$router.push({ path: "/monitor/robotControl" });
    },
  },
};
</script>
<style lang="less" scoped>
.ui-monitor {
  width: 100%;
  height: 100%;
}
</style>
