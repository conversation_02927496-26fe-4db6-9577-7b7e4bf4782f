<template>
  <geek-main-structure>
    <geek-tabs-nav :block="true" :nav-list="permissionNavList" @select="tabsNavChange" />

    <keep-alive>
      <component
        :is="activeName"
        :language-option="languageOption"
        :table-data="tableData"
        @getTableList="getTableList"
      />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import LanguageList from "./components/languageList";
import LanguageAdd from "./components/languageAdd";

export default {
  name: "LanguageManage",
  components: { LanguageList, LanguageAdd },
  data() {
    return {
      permissionNavList: [],
      navList: [
        {
          permissionName: "TabI18nControllerManageHasPage",
          id: "LanguageList",
          text: "lang.rms.fed.languageAdded", // "已添加语言"
        },
        {
          permissionName: "TabI18nControllerManageNewPage",
          id: "LanguageAdd",
          text: "lang.rms.fed.addNewLanguage", //"添加新语言"
        },
      ],
      activeName: "",

      languageOption: [],
      tableData: [],
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "i18nControllerManage"));

    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  methods: {
    tabsNavChange(id) {
      if (this.activeName === id) return;
      this.activeName = id;
    },
    getTableList() {
      $req.get("/athena/api/coreresource/i18n/findAllLanguages").then(res => {
        let list = res?.data || [];
        list = list.map(item => {
          item.languageCode = item.code;
          item.languageName = item.name;
          return item;
        });
        this.tableData = list.filter(i => i.isDeleted === 0);
        this.languageOption = list;
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
