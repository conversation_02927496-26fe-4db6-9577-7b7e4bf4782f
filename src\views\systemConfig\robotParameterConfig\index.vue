<template>
  <geek-main-structure>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />

    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      @row-edit="rowEdit"
      style="margin-top: 10px"
    />

    <paramEditDialog ref="editDialog" @save="save" @updateMainTale="getTableList" />
  </geek-main-structure>
</template>
<script>
import paramEditDialog from "./components/dialogParamEdit";
export default {
  name: "RobotParamConfig",
  components: { paramEditDialog },
  data() {
    const isGuest = this.isRoleGuest();
    return {
      // 搜索内容
      searchData: {
        descriptionChKey: "",
        paramCodeKey: "",
        robotTypeKey: "",
      },
      form: {
        descriptionChKey: "",
        paramCodeKey: "",
        robotTypeKey: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          paramCodeKey: {
            label: "lang.rms.fed.robotParamCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          robotTypeKey: {
            label: "lang.rms.fed.robotType",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          descriptionChKey: {
            label: "lang.rms.fed.describe",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        columns: [
          {
            label: "lang.rms.fed.lineNumber",
            align: "center",
            width: "50",
            formatter: (row, column, cellValue, index) => {
              const { currentPage, pageSize } = this.tablePage;
              return (currentPage - 1) * pageSize + index + 1;
            },
          },
          {
            label: "lang.rms.fed.robotParamCode",
            prop: "paramCode",
            width: "120",
            align: "center",
          },
          {
            label: "lang.rms.fed.robotType",
            prop: "robotType",
            width: "80",
            align: "center",
          },
          {
            label: "lang.rms.fed.robotParamValueType",
            prop: "paramValueType",
            width: "120",
            align: "center",
            formatter: (row, column, cellValue, index) => {
              const paramValueTypeList = {
                0: "lang.rms.fed.robotParamValueTypeDigit",
                1: "lang.rms.fed.robotParamValueTypeBool",
                2: "lang.rms.fed.robotParamValueTypeString",
                3: "lang.rms.fed.robotParamValueTypeJson",
              };
              return this.$t(paramValueTypeList[cellValue]);
            },
          },
          {
            label: "lang.rms.fed.robotParamValueLimit",
            prop: "paramLimit",
            width: "150",
            "show-overflow-tooltip": true,
            align: "center",
          },
          {
            label: "lang.rms.fed.robotParamUnit",
            prop: "unit",
            align: "center",
            width: "100",
          },
          {
            label: "lang.rms.fed.describe",
            prop: "descrI18nCode",
            align: "center",
            formatter: (row, column, cellValue, index) => {
              return this.$t(cellValue);
            },
          },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                width: "100",
                fixed: "right",
                operations: [
                  {
                    label: "lang.rms.fed.edit",
                    handler: "row-edit",
                  },
                ],
              },
        ),
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    // 编辑
    rowEdit(row) {
      this.$refs.editDialog.open(row);
    },

    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { descriptionChKey, paramCodeKey, robotTypeKey } = this.form;

      let data = { language: localStorage.getItem("curRMSLanguage") || "zh_cn" };
      if (descriptionChKey) data.descriptionChKey = descriptionChKey;
      if (paramCodeKey) data.paramCodeKey = paramCodeKey;
      if (robotTypeKey) data.robotTypeKey = robotTypeKey;

      const pageData = "?pageSize=" + this.tablePage.pageSize + "&currentPage=" + this.tablePage.currentPage;
      $req.post("/athena/robot/paramConfig/pageList" + pageData, data).then(res => {
        let result = res.data;
        if (!result) return;
        this.tableData = result?.recordList || [];

        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result?.currentPage || 1,
          pageCount: result?.pageCount || 0,
        });
      });
    },

    // 保存
    save(data) {
      $req.post("/athena/robot/paramConfig/saveParam", data).then(res => {
        if (res.code === 0) this.getTableList();
      });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>

<style lang="less" scoped></style>
