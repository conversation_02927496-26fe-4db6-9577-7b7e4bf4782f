/** <AUTHOR> at 2021/02 */

/** 仓库信息 */
export default [
  /** ************** 地图管理/地图信息 ****************/
  {
    path: "/warehouseManage/mapManage",
    name: "mapManage",
    meta: {
      title: "auth.rms.mapManage.page",
      auth: "auth.rms.mapManage.page",
      pid: "/warehouseManage",
    },
    component: () => import("@views/warehouseManage/mapManagement"),
  },
  /** ************** 编辑地图 ****************/
  {
    path: "/warehouseManage/editMap",
    name: "singleEdit2D",
    meta: {
      title: "lang.rms.fed.editMap",
      auth: "auth.rms.mapManage.page.edit",
      pid: "/warehouseManage",
      notMenu: true,
      noPermissionGuest: true,
    },
    // component: () => import("@views/mapEdit"), // 旧版
    component: () => import("@views/Map/singleEdit2D"), // 新版
  },
  /** ************** 编辑地图背景 ****************/
  {
    path: "/warehouseManage/editMapFloorBg",
    name: "createMapBackground",
    meta: {
      title: "lang.rms.fed.editBackground",
      auth: "auth.rms.mapManage.page.editBackground",
      pid: "/warehouseManage",
      notMenu: true,
      noPermissionGuest: true,
    },
    component: () => import("@views/warehouseManage/editMapFloorBg"),
  },
  /** ************** 绘制地图 ****************/
  {
    path: "/warehouseManage/drawMapFloor",
    name: "drawMap",
    meta: {
      title: "lang.rms.fed.createAMap",
      auth: "auth.rms.mapManage.page.drawMap",
      pid: "/warehouseManage",
      notMenu: true,
      noPermissionGuest: true,
    },
    component: () => import("@views/warehouseManage/drawMapFloor"),
  },
  /** ************** 机器人信息 ****************/
  {
    path: "/warehouseManage/robotMonitor",
    name: "robotMonitor",
    meta: {
      title: "lang.rms.fed.robotInformation",
      auth: "auth.rms.robotInfo.page",
      pid: "/warehouseManage",
      noPermissionGuest: true,
    },
    component: () => import("@views/warehouseManage/robotInformation"),
  },
  /** ************** 任务管理  ****************/
  {
    path: "/warehouseManage/taskManage",
    name: "taskManage",
    meta: {
      title: "lang.rms.fed.fedTaskManage",
      auth: "auth.rms.taskManage.page",
      pid: "/warehouseManage",
      noPermissionGuest: true,
    },
    component: () => import("@views/warehouseManage/taskManage"),
  },
  /** ************** 区域分区管理 ****************/
  {
    path: "/warehouseManage/regionalManage",
    name: "regionalManage",
    meta: {
      title: "lang.rms.fed.regionalManage",
      auth: "auth.rms.regionalManage.page",
      pid: "/warehouseManage",
      noPermissionGuest: true,
    },
    component: () => import("@views/warehouseManage/regionalManage"),
  },
  /** ************** 工作站管理 ****************/
  {
    path: "/warehouseManage/stationManage",
    name: "stationManagement",
    meta: {
      title: "lang.rms.fed.fedWorkStationManage",
      auth: "auth.rms.workStationManage.page",
      pid: "/warehouseManage",
      noPermissionGuest: true,
    },
    component: () => import("@/views/warehouseManage/stationManagement"),
  },
  /** ************** 任务查询 ****************/
  {
    path: "/warehouseManage/taskSynthesis",
    name: "taskSynthesis",
    meta: {
      title: "任务查询",
      auth: "auth.rms.workStationManage.page",
      pid: "/taskSynthesis",
      noPermissionGuest: true,
      notMenu: true, 
    },
    component: () => import("@/views/warehouseManage/taskSynthesis"),
  },
  /** ************** 机器人管理 ****************/
  {
    path: "/warehouseManage/robotManage",
    name: "robotManagement",
    meta: {
      title: "lang.rms.fed.tabRobotManagement",
      auth: "auth.rms.monitor.page.robotManage",
      pid: "/warehouseManage",
      noPermissionGuest: true,
    },
    component: () => import("@/views/warehouseManage/robotManagement"),
  },
  /** ************** 充电站管理 ****************/
  {
    path: "/warehouseManage/chargerManage",
    name: "chargerManagement",
    meta: {
      title: "lang.rms.fed.tabChargingStationManagement",
      auth: "auth.rms.chargeStationInformation.page",
      pid: "/warehouseManage",
      noPermissionGuest: true,
    },
    component: () => import("@/views/warehouseManage/chargerManagement"),
  },
  /** ************** 容器管理 ****************/
  {
    path: "/warehouseManage/containerManage",
    name: "containerManage",
    meta: {
      title: "lang.rms.api.result.warehouse.containerManagement",
      auth: "auth.rms.containerManagement.page",
      pid: "/warehouseManage",
      noPermissionGuest: true,
      hasSubMenu: true, // 三级子菜单必须紧跟着二级菜单，为了不做二次循环
    },
  },
  /** ************** 容器管理--货架管理（二级菜单） ****************/
  {
    path: "/warehouseManage/shelfManage",
    name: "shelfManage",
    meta: {
      title: "auth.rms.monitor.page.shelfManage",
      auth: "auth.rms.monitor.page.shelfManage",
      pid: "/warehouseManage/containerManage",
      noPermissionGuest: true,
    },
    component: () => import("@/views/warehouseManage/shelfManage"),
  },
  /** ************** 容器管理--货箱管理（二级菜单） ****************/
  {
    path: "/warehouseManage/rackManage",
    name: "rackManage",
    meta: {
      title: "auth.rms.page.menu.boxManagement",
      auth: "auth.rms.page.menu.boxManagement",
      pid: "/warehouseManage/containerManage",
      noPermissionGuest: true,
    },
    component: () => import("@views/warehouseManage/rackManage"),
  },
  /** ************** 容器管理--托盘管理（二级菜单） ****************/
  {
    path: "/warehouseManage/palletManage",
    name: "palletManage",
    meta: {
      title: "auth.rms.operator.pallet",
      auth: "auth.rms.operator.pallet",
      pid: "/warehouseManage/containerManage",
      noPermissionGuest: true,
    },
    component: () => import("@views/warehouseManage/palletManage"),
  },
  /** ************** 容器管理--容器模型管理（二级菜单） ****************/
  {
    path: "/warehouseManage/containerModelManage",
    name: "shelfCategoryManage",
    meta: {
      title: "auth.rms.page.menu.containerModelManagement",
      auth: "auth.rms.page.menu.containerModelManagement",
      pid: "/warehouseManage/containerManage",
      noPermissionGuest: true,
    },
    component: () => import("@views/warehouseManage/containerModelManage"),
  },
];
