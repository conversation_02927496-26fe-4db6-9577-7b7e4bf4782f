/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";
import LayerShelfStatus from "./shelf-status";

class LayerShelf implements MRender.Layer {
  floorId: floorId;
  layerShelfStatus: LayerShelfStatus = new LayerShelfStatus();
  private mapCore: MRender.MainCore;
  private floor: any;
  private container: PIXI.Container;
  private hotColors: any;
  private vColor: any;
  private shader: any;
  private fragment: number;
  private meshList: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    const utils = mapCore.utils;
    this.vColor = utils.getShaderColor("SHELF"); // 货架vColor
    this.hotColors = utils.formatHotColor();
    this.shader = utils.getShader("iconColor", utils.getResources("shelf"));
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.init();
  }

  render(shelves: Array<shelfData>): void {
    const mapCore = this.mapCore,
      mapConfig: MRender.RenderConfigMain = mapCore.mapConfig,
      utils = mapCore.utils;

    const shader = this.shader;
    const isHeat = mapConfig.getLayerVisible("shelfHeat"); // 是否显示货架热度
    const heatData = mapConfig.data.getShelfHeatData(); // 货架热度数据

    const fragment = this.fragment;
    for (let i = 0, len = Math.ceil(shelves.length / fragment); i < len; i++) {
      const arr = shelves.slice(i * fragment, i * fragment + fragment);
      const meshData = this.resolveShelves(arr, isHeat, heatData);
      if (!meshData) continue;

      const { meshKey, geometries, holderGeometries, palletGeometries } = meshData;
      if (geometries.length) this.createShelfMesh(meshKey, geometries, shader);

      if (holderGeometries.length) {
        const holderShader = utils.getShader("iconColor", utils.getResources("S_HOLDER"));
        this.createShelfMesh(meshKey, holderGeometries, holderShader);
      }

      if (palletGeometries.length) {
        const palletShader = utils.getShader("iconColor", utils.getResources("X_PALLET"));
        this.createShelfMesh(meshKey, palletGeometries, palletShader);
      }
    }

    this.layerShelfStatus.render();
  }

  renderHeat(isHeat: boolean, mShelves?: Array<mShelfData>): void {
    const _this = this;
    _this.repaint("heat");
    if (!mShelves.length) return;

    const mapCore = _this.mapCore,
      mapConfig = mapCore.mapConfig,
      utils = mapCore.utils;

    const shader = this.shader;
    const heatData = mapConfig.data.getShelfHeatData(); // 货架热度数据
    const fragment = _this.fragment;
    for (let i = 0, len = Math.ceil(mShelves.length / fragment); i < len; i++) {
      const arr = mShelves.slice(i * fragment, i * fragment + fragment);
      const meshData = this.resolveShelvesData(arr, isHeat, heatData);
      if (!meshData) continue;

      const { meshKey, geometries, holderGeometries, palletGeometries } = meshData;
      if (geometries.length) this.createShelfMesh(meshKey, geometries, shader);

      if (holderGeometries.length) {
        const holderShader = utils.getShader("iconColor", utils.getResources("S_HOLDER"));
        this.createShelfMesh(meshKey, holderGeometries, holderShader);
      }

      if (palletGeometries.length) {
        const palletShader = utils.getShader("iconColor", utils.getResources("X_PALLET"));
        this.createShelfMesh(meshKey, palletGeometries, palletShader);
      }
    }
  }

  toggleLayers(): void {
    console.log("shelf 不需要这里toggleLayers 在update里做实时的layer toggle");
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(type?: "heat"): void {
    if (!type) {
      this.mapCore.meshData.shelf.delByFloorId(this.floorId);
    }
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];
    this.layerShelfStatus.repaint();
  }

  destroy(): void {
    this.repaint();
    this.layerShelfStatus.destroy();
    this.container.destroy({ children: true });
    this.layerShelfStatus = null;
    this.container = null;

    this.floorId = null;
    this.mapCore = null;
    this.floor = null;
    this.hotColors = null;
    this.vColor = null;
    this.shader = null;
    this.meshList = null;
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container(); // cell container
    container.name = "shelf";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("shelf");
    this.container = container;

    this.layerShelfStatus.init(mapCore);

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container, this.layerShelfStatus.getContainer());
  }

  private createShelfMesh(meshKey: any, geometries: any, shader: any) {
    const utils = this.mapCore.utils;
    let mesh = utils.createMesh(geometries, shader);
    mesh.name = meshKey;
    mesh.mapType = "shelf";
    mesh.interactive = mesh.buttonMode = true;
    this.meshList.push(mesh);
    this.container.addChild(mesh);
  }

  private resolveShelves(arr: Array<shelfData>, isHeat = false, heatData: shelfHeatApiData): any {
    const _this = this;
    const mapCore = _this.mapCore,
      utils = mapCore.utils,
      mapData = mapCore.mapData,
      meshData = mapCore.meshData,
      mapEvent = mapCore.mapEvent,
      hotColors = _this.hotColors;
    const shelfVColor = _this.vColor;

    let data = [];
    let geometries = [];
    let holderGeometries = [];
    let palletGeometries = [];
    let meshKey, item, options, code, vColor;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatShelf(item);
      code = options["code"];

      if (options["shelfStatus"] === "UN_BUILT" || options["shelfStatus"] === "REMOVED") {
        mapEvent.clearSelects("shelf", [code]);
        mapData.shelf.delData(code);
        continue;
      }

      if (!meshKey) meshKey = code;

      if (!isHeat) vColor = shelfVColor;
      else {
        let score = heatData[code] || 0;
        let calScore = Math.floor(score / 10);
        if (calScore > 10) calScore = 10;
        else if (calScore < 0) calScore = 0;
        vColor = hotColors[calScore];
      }

      // 锁定状态
      if (options["lockedState"] === "LOCKED") {
        _this.layerShelfStatus.drawGeometryLocked(options);
      }
      // faultShelves 状态
      if (options["shelfStatus"] === "POS_CONFIRMED") {
        _this.layerShelfStatus.drawGeometryFault(options);
      }

      let geometry = utils.drawGeometry("iconColor", options["position"], vColor);
      switch (options["shelfType"]) {
        case "S_HOLDER":
          holderGeometries.push(geometry);
          break;
        case "X_PALLET":
          // geometry = utils.drawGeometry("iconColor", options["position"], vColor);
          palletGeometries.push(geometry);
          break;
        default:
          // geometry = utils.drawGeometry("iconColor", options["position"], vColor);
          geometries.push(geometry);
          break;
      }

      mapEvent.updateSelect("shelf", options);
      data.push(options);
      mapData.shelf.setData(code, options);
    }

    if (!meshKey) return null;
    meshData.shelf.setData(meshKey, data);
    return { meshKey, geometries, holderGeometries, palletGeometries };
  }

  private resolveShelvesData(
    arr: Array<mShelfData>,
    isHeat = true,
    heatData: shelfHeatApiData,
  ): any {
    const _this = this;
    const mapCore = _this.mapCore,
      utils = mapCore.utils,
      hotColors = _this.hotColors;
    const shelfVColor = _this.vColor;

    let geometries = [];
    let holderGeometries = [];
    let palletGeometries = [];
    let meshKey, options, code, vColor;
    for (let i = 0, len = arr.length; i < len; i++) {
      options = arr[i];
      code = options["code"];
      if (!meshKey) meshKey = code;

      if (!isHeat) vColor = shelfVColor;
      else {
        let score = heatData[code] || 0;
        let calScore = Math.floor(score / 10);
        if (calScore > 10) calScore = 10;
        else if (calScore < 0) calScore = 0;
        vColor = hotColors[calScore];
      }

      let geometry = utils.drawGeometry("iconColor", options["position"], vColor);
      switch (options["shelfType"]) {
        case "S_HOLDER":
          holderGeometries.push(geometry);
          break;
        case "X_PALLET":
          palletGeometries.push(geometry);
          break;
        default:
          geometries.push(geometry);
          break;
      }
    }

    if (!meshKey) return null;
    return { meshKey, geometries, holderGeometries, palletGeometries };
  }
}
export default LayerShelf;
