/* ! <AUTHOR> at 2022/08/30 */

import axios from "axios";
import { message } from "antd";

let service = axios.create({
  baseURL: "/",
  // 是否携带cookie信息
  withCredentials: true,
  // 超时时间，单位ms
  timeout: 60000,
});
// 添加请求拦截器
service.interceptors.request.use(
  config => {
    const lang = _$utils.getLocalLang();
    if (lang) {
      config.headers["Accept-Language"] = lang;
    }
    return config;
  },
  err => {
    message.error(err?.message || "接口请求异常");
    return Promise.reject(err);
  },
);

// 添加响应拦截器
service.interceptors.response.use(
  (response: any) => {
    if (response.data.code !== 0) {
      if (response.data.code === 2) {
        _$utils.sendParentIframe({ type: "noLogin" });
      } else {
        const msg = response?.data?.msg;
        let errorMsg;
        if (msg) {
          errorMsg = _$utils.transMsgLang(msg);
        } else {
          errorMsg = "server :: " + _$utils.transMsgLang("lang.rms.api.result.error");
        }
        message.error(errorMsg);
      }
    }
    return response;
  },
  err => {
    // 对响应错误做点什么
    message.error(err?.message || "接口响应异常");
    return Promise.reject(err);
  },
);

export default service;
