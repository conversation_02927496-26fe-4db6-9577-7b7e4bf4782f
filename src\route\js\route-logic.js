/* ! <AUTHOR> at 2021/02 */
import store from "@store";
import MenuLogic from "./menu-logic";

export default {
  /**
   * 单点登录check
   * @returns {Promise<void>}
   */
  async ssoCheck(sessionId) {
    const ssoRes = await $req.reqRMSSsoCheck(sessionId);
    const { currentCred, userName, roleNames, menuList } = ssoRes.data;
    $utils.Data.loginResetStorage();
    $utils.Data.setToken(currentCred);
    $utils.Data.setUserInfo(userName);
    $utils.Data.setRoleInfo(roleNames);
    if (menuList && menuList.length > 0) {
      $utils.Data.setMenuList(JSON.stringify(menuList));
    }
  },

  /**
   * 权限模式路由逻辑
   * @param to
   * @param next
   * @returns {Promise<void>}
   */
  async permissionRouter(to, next) {
    if (to.path === "/monitor/robotControl") {
      await $req.checkToken();
      next();
    } else {
      if (!to.meta.auth) {
        next();
      } else {
        if (store.state.menuAuth.indexOf(to.meta.auth) !== -1) {
          next();
        } else {
          next({ path: "/404", replace: true });
        }
      }
    }
  },

  /**
   * 无权限模式路由逻辑
   * @param to
   * @param next
   * @returns {Promise<void>}
   */
  async noPermissionRouter(to, next) {
    const roleInfo = $utils.Data.getRoleInfo();
    if (roleInfo === "guest") {
      if (to.meta.mustPermission || !to.meta.noPermissionGuest) next({ path: "/404", replace: true });
      else next();
    } else {
      if (to.meta.mustPermission) next({ path: "/404", replace: true });
      else next();
    }
  },

  /**
   * 权限模式下获取左侧菜单，并store commit menuList
   */
  resolvePermissionMenuList(router) {
    const menuList = $utils.Data.getMenuList() || [];
    const { menuCodes, btnCodes } = MenuLogic.getPermissionCodes(menuList);

    const layoutRoutes = router.options.routes.find(item => item.name === "layout");
    const menuRoutes = layoutRoutes.children.filter(
      item => !item.meta.auth || (!item.meta.notMenu && menuCodes.indexOf(item.meta.auth) !== -1),
    );
    const rootMenu = menuRoutes.filter(item => item.meta.icon);

    const asideMenu = MenuLogic.getPermissionAsideMenu(menuRoutes, rootMenu);

    const { commit } = store;
    commit("setMenuList", asideMenu);
    commit("setMenuAuth", menuCodes);
    commit("setBtnAuth", btnCodes);
  },

  /**
   * 无权限模式下获取左侧菜单，并store commit menuList
   */
  resolveNoPermissionMenuList(router) {
    const roleInfo = $utils.Data.getRoleInfo();

    const layoutRoutes = router.options.routes.find(item => item.name === "layout");
    const menuRoutes = layoutRoutes.children.filter(item => !item.meta.notMenu && !item.meta.mustPermission);

    if (roleInfo === "guest") {
      const rootMenu = menuRoutes.filter(item => item.meta.icon && item.meta.noPermissionGuest);
      const asideMenu = MenuLogic.getNoPermissionGuestAsideMenu(menuRoutes, rootMenu);
      store.commit("setMenuList", asideMenu);
    } else {
      const rootMenu = menuRoutes.filter(item => item.meta.icon);
      const asideMenu = MenuLogic.getNoPermissionAdminAsideMenu(menuRoutes, rootMenu);
      store.commit("setMenuList", asideMenu);
    }
  },

  resolveHeaderTabs(to) {
    const meta = to.meta;
    if (!meta || meta.notMenu) return;
    const { commit } = store;
    commit("addHeaderTabs", { title: meta.title, path: to.path });
  },

  /**
   * license验证弹窗
   */
  licenseErrorTip(to) {
    setTimeout(() => {
      const errorMsg = localStorage.getItem("errorMsg");
      if (to.path !== "/" && to.path !== "/login" && errorMsg) {
        $app.$message({
          type: "error",
          message: errorMsg,
          showClose: true,
          duration: 1000,
        });
      }
    }, 50);
  },
};
