/* ! <AUTHOR> at 2021/01 */
const path = require("path");
const portfinder = require("portfinder"); // 用于获取端口，防止本地开发环境启用多个实例端口冲突
const webpack = require("webpack");
const { merge } = require("webpack-merge"); // 用于合并webpack配置
const webpackBaseConfig = require("./webpack.base.config");
const {
  API_URL,
  PORT,
  publicPath,
  MAP2D_URL,
  singleEdit2D_URL,
} = require("../config/_conf/build.config");
module.exports = new Promise((resolve, reject) => {
  portfinder.basePort = PORT;
  portfinder.getPort(function (err, port) {
    if (err) {
      reject(err);
    } else {
      resolve(
        merge(webpackBaseConfig, {
          mode: "development",
          devtool: "cheap-source-map",
          cache: true,
          output: { publicPath },
          resolve: { symlinks: true },
          target: "web", // webpack5需要添加此属性实现HRM
          devServer: {
            static: { directory: path.resolve("./dist") },
            client: { logging: "error" },
            open: [`http://localhost:${port}/`],
            port,
            hot: true,
            host: "0.0.0.0",
            proxy: {
              // rms 接口代理
              "/athena": {
                target: API_URL,
                changeOrigin: true,
                secure: false,
                onProxyRes(proxyRes, req, res) {
                  let url = proxyRes.headers.location;
                  if (proxyRes.statusCode === 302 && url.indexOf(API_URL) !== -1) {
                    let start = API_URL.length;
                    if (API_URL.lastIndexOf("/") !== start - 1) start += 1;
                    proxyRes.headers["location"] = `http://localhost:${port}/${url.substr(start)}`;
                  }
                },
              },
              // rms 监控2d地图代理
              "/map-fe-2d": {
                target: MAP2D_URL,
                changeOrigin: true,
                secure: false,
              },
              // rms 地图编辑代理
              "/singleEdit2D": {
                target: singleEdit2D_URL,
                changeOrigin: true,
                secure: false,
              },
            },
          },
          plugins: [new webpack.ProgressPlugin()],
        }),
      );
    }
  });
});
