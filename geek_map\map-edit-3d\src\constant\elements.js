// 单元格空隙，和2D保持一致
export const CELL_SPACE = 0.1;
// 地板空隙 默认扩展10m间距
export const FLOOR_SPACE = 10;

export const CELL_MATERIAL = {};

//  模型默认朝向南；0弧度； Z轴负方向是北；
export const ANGLE = {
  SOUTH: 0,
  NORTH: Math.PI,
  WEST: -Math.PI / 2,
  EAST: Math.PI / 2,
};

export const MODEL_TYPE = ["station", "charger", "conveyor"];

export const MODEL_GROUP_PREFIX = "Model-Group";

// 阻塞点， 和 电梯点不渲染 - 来自历史遗留
export const UN_RENDER_CELL_TYPE_ARR = ["NULL_CELL", "BLOCKED_CELL"];

// export const CUSTOM_MODEL_TYPE = ["station", "conveyor"];
