/* ! <AUTHOR> at 2023/01/31 */
const log4js = require("log4js");
const execSync = require("child_process").execSync; // 同步子进程
const fs = require("fs");

const CONF_ENV = process.env.CONF_ENV || ""; // 环境变量
let logger = log4js.getLogger("version.conf");
logger.level = "debug";

function formatDate(date, fmt) {
  const o = {
    "M+": date.getMonth() + 1, // 月份
    "d+": date.getDate(), // 日
    "h+": date.getHours(), // 小时
    "m+": date.getMinutes(), // 分
    "s+": date.getSeconds(), // 秒
    "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  for (const k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length),
      );
    }
  }
  return fmt;
}

function getBranchInfo() {
  const rmsBranch = execSync("git rev-parse --abbrev-ref HEAD").toString().replace(/\n$/g, "");
  return {
    branch: rmsBranch,
    date: new Date(execSync("git show -s --format=%cd").toString()),
    commitId: execSync("git show -s --format=%h").toString().trim(), // commitId
    auth: execSync("git show -s --format=%cn").toString().trim(),
    message: execSync("git show -s --format=%s").toString().trim(),
  };
}

function autoVersion() {
  if (CONF_ENV != "pro") {
    logger.warn("现在是开发环境，不构建version信息");
    return;
  }
  const gitInfo = getBranchInfo();
  const branch = gitInfo["branch"].substring(1);
  const config = {
    tag: `athena-fe-${branch}`,
    commitId: gitInfo["commitId"],
    date: formatDate(gitInfo["date"], "yyyy-MM-dd hh:mm"),
    auth: gitInfo["auth"],
    message: gitInfo["message"],
  };

  const VERSION_CONF_PATH = "./static/configs/version.config.json";
  if (fs.existsSync(VERSION_CONF_PATH)) fs.unlinkSync(VERSION_CONF_PATH); // 如果已存在此文件，则删除
  fs.writeFileSync(VERSION_CONF_PATH, JSON.stringify(config, null, "\t"));
  logger.info("git版本信息构建完成!");
}

module.exports = autoVersion;
