class Event {
  constructor() {
    this.$dom = document;
    this.componentList = {};
    this.exclusiveName = "";
    this.isPause = false;
  }
  init(dom) {
    this.$dom = dom;
    this._initHandle();
    this._addEvent();
  }
  /**
   *
   * @param {Object} component 事件组件
   *
   * 组件hooks：
   * 初始化 addHandle
   * 按下 downHandle
   * 移动 moveHandle
   * 抬起 upHandle
   * 点击 clickHandle
   * 移出画布 outsideHandle
   * 键盘按下 keydownHandle
   * 键盘抬起 keyupHandle
   * 结束 offHandle
   * 拖拽hover dragoverHandle
   * 拖拽结束 dropHandle
   *
   */
  add(name, component) {
    this.componentList[name] = component;
  }
  off(name) {
    this.exclusiveName = "";
    if (!name) {
      this.componentList = {};
    } else {
      delete this.componentList[name];
    }
  }

  // 排除其他事件回调，只执行名为exclusiveName的回调，传空为全部执行
  exclusive(name) {
    if (!!name) {
      // 防止粘连操作
      // setTimeout(() => {
      this.exclusiveName = name;
      // }, 200)
    } else {
      this.exclusiveName = name;
    }
  }

  pause() {
    this.isPause = true;
  }
  resume() {
    // 预防粘连操作
    setTimeout(() => {
      this.isPause = false;
    }, 200);
  }

  _addEvent() {
    this.$dom.addEventListener("mousedown", this._downHandle);
    this.$dom.addEventListener("mousemove", this._moveHandle);
    this.$dom.addEventListener("dragover", this._dragoverHandle);
    this.$dom.addEventListener("mouseup", this._upHandle);
    this.$dom.addEventListener("drop", this._dropHandle);
    this.$dom.addEventListener("mouseupoutside", this._outsideHandle);
    this.$dom.addEventListener("click", this._clickHandle);
    this.$dom.addEventListener("rightclick", this._rightClickHandle);

    document.addEventListener("keydown", this._keydownHandle);
    document.addEventListener("keyup", this._keyupHandle);

    this.$dom.addEventListener("contextmenu", this._rightClickHandle);

    if (this.component && this.component.addHandle) {
      this.component.addHandle();
    }
  }

  _offEvent() {
    this.$dom.removeEventListener("mousedown", this._downHandle);
    this.$dom.removeEventListener("mousemove", this._moveHandle);
    this.$dom.removeEventListener("dragover", this._dragoverHandle);
    this.$dom.removeEventListener("mouseup", this._upHandle);
    this.$dom.removeEventListener("drop", this._dropHandle);
    this.$dom.removeEventListener("mouseupoutside", this._outsideHandle);
    this.$dom.removeEventListener("rightclick ", this._rightClickHandle);
    this.$dom.removeEventListener("click ", this._clickHandle);

    document.removeEventListener("keydown", this._keydownHandle);
    document.removeEventListener("keyup", this._keyupHandle);

    this.$dom.removeEventListener("contextmenu", this._rightClickHandle);

    this._runHandle("offHandle", "");

    this.componentList = {};
  }

  _initHandle() {
    this._downHandle = this.__downHandle.bind(this);
    this._moveHandle = this.__moveHandle.bind(this);
    this._upHandle = this.__upHandle.bind(this);
    this._outsideHandle = this.__outsideHandle.bind(this);
    this._rightClickHandle = this.__rightClickHandle.bind(this);
    this._keydownHandle = this.__keydownHandle.bind(this);
    this._keyupHandle = this.__keyupHandle.bind(this);
    this._clickHandle = this.__clickHandle.bind(this);
    this._dropHandle = this.__dropHandle.bind(this);
    this._dragoverHandle = this.__dragoverHandle.bind(this);
  }

  __downHandle(event) {
    if (this.isPause) return;
    // console.log("downHandle_event.button", event);
    this._runHandle("downHandle", event);
  }
  __clickHandle(event) {
    if (this.isPause) return;
    // console.log("clickHandle_event.button", event);
    event.preventDefault && event.preventDefault();
    this._runHandle("clickHandle", event);
  }
  __moveHandle(event) {
    if (this.isPause) return;
    this._runHandle("moveHandle", event);
  }

  __upHandle(event) {
    if (this.isPause) return;
    this._runHandle("upHandle", event);
  }
  __outsideHandle(event) {
    if (this.isPause) return;
    this._runHandle("outsideHandle", event);
  }
  __rightClickHandle(event) {
    console.log(event, this.isPause, 12345);
    if (this.isPause) return;

    event.preventDefault && event.preventDefault();
    // if(event.button===2){
    this._runHandle("rightClickHandle", event);
    // }
  }
  __keydownHandle(event) {
    if (this.isPause) return;
    this._runHandle("keydownHandle", event);
  }
  __keyupHandle(event) {
    if (this.isPause) return;
    this._runHandle("keyupHandle", event);
  }
  __dropHandle(event) {
    if (this.isPause) return;
    this._runHandle("dropHandle", event);
  }
  __dragoverHandle(event) {
    if (this.isPause) return;
    event.preventDefault();
    this._runHandle("dragoverHandle", event);
  }

  _runHandle(type, event) {
    if (!!this.exclusiveName) {
      let component = this.componentList[this.exclusiveName];
      if (component && component[type]) component[type](event);
    } else {
      for (const name in this.componentList) {
        if (this.componentList.hasOwnProperty(name)) {
          const component = this.componentList[name];
          if (component && component[type]) component[type](event);
        }
      }
    }
  }
}

export default new Event();
