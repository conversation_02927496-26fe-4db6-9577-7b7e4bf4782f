{"name": "map-edit-3d", "version": "0.0.1", "description": "3d地图渲染core", "main": "dist/index.min.js", "types": "index.d.ts", "scripts": {"build": "node --max-old-space-size=8192 node_modules/rollup/dist/bin/rollup -c", "lint": "eslint  --resolve-plugins-relative-to . ./src --ext .vue,.js,.ts ", "lint-fix": "eslint  --resolve-plugins-relative-to . --fix ./src --ext .vue,.js,.ts "}, "repository": {"type": "git", "url": "http://gitlab.geekplus.cc/rms-fed/map-edit-3d.git"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,vue}": "eslint --resolve-plugins-relative-to . --fix"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@babel/core": "^7.12.1", "@babel/plugin-transform-runtime": "^7.12.10", "@babel/preset-env": "^7.15.4", "@babel/runtime": "^7.12.5", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-commonjs": "^15.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-url": "^7.0.0", "@surma/rollup-plugin-off-main-thread": "^1.4.2", "@types/three": "^0.134.0", "@vue/compiler-sfc": "^3.2.33", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.1.4", "husky": "^7.0.4", "lint-staged": "^11.2.3", "node-sass": "^7.0.3", "postcss-import": "^15.0.0", "prettier": "^2.4.1", "process": "^0.11.10", "rollup": "^2.31.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-vue": "^5.1.9", "rollup-plugin-web-worker-loader": "^1.6.1", "sass-loader": "^13.0.2", "stats.js": "^0.17.0", "vue-template-compiler": "^2.6.12", "webpack": "^5.74.0", "webpack-cli": "^4.10.0"}, "dependencies": {"@tweenjs/tween.js": "^18.6.4", "element-ui": "^2.15.10", "flatbush": "^4.0.0", "intersects": "^2.7.2", "js-md5": "^0.7.3", "mitt": "^3.0.0", "three": "^0.135.0", "tween.js": "^16.6.0", "uuid": "^8.3.2", "vue": "^2.6.12"}}