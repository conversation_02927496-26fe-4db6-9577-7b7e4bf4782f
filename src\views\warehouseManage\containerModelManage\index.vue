<template>
  <geek-main-structure>
    <el-tabs v-model="activeName" class="tab-class">
      <!-- 容器类别管理 -->
      <el-tab-pane
        v-if="tabNamePerssion['categoryManage']"
        :label="$t('lang.rms.web.container.containerCategoryManagement')"
        name="categoryManage"
      >
        <categoryManage />
      </el-tab-pane>
      <!-- 货架模型 -->
      <el-tab-pane
        v-if="tabNamePerssion['containerModel']"
        :label="$t('lang.rms.web.container.containerModelManagement')"
        name="containerModel"
        style="position: relative"
      >
        <containerModel />
      </el-tab-pane>
    </el-tabs>
  </geek-main-structure>
</template>

<script>
import categoryManage from "./categoryManage";
import containerModel from "./containerModel";
export default {
  components: { categoryManage, containerModel },
  data() {
    return {
      tabNamePerssion: {
        categoryManage: this.getTabPermission("TabShelfCategoryPage", "containerModelManage"),
        containerModel: this.getTabPermission("TabModelPage", "containerModelManage"),
      },
      defaultActive: "categoryManage",
    };
  },
  computed: {
    activeName: {
      get() {
        return $utils.Tools.getDefaultActive(this.defaultActive, this.tabNamePerssion);
      },
      set(newValue) {
        this.defaultActive = newValue;
      },
    },
  },
  mounted() {
    this.defaultActive = $utils.Tools.getRouteQueryTabName(
      this.defaultActive,
      this.tabNamePerssion,
    );
  },
};
</script>
<style lang="less" scoped>
:deep(.el-tab-pane) {
  position: relative;
}
</style>
