/* ! <AUTHOR> at 2022/09/08 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseOutlined, InfoCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { Card, Tag, Button, Input, Select } from "antd";
import { getMap2D } from "../../../../singleton";
import OrderPanelGrid from "../common/order-panel-grid";

const { Option } = Select;
type PropsOrderData = {
  visible: boolean;
  shelfCode: code;
  radAngle: number;
  cellData: cellData;
  onCancel: () => void;
};
function ShelfUpdate(props: PropsOrderData) {
  const { t } = useTranslation();
  const [isEmpty, setIsEmpty] = useState(false);
  const [cellCode, setCellCode] = useState("");
  const [shelfAngle, setShelfAngle] = useState("");
  const [location, setLocation] = useState(null);
  const [startBounds, setStartBounds] = useState(null);

  // 地图点击 地图切换可点击层
  useEffect(() => {
    if (!props.visible) return;
    const map2D = getMap2D();
    map2D.mapRender.enableMultiClick(true);
    map2D.mapRender.triggerLayers(["cell"]);
    setShelfAngle(props.radAngle.toString());

    return () => {
      setIsEmpty(false);
      setCellCode("");
      setLocation(null);
      setStartBounds(null);
      setShelfAngle("");
      map2D.mapRender.enableMultiClick(false);
      map2D.mapRender.clearSelects("cell");
      map2D.mapRender.triggerLayers(["shelf"]);
    };
  }, [props.visible]);

  // cellCode 变化
  useEffect(() => {
    const cellData = props.cellData;
    const newCellCode = cellData?.cellCode || "";

    if (newCellCode !== cellCode) {
      const map2D = getMap2D();
      map2D.mapRender.clearSelects("cell", [cellCode]);
      setCellCode(newCellCode);
      setLocation(cellData?.location || null);
      setStartBounds(cellData?.startBounds || null);
    }
  }, [props.cellData]);

  const controlHandler = () => {
    if (!cellCode) {
      setIsEmpty(true);
      return;
    }

    const reqMsg = "ShelfInstructionRequestMsg";
    const resMsg = "ShelfInstructionResponseMsg";
    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket(reqMsg, {
        shelfCode: props.shelfCode,
        instruction: "UPDATE_SHELF_LOCATION",
        destCode: cellCode,
        shelfAngle,
      })
      .then(res => {
        if (res.msgType !== resMsg) return;
        _$utils.wsCmdResponse(res?.body || {});
      });

    props.onCancel();
  };

  return (
    props.visible && (
      <Card
        size="small"
        type="inner"
        title={t("lang.rms.fed.update")}
        extra={<CloseOutlined onClick={props.onCancel} />}
        actions={[
          <Button
            type="primary"
            onClick={controlHandler}
            size="small"
            style={{ float: "right", marginRight: 8 }}
          >
            {t("lang.rms.fed.confirm")}
          </Button>,
        ]}
        className="component-operate-detail"
      >
        <Tag
          icon={<InfoCircleOutlined />}
          color="processing"
          style={{ marginLeft: 8, marginTop: 8 }}
        >
          {t("lang.rms.fed.selectPointOnTheMap")}
        </Tag>
        <OrderPanelGrid
          style={{ borderRight: 0, borderLeft: 0, marginBottom: 0 }}
          items={[
            {
              label: t("lang.rms.fed.targetCoding"),
              node: (
                <Input
                  size="small"
                  readOnly={true}
                  value={cellCode}
                  placeholder={t("lang.rms.fed.targetCoding")}
                />
              ),
            },
            {
              label: t("lang.rms.fed.shelfAngle"),
              node: (
                <Select
                  style={{ width: "100%" }}
                  value={shelfAngle}
                  onChange={value => setShelfAngle(value)}
                >
                  <Option value="0">0</Option>
                  <Option value="90">90</Option>
                  <Option value="180">180</Option>
                  <Option value="-90">-90</Option>
                </Select>
              ),
            },
            {
              label: t("lang.rms.fed.textAbsoluteCoordinate"),
              node: `${location?.x || "--"},${location?.y || "--"}`,
            },
            {
              label: t("lang.rms.fed.textIndexCoordinates"),
              node: `${startBounds?.x || "--"},${startBounds?.y || "--"}`,
            },
          ]}
        />

        <Tag
          icon={<CloseCircleOutlined />}
          color="error"
          style={{ marginLeft: 8, display: isEmpty ? "inline-block" : "none" }}
        >
          {t("lang.rms.fed.nodeCannotBeEmpty")}
        </Tag>
      </Card>
    )
  );
}

export default ShelfUpdate;
