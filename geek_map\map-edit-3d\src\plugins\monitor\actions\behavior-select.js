import globalConfig from "../../../config";
import { lookForModelGroup } from "../../../utils/utils";
class BehaviorSelect {
  constructor(options) {
    this.monitor = options.monitor;
    this.dispatch = options.dispatch || {
      selectEvent() {},
      unSelectEvent() {},
      clearSelectEvent() {},
    };
    this.actionType = "box"; // box-货箱 lattice-货位 robot-机器人 cell-单元格 station-工作站
    this.config = {
      selectColor: globalConfig.THEME.SELECTED,
    };
    this.actionTypeMesh = {};
    this.preSelect = null;
    this.curSelect = null;
  }
  select(event) {
    const { Map3d } = this.monitor;
    if (!this.actionTypeMesh[this.actionType]) return;
    let meshs = this.actionTypeMesh[this.actionType].filter(mesh => mesh.visible);
    if (!meshs || !meshs.length) return;
    const result = Map3d.search.get(meshs, event);
    this.preSelect = this.curSelect;
    this.curSelect = result;
    this.__handlerSelect();
  }
  selectByCode(data) {
    this.preSelect = this.curSelect;
    this.curSelect = data;
    this.__handlerSelect();
  }
  trigger() {
    this.__handlerSelect();
  }
  clearSelect() {
    this.preSelect = this.curSelect;
    this.curSelect = null;
    this.__handlerSelect();
    this.preSelect = null;
    this.dispatch.clearSelectEvent && this.dispatch.clearSelectEvent(this.actionType);
  }
  destory() {
    this.actionTypeMesh = {};
    this.preSelect = null;
    this.curSelect = null;
    this.actionType = "box";
  }
  addMesh(actionType, selectMeshs) {
    this.actionTypeMesh[actionType] = selectMeshs;
  }
  getCurSelectUniqKey() {
    if (!this.curSelect || !this.curSelect.data || !this.curSelect.mesh) return false;
    if (this.actionType === "box" || this.actionType === "lattice")
      return this.curSelect.data[0].latticeCode;
    if (this.actionType === "cell")
      return !this.curSelect.data.length ? false : this.curSelect.data[0].cellCode;
    if (this.actionType === "robot")
      return lookForModelGroup(this.curSelect.mesh, "Model-Group-ROBOT");
    if (this.actionType === "station")
      return lookForModelGroup(this.curSelect.mesh, "Model-Group-STATION");
  }
  __handlerBoxSelect(ov, nv, color) {
    let data = null;
    ov && ov.data && this.monitor.rack.cancelBoxsColor(ov.data[0].latticeCode, color);
    if (nv && nv.data) {
      let code = nv.data[0].latticeCode;
      this.monitor.rack.selectBoxs(code, color);
      const lattice = this.monitor.rack.searchByLatticeCode(code);
      if (!lattice.relateBox) return;
      data = Object.assign(lattice.relateBox, { layer: lattice.layer });
    }
    return data;
  }
  __handlerLatticeSelect(ov, nv, color) {
    let data = null;
    ov && this.monitor.rack.cancelLatticesColor(ov.data[0].latticeCode, color);
    if (nv && nv.data) {
      let code = nv.data[0].latticeCode;
      this.monitor.rack.selectLattices(code, color);
      data = this.monitor.rack.searchByLatticeCode(code);
    }
    return data;
  }

  __handlerCellSelect(ov, nv, color) {
    let data = null;
    const floor = this.monitor.Map3d.floor;
    ov && floor.cancelCellColor(ov.data[0], color);
    if (nv) {
      floor.selectCell(nv.data[0], color);
      data = nv.data[0];
    }
    return data;
  }
  __handlerRobotSelect(ov, nv, color) {
    let data = null;
    ov &&
      ov.mesh &&
      this.monitor.robot.cancelSelectRobot(lookForModelGroup(ov.mesh, "Model-Group-ROBOT"), color);
    if (nv && nv.mesh) {
      const mesh = lookForModelGroup(nv.mesh, "Model-Group-ROBOT");
      this.monitor.robot.selectRobot(mesh, color);
      data = this.monitor.Store.findModelByUuid("ROBOT", mesh.userData.uuid);
    }
    return data;
  }
  __handlerStationSelect(ov, nv, color) {
    let data = null;
    ov &&
      ov.mesh &&
      this.monitor.station.cancelSelectStation(
        lookForModelGroup(ov.mesh, "Model-Group-STATION"),
        color,
      );
    if (nv && nv.mesh) {
      const mesh = lookForModelGroup(nv.mesh, "Model-Group-STATION");
      this.monitor.station.selectStation(mesh, color);
      data = this.monitor.Store.findModelByUuid("STATION", mesh.userData.uuid);
    }
    return data;
  }
  __handlerSelect() {
    const nv = this.curSelect,
      ov = this.preSelect;
    const color = this.config.selectColor;
    let data = null;
    if (this.actionType === "box") {
      data = this.__handlerBoxSelect(ov, nv, color);
    }
    if (this.actionType === "lattice") {
      data = this.__handlerLatticeSelect(ov, nv, color);
    }
    if (this.actionType === "cell") {
      data = this.__handlerCellSelect(ov, nv, color);
    }
    if (this.actionType === "robot") {
      data = this.__handlerRobotSelect(ov, nv, color);
    }
    if (this.actionType === "station") {
      data = this.__handlerStationSelect(ov, nv, color);
    }
    ov && (ov.mesh || ov.data) && this.dispatch.unSelectEvent(this.actionType, ov.data);
    nv && (nv.mesh || nv.data) && this.dispatch.selectEvent(this.actionType, data);
    if (!nv) {
      this.dispatch.clearSelectEvent && this.dispatch.clearSelectEvent(this.actionType);
    }
  }
}

export default BehaviorSelect;
