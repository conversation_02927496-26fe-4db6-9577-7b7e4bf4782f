import globalConfig from "../../../../config";
import * as THREE from "three";

class PoppickStation {
  constructor(Map3d) {
    this.Map3d = Map3d;
    this.config = {
      boxColor: globalConfig.THEME.CRACK_BOX,
      latticeColor: globalConfig.THEME.CRACK_LATTICE,
      boxH: 0.2,
      latticeH: 0.02,
      latticeL: 0.3,
      latticeW: 0.4,
      h: 0.6,
    };
  }
  getStationMesh(item) {
    return this.Map3d.modelInstances[item.uuid].model.children[0];
  }
  afterRender(item) {
    const model = this.Map3d.modelInstances[item.uuid].model;
    model.rotation.y = -item.radAngle;
    this.__renderLattices(model, item);

    // hover
    const body = model.getObjectByName("Scene");
    const box3 = new THREE.Box3().setFromObject(body);
    const hover = new THREE.Box3Helper(box3, globalConfig.THEME.HOVER_3D);
    hover.name = "hover";
    hover.visible = false;
    body.parent.add(hover);
    // select
    const select = new THREE.Box3Helper(box3, globalConfig.THEME.SELECTED);
    select.name = "select";
    select.visible = false;
    body.parent.add(select);

    model.children[1].instanceMatrix.needsUpdate = true;
  }
  update(oldValue, newVal) {
    const model = this.Map3d.modelInstances[oldValue.uuid].model;
    const latticesMesh = model.getObjectByName("lattices");
    const lattices = newVal.virtualRacks?.stationRackLattices;
    if (!lattices) return;
    for (let row in lattices) {
      const layerLattices = lattices[row];
      for (let j = 0, latt; (latt = layerLattices[j++]); ) {
        this.__changeBoxVisible(latticesMesh, row, j - 1, latt);
      }
    }
    latticesMesh.instanceMatrix.needsUpdate = true;
  }
  __renderLattices(group, item) {
    const isRenderLattices = group.userData.isRenderLattices;
    if (isRenderLattices || !item.virtualRacks || !item.virtualRacks.stationRackLattices) return;
    const lattices = item.virtualRacks.stationRackLattices;
    const matrix = new THREE.Matrix4();
    const color = new THREE.Color();
    const count = Object.values(lattices).reduce((pre, cur) => pre + cur.length * 2, 0);
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({
      color: 0xffffff,
      transparent: true,
    });
    const mesh = new THREE.InstancedMesh(geometry, material, count);
    let startIndex = -1;
    let seq = {};
    const { boxH, latticeH, latticeL, latticeW, h, latticeColor, boxColor } = this.config;
    for (let row in lattices) {
      const layer = lattices[row];
      const baseH = 0.6 - Number(row) * h;
      for (let i = 0, latt; (latt = layer[i++]); ) {
        const latticeX = latticeL * 2 - latticeL / 2 - (i - 1) * (latticeL + 0.01);
        const latticeZ = baseH + latticeH / 2;

        // 货位
        ++startIndex;
        matrix.makeScale(latticeL, latticeW, latticeH);
        matrix.setPosition(latticeX, -0.1, latticeZ);
        mesh.setMatrixAt(startIndex, matrix);
        mesh.setColorAt(startIndex, color.set(latticeColor));
        seq[`${row}-${i - 1}`] = [
          startIndex,
          latticeL,
          latticeW,
          latticeH,
          latticeX,
          -0.1,
          latticeZ,
        ];

        // 货箱
        const boxZ = latticeZ + latticeH / 2 + boxH / 2;
        ++startIndex;
        matrix.makeScale(latticeL, latticeW, boxH);
        matrix.setPosition(latticeX, -0.1, boxZ);
        mesh.setMatrixAt(startIndex, matrix);
        mesh.setColorAt(startIndex, color.set(boxColor));
        seq[`${row}-${i - 1}-box`] = [startIndex, latticeL, latticeW, boxH, latticeX, -0.1, boxZ];
      }
    }
    mesh.name = "lattices";
    mesh.userData.seq = seq;
    mesh.rotation.x = -Math.PI / 2;
    mesh.rotation.z = -Math.PI / 2;
    group.add(mesh);
  }
  __changeBoxVisible(mesh, row, column, data) {
    const seq = mesh.userData.seq[`${row}-${column}-box`] || [];
    const matrix = new THREE.Matrix4();
    const isVisible = data.latticeStatus === "OCCUPIED";
    if (isVisible) matrix.makeScale(seq[1], seq[2], seq[3]);
    else matrix.makeScale(0, 0, 0);
    matrix.setPosition(seq[4], seq[5], seq[6]);
    mesh.setMatrixAt(seq[0], matrix);
  }
}
export default PoppickStation;
