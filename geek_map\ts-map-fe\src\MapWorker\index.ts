/* ! <AUTHOR> at 2022/07/11 */
import Cookies from "js-cookie";
import WSocket from "./websocket/websocket";

class WSWorker implements MWorker.Main {
  private wSocket: WSocket;
  private clientFor3D: boolean = false;
  floorIds: Array<floorId> = [];
  cellCodes: Array<code> = [];
  rackCodes: Array<code> = [];
  shelfCodes: Array<code> = [];
  chargerIds: Array<code> = [];
  robotsNeedCoverage: Array<code> = [];

  constructor(wsUrl: string = "") {
    wsUrl = wsUrl || this._getWsUrl();
    if (!wsUrl) throw new Error("wsUrl::" + wsUrl);
    this.wSocket = new WSocket(wsUrl);
  }

  init(): void {
    this.wSocket.init();
  }

  reqFloors(floorIds: Array<floorId> = []): void {
    const { info } = console;
    info("[websocket] >>>>> floorIds::", floorIds);
    this.floorIds = floorIds;

    const params: MWorker.wsParams = {
      msgType: "MapInitRequestMsg",
      body: {
        floorIds,
        clientFor3D: this.clientFor3D,
      },
    };
    this.wSocket.reqInit(params);
  }

  reqUpdate() {
    const params: MWorker.wsParams = {
      msgType: "MapUpdateRequestMsg",
      body: {
        floorIds: this.floorIds,
        clientFor3D: this.clientFor3D,
        robotIds: [],
        rackCodes: this.rackCodes,
        shelfCodes: this.shelfCodes,
        cellCodes: this.cellCodes,
        chargerIds: this.chargerIds,
        stationIds: [],
        robotsNeedCoverage: this.robotsNeedCoverage,
      },
    };
    this.wSocket.reqUpdate(params);
  }

  reqSocket(msgType: MWorker.reqMsgType, cmd: any): Promise<any> {
    switch (cmd?.instruction) {
      case "SHELF_HEAT":
        cmd.floorIds = this.floorIds;
        break;
    }
    const params: MWorker.wsParams = {
      msgType,
      body: cmd,
    };
    return this.wSocket.reqSocket(params);
  }

  reqQuery(params: { layer: string; code: code }): void {
    const { layer, code } = params;
    this.cellCodes = [];
    this.shelfCodes = [];
    this.rackCodes = [];
    this.chargerIds = [];
    switch (layer) {
      case "cell":
        this.cellCodes = [code];
      case "shelf":
        this.shelfCodes = [code];
        break;
      case "rack":
        this.rackCodes = [code];
        break;
      case "charger":
        this.chargerIds = [code];
        break;
    }
    this.wSocket.reqSingleQuery(params);
  }

  stopQuery() {
    this.cellCodes = [];
    this.shelfCodes = [];
    this.rackCodes = [];
    this.chargerIds = [];
    this.wSocket.stopQuery();
  }

  getQueryData(params: { layer: string; code: code }): any {
    switch (params.layer) {
      case "cell":
      case "rack":
      case "shelf":
        return this.wSocket.socketMsg.getSingleQuery(params);
    }
  }

  getMultiQueryData(params: { layer: string; codes: Array<code> }): any {
    // TODO 获取批量的当时的数据
  }

  onCallBack(cb: MWorker.wsCallback) {
    this.wSocket.onCallBack(cb);
  }

  destroy(): void {
    if (this.wSocket) {
      this.wSocket.destroy();
      this.wSocket = null;
    }

    this.floorIds = [];
    this.cellCodes = [];
    this.rackCodes = [];
    this.shelfCodes = [];
    this.chargerIds = [];
  }

  private _getWsUrl() {
    let protocol = window.location.protocol === "http:" ? "ws" : "wss";
    let hostname = window.location.host;
    if (!__rms_env_conf.isPro) {
      hostname = new URL(__rms_env_conf.API_URL).hostname;
    }
    const RMSPermission = localStorage.getItem("Geek_RMSPermission");
    let RMSToken = Cookies.get("rmsToken");
    if (!RMSToken) {
      RMSToken = localStorage.getItem("Geek_RMSToken");
    }
    const token = RMSPermission === "true" ? `?token=${RMSToken}` : "";

    return `${protocol}://${hostname}/athena-monitor${token}`;
  }
}

export default WSWorker;
