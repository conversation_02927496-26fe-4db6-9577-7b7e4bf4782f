/* ! <AUTHOR> at 2023/02/16 */

const log4js = require("log4js");
const fs = require("fs");
const os = require("os");
const path = require("path");
const buildConfig = require("../../config/common.conf");
const appConfig = require("../../config/app.dev.conf");

let logger = log4js.getLogger("auto.map2D.conf");
logger.level = "debug";

function setMap2DApiUrl() {
  const linkPath = buildConfig.linkPath;
  const API_URL = appConfig.API_URL;

  logger.info("rms API_URL::", API_URL);
  const monitor2D_CONF_PATH = `./${linkPath["monitor2D"]}/build/config.js`;
  const content = fs.readFileSync(path.resolve(monitor2D_CONF_PATH), "utf-8");

  const delimeter = os.EOL; //"\r\n";
  let hasPath = false;
  let parts = content.split(delimeter).filter(v => v.length);
  for (let i = 0, len = parts.length; i < len; i++) {
    const item = parts[i];

    const str = item.replace(/^\s+|\s*$/g, "");
    if (str.substring(0, 7) === "API_URL") {
      parts[i] = `  API_URL: "${API_URL}",`;
      hasPath = true;
      break;
    }
  }
  if (!hasPath) {
    parts.splice(parts.length - 1, 0, `  API_URL: "${API_URL}",`);
  }
  fs.writeFileSync(path.resolve(monitor2D_CONF_PATH), parts.join(delimeter));

  logger.info("map2D API_URL 配置成功！！");
}

setMap2DApiUrl();
