/* ! <AUTHOR> at 2021/01 */

import commonComponents from "./common";
import GeekLangSelection from "./components/geek-lang-selection";
import GeekMainStructure from "./components/geek-main-structure";
import GeekTabsNav from "./components/geek-tabs-nav";
import GeekPagination from "./components/geek-pagination";
import GeekCustomizeForm from "./components/geek-customize-form";
import GeekCustomizeTable from "./components/geek-customize-table";
import GeekFuzzySearch from "./components/geek-fuzzy-search";

let Plugins = {};
Plugins.install = function (Vue, options) {
  commonComponents(Vue, options);

  Vue.component("GeekLangSelection", GeekLangSelection);
  Vue.component("GeekMainStructure", GeekMainStructure);
  Vue.component("GeekTabsNav", GeekTabsNav);
  Vue.component("GeekPagination", GeekPagination);
  Vue.component("GeekCustomizeForm", GeekCustomizeForm);
  Vue.component("GeekCustomizeTable", GeekCustomizeTable);
  Vue.component("GeekFuzzySearch", GeekFuzzySearch);
};
export default Plugins;
