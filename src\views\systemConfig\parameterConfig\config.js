/**
 *  lang.rms.fed.developer  开发者
 */
export const searchOptions = [
  { label: "常用", value: "1" },
  { label: "开发者", value: "2" },
];
export const statusList = [
  { title:"地图解析时是否自动生成单行道信息",id: 1, status: true },
  { title:"地图解析时是否自动开启空载方向",id: 2, status: false },
];
export const selectList = [
  { title: "地图数据格式", id: 1 ,status:true},
  { title: "地图解析器", id: 2 ,status:true},
  { title: "矩阵地图解析模式", id: 3 ,status:false},
  { title: "地图结构解析器", id: 4 ,status:true},
];
export const inputList = [
  { title: "下载地图时的文件保存路径", id: 1,status:false },
  { title: "地图xml文件路径", id: 2 ,status:true},
  { title: "楼层高度", id: 3 ,status:false},
];
export const options = [
  { label: "融合地图数据格式", value: "1" },
  { label: "xml地图数据格式", value: "2" },
  { label: "matrix地图数据格式", value: "2" },
];
export const modules = [
  { label: "地图", id: 1 ,code:'map'},
  { label: "路径", id: 2,code:'path'},
  { label: "任务", id: 3,code:'task'},
  { label: "系统", id: 4 ,code:'system'},
  { label: "前端", id: 5 ,code:'fesystem'},
];
export const mapModules = [
  { label: "地图解析", id: 1, showStatus: true ,path:'/map/resolver'},
  { label: "基础信息", id: 2, showStatus: true,path:'/map/base' },
  { label: "工作站", id: 3, showStatus: true ,path:'/map/station'},
  { label: "区域", id: 4, showStatus: true ,path:'/map/area'},
];
export const taskModules = [
  { label: "货架任务", id: 1, showStatus: true,path:'/task/shelfAdjustment' },
  { label: "货箱任务", id: 2, showStatus: true,path:'/task/box' },
  { label: "RSP任务", id: 3, showStatus: true ,path:'/task/rsp'},
  { label: "分拣任务", id: 4, showStatus: true ,path:'/task/sorting'},
  { label: "充电策略", id: 5, showStatus: true ,path:'/task/charging'},
  { label: "机器人休息", id: 6, showStatus: true ,path:'/task/GoRest'},
  { label: "一键类任务", id: 7, showStatus: true ,path:'/task/click'},
  { label: "巡检任务", id: 8, showStatus: true ,path:'/task/inspection'},
  { label: "任务策略", id: 9, showStatus: true,path:'/task/TaskStrategy'},
];
export const pathModules = [
  { label: "路径分配", id: 1, showStatus: true,path:'/path/dispatching'},
  { label: "路径规划", id: 2, showStatus: true ,path:'/path/planning'},
  { label: "系统运行", id: 3, showStatus: true ,path:'/path/system'},
];
export const systemModules = [
  { label: "RMS参数", id: 1, showStatus: true  ,path:'/system/rms'},
  { label: "API参数", id: 2, showStatus: true ,path:'/system/api'},
  { label: "机器人", id: 3, showStatus: true  ,path:'/system/robot'},
  { label: "急停控制", id: 4, showStatus: true  ,path:'/system/stop'},
  { label: "监控", id: 5, showStatus: true ,path:'/system/monitor'},
  { label: "URL", id: 6, showStatus: true  ,path:'/system/URL'},
];
export const frontEndModules = [
  { label: "显隐设置", id: 1, showStatus: true,path:'/fesystem/base' },
  { label: "基础设置", id: 2, showStatus: true,path:'/fesystem/hide' },
];
export default{
    "map": {
      "base": {
        "map.draw.enabledStandardBezier": {
          "id": "35",
          "groupCode": "map",
          "code": "map.draw.enabledStandardBezier",
          "label": "是否使用贝塞尔曲线固定控制点",
          "desc": "绘制地图时，是否使用标准贝塞尔曲线,固定控制点",
          "i18nCode": "lang.rms.config.map.draw.enabledStandardBezier",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.enabledDebug": {
          "id": "36",
          "groupCode": "map",
          "code": "map.enabledDebug",
          "label": "地图调试模式",
          "desc": "是否开启地图调试模式，当且仅当非生产环境中可修改",
          "i18nCode": "lang.rms.config.map.enabledDebug",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.export.savePath": {
          "id": "37",
          "groupCode": "map",
          "code": "map.export.savePath",
          "label": "下载地图时的文件保存路径",
          "desc": "下载地图时的文件保存路径",
          "i18nCode": "lang.rms.config.map.export.savePath",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/var/log/geekplus/tomcat-rms/athena/file/maps/"
          }
        },
        "map.filepath": {
          "id": "38",
          "groupCode": "map",
          "code": "map.filepath",
          "label": "地图xml文件路径",
          "desc": "地图xml文件路径",
          "i18nCode": "lang.rms.config.map.filepath",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/config/system/map.xml"
          }
        },
        "map.floorHeight": {
          "id": "39",
          "groupCode": "map",
          "code": "map.floorHeight",
          "label": "楼层高度",
          "desc": "楼层高度,单位m",
          "i18nCode": "lang.rms.config.map.floorHeight",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10.0",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "map.charger.dataProvider": {
          "id": "150",
          "groupCode": "map",
          "code": "map.charger.dataProvider",
          "label": "充电站数据来源",
          "desc": "充电站数据来源",
          "i18nCode": "lang.rms.config.map.charger.dataProvider",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultChargerDataProvider",
              "defaultXmlChargerDataProvider",
              "rpcChargerDataProvider"
            ],
            "defVal": "defaultChargerDataProvider"
          }
        },
        "map.dataProvider": {
          "id": "151",
          "groupCode": "map",
          "code": "map.dataProvider",
          "label": "地图数据格式",
          "desc": "地图数据格式选择，融合地图数据格式databaseFusionMapDataProvider,xml地图数据格式defaultXmlMapDataProvider,matrix地图数据格式databaseMatrixMapDataProvider",
          "i18nCode": "lang.rms.config.map.dataProvider",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "databaseFusionMapDataProvider",
              "defaultXmlMapDataProvider",
              "databaseMatrixMapDataProvider"
            ]
          }
        },
        "map.ruleResolver": {
          "id": "172",
          "groupCode": "map",
          "code": "map.ruleResolver",
          "label": "地图规则解析器",
          "desc": "地图规则解析器，拣选xml地图解析器\"pickingXmlMapResolver\",拣选db地图解析器\"pickingDatabaseMapResolver\",拣选融合地图解析器\"pickingFusionMapResolver\",搬运xml地图解析器\"transportXmlMapResolver\",搬运db地图解析器\"transportDatabseMapResolver\",搬运融合地图解析器\"transportFusionMapResolver\",分拣xml地图解析器\"sortingXmlMapResolver\",分拣db地图解析器\"sortingDatabaseMapResolver\",分拣融合地图解析器\"sortingFusionMapResolver\",分拣柔性xml地图解析器\"flexibleSortingXmlMapResolver\",分拣柔性db地图解析器\"flexibleSortingDatabaseMapResolver\",分拣柔性融合地图解析器\"flexibleSortingFusionMapResolver\"",
          "i18nCode": "lang.rms.config.map.ruleResolver",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "pickingXmlMapResolver",
              "pickingDatabaseMapResolver",
              "pickingFusionMapResolver",
              "transportXmlMapResolver",
              "transportDatabseMapResolver",
              "transportFusionMapResolver",
              "transportCustomizedFusionMapResolver",
              "transportCustomizedDatabaseMapResolver",
              "sortingXmlMapResolver",
              "sortingDatabaseMapResolver",
              "sortingFusionMapResolver",
              "flexibleSortingXmlMapResolver",
              "flexibleSortingDatabaseMapResolver",
              "flexibleSortingFusionMapResolver"
            ]
          }
        },
        "map.search.fusionSearcher": {
          "id": "173",
          "groupCode": "map",
          "code": "map.search.fusionSearcher",
          "label": "融合地图坐标搜索算法",
          "desc": "融合地图坐标搜索算法：网格搜索是gridMapSearcher，全图搜索是fullMapSearcher",
          "i18nCode": "lang.rms.config.map.search.fusionSearcher",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "gridMapSearcher",
              "fullMapSearcher"
            ],
            "defVal": "gridMapSearcher"
          }
        },
        "map.station.dataProvider": {
          "id": "174",
          "groupCode": "map",
          "code": "map.station.dataProvider",
          "label": "工作站数据来源",
          "desc": "工作站数据来源",
          "i18nCode": "lang.rms.config.map.station.dataProvider",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultStationDataProvider",
              "defaultXmlStationDataProvider",
              "databaseStationDataProvider"
            ],
            "defVal": "defaultStationDataProvider"
          }
        },
        "map.structureResolver": {
          "id": "175",
          "groupCode": "map",
          "code": "map.structureResolver",
          "label": "地图结构解析器",
          "desc": "地图结构解析器:默认地图结结构解析器defaultMapStructureResolver,matrix地图结构解析器matrixMapStructureResolver",
          "i18nCode": "lang.rms.config.map.structureResolver",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "map.matrix.resolvingMode",
                "value": "'no_start_bound'"
              }
            ],
            "componentName": "select",
            "selectList": [
              "defaultMapStructureResolver",
              "matrixMapStructureResolver"
            ]
          }
        },
        "api.smp.serviceUrl": {
          "id": "446",
          "groupCode": "map",
          "code": "api.smp.serviceUrl",
          "label": "SMP服务地址",
          "desc": "SMP服务地址",
          "i18nCode": "lang.rms.config.api.smp.serviceUrl",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "map.resolver.cellZoneRadius": {
          "id": "448",
          "groupCode": "map",
          "code": "map.resolver.cellZoneRadius",
          "label": "按单元格划分小区域的半径",
          "desc": "按单元格划分小区域的半径，为0时该功能不生效，单位米",
          "i18nCode": "lang.rms.config.map.resolver.cellZoneRadius",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "map.robot.autoUpdate": {
          "id": "450",
          "groupCode": "map",
          "code": "map.robot.autoUpdate",
          "label": "地图变更是否自动重启机器人推送背景图片",
          "desc": "地图变更后，是否自动重启机器人推送背景图片",
          "i18nCode": "lang.rms.config.map.robot.autoUpdate",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.upload.maskOffset": {
          "id": "452",
          "groupCode": "map",
          "code": "map.upload.maskOffset",
          "label": "地图上传蒙层的偏移点",
          "desc": "地图上传蒙层的偏移点，蒙层需要设置左下角坐标",
          "i18nCode": "lang.rms.config.map.upload.maskOffset",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "[{0,0}]"
          }
        },
        "map.search.enabledSearchSegment": {
          "id": "453",
          "groupCode": "map",
          "code": "map.search.enabledSearchSegment",
          "label": "融合地图坐标搜索算法是否使用线段",
          "desc": "融合地图坐标搜索算法是否使用线段",
          "i18nCode": "lang.rms.config.map.search.enabledSearchSegment",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "map.slam.robot.to.qrcode": {
          "id": "456",
          "groupCode": "map",
          "code": "map.slam.robot.to.qrcode",
          "label": "二维码点位使用SLAM导航",
          "desc": "二维码点位使用SLAM导航，二维码点位使用SLAM导航， 配置方式注意事项 1、多个矩形区域用;隔开 2、每个区域内需要有四个顶点坐标，每个坐标要包含x坐标和y坐标,x和y之间用:隔开,顶点坐标用,隔开 3、顶点坐标的顺序需要是顺时针或者逆时针 4、顶点坐标需要x坐标和y坐标进行扩展，不要设置中心单元格，否则为导致矩形边上的中心点不被识别。 矩形示例：13.225:24.975,13.225:19.575,15.525:19.575,15.525:24.975;",
          "i18nCode": "lang.rms.config.map.slam.robot.to.qrcode",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "web.monitor.buffer.size": {
          "id": "476",
          "groupCode": "map",
          "code": "web.monitor.buffer.size",
          "label": "地图监控缓冲区大小",
          "desc": "地图监控缓冲区大小，单位KB",
          "i18nCode": "lang.rms.config.web.monitor.buffer.size",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "512",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "box.return.default.areaId": {
          "id": "483",
          "groupCode": "map",
          "code": "box.return.default.areaId",
          "label": "一键归还优先逻辑区域id",
          "desc": "此参数控制货箱管理页面一键归还负载箱子的优先逻辑区域id，默认0则没有优先区域",
          "i18nCode": "lang.rms.config.box.return.default.areaId",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.task.area": {
          "id": "498",
          "groupCode": "map",
          "code": "robot.task.area",
          "label": "任务区域设置",
          "desc": "任务区域设置",
          "i18nCode": "lang.rms.config.robot.usage.stat.areas",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "map.realTimeLimitSpeedArea": {
          "id": "519",
          "groupCode": "map",
          "code": "map.realTimeLimitSpeedArea",
          "label": "实时限速的区域",
          "desc": "速度实时限制区域：用一个矩形区域对角线的两个单元格二维码(左上，右下)表示限速区域，后面的是负载限制速度及空载速度限制（0表示不限制），多个区域用“;”隔开， 多级限速 继续用 “:” 追加，限速区域id 用# 标识,列如 10750125:11350115:1:1.2:0.8:0.9#1;11850125:12450135:0.5:0.8#2;11350116:11850124:1:1.2:0.8:0.9#1 表示：10750125-11350115 这个矩形区域的id 为1， 一级限速负载限速1 空载限速 1.2, 二级限速 负载限速 0.8，空载限速 0.9，11850125-12450135 这个矩形区域的id 为2， 一级限速负载限速0.5 空载限速 0.8",
          "i18nCode": "lang.rms.config.map.realTimeLimitSpeedArea",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "web.monitor.shelf.canUpdateOnRobot": {
          "id": "520",
          "groupCode": "map",
          "code": "web.monitor.shelf.canUpdateOnRobot",
          "label": "更新货架时校验货架是否被机器人运送中",
          "desc": "地图监控中更新货架时，如果货架有机器人在运送是否可以更新位置",
          "i18nCode": "lang.rms.config.web.monitor.shelf.canUpdateOnRobot",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "web.editor.validate.needValidate": {
          "id": "521",
          "groupCode": "map",
          "code": "web.editor.validate.needValidate",
          "label": "地图保存时是否打开校验规则",
          "desc": "地图保存时是否打开校验规则",
          "i18nCode": "lang.rms.config.web.editor.validate.needValidate",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "map.container.shelf.dataProvider": {
          "id": "553",
          "groupCode": "map",
          "code": "map.container.shelf.dataProvider",
          "label": "货架容器基础数据提供者",
          "desc": "货架容器基础数据提供者",
          "i18nCode": "lang.rms.config.map.container.shelf.dataProvider",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "containerShelfDataProvider"
          }
        },
        "map.container.box.dataProvider": {
          "id": "554",
          "groupCode": "map",
          "code": "map.container.box.dataProvider",
          "label": "货箱容器基础数据提供者",
          "desc": "货箱容器基础数据提供者",
          "i18nCode": "lang.rms.config.map.container.box.dataProvider",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "containerBoxDataProvider"
          }
        },
        "map.container.box.lattice.dataProvider": {
          "id": "555",
          "groupCode": "map",
          "code": "map.container.box.lattice.dataProvider",
          "label": "货箱位基础数据提供者",
          "desc": "货箱位基础数据提供者",
          "i18nCode": "lang.rms.config.map.container.box.lattice.dataProvider",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "containerBoxLatticeDataProvider"
          }
        },
        "map.container.box.rack.dataProvider": {
          "id": "556",
          "groupCode": "map",
          "code": "map.container.box.rack.dataProvider",
          "label": "货箱架基础数据提供者",
          "desc": "货箱架基础数据提供者",
          "i18nCode": "lang.rms.config.map.container.box.rack.dataProvider",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "containerBoxRackDataProvider"
          }
        },
        "map.container.pallet.lattice.dataProvider": {
          "id": "557",
          "groupCode": "map",
          "code": "map.container.pallet.lattice.dataProvider",
          "label": "叉车托盘层基础数据提供者",
          "desc": "叉车托盘层基础数据提供者",
          "i18nCode": "lang.rms.config.map.container.pallet.lattice.dataProvider",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "containerPalletLatticeDataProvider"
          }
        },
        "map.container.pallet.rack.dataProvider": {
          "id": "558",
          "groupCode": "map",
          "code": "map.container.pallet.rack.dataProvider",
          "label": "叉车托盘架基础数据提供者",
          "desc": "叉车托盘架基础数据提供者",
          "i18nCode": "lang.rms.config.map.container.pallet.rack.dataProvider",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "containerPalletRackDataProvider"
          }
        },
        "map.coverageMap.scaleX": {
          "id": "1067",
          "groupCode": "map",
          "code": "map.coverageMap.scaleX",
          "label": "栅格地图x轴比例尺",
          "desc": "栅格地图x轴比例尺，单位米",
          "i18nCode": "lang.rms.config.map.coverageMap.scaleX",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.05",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "map.coverageMap.scaleY": {
          "id": "1068",
          "groupCode": "map",
          "code": "map.coverageMap.scaleY",
          "label": "栅格地图y轴比例尺",
          "desc": "栅格地图y轴比例尺，单位米",
          "i18nCode": "lang.rms.config.map.coverageMap.scaleY",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.05",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "map.import.perfixCToCellCode": {
          "id": "1087",
          "groupCode": "map",
          "code": "map.import.perfixCToCellCode",
          "label": "是否在导入地图时为单元格编码增加前缀",
          "desc": "是否在导入地图时为“cellcode”增加前缀“C”,默认为false,使用随机二维码功能开启此配置。",
          "i18nCode": "lang.rms.config.map.import.perfixCToCellCode",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.initFilePath": {
          "id": "1113",
          "groupCode": "map",
          "code": "map.initFilePath",
          "label": "地图文件初始化地址",
          "desc": "地图文件初始化地址",
          "i18nCode": "lang.rms.config.map.initFilePath",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/var/log/geekplus/tomcat-rms/athena/files/"
          }
        },
        "map.initFileUrl": {
          "id": "1114",
          "groupCode": "map",
          "code": "map.initFileUrl",
          "label": "地图文件请求地址",
          "desc": "地图文件请求地址",
          "i18nCode": "lang.rms.config.map.initFileUrl",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}/log/geekplus/tomcat-rms/athena/files/"
          }
        },
        "web.excel.export.pageSize": {
          "id": "1161",
          "groupCode": "map",
          "code": "web.excel.export.pageSize",
          "label": "excel导出的数据数量",
          "desc": "excel导出的数据数量",
          "i18nCode": "lang.rms.config.web.excel.export.pageSize",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "3000",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "shelf.base.defaultLength": {
          "id": "309",
          "groupCode": "shelf",
          "code": "shelf.base.defaultLength",
          "label": "默认货架长度",
          "desc": "默认货架长度，具体为货架在地图上F面朝东时，其在东西方向上的尺寸，默认值0.88，单位米",
          "i18nCode": "lang.rms.config.shelf.base.defaultLength",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "section": "shelfSize",
          "sectionLabel": "货架尺寸",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.88",
            "limitMin": "0",
            "limitMax": "9"
          }
        },
        "shelf.base.defaultWidth": {
          "id": "310",
          "groupCode": "shelf",
          "code": "shelf.base.defaultWidth",
          "label": "默认货架宽度",
          "desc": "默认货架宽度，具体为货架在地图上F面朝东时，其在南北方向上的尺寸，默认值0.88，单位米",
          "i18nCode": "lang.rms.config.shelf.base.defaultWidth",
          "path": "/map/base",
          "pathLabel": "地图/基础配置",
          "section": "shelfSize",
          "sectionLabel": "货架尺寸",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.88",
            "limitMin": "0",
            "limitMax": "9"
          }
        }
      },
      "area": {
        "map.area.robot.countOffsetThreshold": {
          "id": "143",
          "groupCode": "map",
          "code": "map.area.robot.countOffsetThreshold",
          "label": "区域内机器人数量偏差阈值",
          "desc": "区域内机器人数量偏差阈值，当该区域内需求机器人数 - 可用机器人数 < 阈值时，触发区域间的机器人均衡",
          "i18nCode": "lang.rms.config.map.area.robot.countOffsetThreshold",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledAutoBalance",
                "value": true
              }
            ],
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "map.area.robot.demandStrategy": {
          "id": "144",
          "groupCode": "map",
          "code": "map.area.robot.demandStrategy",
          "label": "区域机器人需求计算策略",
          "desc": "区域机器人需求计算策略",
          "i18nCode": "lang.rms.config.map.area.robot.demandStrategy",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledAutoBalance",
                "value": true
              }
            ],
            "componentName": "select",
            "selectList": [
              "robotAreaDemand4AlgoStrategyHandler",
              "robotAreaDemand4ExecutableStrategyHandler",
              "robotAreaDemand4TotalStrategyHandler",
              "robotAreaDemand4ZaraStrategyHandler",
              "palletRobotAreaDemand4ExecutableStrategyHandler"
            ],
            "defVal": "robotAreaDemand4ExecutableStrategyHandler"
          }
        },
        "map.area.robot.dynamicDemandCoefficient": {
          "id": "145",
          "groupCode": "map",
          "code": "map.area.robot.dynamicDemandCoefficient",
          "label": "区域机器人动态需求系数",
          "desc": "区域机器人动态需求系数",
          "i18nCode": "lang.rms.config.map.area.robot.dynamicDemandCoefficient",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledAutoBalance",
                "value": true
              }
            ],
            "componentName": "text",
            "defVal": "0.98",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "map.area.robot.enabledAutoBalance": {
          "id": "146",
          "groupCode": "map",
          "code": "map.area.robot.enabledAutoBalance",
          "label": "区域间机器人数量平衡开关",
          "desc": "区域间机器人数量平衡开关",
          "i18nCode": "lang.rms.config.map.area.robot.enabledAutoBalance",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "checkbox",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledDistributeTask",
                "value": true
              }
            ],
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.area.robot.enabledDistributeTask": {
          "id": "147",
          "groupCode": "map",
          "code": "map.area.robot.enabledDistributeTask",
          "label": "按区域分发任务开关",
          "desc": "按区域分发任务开关",
          "i18nCode": "lang.rms.config.map.area.robot.enabledDistributeTask",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.area.robot.ratioOffsetThreshold": {
          "id": "148",
          "groupCode": "map",
          "code": "map.area.robot.ratioOffsetThreshold",
          "label": "区域内机器人占比偏差阈值",
          "desc": "区域内机器人占比偏差阈值，超过该阈值将触发区域间机器人均衡分配",
          "i18nCode": "lang.rms.config.map.area.robot.ratioOffsetThreshold",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledAutoBalance",
                "value": true
              }
            ],
            "componentName": "text",
            "defVal": "5.0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "map.area.robot.staticDemandCoefficient": {
          "id": "149",
          "groupCode": "map",
          "code": "map.area.robot.staticDemandCoefficient",
          "label": "区域对机器人的静态需求系数",
          "desc": "区域对机器人的静态需求系数",
          "i18nCode": "lang.rms.config.map.area.robot.staticDemandCoefficient",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledAutoBalance",
                "value": true
              }
            ],
            "componentName": "text",
            "defVal": "0.02",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "map.limit.obstacle.enabled": {
          "id": "152",
          "groupCode": "map",
          "code": "map.limit.obstacle.enabled",
          "label": "是否启用避障",
          "desc": "是否启用避障",
          "i18nCode": "lang.rms.config.map.limit.obstacle.enabled",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaObstacle",
          "sectionLabel": "区域避障",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.limit.obstacle.loadAreas": {
          "id": "153",
          "groupCode": "map",
          "code": "map.limit.obstacle.loadAreas",
          "label": "负载避障区域",
          "desc": "负载避障区域：用一个矩形区域对角线的两个单元格二维码(左上，右下)表示避障区域，后面的是避障长度、避障宽度(-1表示使用机器人默认避障设置)；多项用分号分割",
          "i18nCode": "lang.rms.config.map.limit.obstacle.loadAreas",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaObstacle",
          "sectionLabel": "区域避障",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "map.limit.obstacle.enabled",
                "value": true
              }
            ],
            "componentName": "text"
          }
        },
        "map.limit.obstacle.unloadAreas": {
          "id": "154",
          "groupCode": "map",
          "code": "map.limit.obstacle.unloadAreas",
          "label": "空载避障区域",
          "desc": "空载避障区域：用一个矩形区域对角线的两个单元格二维码(左上，右下)表示避障区域，后面的是避障长度、避障宽度(-1表示使用机器人默认避障设置)；多项用分号分割。",
          "i18nCode": "lang.rms.config.map.limit.obstacle.unloadAreas",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaObstacle",
          "sectionLabel": "区域避障",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "map.limit.obstacle.enabled",
                "value": true
              }
            ],
            "componentName": "text"
          }
        },
        "map.limit.speed.area": {
          "id": "155",
          "groupCode": "map",
          "code": "map.limit.speed.area",
          "label": "速度限制区域",
          "desc": "速度限制区域，用一个矩形区域对角线的两个单元格二维码(左上，右下)表示限速区域，后面的是负载限制速度及空载速度限制（0表示使用机器人默认），多个区域用“;”隔开，例如：cellcodeL1:cellcodeR1:负载限速:空载限速;cellcodeL2:cellcodeR2:负载限速:空载限速",
          "i18nCode": "lang.rms.config.map.limit.speed.area",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaSpeed",
          "sectionLabel": "区域限速",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "map.limit.speed.enabled",
                "value": true
              }
            ],
            "componentName": "text"
          }
        },
        "map.limit.speed.enabled": {
          "id": "156",
          "groupCode": "map",
          "code": "map.limit.speed.enabled",
          "label": "RMS是否限制速度",
          "desc": "RMS是否限制速度",
          "i18nCode": "lang.rms.config.map.limit.speed.enabled",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaSpeed",
          "sectionLabel": "区域限速",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.task.end.waitRules": {
          "id": 454,
          "groupCode": "map",
          "code": "robot.task.end.waitRules",
          "label": "趴窝",
          "desc": "机器人任务完成后，在哪些单元格等待",
          "i18nCode": "lang.rms.config.robot.task.end.waitRules",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "Held for next task",
          "sectionLabel": "趴窝",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "map.area.forklift.demandStrategy": {
          "id": "1100",
          "groupCode": "map",
          "code": "map.area.forklift.demandStrategy",
          "label": "区域对叉车需求的计算策略",
          "desc": "区域对叉车需求的计算策略，默认值palletRobotAreaDemand4ExecutableStrategyHandler",
          "i18nCode": "lang.rms.config.map.area.forklift.demandStrategy",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledAutoBalance",
                "value": true
              }
            ],
            "componentName": "select",
            "selectList": [
              "palletRobotAreaDemand4ExecutableStrategyHandler"
            ],
            "defVal": "palletRobotAreaDemand4ExecutableStrategyHandler"
          }
        },
        "map.limit.closeAreaObstacle.enabled": {
          "id": "1101",
          "groupCode": "map",
          "code": "map.limit.closeAreaObstacle.enabled",
          "label": "是否启用关闭区域避障功能",
          "desc": "是否启用关闭区域避障功能，启用：true，关闭：false",
          "i18nCode": "lang.rms.config.map.limit.closeAreaObstacle.enabled",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaObstacle",
          "sectionLabel": "区域避障",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.limit.closeObstacleAreas": {
          "id": "1102",
          "groupCode": "map",
          "code": "map.limit.closeObstacleAreas",
          "label": "关闭避障区域",
          "desc": "关闭避障区域,用一个矩形区域对角线的两个单元格二维码(左上，右下)表示关闭避障区域，后面值为1则表示空载关闭避障、为2则为负载关闭避障、3则空载负载均关闭避障，多个区域用“;”隔开，例如：cellcodeL1:cellcodeR1:1;cellcodeL2:cellcodeR2:2",
          "i18nCode": "lang.rms.config.map.limit.closeObstacleAreas",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaObstacle",
          "sectionLabel": "区域避障",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "map.rsprs.ctl.rs.balanceInterval": {
          "id": 1123,
          "groupCode": "map",
          "code": "map.rsprs.ctl.rs.balanceInterval",
          "label": "RS分区自动间隔时间",
          "desc": "RS分区自动间隔时间,单位为秒",
          "i18nCode": "lang.rms.config.map.rsprs.ctl.rs.balanceInterval",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": 20,
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "map.rsprs.ctl.rs.autoBalance": {
          "id": "1124",
          "groupCode": "map",
          "code": "map.rsprs.ctl.rs.autoBalance",
          "label": "RS分区是否自动均衡",
          "desc": "RS分区是否自动均衡",
          "i18nCode": "lang.rms.config.map.rsprs.ctl.rs.autoBalance",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.rsprs.ctl.rs.balanceMaxNum": {
          "id": "1125",
          "groupCode": "map",
          "code": "map.rsprs.ctl.rs.balanceMaxNum",
          "label": "RS分区开启支援后最大的支持数量",
          "desc": "RS分区开启支援后最大的支持数量",
          "i18nCode": "lang.rms.config.map.rsprs.ctl.rs.balanceMaxNum",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "map.rsprs.ctl.rs.enabledAutoArea": {
          "id": "1149",
          "groupCode": "map",
          "code": "map.rsprs.ctl.rs.enabledAutoArea",
          "label": "rs机器人启动自动分区",
          "desc": "rs机器人启动自动分区",
          "i18nCode": "lang.rms.config.map.rsprs.ctl.rs.enabledAutoArea",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.rsprs.ctl.rsAreaLaneNum": {
          "id": "1174",
          "groupCode": "map",
          "code": "map.rsprs.ctl.rsAreaLaneNum",
          "label": "RS机器人分区每个分区的巷道数量",
          "desc": "RS机器人分区每个分区的巷道数量",
          "i18nCode": "lang.rms.config.map.rsprs.ctl.rsAreaLaneNum",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "map.area.robot.refreshInterval": {
          "id": "411",
          "groupCode": "robot",
          "code": "map.area.robot.refreshInterval",
          "label": "根据任务刷新机器人所属区域时间间隔",
          "desc": "根据任务刷新机器人所属区域的时间间隔, 单位是ms,默认值是5000",
          "i18nCode": "lang.rms.config.map.area.robot.refreshInterval",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledAutoBalance",
                "value": true
              }
            ],
            "componentName": "text",
            "defVal": "5000",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "map.area.robot.balanceInterval": {
          "id": "412",
          "groupCode": "robot",
          "code": "map.area.robot.balanceInterval",
          "label": "均衡调整机器人所属区域的时间间隔",
          "desc": "均衡调整机器人所属区域的时间间隔, 单位是ms,默认值是20000",
          "i18nCode": "lang.rms.config.map.area.robot.balanceInterval",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledAutoBalance",
                "value": true
              }
            ],
            "componentName": "text",
            "defVal": "20000",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "map.area.robot.enabledLimitTaskCount": {
          "id": "413",
          "groupCode": "robot",
          "code": "map.area.robot.enabledLimitTaskCount",
          "label": "是否约束机器人区域的任务数量",
          "desc": "是否约束机器人区域的任务数量,默认为false,不约束",
          "i18nCode": "lang.rms.config.map.area.robot.enabledLimitTaskCount",
          "path": "/map/area",
          "pathLabel": "地图/区域",
          "section": "AreaDispatch",
          "sectionLabel": "分区调度",
          "type": "checkbox",
          "options": {
            "condition": [
              {
                "code": "map.area.robot.enabledAutoBalance",
                "value": true
              }
            ],
            "componentName": "checkbox",
            "defVal": false
          }
        }
      },
      "resolver": {
        "map.matrix.resolvingMode": {
          "id": "158",
          "groupCode": "map",
          "code": "map.matrix.resolvingMode",
          "label": "矩阵地图解析模式",
          "desc": "矩阵地图解析模式，no_start_bound：不使用单元格起始坐标，单元格位置通过中心点坐标和单元格尺寸计算，with_start_bound：使用单元格起始坐标（单元格左下角坐标），默认值no_start_bound（默认是中心，非标的需要改）",
          "i18nCode": "lang.rms.config.map.matrix.resolvingMode",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "no_start_bound",
              "with_start_bound"
            ],
            "defVal": "no_start_bound"
          }
        },
        "map.resolver": {
          "id": "159",
          "groupCode": "map",
          "code": "map.resolver",
          "label": "地图解析器",
          "desc": "地图解析器，\"pickingXmlMapResolver\",拣选xml地图解析器\r\n\"pickingDatabaseMapResolver\",拣选db地图解析器\r\n\"pickingFusionMapResolver\"拣选融合地图解析器\r\n,\"sortingXmlMapResolver\",分拣xml地图解析器\r\n\"sortingDatabaseMapResolver\",分拣db地图解析器\r\n\"transportXmlMapResolver\",搬运xml地图解析器\r\n\"transportDatabaseMapResolver\",搬运db地图解析器\r\n\"transportFusionMapResolver\",搬运融合地图解析器\r\n\"flexibleSortingXmlMapResolver\",分拣柔性xml地图解析器\r\n\"flexibleSortingDatabaseMapResolver\",分拣柔性db地图解析器\r\n\"flexibleSortingFusionMapResolver\",分拣柔性融合地图解析器\r\n\"sortingFusionMapResolver分拣融合地图解析器",
          "i18nCode": "lang.rms.config.map.resolver",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "pickingXmlMapResolver",
              "pickingDatabaseMapResolver",
              "pickingFusionMapResolver",
              "sortingXmlMapResolver",
              "sortingDatabaseMapResolver",
              "transportXmlMapResolver",
              "transportDatabaseMapResolver",
              "transportFusionMapResolver",
              "flexibleSortingXmlMapResolver",
              "flexibleSortingDatabaseMapResolver",
              "flexibleSortingFusionMapResolver",
              "sortingFusionMapResolver"
            ],
            "defVal": "pickingFusionMapResolver"
          }
        },
        "map.resolver.enabledAutoCalcSingleLane": {
          "id": "160",
          "groupCode": "map",
          "code": "map.resolver.enabledAutoCalcSingleLane",
          "label": "是否自动生成配置单行道信息",
          "desc": "地图解析时是否自动生成配置单行道信息",
          "i18nCode": "lang.rms.config.map.resolver.enabledAutoCalcSingleLane",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.resolver.enabledPickingUnloadMapRule": {
          "id": "161",
          "groupCode": "map",
          "code": "map.resolver.enabledPickingUnloadMapRule",
          "label": "是否开启空载方向的地图解析规则",
          "desc": "拣选模式的地图解析时是否开启空载方向的地图解析规则",
          "i18nCode": "lang.rms.config.map.resolver.enabledPickingUnloadMapRule",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.resolver.enabledSplitLongSegment": {
          "id": "162",
          "groupCode": "map",
          "code": "map.resolver.enabledSplitLongSegment",
          "label": "地图解析是否对长线段进行拆段",
          "desc": "地图解析，是否对长线段进行拆段",
          "i18nCode": "lang.rms.config.map.resolver.enabledSplitLongSegment",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "map.resolver.minVirtualNodeSpacing": {
          "id": "163",
          "groupCode": "map",
          "code": "map.resolver.minVirtualNodeSpacing",
          "label": "地图解析最小虚拟节点长度",
          "desc": "地图解析，最小虚拟节点长度，单位米",
          "i18nCode": "lang.rms.config.map.resolver.minVirtualNodeSpacing",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1.0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "map.resolver.rule.closeRoadAndFunctionUnreachableDirection": {
          "id": "164",
          "groupCode": "map",
          "code": "map.resolver.rule.closeRoadAndFunctionUnreachableDirection",
          "label": "关闭道路到功能单元格的不可达方向",
          "desc": "关闭道路到功能单元格的不可达方向，UNACTIVATED：未激活（即不关闭），LOAD：关闭负载方向，UNLOAD：关闭空载方向，BOTH：空负载方向均关闭",
          "i18nCode": "lang.rms.config.map.resolver.rule.closeRoadAndFunctionUnreachableDirection",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "map.ruleResolver",
                "value": "'transportCustomizedMapRuleResolver'"
              }
            ],
            "componentName": "select",
            "selectList": [
              "UNACTIVATED",
              "LOAD",
              "UNLOAD",
              "BOTH"
            ],
            "defVal": "UNACTIVATED"
          }
        },
        "map.resolver.rule.closeShelfAndRoadDislocationDirection": {
          "id": "165",
          "groupCode": "map",
          "code": "map.resolver.rule.closeShelfAndRoadDislocationDirection",
          "label": "关闭俩单元格互通的方向",
          "desc": "若道路单元格和货架单元格错位，则根据配置关闭俩单元格互通的方向（空负载一起关闭），UNACTIVATED：未激活（即不关闭），BOTH：空负载方向均关闭",
          "i18nCode": "lang.rms.config.map.resolver.rule.closeShelfAndRoadDislocationDirection",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "map.ruleResolver",
                "value": "'transportCustomizedMapRuleResolver'"
              }
            ],
            "componentName": "select",
            "selectList": [
              "UNACTIVATED",
              "BOTH"
            ],
            "defVal": "UNACTIVATED"
          }
        },
        "map.resolver.rule.closeShelfToShelfDirection": {
          "id": "166",
          "groupCode": "map",
          "code": "map.resolver.rule.closeShelfToShelfDirection",
          "label": "关闭货架与货架之间的方向",
          "desc": "根据配置关闭货架与货架之间的方向，UNACTIVATED：未激活（即不关闭），LOAD：关闭负载方向，UNLOAD：关闭空载方向，BOTH：空负载方向均关闭",
          "i18nCode": "lang.rms.config.map.resolver.rule.closeShelfToShelfDirection",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "map.ruleResolver",
                "value": "'transportCustomizedMapRuleResolver'"
              }
            ],
            "componentName": "select",
            "selectList": [
              "UNACTIVATED",
              "LOAD",
              "UNLOAD",
              "BOTH"
            ],
            "defVal": "UNACTIVATED"
          }
        },
        "map.resolver.rule.closeStationToShelfDirection": {
          "id": "167",
          "groupCode": "map",
          "code": "map.resolver.rule.closeStationToShelfDirection",
          "label": "关闭工位与货架之间的方向",
          "desc": "根据配置关闭工位与货架之间的方向，UNACTIVATED：未激活（即不关闭），BOTH：空负载方向均关闭",
          "i18nCode": "lang.rms.config.map.resolver.rule.closeStationToShelfDirection",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "map.ruleResolver",
                "value": "'transportCustomizedMapRuleResolver'"
              }
            ],
            "componentName": "select",
            "selectList": [
              "UNACTIVATED",
              "BOTH"
            ],
            "defVal": "UNACTIVATED"
          }
        },
        "map.resolver.rule.closeStationToStationDirection": {
          "id": "168",
          "groupCode": "map",
          "code": "map.resolver.rule.closeStationToStationDirection",
          "label": "关闭工位与工位之间的方向",
          "desc": "根据配置关闭工位与工位之间的方向，UNACTIVATED：未激活（即不关闭），LOAD：关闭负载方向，UNLOAD：关闭空载方向，BOTH：空负载方向均关闭",
          "i18nCode": "lang.rms.config.map.resolver.rule.closeStationToStationDirection",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "map.ruleResolver",
                "value": "'transportCustomizedMapRuleResolver'"
              }
            ],
            "componentName": "select",
            "selectList": [
              "UNACTIVATED",
              "LOAD",
              "UNLOAD",
              "BOTH"
            ],
            "defVal": "UNACTIVATED"
          }
        },
        "map.resolver.rule.openInnerAndOuterShelfDirection": {
          "id": "169",
          "groupCode": "map",
          "code": "map.resolver.rule.openInnerAndOuterShelfDirection",
          "label": "打开内外部货架的负载互通方向",
          "desc": "根据配置打开内外部货架的负载互通方向（空负载一起打开），UNACTIVATED：未激活（关闭），BOTH：空负载方向均打开",
          "i18nCode": "lang.rms.config.map.resolver.rule.openInnerAndOuterShelfDirection",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "map.ruleResolver",
                "value": "'transportCustomizedMapRuleResolver'"
              }
            ],
            "componentName": "select",
            "selectList": [
              "UNACTIVATED",
              "BOTH"
            ],
            "defVal": "BOTH"
          }
        },
        "map.resolver.virtualPointLength": {
          "id": "170",
          "groupCode": "map",
          "code": "map.resolver.virtualPointLength",
          "label": "地图初始化虚拟点长度",
          "desc": "地图初始化虚拟点长度，单位米",
          "i18nCode": "lang.rms.config.map.resolver.virtualPointLength",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "map.resolver.virtualPointWidth": {
          "id": "171",
          "groupCode": "map",
          "code": "map.resolver.virtualPointWidth",
          "label": "地图初始化虚拟点宽度",
          "desc": "地图初始化虚拟点宽度，单位米",
          "i18nCode": "lang.rms.config.map.resolver.virtualPointWidth",
          "path": "/map/resolver",
          "pathLabel": "地图/地图解析",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.5",
            "limitMin": "0",
            "limitMax": "100"
          }
        }
      },
      "station": {
        "station.single.enabledRobotLeaveWhenShelfUnloaded": {
          "id": "378",
          "groupCode": "station",
          "code": "station.single.enabledRobotLeaveWhenShelfUnloaded",
          "label": "单点工位机器人放下货架后是否离开",
          "desc": "单点工位机器人放下货架后离开，true：离开，false：不离开，默认值true",
          "i18nCode": "lang.rms.config.station.single.enabledRobotLeaveWhenShelfUnloaded",
          "path": "/map/station",
          "pathLabel": "地图/工作站",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "station.standard.robotNumAdjusting": {
          "id": "380",
          "groupCode": "station",
          "code": "station.standard.robotNumAdjusting",
          "label": "标准工位机器人数量调节",
          "desc": "标准工位机器人数量调节，默认值0",
          "i18nCode": "lang.rms.config.station.standard.robotNumAdjusting",
          "path": "/map/station",
          "pathLabel": "地图/工作站",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "station.strategy.enabledLayout01TurnOnPath": {
          "id": "383",
          "groupCode": "station",
          "code": "station.strategy.enabledLayout01TurnOnPath",
          "label": "0、1布局的工作站是否允许在非田字格转面",
          "desc": "Layout是0，1的工作站是否允许在非田字格转面",
          "i18nCode": "lang.rms.config.station.strategy.enabledLayout01TurnOnPath",
          "path": "/map/station",
          "pathLabel": "地图/工作站",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "station.strategy.enabledLayout7And13EntryToNoTurnCell": {
          "id": "384",
          "groupCode": "station",
          "code": "station.strategy.enabledLayout7And13EntryToNoTurnCell",
          "label": "7、13布局工作站是否允许不转面的机器人到拣选位前一格",
          "desc": "Layout是7，13的工作站是否允许不转面的机器人直接规划路径到拣选位前一格,默认值:false",
          "i18nCode": "lang.rms.config.station.strategy.enabledLayout7And13EntryToNoTurnCell",
          "path": "/map/station",
          "pathLabel": "地图/工作站",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "station.strategy.taskAllocate": {
          "id": "396",
          "groupCode": "station",
          "code": "station.strategy.taskAllocate",
          "label": "拣选工作站任务分配策略",
          "desc": "拣选工作站任务分配策略。defaultStationTaskAllocateStrategy：默认的任务分配策略；strictLimitStationTaskAllocateStrategy：严格限制排队数量模式。",
          "i18nCode": "lang.rms.config.station.strategy.taskAllocate",
          "path": "/map/station",
          "pathLabel": "地图/工作站",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultStationTaskAllocateStrategy",
              "strictLimitStationTaskAllocateStrategy"
            ],
            "defVal": "defaultStationTaskAllocateStrategy"
          }
        }
      }
    },
    "task": {
      "shelfAdjustment": {
        "station.strategy.enabledForceReTurnOutStation.stations": {
          "id": "382",
          "groupCode": "station",
          "code": "station.strategy.enabledForceReTurnOutStation.stations",
          "label": "库区强制转面",
          "desc": "标准工作站里面哪些在重新转面时强制在库区转面，只支持无田字格工位，且存在可达的转面点时有效，且货架转面策略需要配置为从地图寻找最近的。",
          "i18nCode": "lang.rms.config.station.strategy.enabledForceReTurnOutStation.stations",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "station.strategy.enabledTurnOnPath.stations": {
          "id": "385",
          "groupCode": "station",
          "code": "station.strategy.enabledTurnOnPath.stations",
          "label": "寻找转面点转面的标准工作站集合",
          "desc": "允许在路径上寻找转面点转面的标准工作站集合",
          "i18nCode": "lang.rms.config.station.strategy.enabledTurnOnPath.stations",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "map.turnControlEnable": {
          "id": "1154",
          "groupCode": "root",
          "code": "map.turnControlEnable",
          "label": "是否开启转面控制功能",
          "desc": "是否开启转面控制功能",
          "i18nCode": "lang.rms.config.map.turnControlEnable",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox"
          }
        },
        "path.dispatching.coverageRatioForRotate": {
          "id": "420",
          "groupCode": "path",
          "code": "path.dispatching.coverageRatioForRotate",
          "label": "机器人原地转弯或者货架转面采样率",
          "desc": "机器人原地转弯或者货架转面采样率",
          "i18nCode": "lang.rms.config.path.dispatching.coverageRatioForRotate",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15.0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "algorithm.job.match.needToTurnPara": {
          "id": "1165",
          "groupCode": "algorithm",
          "code": "algorithm.job.match.needToTurnPara",
          "label": "算法任务分配任务是否转面权重 ",
          "desc": "算法任务分配任务是否转面权重 ，拣选场景固定值不需要更改，搬运场景需要改变默认值",
          "i18nCode": "lang.rms.config.algorithm.job.match.needToTurnPara",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5D",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "click.turnShelf.maxAdjustingNum": {
          "id": "139",
          "groupCode": "businessInstruction",
          "code": "click.turnShelf.maxAdjustingNum",
          "label": "一键转面货架最大调整数",
          "desc": "一键转面货架最大调整数，默认10",
          "i18nCode": "lang.rms.config.click.turnShelf.maxAdjustingNum",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "shelf.code.enabledValidate": {
          "id": "311",
          "groupCode": "shelf",
          "code": "shelf.code.enabledValidate",
          "label": "开启货架二维码校验",
          "desc": "开启对已扫描的货架二维码的校验",
          "i18nCode": "lang.rms.config.shelf.code.enabledValidate",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "taskShelfMoving",
          "sectionLabel": "货架搬运",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.dynamicAdjustment.areas": {
          "id": "321",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.areas",
          "label": "货架位置调整的区域",
          "desc": "可进行货架位置调整的区域",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.areas",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "shelf.dynamicAdjustment.enabledDynamicAdjustment",
                "value": true
              }
            ],
            "componentName": "text"
          }
        },
        "shelf.dynamicAdjustment.enabledCalcReturnDist": {
          "id": "322",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.enabledCalcReturnDist",
          "label": "归还货架距离优先",
          "desc": "货架动态调整是否只考虑工作站还货架时的横向移动，不考虑货架热度",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.enabledCalcReturnDist",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "checkbox",
          "options": {
            "condition": [
              {
                "code": "shelf.dynamicAdjustment.enabledDynamicAdjustment",
                "value": true
              }
            ],
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.dynamicAdjustment.enabledDiffArea": {
          "id": "323",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.enabledDiffArea",
          "label": "是否支持跨区货架位调整",
          "desc": "是否支持跨区货架位调整",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.enabledDiffArea",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "checkbox",
          "options": {
            "condition": [
              {
                "code": "shelf.dynamicAdjustment.enabledDynamicAdjustment",
                "value": true
              }
            ],
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.dynamicAdjustment.enabledDynamicAdjustment": {
          "id": "324",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.enabledDynamicAdjustment",
          "label": "是否开启货架动态调整",
          "desc": "是否开启货架动态调整",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.enabledDynamicAdjustment",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.dynamicAdjustment.enabledInitialZone": {
          "id": "325",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.enabledInitialZone",
          "label": "是否初始化货架位的热度梯度",
          "desc": "启动流程是否初始化货架位的热度梯度",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.enabledInitialZone",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.dynamicAdjustment.enabledPoller": {
          "id": "326",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.enabledPoller",
          "label": "是否支持动态调整货架位置",
          "desc": "是否支持动态调整货架位置",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.enabledPoller",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "checkbox",
          "options": {
            "condition": [
              {
                "code": "shelf.dynamicAdjustment.enabledDynamicAdjustment",
                "value": true
              }
            ],
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.dynamicAdjustment.forbidTimeRanges": {
          "id": "327",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.forbidTimeRanges",
          "label": "禁止货架位置调整的时间段",
          "desc": "禁止货架位置调整的时间段列表",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.forbidTimeRanges",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "timeslots",
          "options": {
            "condition": [
              {
                "code": "shelf.dynamicAdjustment.enabledDynamicAdjustment",
                "value": true
              }
            ],
            "componentName": "timeslots"
          }
        },
        "shelf.dynamicAdjustment.globalConcurrency": {
          "id": "328",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.globalConcurrency",
          "label": "货架动态调整任务并发数上限",
          "desc": "货架位动态调整的任务并发数上限",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.globalConcurrency",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "shelf.dynamicAdjustment.enabledDynamicAdjustment",
                "value": true
              }
            ],
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "shelf.dynamicAdjustment.idleRobotCount": {
          "id": "329",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.idleRobotCount",
          "label": "可触发货架位置动态调整的空闲机器人数量下限",
          "desc": "可触发货架位置动态调整的空闲机器人数量下限",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.idleRobotCount",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "shelf.dynamicAdjustment.enabledDynamicAdjustment",
                "value": true
              }
            ],
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "shelf.dynamicAdjustment.idleRobotRate": {
          "id": "330",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.idleRobotRate",
          "label": "可触发货架位置动态调整的机器人空闲率下限",
          "desc": "可触发货架位置动态调整的机器人空闲率下限",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.idleRobotRate",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "text",
          "options": {
            "condition": [
              {
                "code": "shelf.dynamicAdjustment.enabledDynamicAdjustment",
                "value": true
              }
            ],
            "componentName": "text",
            "defVal": "15.0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "shelf.dynamicAdjustment.zoneLevel": {
          "id": "331",
          "groupCode": "shelf",
          "code": "shelf.dynamicAdjustment.zoneLevel",
          "label": "货架位热度梯度",
          "desc": "货架位热度梯度",
          "i18nCode": "lang.rms.config.shelf.dynamicAdjustment.zoneLevel",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1,2,3,4,5,6,7"
          }
        },
        "shelf.enabledEnter2Road": {
          "id": "333",
          "groupCode": "shelf",
          "code": "shelf.enabledEnter2Road",
          "label": "货架入场时是否允许入到非货架单元格",
          "desc": "货架入场时是否允许入到非货架单元格",
          "i18nCode": "lang.rms.config.shelf.enabledEnter2Road",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "dynamicAdjust",
          "sectionLabel": "货架动态调整",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.staticAdjustment.enabledStaticAdjustment": {
          "id": "334",
          "groupCode": "shelf",
          "code": "shelf.staticAdjustment.enabledStaticAdjustment",
          "label": "开启货架静态调整",
          "desc": "是否开启货架静态调整",
          "i18nCode": "lang.rms.config.shelf.staticAdjustment.enabledStaticAdjustment",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "staticAdjustment",
          "sectionLabel": "货架静态调整",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.staticAdjustment.maxShelfNumConcurrency": {
          "id": "335",
          "groupCode": "shelf",
          "code": "shelf.staticAdjustment.maxShelfNumConcurrency",
          "label": "同时调整的货架量",
          "desc": "静态调整时同时调整的货架量",
          "i18nCode": "lang.rms.config.shelf.staticAdjustment.maxShelfNumConcurrency",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "staticAdjustment",
          "sectionLabel": "货架静态调整",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "shelf.staticAdjustment.shelfScoreStatisticsStrategy": {
          "id": "336",
          "groupCode": "shelf",
          "code": "shelf.staticAdjustment.shelfScoreStatisticsStrategy",
          "label": "货架热度分数获取策略",
          "desc": "货架热度分数获取策略。numberOfShelfMoves（按搬运次数统计热度分数），externalSettings（外部设置货架热度分数）",
          "i18nCode": "lang.rms.config.shelf.staticAdjustment.shelfScoreStatisticsStrategy",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "staticAdjustment",
          "sectionLabel": "货架静态调整",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "numberOfShelfMoves",
              "externalSettings"
            ],
            "defVal": "numberOfShelfMoves"
          }
        },
        "shelf.turning.angleInjectingStrategy": {
          "id": "337",
          "groupCode": "shelf",
          "code": "shelf.turning.angleInjectingStrategy",
          "label": "货架转面角度注入策略",
          "desc": "货架转面角度注入策略",
          "i18nCode": "lang.rms.config.shelf.turning.angleInjectingStrategy",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "defaultInjectShelfAngleStrategy"
          }
        },
        "shelf.turning.isWithBodyOfRobot": {
          "id": "338",
          "groupCode": "shelf",
          "code": "shelf.turning.isWithBodyOfRobot",
          "label": "货架是否需要跟着机器人本体转 ",
          "desc": "货架是否需要跟着机器人本体转 ",
          "i18nCode": "lang.rms.config.shelf.turning.isWithBodyOfRobot",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.turning.lookingDirection": {
          "id": "339",
          "groupCode": "shelf",
          "code": "shelf.turning.lookingDirection",
          "label": "转面点寻找方向",
          "desc": "转面点寻找方向，0：从前向后寻找，1：从后向前寻找",
          "i18nCode": "lang.rms.config.shelf.turning.lookingDirection",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              0,
              1
            ],
            "defVal": "0"
          }
        },
        "shelf.turning.modelInjectingStrategy": {
          "id": "340",
          "groupCode": "shelf",
          "code": "shelf.turning.modelInjectingStrategy",
          "label": "货架模型注入策略",
          "desc": "货架模型注入策略",
          "i18nCode": "lang.rms.config.shelf.turning.modelInjectingStrategy",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "defaultInjectShelfModelStrategy"
          }
        },
        "shelf.turning.strategy": {
          "id": "341",
          "groupCode": "shelf",
          "code": "shelf.turning.strategy",
          "label": "货架转面点策略",
          "desc": "货架转面策略，从整个地图上寻找最近的：looking_from_map，从已规划的路径上寻找：looking_from_path",
          "i18nCode": "lang.rms.config.shelf.turning.strategy",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "looking_from_map",
              "looking_from_path"
            ],
            "defVal": "looking_from_map"
          }
        },
        "target.cell.exist.shelf.allow.move": {
          "id": "517",
          "groupCode": "shelf",
          "code": "target.cell.exist.shelf.allow.move",
          "label": "目标单元格存在货架时是否允许移动",
          "desc": "目标单元格存在货架时是否允许移动",
          "i18nCode": "lang.rms.config.shelf.targetCellExistShelfAllowMove",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "taskShelfMoving",
          "sectionLabel": "货架搬运",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "shelf.turning.enabledWhenPathPlanningFailed": {
          "id": "523",
          "groupCode": "shelf",
          "code": "shelf.turning.enabledWhenPathPlanningFailed",
          "label": "路径规划失败是否转面",
          "desc": "路径规划失败是否转面",
          "i18nCode": "lang.rms.config.shelf.turning.enabledWhenPathPlanningFailed",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "shelf.base.placementDuplicated": {
          "id": "1088",
          "groupCode": "shelf",
          "code": "shelf.base.placementDuplicated",
          "label": "货架老家位是否重复校验开关",
          "desc": "货架老家位是否重复校验开关",
          "i18nCode": "lang.rms.config.shelf.base.placementDuplicated",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "taskShelfMoving",
          "sectionLabel": "货架搬运",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "shelf.turning.behaviorForGoTurn": {
          "id": "1104",
          "groupCode": "shelf",
          "code": "shelf.turning.behaviorForGoTurn",
          "label": "非标准工位，货架转面行为策略",
          "desc": "非标准工位，货架下发[GO_TURN]转面行为，0：原地转面，1：寻找转面点转面，默认0",
          "i18nCode": "lang.rms.shelf.turning.behaviorForGoTurn",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "0",
              "1"
            ],
            "defVal": "0"
          }
        },
        "shelf.denseStorage.enabledExchange": {
          "id": "314",
          "groupCode": "shelf",
          "code": "shelf.denseStorage.enabledExchange",
          "label": "是否开启内外部货架交换",
          "desc": "是否开启内外部货架交换，true：开启，false：不开启，默认值false",
          "i18nCode": "lang.rms.config.shelf.denseStorage.enabledExchange",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "denseStorage",
          "sectionLabel": "密集存储",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "shelf.denseStorage.enabledLazyGoCircle": {
          "id": "315",
          "groupCode": "shelf",
          "code": "shelf.denseStorage.enabledLazyGoCircle",
          "label": "是否延迟生成密集存储绕圈任务 ",
          "desc": "是否延迟生成密集存储绕圈任务 ，立即生成：false；延迟生成：true，默认true",
          "i18nCode": "lang.rms.config.shelf.denseStorage.enabledLazyGoCircle",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "denseStorage",
          "sectionLabel": "密集存储",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "shelf.denseStorage.strategy.goCircleHandler": {
          "id": "319",
          "groupCode": "shelf",
          "code": "shelf.denseStorage.strategy.goCircleHandler",
          "label": "密集存储处理模式",
          "desc": "密集存储处理模式，默认，根据货架老家处理defaultGoCircleTaskHandlerStrategy;根据货架位置处理绕圈任务，支持货架无老家或是还/送货架位置不是该货架老家 transportGoCircleTaskHandlerStrategy",
          "i18nCode": "lang.rms.config.shelf.denseStorage.strategy.goCircleHandler",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "denseStorage",
          "sectionLabel": "密集存储",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultGoCircleTaskHandlerStrategy",
              "transportGoCircleTaskHandlerStrategy"
            ],
            "defVal": "defaultGoCircleTaskHandlerStrategy"
          }
        },
        "robot.task.shelfCodeValidateFailureStrategy": {
          "id": "64",
          "groupCode": "taskFailure",
          "code": "robot.task.shelfCodeValidateFailureStrategy",
          "label": "货架码校验失败策略",
          "desc": "开启货架码校验之后，货架码不一致导致任务失败的处理策略。AUTO_CANCEL:自动取消; IGNORE_FAILURE:忽略失败; BUSINESS_CONTROL:业务控制; HANGUP_TASK:任务挂起，等待货架位置更新之后继续执行。默认AUTO_CANCEL",
          "i18nCode": "lang.rms.config.robot.task.shelfCodeValidateFailureStrategy",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "taskShelfMoving",
          "sectionLabel": "货架搬运",
          "type": "select",
          "options": {
            "condition": [
              {
                "code": "shelf.code.enabledValidate",
                "value": true
              }
            ],
            "componentName": "select",
            "selectList": [
              "AUTO_CANCEL",
              "IGNORE_FAILURE",
              "BUSINESS_CONTROL",
              "HANGUP_TASK"
            ],
            "defVal": "AUTO_CANCEL"
          }
        },
        "path.dispatching.enabledInFieldWithArcPath": {
          "id": "205",
          "groupCode": "path",
          "code": "path.dispatching.enabledInFieldWithArcPath",
          "label": "是否开启弧线进田字格",
          "desc": "是否开启弧线进田字格(非标地图需要 关闭此参数",
          "i18nCode": "lang.rms.config.path.dispatching.enabledInFieldWithArcPath",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "taskShelfMoving",
          "sectionLabel": "货架搬运",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.enabledAutoCancel": {
          "id": "298",
          "groupCode": "robot",
          "code": "robot.task.enabledAutoCancel",
          "label": "末段机器人任务自动取消",
          "desc": "末段机器人任务自动取消",
          "i18nCode": "lang.rms.config.robot.task.enabledAutoCancel",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "taskShelfMoving",
          "sectionLabel": "货架搬运",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.accessibleDeviationDist": {
          "id": "421",
          "groupCode": "robot",
          "code": "robot.task.accessibleDeviationDist",
          "label": "机器人转面距离绝对位置可接受的误差",
          "desc": "机器人转面距离绝对位置可接受的误差",
          "i18nCode": "lang.rms.config.robot.task.accessibleDeviationDist",
          "path": "/task/shelfAdjustment",
          "pathLabel": "任务/货架任务",
          "section": "shelfTurning",
          "sectionLabel": "货架转面",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.05",
            "limitMin": "0",
            "limitMax": "1"
          }
        }
      },
      "GoRest": {
        "robot.dispatching.enabledGoRestWhenFindNoneRestCell": {
          "id": "485",
          "groupCode": "path",
          "code": "robot.dispatching.enabledGoRestWhenFindNoneRestCell",
          "label": "无可用休息点时是否允许随机找一个点位休息",
          "desc": "当地图无可用休息点后，是否允许系统随机从地图中选择一个点位作为休息点。默认：false",
          "i18nCode": "lang.rms.config.robot.dispatching.enabledGoRestWhenFindNoneRestCell",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.dispatching.enabledGoRestAfterShelfTaskCompleted": {
          "id": "272",
          "groupCode": "robot",
          "code": "robot.dispatching.enabledGoRestAfterShelfTaskCompleted",
          "label": "开启机器人放下货架后去休息点",
          "desc": "开启机器人放下货架后去休息点",
          "i18nCode": "lang.rms.config.robot.dispatching.enabledGoRestAfterShelfTaskCompleted",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.dispatching.enabledGoRestAfterTaskCompletedForStrategy": {
          "id": "273",
          "groupCode": "robot",
          "code": "robot.dispatching.enabledGoRestAfterTaskCompletedForStrategy",
          "label": "任务完成去休息点逻辑",
          "desc": "任务完成go rest策略。defaultIdleRobotNeedGoRestStrategy:默认策略，当【完成任务为GO_SOMEWHERE_TO_STAY】、【顶升机器人在货架位】、【单点工位未启用完成离开配置】时，机器人不生成go rest任务；反之生成。idleRobotNeedGoRestForShelfCellStrategy:货架策略，当【完成任务为GO_SOMEWHERE_TO_STAY】、【单点工位未启用完成离开配置】时，机器人不生成go rest任务；反之生成",
          "i18nCode": "lang.rms.config.robot.dispatching.enabledGoRestAfterTaskCompletedForStrategy",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultIdleRobotNeedGoRestStrategy",
              "idleRobotNeedGoRestForShelfCellStrategy"
            ],
            "defVal": "defaultIdleRobotNeedGoRestStrategy"
          }
        },
        "robot.dispatching.findShelfRestCellStrategy": {
          "id": "274",
          "groupCode": "robot",
          "code": "robot.dispatching.findShelfRestCellStrategy",
          "label": "查询货架休息点策略",
          "desc": "查询货架休息点策略。findShelfRestCellByRoundHandler:默认策略，通过圆形扩散方式，由近及远查询密度最小货架位。findShelfRestCellByFanShapedHandler：通过扇形扩散方式，由近及远查询最小密度的货架位",
          "i18nCode": "lang.rms.config.robot.dispatching.findShelfRestCellStrategy",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "findShelfRestCellByRoundStrategy",
              "findShelfRestCellByFanShapedStrategy"
            ],
            "defVal": "findShelfRestCellByRoundStrategy"
          }
        },
        "robot.dispatching.enabledGoRestAfterTaskCompletedForTime": {
          "id": "8",
          "groupCode": "robot",
          "code": "robot.dispatching.enabledGoRestAfterTaskCompletedForTime",
          "label": "任务完成去休息时间",
          "desc": "任务完成go rest配置。为-1时（默认值）：任务完成不生成go rest。为0时：任务完成，立即go rest。大于0时（N）：任务完成，N秒后，go rest",
          "i18nCode": "lang.rms.config.robot.dispatching.enabledGoRestAfterTaskCompletedForTime",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "`-1",
            "limitMax": "100"
          }
        },
        "robot.dispatching.enabledUpdateGoRestEndPoint": {
          "id": "415",
          "groupCode": "robot",
          "code": "robot.dispatching.enabledUpdateGoRestEndPoint",
          "label": "启用更新去休息任务终点",
          "desc": "启用更新去休息任务终点，去休息的任务终点有其他任务机器人前往。那么更新当前去休息任务终点",
          "i18nCode": "lang.rms.config.robot.dispatching.enabledUpdateGoRestEndPoint",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.dispatching.restCellOutDegreeFirst": {
          "id": "422",
          "groupCode": "robot",
          "code": "robot.dispatching.restCellOutDegreeFirst",
          "label": "优先选择出度大于1的休息点开关",
          "desc": "优先选择出度>1的休息点 开关",
          "i18nCode": "lang.rms.config.robot.dispatching.restCellOutDegreeFirst",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.dispatching.findRestCellStrategy": {
          "id": "423",
          "groupCode": "robot",
          "code": "robot.dispatching.findRestCellStrategy",
          "label": "查询休息点策略",
          "desc": "机器人完成任务后找休息点策略，默认机器人寻找策略defaultRobotFindRestCellStrategy,默认货架机器人策略defaultShelfRobotFindRestCellStrategy,P40机器人策略 p40RobotFindRestCellStrategy,RS机器人策略rSSeriesFindRestCellStrategy",
          "i18nCode": "lang.rms.config.robot.dispatching.findRestCellStrategy",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultRobotFindRestCellStrategy",
              "defaultShelfRobotFindRestCellStrategy",
              "p40RobotFindRestCellStrategy",
              "rSSeriesFindRestCellStrategy",
              "robotFindRestCellByAreaFirstStrategy",
              "robotFindRestCellByPriorityStrategy",
              "robotFindRestCellByStrictAreaStrategy",
              "xSeriesFindRestCellStrategy"
            ]
          }
        },
        "robot.dispatching.enabledMovingObstacleRobotWhenFindNoneRestCell": {
          "id": "486",
          "groupCode": "robot",
          "code": "robot.dispatching.enabledMovingObstacleRobotWhenFindNoneRestCell",
          "label": "找不到可用休息点时是否查询避让点",
          "desc": "当路径堵塞时，障碍机器人找不到可用rest cell时，是否开启查询避让点功能。默认：false",
          "i18nCode": "lang.rms.config.robot.dispatching.enabledMovingObstacleRobotWhenFindNoneRestCell",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.dispatching.findRestAllMap": {
          "id": "1072",
          "groupCode": "robot",
          "code": "robot.dispatching.findRestAllMap",
          "label": "机器人当前区域内没有找到休息点，是否允许全地图寻找",
          "desc": "如果在机器人当前所属的区域内没有找到休息点，是否允许全地图寻找，true允许，false不允许，默认false",
          "i18nCode": "lang.rms.config.robot.dispatching.findRestAllMap",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.task.enabledRestOnShelfCellForPriorityStrategy": {
          "id": "1151",
          "groupCode": "robot",
          "code": "robot.task.enabledRestOnShelfCellForPriorityStrategy",
          "label": "休息点优先级策略是否允许在货架点休息",
          "desc": "休息点优先级策略是否允许在货架点休息",
          "i18nCode": "lang.rms.config.robot.task.enabledRestOnShelfCellForPriorityStrategy",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.dispatching.enabledUpdateGoRestEndPointForWaitingRobot": {
          "id": "1159",
          "groupCode": "robot",
          "code": "robot.dispatching.enabledUpdateGoRestEndPointForWaitingRobot",
          "label": "是否允许交换休息点",
          "desc": "是否允许交换休息点",
          "i18nCode": "lang.rms.config.robot.dispatching.enabledUpdateGoRestEndPointForWaitingRobot",
          "path": "/task/GoRest",
          "pathLabel": "任务/机器人休息",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        }
      },
      "TaskStrategy": {
        "algorithm.job.match.shelfHitNumBase": {
          "id": "1166",
          "groupCode": "algorithm",
          "code": "algorithm.job.match.shelfHitNumBase",
          "label": "算法任务分配货架命中数量基数",
          "desc": "算法任务分配货架命中数量基数",
          "i18nCode": "lang.rms.config.algorithm.job.match.shelfHitNumBase",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "algorithmStrategy",
          "sectionLabel": "算法任务分配策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5D",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "algorithm.job.match.pathCostParam": {
          "id": "1167",
          "groupCode": "algorithm",
          "code": "algorithm.job.match.pathCostParam",
          "label": "算法任务分配路径权重",
          "desc": "算法任务分配路径权重",
          "i18nCode": "lang.rms.config.algorithm.job.match.pathCostParam",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "algorithmStrategy",
          "sectionLabel": "算法任务分配策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.4D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "algorithm.job.match.priorityCostParam": {
          "id": "1168",
          "groupCode": "algorithm",
          "code": "algorithm.job.match.priorityCostParam",
          "label": "算法任务分配优先级权重",
          "desc": "算法任务分配优先级权重",
          "i18nCode": "lang.rms.config.algorithm.job.match.priorityCostParam",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "algorithmStrategy",
          "sectionLabel": "算法任务分配策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.4D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "algorithm.job.match.updateTimeCostParam": {
          "id": "1169",
          "groupCode": "algorithm",
          "code": "algorithm.job.match.updateTimeCostParam",
          "label": "算法任务分配任务更新时间权重",
          "desc": "算法任务分配任务更新时间权重",
          "i18nCode": "lang.rms.config.algorithm.job.match.updateTimeCostParam",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "algorithmStrategy",
          "sectionLabel": "算法任务分配策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.1D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "algorithm.job.match.shelfHitNumCostParam": {
          "id": "1170",
          "groupCode": "algorithm",
          "code": "algorithm.job.match.shelfHitNumCostParam",
          "label": "算法任务分配货架命中数量权重",
          "desc": "算法任务分配货架命中数量权重",
          "i18nCode": "lang.rms.config.algorithm.job.match.shelfHitNumCostParam",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "algorithmStrategy",
          "sectionLabel": "算法任务分配策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.05D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "algorithm.job.match.taskToTargetPathParam": {
          "id": "1171",
          "groupCode": "algorithm",
          "code": "algorithm.job.match.taskToTargetPathParam",
          "label": "算法任务分配负载路径与空载路径的比值权重",
          "desc": "算法任务分配负载路径与空载路径的比值权重",
          "i18nCode": "lang.rms.config.algorithm.job.match.taskToTargetPathParam",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "algorithmStrategy",
          "sectionLabel": "算法任务分配策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.5D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "algorithm.job.match.updateTimeSensitivity": {
          "id": "1172",
          "groupCode": "algorithm",
          "code": "algorithm.job.match.updateTimeSensitivity",
          "label": "算法任务分配：任务更新时间敏感度",
          "desc": "算法任务分配：任务更新时间敏感度",
          "i18nCode": "lang.rms.config.algorithm.job.match.updateTimeSensitivity",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "algorithmStrategy",
          "sectionLabel": "算法任务分配策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "60D",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "cemat.isNeedGoOn": {
          "id": "437",
          "groupCode": "cemat",
          "code": "cemat.isNeedGoOn",
          "label": "是否启动脚本",
          "desc": "是否启动脚本",
          "i18nCode": "lang.rms.config.cemat.isNeedGoOn",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "cemat.adjustArmCell": {
          "id": "438",
          "groupCode": "cemat",
          "code": "cemat.adjustArmCell",
          "label": "C系列调整抱叉坐标",
          "desc": "C系列调整抱叉坐标",
          "i18nCode": "lang.rms.config.cemat.adjustArmCell",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "cemat.restCell": {
          "id": "439",
          "groupCode": "cemat",
          "code": "cemat.restCell",
          "label": "休息坐标",
          "desc": "休息坐标",
          "i18nCode": "lang.rms.config.cemat.restCell",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "cemat.adjustArmCell4High": {
          "id": "440",
          "groupCode": "cemat",
          "code": "cemat.adjustArmCell4High",
          "label": "高位C200M休息坐标",
          "desc": "高位C201M休息坐标",
          "i18nCode": "lang.rms.config.cemat.adjustArmCell4High",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "cemat.restCell4High": {
          "id": "441",
          "groupCode": "cemat",
          "code": "cemat.restCell4High",
          "label": "高位C200M调整抱叉坐标",
          "desc": "高位C201M调整抱叉坐标",
          "i18nCode": "lang.rms.config.cemat.restCell4High",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "yunji.api.signSecretValue": {
          "id": "404",
          "groupCode": "project",
          "code": "yunji.api.signSecretValue",
          "label": "定制化配置内容",
          "desc": "定制化配置内容，当 YUNJI_API_SIGN_TYPE = md5 时，value 代表密钥 secret",
          "i18nCode": "lang.rms.config.yunji.api.signSecretValue",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "yunji.api.signType": {
          "id": "405",
          "groupCode": "project",
          "code": "yunji.api.signType",
          "label": "定制化配置类型",
          "desc": "定制化配置类型，md5=云集 MD5 加密定制化",
          "i18nCode": "lang.rms.config.yunji.api.signType",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "default"
          }
        },
        "project.zara.realtimeObstacle.dartRtlsHost": {
          "id": "537",
          "groupCode": "project",
          "code": "project.zara.realtimeObstacle.dartRtlsHost",
          "label": "反光背心定位系统主机地址",
          "desc": "反光背心定位系统主机地址",
          "i18nCode": "lang.rms.config.project.zara.realtimeObstacle.dartRtlsHost",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "project.zara.realtimeObstacle.dartRtlsPort": {
          "id": "538",
          "groupCode": "project",
          "code": "project.zara.realtimeObstacle.dartRtlsPort",
          "label": "反光背心定位系统主机端口",
          "desc": "反光背心定位系统主机端口",
          "i18nCode": "lang.rms.config.project.zara.realtimeObstacle.dartRtlsPort",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5117",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "project.zara.realtimeObstacle.messageTimestampDeviation": {
          "id": "541",
          "groupCode": "project",
          "code": "project.zara.realtimeObstacle.messageTimestampDeviation",
          "label": "反光背心定位系统消息时间戳误差",
          "desc": "反光背心定位系统消息时间戳误差, 单位s，默认5s",
          "i18nCode": "lang.rms.config.project.zara.realtimeObstacle.messageTimestampDeviation",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "customizedProjest",
          "sectionLabel": "项目定制",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "robot.task.allocationFailureStrategy": {
          "id": "59",
          "groupCode": "taskFailure",
          "code": "robot.task.allocationFailureStrategy",
          "label": "分配任务失败策略",
          "desc": "分配任务失败策略。AUTO_CANCEL:自动取消，IGNORE_FAILURE:忽略失败，BUSINESS_CONTROL：业务控制。默认AUTO_CANCEL",
          "i18nCode": "lang.rms.config.robot.task.allocationFailureStrategy",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "AUTO_CANCEL",
              "IGNORE_FAILURE",
              "BUSINESS_CONTROL"
            ],
            "defVal": "IGNORE_FAILURE"
          }
        },
        "task.enableDealRobotTurnForEnd": {
          "id": "1150",
          "groupCode": "task",
          "code": "task.enableDealRobotTurnForEnd",
          "label": "滚筒机器人到达终点后是否需要本体旋转",
          "desc": "滚筒机器人到达终点后是否需要本体旋转 true：启用；false：不启用",
          "i18nCode": "lang.rms.config.task.enableDealRobotTurnForEnd",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.allocation.findTargetRobotForCommonTaskStrategy": {
          "id": "290",
          "groupCode": "robot",
          "code": "robot.task.allocation.findTargetRobotForCommonTaskStrategy",
          "label": "通用任务分配策略",
          "desc": "通用任务分配策略",
          "i18nCode": "lang.rms.config.robot.task.allocation.findTargetRobotForCommonTaskStrategy",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "defaultFindTargetRobotForCommonTaskStrategy"
          }
        },
        "robot.task.allocation.findTargetRobotForRollerTaskStrategy": {
          "id": "291",
          "groupCode": "robot",
          "code": "robot.task.allocation.findTargetRobotForRollerTaskStrategy",
          "label": "滚筒任务分配策略",
          "desc": "滚筒任务分配策略",
          "i18nCode": "lang.rms.config.robot.task.allocation.findTargetRobotForRollerTaskStrategy",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "defaultFindTargetRobotForRollerTaskStrategy"
          }
        },
        "robot.task.allocation.findTargetRobotForSimpleShelfTaskStrategy": {
          "id": "292",
          "groupCode": "robot",
          "code": "robot.task.allocation.findTargetRobotForSimpleShelfTaskStrategy",
          "label": "简单货架任务分配策略",
          "desc": "简单货架任务分配策略，默认defaultFindTargetRobotForSimpleShelfTaskStrategy,定制项目策略jinkoFindTargetRobotForSimpleShelfTaskStrategy",
          "i18nCode": "lang.rms.config.robot.task.allocation.findTargetRobotForSimpleShelfTaskStrategy",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultFindTargetRobotForSimpleShelfTaskStrategy",
              "jinkoFindTargetRobotForSimpleShelfTaskStrategy"
            ],
            "defVal": "defaultFindTargetRobotForSimpleShelfTaskStrategy"
          }
        },
        "robot.task.allocation.findTargetRobotForStationShelfTaskStrategy": {
          "id": "294",
          "groupCode": "robot",
          "code": "robot.task.allocation.findTargetRobotForStationShelfTaskStrategy",
          "label": "工作站货架任务分配策略",
          "desc": "工作站货架任务分配策略，简单货架任务策略defaultFindTargetRobotForSimpleShelfTaskStrategy,工作站货架任务策略defaultFindTargetRobotForStationShelfTaskStrategy",
          "i18nCode": "lang.rms.config.robot.task.allocation.findTargetRobotForStationShelfTaskStrategy",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultFindTargetRobotForSimpleShelfTaskStrategy",
              "defaultFindTargetRobotForStationShelfTaskStrategy",
              "jinkoFindTargetRobotForSimpleShelfTaskStrategy",
              "jinkoFindTargetRobotForStationShelfTaskStrategy"
            ],
            "defVal": "defaultFindTargetRobotForStationShelfTaskStrategy"
          }
        },
        "robot.task.allocationWaitTime": {
          "id": "296",
          "groupCode": "robot",
          "code": "robot.task.allocationWaitTime",
          "label": "分配任务等待时间",
          "desc": "分配任务等待时间。单位(秒),默认300秒\r\n",
          "i18nCode": "lang.rms.config.robot.task.allocationWaitTime",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "300",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "polling.group.boxTaskAllocation": {
          "id": 256,
          "groupCode": "polling",
          "code": "polling.group.boxTaskAllocation",
          "label": "抱箱任务分配群组",
          "desc": "抱箱任务分配群组",
          "i18nCode": "lang.rms.config.polling.group.boxTaskAllocation",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "polling.group.chargerManagement": {
          "id": "258",
          "groupCode": "polling",
          "code": "polling.group.chargerManagement",
          "label": "充电管理群组",
          "desc": "充电管理群组",
          "i18nCode": "lang.rms.config.polling.group.chargerManagement",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "polling.group.commonTaskAllocation": {
          "id": "259",
          "groupCode": "polling",
          "code": "polling.group.commonTaskAllocation",
          "label": "普通任务分配群组",
          "desc": "普通任务分配群组",
          "i18nCode": "lang.rms.config.polling.group.commonTaskAllocation",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "polling.group.forkliftTaskAllocation": {
          "id": "260",
          "groupCode": "polling",
          "code": "polling.group.forkliftTaskAllocation",
          "label": "叉车任务分配群组",
          "desc": "叉车任务分配群组",
          "i18nCode": "lang.rms.config.polling.group.forkliftTaskAllocation",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "polling.group.maintenanceTaskAllocation": {
          "id": "262",
          "groupCode": "polling",
          "code": "polling.group.maintenanceTaskAllocation",
          "label": "自维护任务分配群组",
          "desc": "自维护任务分配群组",
          "i18nCode": "lang.rms.config.polling.group.maintenanceTaskAllocation",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "polling.group.robotDispatching": {
          "id": "264",
          "groupCode": "polling",
          "code": "polling.group.robotDispatching",
          "label": "机器人调度群组",
          "desc": "机器人调度群组",
          "i18nCode": "lang.rms.config.polling.group.robotDispatching",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "polling.group.rollerTaskAllocation": {
          "id": "265",
          "groupCode": "polling",
          "code": "polling.group.rollerTaskAllocation",
          "label": "滚筒任务分配群组",
          "desc": "滚筒任务分配群组",
          "i18nCode": "lang.rms.config.polling.group.rollerTaskAllocation",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "polling.group.shelfTaskAllocation": {
          "id": "266",
          "groupCode": "polling",
          "code": "polling.group.shelfTaskAllocation",
          "label": "货架任务分配群组",
          "desc": "货架任务分配群组",
          "i18nCode": "lang.rms.config.polling.group.shelfTaskAllocation",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "polling.group.sortingTaskAllocation": {
          "id": "267",
          "groupCode": "polling",
          "code": "polling.group.sortingTaskAllocation",
          "label": "分拣任务分配群组",
          "desc": "分拣任务分配群组",
          "i18nCode": "lang.rms.config.polling.group.sortingTaskAllocation",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "sortingAllocatingIdleRobotInEntryPathToEntryPollingTask,sortingAllocatingIdleRobotInWorkingAreaToEntryPollingTask,sortingAllocatingObstacleRobotInPathToEntryPollingTask,sortingAllocatingRedundantRobotInLogoutStationToEntryPollingTask,sortingAllocatingRobotInQueueAreaToStationPollingTask,sortingGenerateTaskForRobotInStationPollingTask,sortingRemoveLeavingRobotFromStationQueuePollingTask,sortingRemoveSortingTaskOfLogoutStationPollingTask"
          }
        },
        "polling.group.taskDistribution": {
          "id": "268",
          "groupCode": "polling",
          "code": "polling.group.taskDistribution",
          "label": "任务分发群组",
          "desc": "任务分发群组",
          "i18nCode": "lang.rms.config.polling.group.taskDistribution",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "robot.task.callback.converter": {
          "id": "297",
          "groupCode": "robot",
          "code": "robot.task.callback.converter",
          "label": "任务回调转换策略",
          "desc": "任务回调转换策略，项目定制使用，无开发建议请勿修改，[一般机器人策略\"commonRobotTaskCallbackConverterImpl\",托盘机器人策略\"palletRobotTaskCallbackConverterImpl\",辊筒机器人策略\"rollerRobotTaskCallbackConverterImpl\",货架机器人策略\"shelfRobotTaskCallbackConverterImpl\",分拣机器人策略\"sortingRobotTaskCallbackConverterImpl\"]",
          "i18nCode": "lang.rms.config.robot.task.callback.converter",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "commonRobotTaskCallbackConverterImpl",
              "palletRobotTaskCallbackConverterImpl",
              "rollerRobotTaskCallbackConverterImpl",
              "shelfRobotTaskCallbackConverterImpl",
              "sortingRobotTaskCallbackConverterImpl"
            ]
          }
        },
        "robot.task.enabledCrossFloorCharge": {
          "id": "299",
          "groupCode": "robot",
          "code": "robot.task.enabledCrossFloorCharge",
          "label": "开启跨层充电",
          "desc": "跨层充电",
          "i18nCode": "lang.rms.config.robot.task.enabledCrossFloorCharge",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.enabledCrossFloorRest": {
          "id": "300",
          "groupCode": "robot",
          "code": "robot.task.enabledCrossFloorRest",
          "label": "开启跨层休息",
          "desc": "跨层休息",
          "i18nCode": "lang.rms.config.robot.task.enabledCrossFloorRest",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.enabledCrossFloorTransport": {
          "id": "301",
          "groupCode": "robot",
          "code": "robot.task.enabledCrossFloorTransport",
          "label": "开启跨层搬运",
          "desc": "跨层搬运(货架起点和机器人在同一层)",
          "i18nCode": "lang.rms.config.robot.task.enabledCrossFloorTransport",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.enabledMatrixTask2SlamRobot": {
          "id": "302",
          "groupCode": "robot",
          "code": "robot.task.enabledMatrixTask2SlamRobot",
          "label": "是否允许将二维码区域的任务分配给SLAM机器人",
          "desc": "是否允许将二维码区域的任务分配给SLAM机器人",
          "i18nCode": "lang.rms.config.robot.task.enabledMatrixTask2SlamRobot",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.enabledRobotCancelOnWaitCell": {
          "id": "303",
          "groupCode": "robot",
          "code": "robot.task.enabledRobotCancelOnWaitCell",
          "label": "任务是否允许在等待点进行取消",
          "desc": "任务是否允许在等待点进行取消",
          "i18nCode": "lang.rms.config.robot.task.enabledRobotCancelOnWaitCell",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.task.enabledValidateFloorAfterEnteredElevator": {
          "id": "304",
          "groupCode": "robot",
          "code": "robot.task.enabledValidateFloorAfterEnteredElevator",
          "label": "进入电梯后校验机器人上报的楼层是否已经是目标楼层",
          "desc": "进入电梯后校验机器人上报的楼层是否已经是目标楼层",
          "i18nCode": "lang.rms.config.robot.task.enabledValidateFloorAfterEnteredElevator",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "Elevator",
          "sectionLabel": "电梯策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.task.pool.deadTime": {
          "id": "306",
          "groupCode": "robot",
          "code": "robot.task.pool.deadTime",
          "label": "任务分配等待超时时间",
          "desc": "任务分配等待超时时间，超过这个时间RMS将提自动高该任务优先级（单位：分钟）",
          "i18nCode": "lang.rms.config.robot.task.pool.deadTime",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "robot.task.toEndpointMaxRobotQueueSize": {
          "id": "307",
          "groupCode": "robot",
          "code": "robot.task.toEndpointMaxRobotQueueSize",
          "label": "终点支持的最大排队机器人数量",
          "desc": "机器人向终点移动时，终点支持的最大排队机器人数量。为0时，则此排队超限功能不启用",
          "i18nCode": "lang.rms.config.robot.task.toEndpointMaxRobotQueueSize",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "robot.task.globalRestoreMode": {
          "id": "62",
          "groupCode": "robot",
          "code": "robot.task.globalRestoreMode",
          "label": "重启时全局任务恢复模式",
          "desc": "重启时全局任务恢复模式，任务恢复：restore,清除任务：clean,归还货架：returnShelf",
          "i18nCode": "lang.rms.config.robot.task.globalRestoreMode",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "taskRestore",
          "sectionLabel": "任务恢复策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "restore",
              "clean",
              "returnShelf"
            ],
            "defVal": "clean"
          }
        },
        "robot.task.enabledAllocationManualRobot": {
          "id": "414",
          "groupCode": "robot",
          "code": "robot.task.enabledAllocationManualRobot",
          "label": "任务是否可以分配进入遥控模式的机器人",
          "desc": "任务是否可以分配给进入了遥控模式的机器人",
          "i18nCode": "lang.rms.config.robot.task.enabledAllocationManualRobot",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "polling.groups": {
          "id": "10",
          "groupCode": "polling",
          "code": "polling.groups",
          "label": "轮询任务组",
          "desc": "轮询任务组，系统运行参数，非开发人员请勿修改",
          "i18nCode": "lang.rms.config.polling.groups",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskAllocation",
          "sectionLabel": "任务分配群组",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "robot-task-dispatching-group,robot-path-dispatching-group,workstation-controller,equipment-controller,task-distribution-group,charging-management-group,common-task-allocation-group,shelf-task-allocation-group,roller-task-allocation-group,box-task-allocation-group,forklift-task-allocation-group,sorting-task-allocation-group,maintenance-task-allocation-group,job-dispatch-polling-group,elevator-manager,robot-monitor-communication,realtime-path-planning,remove-completed-task,obstacle-scan-task-group,task-reenter-taskpool-group,shelf-location-update,wh-job-distribution-group,RobotAreaDispatchingPollingTask,serial-job-plan-polling-task"
          }
        },
        "polling.task.enabledSupportTaskStop": {
          "id": "269",
          "groupCode": "polling",
          "code": "polling.task.enabledSupportTaskStop",
          "label": "任务暂停后是否允许调度机器人",
          "desc": "任务暂停后是否允许调度机器人",
          "i18nCode": "lang.rms.config.polling.task.enabledSupportTaskStop",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.task.allocation.forkliftTaskStrategy": {
          "id": "1099",
          "groupCode": "robot",
          "code": "robot.task.allocation.forkliftTaskStrategy",
          "label": "叉车任务分配策略",
          "desc": "叉车任务分配策略",
          "i18nCode": "lang.rms.config.robot.task.allocation.forkliftTaskStrategy",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "defaultFindTargetRobotForPalletTaskStrategy"
          }
        },
        "robot.task.allocation.enabledNewAlgoMatch": {
          "id": "1103",
          "groupCode": "robot",
          "code": "robot.task.allocation.enabledNewAlgoMatch",
          "label": "任务分配服务化开关",
          "desc": "任务分配服务化开关",
          "i18nCode": "lang.rms.config.robot.task.allocation.enabledNewAlgoMatch",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.allocation.timeWindowRange": {
          "id": "1106",
          "groupCode": "robot",
          "code": "robot.task.allocation.timeWindowRange",
          "label": "任务分配时间窗口范围",
          "desc": "任务分配时间窗口范围",
          "i18nCode": "lang.rms.config.robot.task.allocation.timeWindowRange",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.task.callback.sendSoonArrivingCellNum": {
          "id": "1115",
          "groupCode": "robot",
          "code": "robot.task.callback.sendSoonArrivingCellNum",
          "label": "提前N个单元格数发送即将到达回调",
          "desc": "提前N个单元格数发送即将到达回调(SOON_ARRIVING)",
          "i18nCode": "lang.rms.config.robot.task.callback.sendSoonArrivingCellNum",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "0",
              "1",
              "2"
            ],
            "defVal": "1"
          }
        },
        "robot.task.callback.enabledSendSoonArrivingAndQueuing": {
          "id": "1116",
          "groupCode": "robot",
          "code": "robot.task.callback.enabledSendSoonArrivingAndQueuing",
          "label": "是否开启即将到达与进入排队回调",
          "desc": "是否开启即将到达与进入排队回调",
          "i18nCode": "lang.rms.config.robot.task.callback.enabledSendSoonArrivingAndQueuing",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.task.enabledTurnAtEndPoint": {
          "id": "1117",
          "groupCode": "robot",
          "code": "robot.task.enabledTurnAtEndPoint",
          "label": "机器人到达终点后提前转向开关",
          "desc": "机器人到达终点后提前转向开关",
          "i18nCode": "lang.rms.config.robot.task.enabledTurnAtEndPoint",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "baseStrategy",
          "sectionLabel": "基础策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.globalRestoreMode.ha": {
          "id": "1162",
          "groupCode": "robot",
          "code": "robot.task.globalRestoreMode.ha",
          "label": "重启时系统恢复模式",
          "desc": "重启时系统恢复模式，\"ha\"：RMS服务状态恢复为重启前的系统状态，重启前未完成的任务，将在服务重启后恢复执行（默认）; \"clean\"：RMS服务状态恢复为首次启动的系统状态，不恢复任务，但会根据机器人信息及任务数据更新货架等资源的相关信息; \"returnShelf\"：RMS服务状态恢复为首次启动的系统状态，并将所有机器人正在负载的货架还回老家（ha模式的降级模式）。",
          "i18nCode": "lang.rms.config.robot.task.globalRestoreMode.ha",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "taskRestore",
          "sectionLabel": "任务恢复策略",
          "type": "checkbox",
          "options": {
            "condition": [
              {
                "code": "robot.task.globalRestoreMode",
                "value": "'ha'"
              }
            ],
            "componentName": "checkbox",
            "defVal": "ha"
          }
        },
        "robot.task.allocation.businessStrategy": {
          "id": "1184",
          "groupCode": "robot",
          "code": "robot.task.allocation.businessStrategy",
          "label": "任务分配服务化业务策略",
          "desc": "任务分配服务化业务策略，STATION_BALANCE_PICKING:工作站均衡拣选， POINT_TO_POINT_TRANSPORT: 点到点搬运，AUTO: 系统自动选择",
          "i18nCode": "lang.rms.config.robot.task.allocation.businessStrategy",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "priorityFirst",
              "STATION_BALANCE_PICKING",
              "POINT_TO_POINT_TRANSPORT",
              "AUTO"
            ],
            "defVal": "priorityFirst"
          }
        },
        "robot.task.allocation.algorithmType": {
          "id": "1185",
          "groupCode": "robot",
          "code": "robot.task.allocation.algorithmType",
          "label": "任务分配算法类型",
          "desc": "任务分配算法类型，[快速：FAST,ortools求解器：ORTOOLS,匈牙利算法：HUNGARIAN,模拟退火：SASOLVER]",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "TaskStrategy",
          "sectionLabel": "任务策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "FAST",
              "ORTOOLS",
              "HUNGARIAN",
              "SASOLVER"
            ],
            "defVal": "FAST"
          }
        },
        "robot.task.globalRestoreMode.ha.isSystemStopped": {
          "id": "1186",
          "groupCode": "robot",
          "code": "robot.task.globalRestoreMode.ha.isSystemStopped",
          "label": "是否强制系统是停止状态",
          "desc": "是否强制系统是停止状态，“是”：系统重启后，RMS服务状态恢复为首次启动的系统状态；“否”：RMS服务状态恢复为重启前的系统状态。（该参数仅在任务恢复模式是ha的情况下生效）",
          "i18nCode": "lang.rms.config.robot.task.globalRestoreMode.ha.isSystemStopped",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "taskRestore",
          "sectionLabel": "任务恢复策略",
          "type": "checkbox",
          "options": {
            "condition": [
              {
                "code": "robot.task.globalRestoreMode",
                "value": "'ha'"
              }
            ],
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.task.enabledUseElevatorTask": {
          "id": "471",
          "groupCode": "robot",
          "code": "robot.task.enabledUseElevatorTask",
          "label": "是否使用电梯",
          "desc": "是否使用电梯",
          "i18nCode": "robot.task.enabledUseElevatorTask",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "Elevator",
          "sectionLabel": "电梯策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.task.elevatorCloseDelayTime": {
          "id": "472",
          "groupCode": "robot",
          "code": "robot.task.elevatorCloseDelayTime",
          "label": "电梯延时n秒关门",
          "desc": "电梯延时n秒关门",
          "i18nCode": "robot.task.elevatorCloseDelayTime",
          "path": "/task/TaskStrategy",
          "pathLabel": "任务/任务策略",
          "section": "Elevator",
          "sectionLabel": "电梯策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "100"
          }
        }
      },
      "charging": {
        "charger.exception.monitoringItems": {
          "id": "3",
          "groupCode": "charger",
          "code": "charger.exception.monitoringItems",
          "label": "充电站异常监控项",
          "desc": "充电站异常监控项",
          "i18nCode": "lang.rms.config.charger.exception.monitoringItems",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerStrategy",
          "sectionLabel": "充电站策略",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "20000,20001,20002,20003,20004,20007,20008,20009,20010,20011,21000,21001"
          }
        },
        "charging.cancelTaskDueToAbnormalTimeout": {
          "id": "13",
          "groupCode": "charging",
          "code": "charging.cancelTaskDueToAbnormalTimeout",
          "label": "充电站异常持续时间超时取消任务时间设置",
          "desc": "充电站异常持续时间超过设置，会自动取消当前的充电任务。默认120秒。单位秒",
          "i18nCode": "lang.rms.config.charging.cancelTaskDueToAbnormalTimeout",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerStrategy",
          "sectionLabel": "充电站策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "120",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "charging.charger.enabledInteractiveMode": {
          "id": "14",
          "groupCode": "charging",
          "code": "charging.charger.enabledInteractiveMode",
          "label": "充电站开启交互充电模式",
          "desc": "充电站开启交互充电模式，true：开启，false：不开启，默认值true",
          "i18nCode": "lang.rms.config.charging.charger.enabledInteractiveMode",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerStrategy",
          "sectionLabel": "充电站策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "charging.chargingTime": {
          "id": "15",
          "groupCode": "charging",
          "code": "charging.chargingTime",
          "label": "最低充电时长",
          "desc": "充电时间，单位：分钟，充电时间满足且达到安全电量比则断开充电，默认值10",
          "i18nCode": "lang.rms.config.charging.chargingTime",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "charging.disconnectedTimeout": {
          "id": "16",
          "groupCode": "charging",
          "code": "charging.disconnectedTimeout",
          "label": "充电站超时断连设置时间",
          "desc": "充电站超过设置时间会变为连接断开状态，默认10秒，单位秒",
          "i18nCode": "lang.rms.config.charging.disconnectedTimeout",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerStrategy",
          "sectionLabel": "充电站策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "charging.enabledCrossAreaCharging": {
          "id": "17",
          "groupCode": "charging",
          "code": "charging.enabledCrossAreaCharging",
          "label": "是否允许跨区域充电",
          "desc": "是否允许跨区域充电，true：允许，false：不允许，默认值true",
          "i18nCode": "lang.rms.config.charging.enabledCrossAreaCharging",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "charging.forbidTime.forbidTimeRange1": {
          "id": "18",
          "groupCode": "charging",
          "code": "charging.forbidTime.forbidTimeRange1",
          "label": "禁止充电时段",
          "desc": "禁止充电时段",
          "i18nCode": "lang.rms.config.charging.forbidTime.forbidTimeRange1",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerIdleStrategy",
          "sectionLabel": "空闲充电策略",
          "type": "timeslot",
          "options": {
            "componentName": "timeslot"
          }
        },
        "charging.forbidTime.forbidTimeRange2": {
          "id": "19",
          "groupCode": "charging",
          "code": "charging.forbidTime.forbidTimeRange2",
          "label": "禁止充电时段",
          "desc": "禁止充电时段",
          "i18nCode": "lang.rms.config.charging.forbidTime.forbidTimeRange2",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerIdleStrategy",
          "sectionLabel": "空闲充电策略",
          "type": "timeslot",
          "options": {
            "componentName": "timeslot"
          }
        },
        "charging.forbidTime.forbidTimeRange3": {
          "id": "20",
          "groupCode": "charging",
          "code": "charging.forbidTime.forbidTimeRange3",
          "label": "禁止充电时段",
          "desc": "禁止充电时段",
          "i18nCode": "lang.rms.config.charging.forbidTime.forbidTimeRange3",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerIdleStrategy",
          "sectionLabel": "空闲充电策略",
          "type": "timeslot",
          "options": {
            "componentName": "timeslot"
          }
        },
        "charging.idleCharging.enabledIdleCharging": {
          "id": "21",
          "groupCode": "charging",
          "code": "charging.idleCharging.enabledIdleCharging",
          "label": "是否开启空闲充电",
          "desc": "开启空闲充电，true：开启，false：不开启，默认值true",
          "i18nCode": "lang.rms.config.charging.idleCharging.enabledIdleCharging",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerIdleStrategy",
          "sectionLabel": "空闲充电策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "charging.idleCharging.idleTime": {
          "id": "22",
          "groupCode": "charging",
          "code": "charging.idleCharging.idleTime",
          "label": "空闲充电时间",
          "desc": "空闲充电时间（单位：分钟），若开启空闲充电 && 机器人空闲 && (机器人空闲时间超过N分钟 || 机器人超过2小时未充电)， 默认值15",
          "i18nCode": "lang.rms.config.charging.idleCharging.idleTime",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerIdleStrategy",
          "sectionLabel": "空闲充电策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "charging.idleCharging.lowPowerPercent": {
          "id": "23",
          "groupCode": "charging",
          "code": "charging.idleCharging.lowPowerPercent",
          "label": "闲时触发充电的电量下限",
          "desc": "闲时充电，触发充电的电量下限，默认：60",
          "i18nCode": "lang.rms.config.charging.idleCharging.lowPowerPercent",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerIdleStrategy",
          "sectionLabel": "空闲充电策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "80",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.idleCharging.taskNum": {
          "id": "24",
          "groupCode": "charging",
          "code": "charging.idleCharging.taskNum",
          "label": "闲时充电连续执行任务数后充电",
          "desc": "闲时充电，连续执行任务数后充电，默认：60",
          "i18nCode": "lang.rms.config.charging.idleCharging.taskNum",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerIdleStrategy",
          "sectionLabel": "空闲充电策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "60",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "charging.interruptPowerPercent": {
          "id": "25",
          "groupCode": "charging",
          "code": "charging.interruptPowerPercent",
          "label": "允许任务打断的电量下限",
          "desc": "允许任务打断的电量下限",
          "i18nCode": "lang.rms.config.charging.interruptPowerPercent",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "60",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.lowPowerPercent": {
          "id": "26",
          "groupCode": "charging",
          "code": "charging.lowPowerPercent",
          "label": "低电量比",
          "desc": "低电量比（0~100），若关闭了空闲充电且机器人电量大于低电量比，则清除充电需求，默认值30",
          "i18nCode": "lang.rms.config.charging.lowPowerPercent",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "60",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.offlineTimeout": {
          "id": "27",
          "groupCode": "charging",
          "code": "charging.offlineTimeout",
          "label": "充电站连接断开超过设置时间会变为失联状态",
          "desc": "充电站连接断开超过设置时间会变为失联状态，默认50秒，单位秒",
          "i18nCode": "lang.rms.config.charging.offlineTimeout",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "50",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "charging.power.enabledPowerStrategy": {
          "id": "28",
          "groupCode": "charging",
          "code": "charging.power.enabledPowerStrategy",
          "label": "充电电量控制",
          "desc": "默认启用true",
          "i18nCode": "lang.rms.config.charging.power.enabledPowerStrategy",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "charging.priorityWhenLowPowerPercent": {
          "id": "29",
          "groupCode": "charging",
          "code": "charging.priorityWhenLowPowerPercent",
          "label": "低电量充电优先级",
          "desc": "当电量低于配置时，优先让idle与最后一次go_return的机器人去充电",
          "i18nCode": "lang.rms.config.charging.priorityWhenLowPowerPercent",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "35",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.safePowerPercent": {
          "id": "30",
          "groupCode": "charging",
          "code": "charging.safePowerPercent",
          "label": "充电安全电量",
          "desc": "安全电量比（0~100），充电时间满足且达到安全电量比，则断开充电",
          "i18nCode": "lang.rms.config.charging.safePowerPercent",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "70",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.task.waitTimeMinutes": {
          "id": "31",
          "groupCode": "charging",
          "code": "charging.task.waitTimeMinutes",
          "label": "充电任务超时未执行重新分配",
          "desc": "充电任务超时未执行重新分配，单位秒，默认：10",
          "i18nCode": "lang.rms.config.charging.task.waitTimeMinutes",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "charging.task.waitTimeoutEnabled": {
          "id": "32",
          "groupCode": "charging",
          "code": "charging.task.waitTimeoutEnabled",
          "label": "充电任务控制",
          "desc": "充电任务控制",
          "i18nCode": "lang.rms.config.charging.task.waitTimeoutEnabled",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "charging.upperPowerPercent": {
          "id": "33",
          "groupCode": "charging",
          "code": "charging.upperPowerPercent",
          "label": "充电电量上限",
          "desc": "充电电量上限",
          "i18nCode": "lang.rms.config.charging.upperPowerPercent",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "90",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.forbidTime.forbidTimeRange": {
          "id": "130",
          "groupCode": "charging",
          "code": "charging.forbidTime.forbidTimeRange",
          "label": "禁止充电时段",
          "desc": "禁止充电时段",
          "i18nCode": "lang.rms.config.charging.forbidTime.forbidTimeRange",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerIdleStrategy",
          "sectionLabel": "空闲充电策略",
          "type": "timeslot",
          "options": {
            "componentName": "timeslot"
          }
        },
        "charging.task.allowedPowerToBePreempted": {
          "id": "408",
          "groupCode": "charging",
          "code": "charging.task.allowedPowerToBePreempted",
          "label": "允许被抢占的电量",
          "desc": "允许被抢占的电量",
          "i18nCode": "lang.rms.config.charging.task.allowedPowerToBePreempted",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerGrabStrategy",
          "sectionLabel": "充电抢占策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "30",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.task.triggerPreemptionPower": {
          "id": "409",
          "groupCode": "charging",
          "code": "charging.task.triggerPreemptionPower",
          "label": "触发抢占的电量",
          "desc": "触发抢占的电量",
          "i18nCode": "lang.rms.config.charging.task.triggerPreemptionPower",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerGrabStrategy",
          "sectionLabel": "充电抢占策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.task.electricityPreemptionDifference": {
          "id": "410",
          "groupCode": "charging",
          "code": "charging.task.electricityPreemptionDifference",
          "label": "电量抢占差值",
          "desc": "电量抢占差值",
          "i18nCode": "lang.rms.config.charging.task.electricityPreemptionDifference",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerGrabStrategy",
          "sectionLabel": "充电抢占策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.lowPowerPercentWarnThreshold": {
          "id": "455",
          "groupCode": "charging",
          "code": "charging.lowPowerPercentWarnThreshold",
          "label": "低电量告警百分比阈值",
          "desc": "低电量告警百分比阈值",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "charging.enabledFullCharging": {
          "id": "457",
          "groupCode": "charging",
          "code": "charging.enabledFullCharging",
          "label": "定期满充校验",
          "desc": "定期满充校验",
          "i18nCode": "lang.rms.config.charging.enabledFullCharging",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerFullCharging",
          "sectionLabel": "满充策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "charging.fullChargingCycle": {
          "id": "458",
          "groupCode": "charging",
          "code": "charging.fullChargingCycle",
          "label": "校验周期",
          "desc": "满充检测周期，单位为天",
          "i18nCode": "lang.rms.config.charging.fullChargingCycle",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerFullCharging",
          "sectionLabel": "满充策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "9",
            "limitMin": "0",
            "limitMax": "360"
          }
        },
        "charging.fullChargingDeviation": {
          "id": "459",
          "groupCode": "charging",
          "code": "charging.fullChargingDeviation",
          "label": "满充容错值",
          "desc": "满充容错值，如：1，代表允许1%的偏差，即充到99%即可认为充满了",
          "i18nCode": "lang.rms.config.charging.fullChargingDeviation",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerFullCharging",
          "sectionLabel": "满充策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "robot.task.unworkableWhenLowPower": {
          "id": "65",
          "groupCode": "robot",
          "code": "robot.task.unworkableWhenLowPower",
          "label": "不接收任务的最低电量",
          "desc": "不接收任务的最低电量",
          "i18nCode": "lang.rms.config.robot.task.unworkableWhenLowPower",
          "path": "/task/charging",
          "pathLabel": "任务/充电策略",
          "section": "chargerTaskStrategy",
          "sectionLabel": "充电任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15",
            "limitMin": "0",
            "limitMax": "100"
          }
        }
      },
      "inspection": {
        "inspection.task.savePath": {
          "id": "34",
          "groupCode": "inspection",
          "code": "inspection.task.savePath",
          "label": "巡检结果保存路径",
          "desc": "巡检结果保存路径",
          "i18nCode": "lang.rms.config.inspection.task.savePath",
          "path": "/task/inspection",
          "pathLabel": "任务/巡检任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/var/log/geekplus/tomcat-rms/athena/file/excel/"
          }
        },
        "inspection.task.inspectionWaitingTime": {
          "id": "141",
          "groupCode": "inspection",
          "code": "inspection.task.inspectionWaitingTime",
          "label": "巡检任务完成等待时间",
          "desc": "巡检任务完成等待时间，单位s",
          "i18nCode": "lang.rms.config.inspection.task.inspectionWaitingTime",
          "path": "/task/inspection",
          "pathLabel": "任务/巡检任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "inspection.task.sequencingStrategy": {
          "id": "142",
          "groupCode": "inspection",
          "code": "inspection.task.sequencingStrategy",
          "label": "巡检任务行进点策略",
          "desc": "巡检任务行进点策略",
          "i18nCode": "lang.rms.config.inspection.task.sequencingStrategy",
          "path": "/task/inspection",
          "pathLabel": "任务/巡检任务",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "inspectionWithNearFirstStrategy",
              "defaultWarehouseInspectionTaskStrategy"
            ],
            "defVal": "defaultWarehouseInspectionTaskStrategy"
          }
        },
        "inspection.enabledInspectionMap": {
          "id": "469",
          "groupCode": "inspection",
          "code": "inspection.enabledInspectionMap",
          "label": "开启地图巡检功能",
          "desc": "开启地图巡检功能",
          "i18nCode": "lang.rms.config.inspection.enabledInspectionMap",
          "path": "/task/inspection",
          "pathLabel": "任务/巡检任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "inspection.inspectionIntervalTime": {
          "id": "470",
          "groupCode": "inspection",
          "code": "inspection.inspectionIntervalTime",
          "label": "巡检间隔时间",
          "desc": "默认的巡检间隔时间。默认：10，单位分钟。",
          "i18nCode": "lang.rms.config.inspection.inspectionIntervalTime",
          "path": "/task/inspection",
          "pathLabel": "任务/巡检任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "inspection.task.groundQRAngleDeviation": {
          "id": "1145",
          "groupCode": "inspection",
          "code": "inspection.task.groundQRAngleDeviation",
          "label": "地面二维码角度偏差值",
          "desc": "地面二维码角度偏差值",
          "i18nCode": "lang.rms.config.inspection.task.groundQRAngleDeviation",
          "path": "/task/inspection",
          "pathLabel": "任务/巡检任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "inspection.task.groundQRLocationDeviation": {
          "id": "1146",
          "groupCode": "inspection",
          "code": "inspection.task.groundQRLocationDeviation",
          "label": "地面二维码偏差",
          "desc": "地面二维码偏差，单位米",
          "i18nCode": "lang.rms.config.inspection.task.groundQRLocationDeviation",
          "path": "/task/inspection",
          "pathLabel": "任务/巡检任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.03",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "inspection.task.shelfQRAngleDeviation": {
          "id": "1147",
          "groupCode": "inspection",
          "code": "inspection.task.shelfQRAngleDeviation",
          "label": "货架角度偏差",
          "desc": "货架角度偏差",
          "i18nCode": "lang.rms.config.inspection.task.shelfQRAngleDeviation",
          "path": "/task/inspection",
          "pathLabel": "任务/巡检任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "inspection.task.shelfQRLocationDeviation": {
          "id": "1148",
          "groupCode": "inspection",
          "code": "inspection.task.shelfQRLocationDeviation",
          "label": "货架坐标偏差",
          "desc": "货架坐标偏差，单位米",
          "i18nCode": "lang.rms.config.inspection.task.shelfQRLocationDeviation",
          "path": "/task/inspection",
          "pathLabel": "任务/巡检任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.03",
            "limitMin": "0",
            "limitMax": "1"
          }
        }
      },
      "click": {
        "click.gathering.area": {
          "id": "131",
          "groupCode": "businessInstruction",
          "code": "click.gathering.area",
          "label": "集合区域",
          "desc": "集合区域：用一个矩形区域对角线的两个单元格二维码表示，多个区域用“;”隔开，",
          "i18nCode": "lang.rms.config.click.gathering.area",
          "path": "/task/click",
          "pathLabel": "任务/一键类任务",
          "section": "clickGathering",
          "sectionLabel": "一键集合任务",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "click.gathering.enabledUsingShelfCell": {
          "id": "132",
          "groupCode": "businessInstruction",
          "code": "click.gathering.enabledUsingShelfCell",
          "label": "是否使用货架下的位置",
          "desc": "集合区域是否使用货架下的位置",
          "i18nCode": "lang.rms.config.click.gathering.enabledUsingShelfCell",
          "path": "/task/click",
          "pathLabel": "任务/一键类任务",
          "section": "clickGathering",
          "sectionLabel": "一键集合任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "click.gathering.keepingSpaceIntervalDistance": {
          "id": "133",
          "groupCode": "businessInstruction",
          "code": "click.gathering.keepingSpaceIntervalDistance",
          "label": "机器人间隔距离",
          "desc": "机器人间隔距离，当使用间隔时（即\"click.gathering.keepingSpaceIntervalMode\">0），此值表示间隔几个单元格",
          "i18nCode": "lang.rms.config.click.gathering.keepingSpaceIntervalDistance",
          "path": "/task/click",
          "pathLabel": "任务/一键类任务",
          "section": "clickGathering",
          "sectionLabel": "一键集合任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "3",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "click.gathering.keepingSpaceIntervalMode": {
          "id": "134",
          "groupCode": "businessInstruction",
          "code": "click.gathering.keepingSpaceIntervalMode",
          "label": "机器人间是否有间隔",
          "desc": "是否使用间隔, 0不使用, 1：x方向间隔，2：y方向间隔，3：xy方向都间隔",
          "i18nCode": "lang.rms.config.click.gathering.keepingSpaceIntervalMode",
          "path": "/task/click",
          "pathLabel": "任务/一键类任务",
          "section": "clickGathering",
          "sectionLabel": "一键集合任务",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "0",
              "1",
              "2",
              "3"
            ],
            "defVal": "0"
          }
        },
        "click.gathering.strategy": {
          "id": "135",
          "groupCode": "businessInstruction",
          "code": "click.gathering.strategy",
          "label": "集合策略",
          "desc": "集合策略：normal 按照区域方向集合，默认值normal，目前没用到",
          "i18nCode": "lang.rms.config.click.gathering.strategy",
          "path": "/task/click",
          "pathLabel": "任务/一键类任务",
          "section": "clickGathering",
          "sectionLabel": "一键集合任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "normal"
          }
        },
        "click.scanningCell.executor": {
          "id": "136",
          "groupCode": "businessInstruction",
          "code": "click.scanningCell.executor",
          "label": "扫描策略",
          "desc": "扫描执行器，可选[全场随机策略的扫描执行器：randomScanExecutor]",
          "i18nCode": "lang.rms.config.click.scanningCell.executor",
          "path": "/task/click",
          "pathLabel": "任务/一键类任务",
          "section": "clickScanning",
          "sectionLabel": "一键扫描任务",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "randomScanExecutor"
            ],
            "defVal": "randomScanExecutor"
          }
        },
        "click.scanningCell.maxScanningRobotNum": {
          "id": "137",
          "groupCode": "businessInstruction",
          "code": "click.scanningCell.maxScanningRobotNum",
          "label": "同时扫描的最大机器人数量",
          "desc": "同时扫描的机器人最大数量",
          "i18nCode": "lang.rms.config.click.scanningCell.maxScanningRobotNum",
          "path": "/task/click",
          "pathLabel": "任务/一键类任务",
          "section": "clickScanning",
          "sectionLabel": "一键扫描任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "click.shutdown.executor": {
          "id": "138",
          "groupCode": "businessInstruction",
          "code": "click.shutdown.executor",
          "label": "一键关机模式",
          "desc": "一键关机模式0：集合后关机；1：移动到高速路单元格关机；2：直接关机",
          "i18nCode": "lang.rms.config.click.shutdown.executor",
          "path": "/task/click",
          "pathLabel": "任务/一键类任务",
          "section": "clickShutdown.",
          "sectionLabel": "一键关机任务",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              0,
              1,
              2
            ],
            "defVal": "1"
          }
        }
      },
      "sorting": {
        "sorting.area.difference": {
          "id": "342",
          "groupCode": "sorting",
          "code": "sorting.area.difference",
          "label": "分拣区域的机器人的差数",
          "desc": "分拣区域的机器人的差数",
          "i18nCode": "lang.rms.config.sorting.area.difference",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.area.top": {
          "id": "343",
          "groupCode": "sorting",
          "code": "sorting.area.top",
          "label": "离的近的前N个分拣区域",
          "desc": "离的近的前N个分拣区域，默认值2",
          "i18nCode": "lang.rms.config.sorting.area.top",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.cell.enabledSpareDrop": {
          "id": "344",
          "groupCode": "sorting",
          "code": "sorting.cell.enabledSpareDrop",
          "label": "是否开启备用投递",
          "desc": "是否开启备用投递",
          "i18nCode": "lang.rms.config.sorting.cell.enabledSpareDrop",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "sorting.cell.maxDropNumConcurrency": {
          "id": "345",
          "groupCode": "sorting",
          "code": "sorting.cell.maxDropNumConcurrency",
          "label": "过滤掉预定量已经大于N的投递口",
          "desc": "过滤掉预定量已经大于N的投递口",
          "i18nCode": "lang.rms.config.sorting.cell.maxDropNumConcurrency",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "100",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "sorting.cell.noDropCellFoundAction": {
          "id": "346",
          "groupCode": "sorting",
          "code": "sorting.cell.noDropCellFoundAction",
          "label": "分拣格口不可投递后的策略",
          "desc": "分拣格口不可投递后的策略，0：原地不动，1：强制投递收容格口",
          "i18nCode": "lang.rms.config.sorting.cell.noDropCellFoundAction",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              0,
              1
            ],
            "defVal": "0"
          }
        },
        "sorting.entry.allocationStrategy": {
          "id": "347",
          "groupCode": "sorting",
          "code": "sorting.entry.allocationStrategy",
          "label": "分配分拣机器人去入口的策略",
          "desc": "分配分拣机器人去入口的策略，非开发建议请勿修改，[\"defaultEntryAllocationStrategy\",\"entryAllocationStrategyRouter\",\"newEntryAllocationStrategy\"]",
          "i18nCode": "lang.rms.config.sorting.entry.allocationStrategy",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "entry",
          "sectionLabel": "入口点策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultEntryAllocationStrategy",
              "entryAllocationStrategyRouter",
              "newEntryAllocationStrategy"
            ],
            "defVal": "defaultEntryAllocationStrategy"
          }
        },
        "sorting.entry.enabledChooseOptimization": {
          "id": "348",
          "groupCode": "sorting",
          "code": "sorting.entry.enabledChooseOptimization",
          "label": "开启格口到入口选择的优化",
          "desc": "分拣是否开启格口到入口选择的优化（默认使用曼哈顿距离，开启后使用AStar距离）",
          "i18nCode": "lang.rms.config.sorting.entry.enabledChooseOptimization",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "entry",
          "sectionLabel": "入口点策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "sorting.entry.globalMaxQueueSize": {
          "id": "349",
          "groupCode": "sorting",
          "code": "sorting.entry.globalMaxQueueSize",
          "label": "分拣入口最大排队数量",
          "desc": "分拣入口排队最大长度",
          "i18nCode": "lang.rms.config.sorting.entry.globalMaxQueueSize",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "entry",
          "sectionLabel": "入口点策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.entry.queueSizeSmoothing": {
          "id": "350",
          "groupCode": "sorting",
          "code": "sorting.entry.queueSizeSmoothing",
          "label": "入口机器人数量小于该阈值时不启用饥饿优先",
          "desc": "入口机器人数量小于该阈值时不启用饥饿优先，取值范围：[1,+∞]，默认值1",
          "i18nCode": "lang.rms.config.sorting.entry.queueSizeSmoothing",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "entry",
          "sectionLabel": "入口点策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "1",
            "limitMax": "99999999"
          }
        },
        "sorting.entry.select": {
          "id": "351",
          "groupCode": "sorting",
          "code": "sorting.entry.select",
          "label": "从最近的入口里再选排队最少的n个",
          "desc": "从最近的入口里再选排队最少的n个，默认值：1",
          "i18nCode": "lang.rms.config.sorting.entry.select",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "entry",
          "sectionLabel": "入口点策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.entry.top": {
          "id": "352",
          "groupCode": "sorting",
          "code": "sorting.entry.top",
          "label": "分拣最近的n个入口",
          "desc": "分拣最近的n个入口，默认值5",
          "i18nCode": "lang.rms.config.sorting.entry.top",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "entry",
          "sectionLabel": "入口点策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.robot.enabledDropBeforeTurnAround": {
          "id": "353",
          "groupCode": "sorting",
          "code": "sorting.robot.enabledDropBeforeTurnAround",
          "label": "是否允许分拣机器人转身后投递",
          "desc": "是否允许分拣机器人转身后投递",
          "i18nCode": "lang.rms.config.sorting.robot.enabledDropBeforeTurnAround",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "robot",
          "sectionLabel": "机器人策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "sorting.robot.enabledGotoGatherArea": {
          "id": "354",
          "groupCode": "sorting",
          "code": "sorting.robot.enabledGotoGatherArea",
          "label": "分拣机器人是否去指定集合区",
          "desc": "分拣机器人是否去指定集合区",
          "i18nCode": "lang.rms.config.sorting.robot.enabledGotoGatherArea",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "robot",
          "sectionLabel": "机器人策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "sorting.robot.moveCost": {
          "id": "355",
          "groupCode": "sorting",
          "code": "sorting.robot.moveCost",
          "label": "分拣机器人单位路径耗时",
          "desc": "分拣机器人单位路径耗时",
          "i18nCode": "lang.rms.config.sorting.robot.moveCost",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "robot",
          "sectionLabel": "机器人策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "750.0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "sorting.robot.tray.adjustingAngle": {
          "id": "356",
          "groupCode": "sorting",
          "code": "sorting.robot.tray.adjustingAngle",
          "label": "启动时旋转平台角度",
          "desc": "启动时旋转平台角度，默认值90",
          "i18nCode": "lang.rms.config.sorting.robot.tray.adjustingAngle",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "robot",
          "sectionLabel": "机器人策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "90",
            "limitMin": "0",
            "limitMax": "360"
          }
        },
        "sorting.robot.tray.enabledAutoAdjusting": {
          "id": "357",
          "groupCode": "sorting",
          "code": "sorting.robot.tray.enabledAutoAdjusting",
          "label": "启动时是否旋转平台",
          "desc": "启动时是否旋转平台，S20带有机构的机器人需要开启",
          "i18nCode": "lang.rms.config.sorting.robot.tray.enabledAutoAdjusting",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "robot",
          "sectionLabel": "机器人策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "sorting.station.allocationStrategy": {
          "id": "358",
          "groupCode": "sorting",
          "code": "sorting.station.allocationStrategy",
          "label": "分拣机器人分配工作站策略",
          "desc": "分配分拣机器人去工作站的策略，[\"defaultStationAllocationStrategy\",\"enterStationAllocationStrategy\",\"enterStationByPredictorStrategy\",\"newEnterStationAllocationStrategy\",\"newStationAllocationStrategy\"]",
          "i18nCode": "lang.rms.config.sorting.station.allocationStrategy",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultStationAllocationStrategy",
              "enterStationAllocationStrategy",
              "enterStationByPredictorStrategy",
              "newEnterStationAllocationStrategy",
              "newStationAllocationStrategy"
            ],
            "defVal": "defaultStationAllocationStrategy"
          }
        },
        "sorting.station.beltAdjustingDistance": {
          "id": "359",
          "groupCode": "sorting",
          "code": "sorting.station.beltAdjustingDistance",
          "label": "皮带的调整距离",
          "desc": "皮带的调整距离，单位为mm，默认值为0",
          "i18nCode": "lang.rms.config.sorting.station.beltAdjustingDistance",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "sorting.station.enabledAvailableRobotIncludeQueue": {
          "id": "360",
          "groupCode": "sorting",
          "code": "sorting.station.enabledAvailableRobotIncludeQueue",
          "label": "是否只统计排队的机器人",
          "desc": "是否只统计排队的机器人",
          "i18nCode": "lang.rms.config.sorting.station.enabledAvailableRobotIncludeQueue",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "sorting.station.enabledChooseOptimization": {
          "id": "361",
          "groupCode": "sorting",
          "code": "sorting.station.enabledChooseOptimization",
          "label": "优化从格口到工作站的选择",
          "desc": "优化从格口到工作站的选择",
          "i18nCode": "lang.rms.config.sorting.station.enabledChooseOptimization",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "sorting.station.enabledDropParcelBeforeUnloadLeaving": {
          "id": "362",
          "groupCode": "sorting",
          "code": "sorting.station.enabledDropParcelBeforeUnloadLeaving",
          "label": "是否分拣机器人空载离开工位时，先做一次投递",
          "desc": "是否分拣机器人空载离开工位时，先做一次投递",
          "i18nCode": "lang.rms.config.sorting.station.enabledDropParcelBeforeUnloadLeaving",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "sorting.station.enabledOfflineAllowRobotOverflow": {
          "id": "363",
          "groupCode": "sorting",
          "code": "sorting.station.enabledOfflineAllowRobotOverflow",
          "label": "是否允许离线站排队机器人溢出",
          "desc": "是否允许离线站排队机器人溢出",
          "i18nCode": "lang.rms.config.sorting.station.enabledOfflineAllowRobotOverflow",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "sorting.station.login.maxQueueSize": {
          "id": "364",
          "groupCode": "sorting",
          "code": "sorting.station.login.maxQueueSize",
          "label": "可用分拣工作站排队最大长度",
          "desc": "可用分拣工作站排队最大长度，默认值5",
          "i18nCode": "lang.rms.config.sorting.station.login.maxQueueSize",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.station.login.minQueueSize": {
          "id": "365",
          "groupCode": "sorting",
          "code": "sorting.station.login.minQueueSize",
          "label": "可用分拣工作站排队最小长度",
          "desc": "可用分拣工作站排队最小长度（若未配置则是可用分拣工作站排队最大长度/2），默认值3",
          "i18nCode": "lang.rms.config.sorting.station.login.minQueueSize",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "3",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.station.logout.enabledCancelReceive": {
          "id": "366",
          "groupCode": "sorting",
          "code": "sorting.station.logout.enabledCancelReceive",
          "label": "分拣工作站关闭是否取消收货",
          "desc": "分拣工作站关闭是否取消收货",
          "i18nCode": "lang.rms.config.sorting.station.logout.enabledCancelReceive",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "sorting.station.logout.maxQueueSize": {
          "id": "367",
          "groupCode": "sorting",
          "code": "sorting.station.logout.maxQueueSize",
          "label": "不可用分拣工作站排队最大长度",
          "desc": "不可用分拣工作站排队最大长度，默认值3",
          "i18nCode": "lang.rms.config.sorting.station.logout.maxQueueSize",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "3",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.station.queueSizeCostPow": {
          "id": "368",
          "groupCode": "sorting",
          "code": "sorting.station.queueSizeCostPow",
          "label": "分拣工作站排队机器人数量的影响指数",
          "desc": "分拣工作站排队机器人数量的影响指数,指数级增长,不宜过大,默认为3",
          "i18nCode": "lang.rms.config.sorting.station.queueSizeCostPow",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "3",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.station.queueSizeSmoothing": {
          "id": "369",
          "groupCode": "sorting",
          "code": "sorting.station.queueSizeSmoothing",
          "label": "工作站机器人数量小于该阈值时不启用饥饿优先",
          "desc": "工作站机器人数量小于该阈值时不启用饥饿优先，取值范围：[1,+∞]",
          "i18nCode": "lang.rms.config.sorting.station.queueSizeSmoothing",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "1",
            "limitMax": "99999999"
          }
        },
        "sorting.station.top": {
          "id": "370",
          "groupCode": "sorting",
          "code": "sorting.station.top",
          "label": "分拣工作站选择",
          "desc": "分拣工作站选择，默认值：5",
          "i18nCode": "lang.rms.config.sorting.station.top",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.task.callbackChannelId": {
          "id": "371",
          "groupCode": "sorting",
          "code": "sorting.task.callbackChannelId",
          "label": "分拣任务回调消息通道ID",
          "desc": "分拣任务回调消息通道ID",
          "i18nCode": "lang.rms.config.sorting.task.callbackChannelId",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "sorting.test.bigPackageScale": {
          "id": "372",
          "groupCode": "sorting",
          "code": "sorting.test.bigPackageScale",
          "label": "分拣测试上件大包裹率",
          "desc": "分拣测试上件大包裹率，分拣测试 - 上件大包裹率，取值范围[0，100]",
          "i18nCode": "lang.rms.config.sorting.test.bigPackageScale",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "35",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.test.feedTime": {
          "id": "373",
          "groupCode": "sorting",
          "code": "sorting.test.feedTime",
          "label": "分拣测试上件时间",
          "desc": "分拣测试上件时间，分拣测试 - 上件时间,单位：秒,默认值15",
          "i18nCode": "lang.rms.config.sorting.test.feedTime",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.task.enabledAutoGenerate": {
          "id": "406",
          "groupCode": "sorting",
          "code": "sorting.task.enabledAutoGenerate",
          "label": "分拣是否自动生成供包任务",
          "desc": "分拣是否自动生成供包任务，任务下发模式需要设置为关闭",
          "i18nCode": "lang.rms.config.sorting.task.enabledAutoGenerate",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "sorting.task.enabledDropCrossStation": {
          "id": "467",
          "groupCode": "sorting",
          "code": "sorting.task.enabledDropCrossStation",
          "label": "分拣是否允许投递穿过工作站",
          "desc": "分拣是否允许投递穿过工作站",
          "i18nCode": "lang.rms.config.sorting.task.enabledDropCrossStation",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "sorting.task.enabledAdvanceCompleteQueueTask": {
          "id": "1063",
          "groupCode": "sorting",
          "code": "sorting.task.enabledAdvanceCompleteQueueTask",
          "label": "分拣是否提前完成进站任务",
          "desc": "分拣是否提前完成进站任务",
          "i18nCode": "lang.rms.config.sorting.task.enabledAdvanceCompleteQueueTask",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "sorting.task.enabledAdvanceCompleteGoNextTask": {
          "id": "1064",
          "groupCode": "sorting",
          "code": "sorting.task.enabledAdvanceCompleteGoNextTask",
          "label": "分拣是否提前完成GO_NEXT任务",
          "desc": "分拣是否提前完成GO_NEXT任务",
          "i18nCode": "lang.rms.config.sorting.task.enabledAdvanceCompleteGoNextTask",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "sorting.task.advanceCompleteQueueTaskDistance": {
          "id": "1065",
          "groupCode": "sorting",
          "code": "sorting.task.advanceCompleteQueueTaskDistance",
          "label": "进站距终点多少距离提前完成任务",
          "desc": "分拣进站距终点多少距离以内提前完成任务",
          "i18nCode": "lang.rms.config.sorting.task.advanceCompleteQueueTaskDistance",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "task",
          "sectionLabel": "任务策略",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "sorting.station.enabledClosedWhenInit": {
          "id": "1108",
          "groupCode": "sorting",
          "code": "sorting.station.enabledClosedWhenInit",
          "label": "分拣工位初始化时状态是否默认登出",
          "desc": "分拣工位初始化时状态是否默认登出",
          "i18nCode": "lang.rms.config.sorting.station.enabledClosedWhenInit",
          "path": "/task/sorting",
          "pathLabel": "任务/分拣任务",
          "section": "station",
          "sectionLabel": "工作站策略",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        }
      },
      "box": {
        "box.singleJob.batchThreshold": {
          "id": "489",
          "groupCode": "box",
          "code": "box.singleJob.batchThreshold",
          "label": "单个任务请求取货箱数量上限",
          "desc": "单个任务请求取货箱数量上限",
          "i18nCode": "lang.rms.config.box.singleJob.batchThreshold",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "box.swap.outerToInnerLattice": {
          "id": "491",
          "groupCode": "box",
          "code": "box.swap.outerToInnerLattice",
          "label": "取内箱时是否将外部货箱归还到内部货位",
          "desc": "取内箱时是否将外部货箱归还到内部货位",
          "i18nCode": "lang.rms.config.box.swap.outerToInnerLattice",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "box.destLattice.matcherStrategy": {
          "id": "492",
          "groupCode": "box",
          "code": "box.destLattice.matcherStrategy",
          "label": "货箱目的货位匹配策略",
          "desc": "货箱目的货位匹配策略",
          "i18nCode": "lang.rms.config.box.destLattice.matcherStrategy",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "defaultDestLatticeStrategy"
          }
        },
        "box.job.completeInAdvance": {
          "id": "493",
          "groupCode": "box",
          "code": "box.job.completeInAdvance",
          "label": "是否提前完成任务",
          "desc": "是否提前完成任务",
          "i18nCode": "lang.rms.config.box.job.completeInAdvance",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.dispatching.endpointOffsetForBox": {
          "id": "504",
          "groupCode": "robot",
          "code": "robot.dispatching.endpointOffsetForBox",
          "label": "货箱任务的终点偏移配置",
          "desc": "货箱任务的终点偏移配置，默认0：:go_fetch_box/go_return_box偏移；1：go_fetch_box/go_return_box/go_deliver_box偏移；2：go_fetch_box/go_return_box/go_deliver_box/go_somewhere偏移。",
          "i18nCode": "lang.rms.config.robot.dispatching.endpointOffsetForBox",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              0,
              1,
              2
            ],
            "defVal": "0"
          }
        },
        "robot.task.boxTaskStopTimeOfFetchOrReturn": {
          "id": "509",
          "groupCode": "robot",
          "code": "robot.task.boxTaskStopTimeOfFetchOrReturn",
          "label": "抱箱任务取箱与还箱需要的时间",
          "desc": "抱箱任务取箱与还箱需要的时间，单位：秒",
          "i18nCode": "lang.rms.config.robot.task.boxTaskStopTimeOfFetchOrReturn",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "45.0",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "box.dynamicAdjustment.areas": {
          "id": "118",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.areas",
          "label": "可进行货箱位置调整的区域",
          "desc": "可进行货箱位置调整的区域",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.areas",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "box.dynamicAdjustment.enabledCalcReturnDist": {
          "id": "119",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.enabledCalcReturnDist",
          "label": "当所有货箱的zone都相等时，开启根据还货箱距离进行动态调整",
          "desc": "当所有货箱的zone都相等时，开启根据还货箱距离进行动态调整",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.enabledCalcReturnDist",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "box.dynamicAdjustment.enabledDiffArea": {
          "id": "120",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.enabledDiffArea",
          "label": "是否支持跨区货箱位调整",
          "desc": "是否支持跨区货箱位调整，true：是，false：否，默认为false。",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.enabledDiffArea",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "box.dynamicAdjustment.enabledDynamicAdjustment": {
          "id": "121",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.enabledDynamicAdjustment",
          "label": "开启货箱归还时动态调整",
          "desc": "开启货箱归还时动态调整",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.enabledDynamicAdjustment",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "box.dynamicAdjustment.enabledPoller": {
          "id": "122",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.enabledPoller",
          "label": "是否支持动态调整货箱位置",
          "desc": "是否支持动态调整货箱位置，true：是，false：否，默认为false。",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.enabledPoller",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "box.dynamicAdjustment.enabledReturnToVacantLatticeFirst": {
          "id": "123",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.enabledReturnToVacantLatticeFirst",
          "label": "是否开启货箱动态调整优先归还到高zone空闲货位",
          "desc": "是否开启货箱动态调整优先归还到高zone空闲货位，仅在归还时货箱动态调整开关打开时生效）",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.enabledReturnToVacantLatticeFirst",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "box.dynamicAdjustment.forbidTimeRanges": {
          "id": "124",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.forbidTimeRanges",
          "label": "禁止货箱调整的时间段",
          "desc": "禁止货箱调整的时间段，例如：[\"09:00:00\",\"12:00:00\"];[\"13:00:00\",\"20:00:00\"]",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.forbidTimeRanges",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "timeslots",
          "options": {
            "componentName": "timeslots"
          }
        },
        "box.dynamicAdjustment.globalConcurrency": {
          "id": "125",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.globalConcurrency",
          "label": "货箱位动态调整的任务并发数上限",
          "desc": "货箱位动态调整的任务并发数上限",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.globalConcurrency",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "box.dynamicAdjustment.idleRobotCount": {
          "id": "126",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.idleRobotCount",
          "label": "可触发货箱位置动态调整的空闲机器人数量下限",
          "desc": "可触发货箱位置动态调整的空闲机器人数量下限",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.idleRobotCount",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "box.dynamicAdjustment.idleRobotRate": {
          "id": "127",
          "groupCode": "box",
          "code": "box.dynamicAdjustment.idleRobotRate",
          "label": "可触发货箱位置动态调整的机器人空闲率下限",
          "desc": "可触发货箱位置动态调整的机器人空闲率下限",
          "i18nCode": "lang.rms.config.box.dynamicAdjustment.idleRobotRate",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15.0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "box.task.enabledFindHighZoneLatticeFirst": {
          "id": "128",
          "groupCode": "box",
          "code": "box.task.enabledFindHighZoneLatticeFirst",
          "label": "是否开启寻找可用货箱位优先策略",
          "desc": "是否开启在寻找可用货箱位时，优先使用高zone值的货箱位",
          "i18nCode": "lang.rms.config.box.task.enabledFindHighZoneLatticeFirst",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "box.task.enabledGoCircle": {
          "id": "129",
          "groupCode": "box",
          "code": "box.task.enabledGoCircle",
          "label": "是否允许机器人进行绕圈",
          "desc": "是否允许机器人进行绕圈（在还货箱时发现终点货位上仍有货箱时）",
          "i18nCode": "lang.rms.config.box.task.enabledGoCircle",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "box.distance.betweenInnerAndOuter": {
          "id": "479",
          "groupCode": "box",
          "code": "box.distance.betweenInnerAndOuter",
          "label": "内外货箱间距",
          "desc": "内外货箱间距,默认:60",
          "i18nCode": "lang.rms.config.box.distance.betweenInnerAndOuter",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "60",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "box.rs.ctl.destLane.randomFactor": {
          "id": "1131",
          "groupCode": "box",
          "code": "box.rs.ctl.destLane.randomFactor",
          "label": "归还的目的巷道的随机因子",
          "desc": "归还的目的巷道的随机因子,默认为0",
          "i18nCode": "lang.rms.box.rs.ctl.destLane.randomFactor",
          "path": "/task/box",
          "pathLabel": "任务/货箱任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        }
      },
      "rsp": {
        "box.rsp.ctl.diff": {
          "id": "1122",
          "groupCode": "box",
          "code": "box.rsp.ctl.diff",
          "label": "前端货架合并的diff值",
          "desc": "RSP场景下做缓存位与正常货架的合并展示用",
          "i18nCode": "lang.rms.config.box.rsp.ctl.diff",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.5",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "job.rsp.ctl.laneTransferAllocatedRate": {
          "id": "1142",
          "groupCode": "box",
          "code": "job.rsp.ctl.laneTransferAllocatedRate",
          "label": "暂存货位使用率",
          "desc": "暂存货位使用率",
          "i18nCode": "lang.rms.config.job.rsp.ctl.laneTransferAllocatedRate",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.9",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "job.rsp.ctl.laneTransferOccupyThreshold": {
          "id": "1143",
          "groupCode": "box",
          "code": "job.rsp.ctl.laneTransferOccupyThreshold",
          "label": "高热度货箱当前巷道暂存货位使用率",
          "desc": "高热度货箱当前巷道暂存货位使用率",
          "i18nCode": "lang.rms.config.job.rsp.ctl.laneTransferOccupyThreshold",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.7",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "job.rsprs.ctl.excludeCallbackMsg": {
          "id": "1144",
          "groupCode": "box",
          "code": "job.rsprs.ctl.excludeCallbackMsg",
          "label": "定义需要过滤掉的消息类型,默认为,逗号作为分隔符",
          "desc": "定义需要过滤掉的消息类型,默认为,逗号作为分隔符",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.excludeCallbackMsg",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "middleArrived"
          }
        },
        "job.rsprs.ctl.jobGetCount": {
          "id": "1182",
          "groupCode": "job",
          "code": "job.rsprs.ctl.jobGetCount",
          "label": "机器人任务池最大取任务数",
          "desc": "机器人任务池最大取任务数",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.jobGetCount",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1024",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "job.rsprs.ctl.jobBrokerBatchGetMaxSize": {
          "id": "1183",
          "groupCode": "job",
          "code": "job.rsprs.ctl.jobBrokerBatchGetMaxSize",
          "label": "机器人任务池最大取任务数",
          "desc": "机器人任务池最大取任务数",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.jobBrokerBatchGetMaxSize",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2048",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "job.rsprs.ctl.algoBusinessStrategy": {
          "id": "1118",
          "groupCode": "job",
          "code": "job.rsprs.ctl.algoBusinessStrategy",
          "label": "任务分配策略",
          "desc": "提交给算法的任务分配业务策略， 是效率优先efficiencyFirst还是任务优先priorityFirst",
          "i18nCode": "lang.rms.job.rsprs.ctl.algoBusinessStrategy",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "efficiencyFirst",
              "priorityFirst"
            ],
            "defVal": "efficiencyFirst"
          }
        },
        "job.rsprs.ctl.stage.batch.maxSize": {
          "id": "1119",
          "groupCode": "job",
          "code": "job.rsprs.ctl.stage.batch.maxSize",
          "label": "jobStage批量获取任务的数量",
          "desc": "jobStage批量获取任务的数量",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.stage.batch.maxSize",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1024",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "job.rsprs.ctl.stage.enable.deadMessage": {
          "id": "1120",
          "groupCode": "job",
          "code": "job.rsprs.ctl.stage.enable.deadMessage",
          "label": "warehouseStage任务池是否开启死信队列",
          "desc": "warehouseStage任务池是否开启死信队列",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.stage.enable.deadMessage",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "job.rsprs.ctl.stage.deadMessage.queryCount": {
          "id": "1121",
          "groupCode": "job",
          "code": "job.rsprs.ctl.stage.deadMessage.queryCount",
          "label": "warehouseStage任务池死信队列查询数量",
          "desc": "warehouseStage任务池死信队列查询数量",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.stage.deadMessage.queryCount",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "256",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "job.rsprs.ctl.allocationStrategy": {
          "id": "1126",
          "groupCode": "job",
          "code": "job.rsprs.ctl.allocationStrategy",
          "label": "抱箱Job任务匹配策略",
          "desc": "抱箱Job任务匹配策略，货箱任务算法分配策略boxJobAlgoAllocationStrategy,货箱任务默认分配策略：boxJobDefaultAllocationStrategy,实时注册距离的算法任务分配策略：boxJobAlgoAllocationWithDistanceStrategy",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.allocationStrategy",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "boxJobAlgoAllocationStrategy",
              "boxJobDefaultAllocationStrategy",
              "boxJobAlgoAllocationWithDistanceStrategy"
            ],
            "defVal": "boxJobAlgoAllocationStrategy"
          }
        },
        "job.rsp.ctl.rsLaneJobMaxSize": {
          "id": "1127",
          "groupCode": "job",
          "code": "job.rsp.ctl.rsLaneJobMaxSize",
          "label": "RS任务资源分配一个巷道获取任务最大数",
          "desc": "RS任务资源分配时，从一个巷道获取任务最大数",
          "i18nCode": "lang.rms.config.job.rsp.ctl.rsLaneJobMaxSize",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "128",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "job.rsp.ctl.robotMatcherStrategy": {
          "id": "1128",
          "groupCode": "job",
          "code": "job.rsp.ctl.robotMatcherStrategy",
          "label": "P+RS分区分配任务的策略",
          "desc": "P+RS分区分配任务的策略，默认的RS任务分配策略：DefaultRs4GoReturnMatcher,基于区域的RS任务分配策略：AreaRs4GoReturnMatcher",
          "i18nCode": "lang.rms.config.job.rsp.ctl.robotMatcherStrategy",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "DefaultRs4GoReturnMatcher",
              "AreaRs4GoReturnMatcher"
            ],
            "defVal": "DefaultRs4GoReturnMatcher"
          }
        },
        "job.rsp.ctl.transferLaneComparators": {
          "id": "1129",
          "groupCode": "job",
          "code": "job.rsp.ctl.transferLaneComparators",
          "label": "货箱选择比较器",
          "desc": "货箱选择比较器",
          "i18nCode": "lang.rms.config.job.rsp.ctl.transferLaneComparators",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "laneEmptyRateComparator:0.7,transferLaneEmptyRateComparator:0.5,transferLaneRobotCountComparator:5,transferLanePriorityComparator,transferLaneDistanceComparator"
          }
        },
        "job.rs.ctl.box.allocateStationLattice": {
          "id": "1130",
          "groupCode": "job",
          "code": "job.rs.ctl.box.allocateStationLattice",
          "i18nCode": "lang.rms.job.rs.ctl.algoBusinessStrategy",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "box.rsp.p40.fetchCell.open.shelfType": {
          "id": "1136",
          "groupCode": "job",
          "code": "box.rsp.p40.fetchCell.open.shelfType",
          "i18nCode": "lang.rms.config.box.rsp.p40.fetchCell.open.shelfTyper",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "job.rsp.ctl.stage.isDestTransfer": {
          "id": "1137",
          "groupCode": "job",
          "code": "job.rsp.ctl.stage.isDestTransfer",
          "label": "默认是否归还至缓存位",
          "desc": "默认是否归还至缓存位",
          "i18nCode": "lang.rms.config.job.rsp.ctl.stage.isDestTransfer",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "job.rsp.ctl.autoReturnBoxOnTransferLattice": {
          "id": "1139",
          "groupCode": "job",
          "code": "job.rsp.ctl.autoReturnBoxOnTransferLattice",
          "label": "是否开启自动归还缓存货位上的空闲箱子",
          "desc": "是否开启自动归还缓存货位上的空闲箱子",
          "i18nCode": "lang.rms.config.job.rsp.ctl.autoReturnBoxOnTransferLattice",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "job.rsp.ctl.box.autoReturnBoxOnTransferLattice.useP40": {
          "id": "1140",
          "groupCode": "job",
          "code": "job.rsp.ctl.box.autoReturnBoxOnTransferLattice.useP40",
          "label": "是否开启使用P40来做自动归还缓存货位上的空闲箱子",
          "desc": "是否开启使用P40来做自动归还缓存货位上的空闲箱子",
          "i18nCode": "lang.rms.config.job.rsp.ctl.box.autoReturnBoxOnTransferLattice.useP40",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "job.rsp.ctl.box.autoReturnBoxOnTransferLattice.useP40Rate": {
          "id": "1141",
          "groupCode": "job",
          "code": "job.rsp.ctl.box.autoReturnBoxOnTransferLattice.useP40Rate",
          "label": "P40归还缓存货位上的空闲箱子占总P40总数的百分比",
          "desc": "使用的P40来做自动归还缓存货位上的空闲箱子的占总P40总数百分比",
          "i18nCode": "lang.rms.config.job.rsp.ctl.box.autoReturnBoxOnTransferLattice.useP40Rate",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "job.rsp.ctl.p40.workHeight": {
          "id": "1155",
          "groupCode": "job",
          "code": "job.rsp.ctl.p40.workHeight",
          "label": "机器人作业高度配置",
          "desc": "机器人作业高度配置，多个高度值用逗号隔开，单位mm",
          "i18nCode": "lang.rms.config.job.rsp.ctl.p40.workHeight",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "295,395,700"
          }
        },
        "job.rsp.ctl.p40.special.height": {
          "id": "1156",
          "groupCode": "job",
          "code": "job.rsp.ctl.p40.special.height",
          "label": "P40在指定的单元格取箱举升高度参数",
          "desc": "P40在指定的单元格取箱举升高度参数",
          "i18nCode": "lang.rms.config.job.rsp.ctl.p40.special.height",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "job.rsprs.ctl.enabledAutoCancelEmptyFetchOrReturnTask": {
          "id": "1160",
          "groupCode": "job",
          "code": "job.rsprs.ctl.enabledAutoCancelEmptyFetchOrReturnTask",
          "label": "箱子空取空放异常时是否取消任务",
          "desc": "箱子空取空放异常时，是否取消任务",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.enabledAutoCancelEmptyFetchOrReturnTask",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "job.rsp.ctl.sequenceArrivedRrefertoPrevLocation": {
          "id": "1173",
          "groupCode": "job",
          "code": "job.rsp.ctl.sequenceArrivedRrefertoPrevLocation",
          "label": "有序到达是否考虑前面任务执行情况",
          "desc": "有序到达是否考虑前面任务执行情况",
          "i18nCode": "lang.rms.config.job.rsp.ctl.sequenceArrivedRrefertoPrevLocation",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "job.rsprs.ctl.rts.cost.jobPriorityFactor": {
          "id": "1175",
          "groupCode": "job",
          "code": "job.rsprs.ctl.rts.cost.jobPriorityFactor",
          "label": "货箱任务RTS代价计算Job的priority权重因子",
          "desc": "货箱任务RTS代价计算Job的priority权重因子，因子越大，priority字段的影响力越大，取值[0~1])",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.rts.cost.jobPriorityFactor",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.0D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "job.rsprs.ctl.rts.cost.jobSequenceFactor": {
          "id": "1176",
          "groupCode": "job",
          "code": "job.rsprs.ctl.rts.cost.jobSequenceFactor",
          "label": "货箱任务代价计算权重因子",
          "desc": "货箱任务RTS代价计算Job的businessSequence权重因子，因子越大，businessSequence字段的影响力越大，取值[0~1])",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.rts.cost.jobSequenceFactor",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.0D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "job.rsprs.ctl.rts.cost.goFetchFactor": {
          "id": "1177",
          "groupCode": "job",
          "code": "job.rsprs.ctl.rts.cost.goFetchFactor",
          "label": "货箱任务RTS代价计算GO_FETCH的权重因子",
          "desc": "货箱任务RTS代价计算GO_FETCH的权重因子，因子越大，GO_FETCH类型的任务就具有更优先的执行权，取值[0~1])",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.rts.cost.goFetchFactor",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.0D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "job.rsprs.ctl.rts.cost.goReturnFactor": {
          "id": "1178",
          "groupCode": "job",
          "code": "job.rsprs.ctl.rts.cost.goReturnFactor",
          "label": "货箱任务RTS代价计算GO_RETURN的权重因子",
          "desc": "货箱任务RTS代价计算GO_RETURN的权重因子，因子越大，GO_RETURN类型的任务就具有更优先的执行权，取值[0~1]",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.rts.cost.goReturnFactor",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.0D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "job.rsprs.ctl.rts.cost.distanceFactor": {
          "id": "1179",
          "groupCode": "job",
          "code": "job.rsprs.ctl.rts.cost.distanceFactor",
          "label": "货箱任务RTS代价计算距离权重因子",
          "desc": "货箱任务RTS代价计算距离权重因子，因子越大，距离评价具有更高权重，取值[0~1]",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.rts.cost.distanceFactor",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.0D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "job.rsprs.ctl.rts.cost.delayFactor": {
          "id": "1180",
          "groupCode": "job",
          "code": "job.rsprs.ctl.rts.cost.delayFactor",
          "label": "货箱任务RTS代价计算任务时延权重因子",
          "desc": "货箱任务RTS代价计算任务时延权重因子，因子越大，时延评价具有更高权重，取值[0~1])",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.rts.cost.delayFactor",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.0D",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "job.rsprs.ctl.rts.distance.strategy": {
          "id": "1181",
          "groupCode": "job",
          "code": "job.rsprs.ctl.rts.distance.strategy",
          "label": "货箱任务RTS代价计算距离计算策略",
          "desc": "货箱任务RTS代价计算距离计算策略，默认是曼哈顿距离manhattan",
          "i18nCode": "lang.rms.config.job.rsprs.ctl.rts.distance.strategy",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "astar",
              "manhattan",
              "default"
            ],
            "defVal": "manhattan"
          }
        },
        "task.rsp.rs.callbackHasOutExeTask": {
          "id": "1163",
          "groupCode": "task",
          "code": "task.rsp.rs.callbackHasOutExeTask",
          "label": "货箱机器人还有倒箱外箱子任务时是否可提前推送任务完成",
          "desc": "rs机器人还有倒箱外箱子任务时，是否可提前推送任务完成\r\n",
          "i18nCode": "lang.rms.task.rsp.rs.callbackHasOutExeTask",
          "path": "/task/rsp",
          "pathLabel": "任务/RSP任务",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        }
      }
    },
    "path": {
      "system": {
        "path.dispatching.trafficControlManager.baseUrl": {
          "id": "6",
          "groupCode": "path",
          "code": "path.dispatching.trafficControlManager.baseUrl",
          "label": "交通管制Base URL",
          "desc": "交通管制Base URL，如果没配置接口地址，则交通管制功能不生效",
          "path": "/path/system",
          "pathLabel": "路径/系统运行",
          "section": "trafficLight",
          "sectionLabel": "交通管制",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "path.context.builder": {
          "id": "177",
          "groupCode": "path",
          "code": "path.context.builder",
          "label": "指定路径规划的参数构造器",
          "desc": "用于指定路径规划的上下文参数构造器,拣选pickingAStarPlanContextBuilder,分拣sortingAStarPlanContextBuilder,柔性分拣flexibleSortingAStarPlanContextBuilder,搬运transport4XAStarPlanContextBuilder,\"transportAStarPlanContextBuilder\",\"transportJointownAStarPlanContextBuilder\",\"transportOrderAStarPlanContextBuilder\",\"transportQueueableAStarPlanContextBuilder\"]",
          "i18nCode": "lang.rms.config.path.context.builder",
          "path": "/path/system",
          "pathLabel": "路径/系统运行",
          "sectionLabel": "交通管制",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "pickingAStarPlanContextBuilder",
              "sortingAStarPlanContextBuilder",
              "flexibleSortingAStarPlanContextBuilder",
              "transport4XAStarPlanContextBuilder",
              "transportAStarPlanContextBuilder",
              "transportJointownAStarPlanContextBuilder",
              "transportOrderAStarPlanContextBuilder",
              "transportQueueableAStarPlanContextBuilder"
            ],
            "defVal": "pickingAStarPlanContextBuilder"
          }
        },
        "path.dispatching.trafficLight.switch": {
          "id": "463",
          "groupCode": "path",
          "code": "path.dispatching.trafficLight.switch",
          "label": "交通管制灯开关",
          "desc": "交通管制灯开关",
          "i18nCode": "lang.rms.config.path.dispatching.trafficLight.switch",
          "path": "/path/system",
          "pathLabel": "路径/系统运行",
          "section": "trafficLight",
          "sectionLabel": "交通管制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.dispatching.trafficLight.autoResetTime": {
          "id": "464",
          "groupCode": "path",
          "code": "path.dispatching.trafficLight.autoResetTime",
          "label": "交通管制灯自动复位时间",
          "desc": "交通管制灯自动复位时间,单位毫秒,默认5分钟",
          "i18nCode": "lang.rms.config.path.dispatching.trafficLight.autoResetTime",
          "path": "/path/system",
          "pathLabel": "路径/系统运行",
          "section": "trafficLight",
          "sectionLabel": "交通管制",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "300",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.dispatching.trafficLight.autoResetSwitch": {
          "id": "465",
          "groupCode": "path",
          "code": "path.dispatching.trafficLight.autoResetSwitch",
          "label": "交通管制灯自动复位开关",
          "desc": "交通管制灯自动复位开关",
          "i18nCode": "lang.rms.config.path.dispatching.trafficLight.autoResetSwitch",
          "path": "/path/system",
          "pathLabel": "路径/系统运行",
          "section": "trafficLight",
          "sectionLabel": "交通管制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "picking.station.shelfVieAmount": {
          "id": "466",
          "groupCode": "root",
          "code": "picking.station.shelfVieAmount",
          "label": "货架被工作站竞争的总数",
          "desc": "用于控制货架被多少个工作站竞争时，开启货架竞争调整",
          "path": "/path/system",
          "pathLabel": "路径/系统运行",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "100",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "path.git.properties": {
          "id": "468",
          "groupCode": "path",
          "code": "path.git.properties",
          "label": "配置文件地址",
          "desc": "配置文件地址",
          "path": "/path/system",
          "pathLabel": "路径/系统运行",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/usr/local/geekplus/tomcat-rms/webapps/athena/WEB-INF/classes/"
          }
        },
        "path.traffic.light": {
          "id": "487",
          "groupCode": "path",
          "code": "path.traffic.light",
          "label": "消息对接上游",
          "desc": "控制消息发给上游upstream还是dmp，默认发给上游upstream",
          "i18nCode": "lang.rms.config.path.traffic.light",
          "path": "/path/system",
          "pathLabel": "路径/系统运行",
          "section": "trafficLight",
          "sectionLabel": "交通管制",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "upstream",
              "dmp"
            ],
            "defVal": "upstream"
          }
        }
      },
      "dispatching": {
        "path.dispatching.coverageCalculator": {
          "id": "9",
          "groupCode": "path",
          "code": "path.dispatching.coverageCalculator",
          "label": "包络计算",
          "desc": "机器人包罗计算器可选值[占用当前单元格：currentCell，根据货物及机器人实际尺寸占用单元格：actualCoverage，占用包络范围：currentArea]",
          "i18nCode": "lang.rms.config.path.dispatching.coverageCalculator",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "section": "coverage",
          "sectionLabel": "包络计算",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "currentCell",
              "actualCoverage",
              "currentArea"
            ],
            "defVal": "actualCoverage"
          }
        },
        "path.dispatching.allocation.maxDistance": {
          "id": "178",
          "groupCode": "path",
          "code": "path.dispatching.allocation.maxDistance",
          "label": "分配单元格距离上限",
          "desc": "分配单元格距离上限，0：表示不启用，将尝试分配预分配列表中的所有单元格",
          "i18nCode": "lang.rms.config.path.dispatching.allocation.maxDistance",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.0",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.dispatching.allocation.maxNum": {
          "id": "179",
          "groupCode": "path",
          "code": "path.dispatching.allocation.maxNum",
          "label": "分配单元格数量上限",
          "desc": "分配单元格数量上限，0：表示不启用，将尝试分配预分配列表中的所有单元格",
          "i18nCode": "lang.rms.config.path.dispatching.allocation.maxNum",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "4",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.dispatching.coverageExtendLength": {
          "id": "193",
          "groupCode": "path",
          "code": "path.dispatching.coverageExtendLength",
          "label": "机器人移动时计算包络的误差系数length方向",
          "desc": "机器人移动时计算包络的误差系数 length方向,单位为米",
          "i18nCode": "lang.rms.config.path.dispatching.coverageExtendLength",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "section": "coverage",
          "sectionLabel": "包络计算",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.05",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "path.dispatching.coverageExtendWidth": {
          "id": "194",
          "groupCode": "path",
          "code": "path.dispatching.coverageExtendWidth",
          "label": "机器人移动时计算包络的误差系数width方向",
          "desc": "机器人移动时计算包络的误差系数 width方向,单位为米",
          "i18nCode": "lang.rms.config.path.dispatching.coverageExtendWidth",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "section": "coverage",
          "sectionLabel": "包络计算",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.02",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "path.dispatching.coverageRatioForBezier": {
          "id": "195",
          "groupCode": "path",
          "code": "path.dispatching.coverageRatioForBezier",
          "label": "机器人进行贝塞尔曲线运动时，采样率",
          "desc": "机器人进行贝塞尔曲线运动时，采样率（每n米采样一次）",
          "i18nCode": "lang.rms.config.path.dispatching.coverageRatioForBezier",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "section": "coverage",
          "sectionLabel": "包络计算",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.dispatching.enabledDynamicPath": {
          "id": "202",
          "groupCode": "path",
          "code": "path.dispatching.enabledDynamicPath",
          "label": "动态路径",
          "desc": "启动动态路径（目前仅支持分拣），true：开启，false：不开启，默认值false",
          "i18nCode": "lang.rms.config.path.dispatching.enabledDynamicPath",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.dispatching.enabledForkLiftOffset": {
          "id": "204",
          "groupCode": "path",
          "code": "path.dispatching.enabledForkLiftOffset",
          "label": "叉车是否开启终点偏移计算",
          "desc": "叉车路径规划是否开启终点偏移计算，默认为false，用于兼容历史地图根据机器人上报坐标画托盘位的情况，如果实施时托盘位坐标点与托盘架中心店重合需设置为true",
          "i18nCode": "lang.rms.config.path.dispatching.enabledForkLiftOffset",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.dispatching.enabledPaperclipInfield": {
          "id": "206",
          "groupCode": "path",
          "code": "path.dispatching.enabledPaperclipInfield",
          "label": "回形工位是否允许从入口处进入田字格",
          "desc": "回形工位是否允许从入口处进入田字格",
          "i18nCode": "lang.rms.config.path.dispatching.enabledPaperclipInfield",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.dispatching.forkBackDistance": {
          "id": "210",
          "groupCode": "path",
          "code": "path.dispatching.forkBackDistance",
          "label": "叉车识别托盘时后退的最大距离",
          "desc": "叉车识别托盘时后退的最大距离,单位为米",
          "i18nCode": "lang.rms.config.path.dispatching.forkBackDistance",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.7",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.dispatching.forkBroadWiseExtendRatio": {
          "id": "211",
          "groupCode": "path",
          "code": "path.dispatching.forkBroadWiseExtendRatio",
          "label": "叉车识别托盘时侧面所需额外空间比例系数",
          "desc": "叉车识别托盘时侧面所需额外空间比例系数",
          "i18nCode": "lang.rms.config.path.dispatching.forkBroadWiseExtendRatio",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.5",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "path.dispatching.waitCellReleaseModel": {
          "id": "231",
          "groupCode": "path",
          "code": "path.dispatching.waitCellReleaseModel",
          "label": "等待点释放方式",
          "desc": "等待点释放方式 0：只要包络不再覆盖等待点，则释放。1：只要机器人当前点不是等待点单元格，默认值0",
          "i18nCode": "lang.rms.config.path.dispatching.waitCellReleaseModel",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              0,
              1
            ],
            "defVal": "0"
          }
        },
        "path.dispatching.waitCellEnabledCellCodes": {
          "id": "449",
          "groupCode": "path",
          "code": "path.dispatching.waitCellEnabledCellCodes",
          "label": "暂停点开关",
          "desc": "暂停点开关，临时代码，路径起点终点均在集合里才生效，集合为空不考虑该配置",
          "i18nCode": "lang.rms.config.path.dispatching.waitCellEnabledCellCodes",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "path.dispatching.allocatedCellTurnBuf": {
          "id": "451",
          "groupCode": "path",
          "code": "path.dispatching.allocatedCellTurnBuf",
          "label": "分配单元格时同时旋转的机器人预留buff",
          "desc": "分配单元格时检测两个同时旋转的机器人时预留buf，单位m 默认是0",
          "i18nCode": "lang.rms.config.path.dispatching.allocatedCellTurnBuf",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.dispatching.realtimeObstacle.enabledAvoidance": {
          "id": "536",
          "groupCode": "path",
          "code": "path.dispatching.realtimeObstacle.enabledAvoidance",
          "label": "是否激活实时避障",
          "desc": "是否激活实时避障",
          "i18nCode": "lang.rms.config.path.dispatching.realtimeObstacle.enabledAvoidance",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": "0"
          }
        },
        "path.dispatching.realtimeObstacle.radius": {
          "id": "539",
          "groupCode": "path",
          "code": "path.dispatching.realtimeObstacle.radius",
          "label": "障碍物作用半径",
          "desc": "障碍物作用半径",
          "i18nCode": "lang.rms.config.path.dispatching.realtimeObstacle.radius",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "path.dispatching.realtimeObstacle.maxStillTime": {
          "id": "540",
          "groupCode": "path",
          "code": "path.dispatching.realtimeObstacle.maxStillTime",
          "label": "障碍物移除最大等待时间",
          "desc": "障碍物移除最大等待时间,单位秒，默认5s",
          "i18nCode": "lang.rms.config.path.dispatching.realtimeObstacle.maxStillTime",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.dispatching.obstacleOutRectangleOffset": {
          "id": "1054",
          "groupCode": "path",
          "code": "path.dispatching.obstacleOutRectangleOffset",
          "label": "路径调度路径外接矩形的偏移量",
          "desc": "路径调度路径外接矩形的偏移量",
          "i18nCode": "lang.rms.config.path.dispatching.obstacleOutRectangleOffset",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2.0",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "path.dispatching.algoStrategy": {
          "id": "1056",
          "groupCode": "path",
          "code": "path.dispatching.algoStrategy",
          "label": "栅格路径调度策略",
          "desc": "栅格路径调度策略",
          "i18nCode": "lang.rms.config.path.dispatching.algoStrategy",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "prioritizedPathDispatcher",
              "simplePathDispatcher"
            ],
            "defVal": "prioritizedPathDispatcher"
          }
        },
        "path.dispatching.enabledMoveHalfCellWhenOutField": {
          "id": "1062",
          "groupCode": "path",
          "code": "path.dispatching.enabledMoveHalfCellWhenOutField",
          "label": "是否允许机器人出田字格时提前走半个格子",
          "desc": "是否允许机器人出田字格时提前走半个格子",
          "i18nCode": "lang.rms.path.dispatching.enabledMoveHalfCellWhenOutField",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": "1"
          }
        },
        "path.dispatching.enabledAllocateWhenOverlap": {
          "id": "1077",
          "groupCode": "path",
          "code": "path.dispatching.enabledAllocateWhenOverlap",
          "i18nCode": "lang.rms.config.path.dispatching.enabledAllocateWhenOverlap",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.dispatching.enabledGlobalFollow": {
          "id": "1094",
          "groupCode": "path",
          "code": "path.dispatching.enabledGlobalFollow",
          "label": "是否开启全局跟随",
          "desc": "是否开启全局跟随",
          "i18nCode": "lang.rms.config.path.dispatching.enabledGlobalFollow",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "section": "Follow",
          "sectionLabel": "跟随功能",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.dispatching.stationFollowDistance": {
          "id": "1095",
          "groupCode": "path",
          "code": "path.dispatching.stationFollowDistance",
          "label": "工作站跟随拆段距离",
          "desc": "工作站跟随拆段距离，默认值：0.1，进工作站跟随车辆间距",
          "i18nCode": "lang.rms.config.path.dispatching.stationFollowDistance",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "section": "Follow",
          "sectionLabel": "跟随功能",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.1",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.dispatching.globalFollowDistance": {
          "id": "1096",
          "groupCode": "path",
          "code": "path.dispatching.globalFollowDistance",
          "label": "全局跟随拆段距离",
          "desc": "全局跟随拆段距离，默认值：0.2，全场径拆段每段距离大小",
          "i18nCode": "lang.rms.config.path.dispatching.globalFollowDistance",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "section": "Follow",
          "sectionLabel": "跟随功能",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.2",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.dispatching.turnAllowFollow": {
          "id": "1097",
          "groupCode": "path",
          "code": "path.dispatching.turnAllowFollow",
          "label": "前车转弯是否允许跟随",
          "desc": "前车转弯是否允许跟随，true：允许，false：不允许，默认值：false",
          "i18nCode": "lang.rms.config.path.dispatching.turnAllowFollow",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "section": "Follow",
          "sectionLabel": "跟随功能",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.dispatching.enabledStationFollow": {
          "id": "1098",
          "groupCode": "path",
          "code": "path.dispatching.enabledStationFollow",
          "label": "是否开启工作站跟随",
          "desc": "是否开启工作站跟随，true：开启，false：不开启，默认值：false",
          "i18nCode": "lang.rms.config.path.dispatching.enabledStationFollow",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "section": "Follow",
          "sectionLabel": "跟随功能",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "path.dispatching.keepDistanceWithSingleLane": {
          "id": "1105",
          "groupCode": "path",
          "code": "path.dispatching.keepDistanceWithSingleLane",
          "label": "机器人距离单行道的距离",
          "desc": "机器人距离单行道的距离",
          "i18nCode": "lang.rms.config.path.dispatching.keepDistanceWithSingleLane",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.5",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.dispatching.deduce.minWindow": {
          "id": "1110",
          "groupCode": "path",
          "code": "path.dispatching.deduce.minWindow",
          "label": "最小推演窗口",
          "desc": "路径规划推演窗口-最小推演窗口",
          "i18nCode": "lang.rms.config.path.dispatching.deduce.minWindow",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "3",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.dispatching.deduce.windowScale": {
          "id": "1111",
          "groupCode": "path",
          "code": "path.dispatching.deduce.windowScale",
          "label": "推演窗口比例系数",
          "desc": "推演窗口比例系数",
          "i18nCode": "lang.rms.config.path.dispatching.deduce.windowScale",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "path.dispatching.trafficControlManager.strategy": {
          "id": "1152",
          "groupCode": "root",
          "code": "path.dispatching.trafficControlManager.strategy",
          "label": "交通管制策略",
          "desc": "交通管制策略，默认GeekplusArkTrafficControlAreaApplicationStrategy",
          "i18nCode": "lang.rms.config.path.dispatching.trafficControlManager.strategy",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "GeekplusArkTrafficControlAreaApplicationStrategy"
            ],
            "defVal": "geekplusArkTrafficControlAreaApplicationStrategy"
          }
        },
        "path.dispatching.trafficControlManager.baseParams": {
          "id": "1153",
          "groupCode": "root",
          "code": "path.dispatching.trafficControlManager.baseParams",
          "label": "交通管制基础参数",
          "desc": "交通管制上游系统访问基本参数",
          "i18nCode": "lang.rms.config.path.dispatching.trafficControlManager.baseParams",
          "path": "/path/dispatching",
          "pathLabel": "路径/路径分配",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        }
      },
      "planning": {
        "path.planning.cost.backOff": {
          "id": "232",
          "groupCode": "path",
          "code": "path.planning.cost.backOff",
          "label": "倒车系数",
          "desc": "倒车系数，基数为costG的值，表示为costG的值的倍数",
          "i18nCode": "lang.rms.config.path.planning.cost.backOff",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.bad": {
          "id": "233",
          "groupCode": "path",
          "code": "path.planning.cost.bad",
          "label": "糟糕的路径点系数",
          "desc": "糟糕的路径点系数，默认值1000",
          "i18nCode": "lang.rms.config.path.planning.cost.bad",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1000",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.g": {
          "id": "234",
          "groupCode": "path",
          "code": "path.planning.cost.g",
          "label": "cost计算器G系数",
          "desc": "cost计算器G系数，默认值10",
          "i18nCode": "lang.rms.config.path.planning.cost.g",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.h": {
          "id": "235",
          "groupCode": "path",
          "code": "path.planning.cost.h",
          "label": "cost计算器H系数",
          "desc": "cost计算器H系数，默认值10",
          "i18nCode": "lang.rms.config.path.planning.cost.h",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.highwayEast": {
          "id": "236",
          "groupCode": "path",
          "code": "path.planning.cost.highwayEast",
          "label": "高速路通道往东方向权重",
          "desc": "高速路通道往东方向权重",
          "i18nCode": "lang.rms.config.path.planning.cost.highwayEast",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.highwayNorth": {
          "id": "237",
          "groupCode": "path",
          "code": "path.planning.cost.highwayNorth",
          "label": "高速路通道往北方向权重",
          "desc": "高速路通道往北方向权重",
          "i18nCode": "lang.rms.config.path.planning.cost.highwayNorth",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.highwaySouth": {
          "id": "238",
          "groupCode": "path",
          "code": "path.planning.cost.highwaySouth",
          "label": "高速路通道往南方向权重",
          "desc": "高速路通道往南方向权重",
          "i18nCode": "lang.rms.config.path.planning.cost.highwaySouth",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.highwayWest": {
          "id": "239",
          "groupCode": "path",
          "code": "path.planning.cost.highwayWest",
          "label": "高速路通道往西方向权重",
          "desc": "高速路通道往西方向权重",
          "i18nCode": "lang.rms.config.path.planning.cost.highwayWest",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.jam": {
          "id": "240",
          "groupCode": "path",
          "code": "path.planning.cost.jam",
          "label": "堵塞的路径点系数",
          "desc": "堵塞的路径点系数",
          "i18nCode": "lang.rms.config.path.planning.cost.jam",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.traffic": {
          "id": "241",
          "groupCode": "path",
          "code": "path.planning.cost.traffic",
          "label": "交通拥堵系数",
          "desc": "交通拥堵系数",
          "i18nCode": "lang.rms.config.path.planning.cost.traffic",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "30",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.cost.turn": {
          "id": "242",
          "groupCode": "path",
          "code": "path.planning.cost.turn",
          "label": "转弯系数",
          "desc": "转弯系数",
          "i18nCode": "lang.rms.config.path.planning.cost.turn",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "50",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.crossFloorCostG": {
          "id": "243",
          "groupCode": "path",
          "code": "path.planning.crossFloorCostG",
          "label": "路径规划跨层G系数",
          "desc": "路径规划跨层G系数",
          "i18nCode": "lang.rms.config.path.planning.crossFloorCostG",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1000",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.crossFloorCostH": {
          "id": "244",
          "groupCode": "path",
          "code": "path.planning.crossFloorCostH",
          "label": "路径规划跨层H系数",
          "desc": "路径规划跨层H系数",
          "i18nCode": "lang.rms.config.path.planning.crossFloorCostH",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1000",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.crossRepeatCellCost": {
          "id": "245",
          "groupCode": "path",
          "code": "path.planning.crossRepeatCellCost",
          "label": "规划路径时对重复节点的附加权重",
          "desc": "规划路径时对重复节点的附加权重",
          "i18nCode": "lang.rms.config.path.planning.crossRepeatCellCost",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "100",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.distanceCalculatingStrategy": {
          "id": "246",
          "groupCode": "path",
          "code": "path.planning.distanceCalculatingStrategy",
          "label": "距离计算模式",
          "desc": "距离计算模式，曼哈顿：Manhattan，AStar：AStar",
          "i18nCode": "lang.rms.config.path.planning.distanceCalculatingStrategy",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "Manhattan",
              "AStar",
              "ManhattanWithBlock",
              "KeyNodeAStar"
            ],
            "defVal": "Manhattan"
          }
        },
        "path.planning.enabledAsync": {
          "id": "247",
          "groupCode": "path",
          "code": "path.planning.enabledAsync",
          "label": "是否允许异步路径规划",
          "desc": "是否允许异步路径规划",
          "i18nCode": "lang.rms.config.path.planning.enabledAsync",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "path.planning.enabledCloseReverseDirection": {
          "id": "248",
          "groupCode": "path",
          "code": "path.planning.enabledCloseReverseDirection",
          "label": "是否启用反向禁行",
          "desc": "路径规划是否启用反向禁行",
          "i18nCode": "lang.rms.config.path.planning.enabledCloseReverseDirection",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "path.planning.enabledCrossRepeatCell": {
          "id": "249",
          "groupCode": "path",
          "code": "path.planning.enabledCrossRepeatCell",
          "label": "是否允许规划路径时通过重复节点",
          "desc": "是否允许规划路径时通过重复节点",
          "i18nCode": "lang.rms.config.path.planning.enabledCrossRepeatCell",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.planning.enabledCrossStation": {
          "id": "250",
          "groupCode": "path",
          "code": "path.planning.enabledCrossStation",
          "label": "机器人是否允许穿行工作站",
          "desc": "机器人是否允许穿行工作站,默认值为false,不允许穿行",
          "i18nCode": "lang.rms.config.path.planning.enabledCrossStation",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "path.planning.failedRetryInterval": {
          "id": "251",
          "groupCode": "path",
          "code": "path.planning.failedRetryInterval",
          "label": "子任务为新建状态时重新规划路径的间隔时间",
          "desc": "子任务为新建状态时重新规划路径的间隔时间，单位s",
          "i18nCode": "lang.rms.config.path.planning.failedRetryInterval",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.highwayArea": {
          "id": "252",
          "groupCode": "path",
          "code": "path.planning.highwayArea",
          "label": "高速路区域",
          "desc": "高速路区域",
          "i18nCode": "lang.rms.config.path.planning.highwayArea",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "path.planning.quickChangePathStrategy": {
          "id": "253",
          "groupCode": "path",
          "code": "path.planning.quickChangePathStrategy",
          "label": "快速更换路径策略",
          "desc": "快速更换路径策略",
          "i18nCode": "lang.rms.config.path.planning.quickChangePathStrategy",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "commonRobotQuickChangePathStrategy",
              "shelfRobotQuickChangePathStrategy",
              "sortingRobotQuickChangePathStrategy"
            ],
            "defVal": "defaultQuickChangePathStrategy"
          }
        },
        "path.planning.validTryTimes": {
          "id": "254",
          "groupCode": "path",
          "code": "path.planning.validTryTimes",
          "label": "重新规划路径时关闭最近n次归划的方向",
          "desc": "重新规划路径时关闭最近n次归划的方向",
          "i18nCode": "lang.rms.config.path.planning.validTryTimes",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.planning.waitTimeout": {
          "id": "255",
          "groupCode": "path",
          "code": "path.planning.waitTimeout",
          "label": "路径规划等待超时时间",
          "desc": "路径规划等待超时时间，单位s",
          "i18nCode": "lang.rms.config.path.planning.waitTimeout",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.planning.trafficCostOpposite": {
          "id": "424",
          "groupCode": "path",
          "code": "path.planning.trafficCostOpposite",
          "label": "路径规划反向路径系数",
          "desc": "路径规划反向路径系数",
          "i18nCode": "lang.rms.config.path.planning.trafficCostOpposite",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "300",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.trafficCostCross": {
          "id": "425",
          "groupCode": "path",
          "code": "path.planning.trafficCostCross",
          "label": "路径规划交叉路径系数",
          "desc": "路径规划交叉路径系数",
          "i18nCode": "lang.rms.config.path.planning.trafficCostCross",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "30",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.trafficCostFollow": {
          "id": "426",
          "groupCode": "path",
          "code": "path.planning.trafficCostFollow",
          "label": "路径规划跟随路径系数",
          "desc": "路径规划跟随路径系数",
          "i18nCode": "lang.rms.config.path.planning.trafficCostFollow",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.trafficCostStay": {
          "id": "427",
          "groupCode": "path",
          "code": "path.planning.trafficCostStay",
          "label": "路径规划停留路径系数",
          "desc": "路径规划停留路径系数",
          "i18nCode": "lang.rms.config.path.planning.trafficCostStay",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "100",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.trafficCostDestBalance": {
          "id": "428",
          "groupCode": "path",
          "code": "path.planning.trafficCostDestBalance",
          "label": "任务终点均衡系数",
          "desc": "任务终点均衡系数",
          "i18nCode": "lang.rms.config.path.planning.trafficCostDestBalance",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "500",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.robotAsObstacleWhenReady": {
          "id": "442",
          "groupCode": "path",
          "code": "path.planning.robotAsObstacleWhenReady",
          "label": "任务就绪状态的机器人是否作为障碍",
          "desc": "任务就绪状态的机器人是否作为障碍",
          "i18nCode": "lang.rms.config.path.planning.robotAsObstacleWhenReady",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "path.planning.enableObstacleRobotArea": {
          "id": "443",
          "groupCode": "path",
          "code": "path.planning.enableObstacleRobotArea",
          "label": "障碍机器人的区域都作为障碍",
          "desc": "障碍机器人的区域都作为障碍,默认为false",
          "i18nCode": "lang.rms.config.path.planning.enableObstacleRobotArea",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.planning.ignoreRobotAsObstacles": {
          "id": "484",
          "groupCode": "path",
          "code": "path.planning.ignoreRobotAsObstacles",
          "label": "是否忽略机器人所在位置作为障碍",
          "desc": "当路径规划时，忽略机器人所在位置作为障碍。默认：false，表示不忽略。",
          "i18nCode": "lang.rms.config.path.planning.ignoreRobotAsObstacles",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.planning.costCalculatorSigma": {
          "id": "494",
          "groupCode": "path",
          "code": "path.planning.costCalculatorSigma",
          "label": "路径规划cost计算sigma值",
          "desc": "路径规划cost计算sigma值",
          "i18nCode": "lang.rms.config.path.planning.costCalculatorSigma",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "4.0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.costCalculatorDistanceBottomNumber": {
          "id": "495",
          "groupCode": "path",
          "code": "path.planning.costCalculatorDistanceBottomNumber",
          "label": "路径规划cost计算底数值",
          "desc": "路径规划cost计算底数值",
          "i18nCode": "lang.rms.config.path.planning.costCalculatorDistanceBottomNumber",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1.0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.costCalculatorConflictRobotSizeBottomNumber": {
          "id": "496",
          "groupCode": "path",
          "code": "path.planning.costCalculatorConflictRobotSizeBottomNumber",
          "label": "路径规划cost计算冲突机器人数量的底数",
          "desc": "路径规划cost计算，冲突机器人数量的底数（底数越大，则冲突机器人数量相同的情况下，计算出的cost越大）",
          "i18nCode": "lang.rms.config.path.planning.costCalculatorConflictRobotSizeBottomNumber",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1.0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.enabledBlockAreaSearch": {
          "id": "501",
          "groupCode": "path",
          "code": "path.planning.enabledBlockAreaSearch",
          "label": "启用分区规划",
          "desc": "启用分区规划",
          "i18nCode": "lang.rms.config.path.planning.enabledBlockAreaSearch",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.planning.angleErrorRange": {
          "id": "506",
          "groupCode": "path",
          "code": "path.planning.angleErrorRange",
          "label": "路径规划允许的角度误差值",
          "desc": "路径规划允许的角度误差值",
          "i18nCode": "lang.rms.config.path.planning.angleErrorRange",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15.0",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "path.planning.trafficWindowSize": {
          "id": "507",
          "groupCode": "path",
          "code": "path.planning.trafficWindowSize",
          "label": "检测拥堵窗口大小",
          "desc": "路径规划时检测拥堵的窗口大小",
          "i18nCode": "lang.rms.config.path.planning.trafficWindowSize",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "25",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.changePathCost": {
          "id": "508",
          "groupCode": "path",
          "code": "path.planning.changePathCost",
          "label": "路径规划换路cost",
          "desc": "路径规划换路cost",
          "i18nCode": "lang.rms.config.path.planning.changePathCost",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "100",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.idleRobotCost": {
          "id": "511",
          "groupCode": "path",
          "code": "path.planning.idleRobotCost",
          "label": "空闲机器人Cost",
          "desc": "空闲机器人Cost",
          "i18nCode": "lang.rms.config.path.planning.idleRobotCost",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "300",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.stationCellCost": {
          "id": "512",
          "groupCode": "path",
          "code": "path.planning.stationCellCost",
          "label": "工作站单元格Cost",
          "desc": "工作站单元格Cost",
          "i18nCode": "lang.rms.config.path.planning.stationCellCost",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1000",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.makeExceptionIfStopOverTime": {
          "id": "513",
          "groupCode": "path",
          "code": "path.planning.makeExceptionIfStopOverTime",
          "label": "路径规划在原地停留多久的机器人会当作障碍",
          "desc": "路径规划在原地停留多久的机器人会当作障碍，单位s",
          "i18nCode": "lang.rms.config.path.planning.makeExceptionIfStopOverTime",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "25.0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.trafficCostForExceptionStop": {
          "id": "514",
          "groupCode": "path",
          "code": "path.planning.trafficCostForExceptionStop",
          "label": "经过异常停止机器人的cost",
          "desc": "路径规划cost计算，经过异常停止（包括机器人掉线，机器人本体异常，堵路时间超长）机器人的cost",
          "i18nCode": "lang.rms.config.path.planning.trafficCostForExceptionStop",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "300",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "path.planning.enabledDebug": {
          "id": "518",
          "groupCode": "path",
          "code": "path.planning.enabledDebug",
          "i18nCode": "lang.rms.config.path.planning.enabledDebug",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "path.planning.enabledCrossFloorSplitAvoid": {
          "id": "522",
          "groupCode": "path",
          "code": "path.planning.enabledCrossFloorSplitAvoid",
          "label": "跨楼层拆分避让点",
          "desc": "跨楼层拆分避让点",
          "i18nCode": "lang.rms.config.path.planning.enabledCrossFloorSplitAvoid",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "path.planning.groupStrategy": {
          "id": "525",
          "groupCode": "path",
          "code": "path.planning.groupStrategy",
          "label": "路径规划分组策略",
          "desc": "路径规划分组策略",
          "i18nCode": "lang.rms.config.path.planning.groupStrategy",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "basePathPlanGroupStrategy",
              "areaPathPlanGroupStrategy"
            ],
            "defVal": "areaPathPlanGroupStrategy"
          }
        },
        "path.planning.groupRobotCount": {
          "id": "526",
          "groupCode": "path",
          "code": "path.planning.groupRobotCount",
          "label": "路径规划最小分组机器人数",
          "desc": "路径规划最小分组机器人数",
          "i18nCode": "lang.rms.config.path.planning.groupRobotCount",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "path.planning.groupAreaDistance": {
          "id": "527",
          "groupCode": "path",
          "code": "path.planning.groupAreaDistance",
          "label": "路径规划机器人跨域计算距离",
          "desc": "路径规划机器人跨域计算距离，单位m",
          "i18nCode": "lang.rms.config.path.planning.groupAreaDistance",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15.0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.robot.apply.timeout": {
          "id": "528",
          "groupCode": "path",
          "code": "path.planning.robot.apply.timeout",
          "label": "路径规划机器人路径应用过期时间",
          "desc": "路径规划机器人路径应用过期时间",
          "i18nCode": "lang.rms.config.path.planning.robot.apply.timeout",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.filter.strategy": {
          "id": "529",
          "groupCode": "path",
          "code": "path.planning.filter.strategy",
          "label": "路径规划筛选策略",
          "desc": "路径规划筛选策略",
          "i18nCode": "lang.rms.config.path.planning.filter.strategy",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "defaultPathPlanFilterStrategy"
          }
        },
        "path.planning.apply.checkStrategy": {
          "id": "530",
          "groupCode": "path",
          "code": "path.planning.apply.checkStrategy",
          "label": "路径应用合法行校验策略",
          "desc": "路径应用合法行校验策略",
          "i18nCode": "lang.rms.config.path.planning.apply.checkStrategy",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "defaultPathCheckingStrategy"
          }
        },
        "path.planning.deal.timeout": {
          "id": "532",
          "groupCode": "path",
          "code": "path.planning.deal.timeout",
          "label": "路径规划处理超时时间",
          "desc": "路径规划处理超时时间",
          "i18nCode": "lang.rms.config.path.planning.deal.timeout",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "800",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.replan.traffic.conflict.num": {
          "id": "533",
          "groupCode": "path",
          "code": "path.planning.replan.traffic.conflict.num",
          "label": "路径重规划冲突阈值",
          "desc": "路径重规划冲突阈值",
          "i18nCode": "lang.rms.config.path.planning.replan.traffic.conflict.num",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.individual.rePlanintervalTime": {
          "id": "534",
          "groupCode": "path",
          "code": "path.planning.individual.rePlanintervalTime",
          "label": "单台机器人重规划间隔时间",
          "desc": "单台机器人重规划间隔时间，单位秒",
          "i18nCode": "lang.rms.config.path.planning.individual.rePlanintervalTime",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "30",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.defaultBizType": {
          "id": "542",
          "groupCode": "path",
          "code": "path.planning.defaultBizType",
          "label": "路径规划默认业务倾向",
          "desc": "路径规划默认业务倾向",
          "i18nCode": "lang.rms.config.path.planning.defaultBizType",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "FAST_PLAN",
              "BEST_PLAN"
            ],
            "defVal": "FAST_PLAN"
          }
        },
        "path.planning.fastPlanPlanner": {
          "id": "543",
          "groupCode": "path",
          "code": "path.planning.fastPlanPlanner",
          "label": "fastPlan模式下的路径规划器",
          "desc": "fastPlan模式下的路径规划器",
          "i18nCode": "lang.rms.config.path.planning.fastPlanPlanner",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "aStar"
            ],
            "defVal": "aStar"
          }
        },
        "path.planning.fastPlanNodeIterator": {
          "id": "544",
          "groupCode": "path",
          "code": "path.planning.fastPlanNodeIterator",
          "label": "fastPlan模式下的路径规划迭代器",
          "desc": "fastPlan模式下的路径规划迭代器，简单节点迭代器：simpleNodeIterator，默认节点迭代器：defaultNodeIterator",
          "i18nCode": "lang.rms.config.path.planning.fastPlanNodeIterator",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "simpleNodeIterator",
              "defaultNodeIterator"
            ],
            "defVal": "simpleNodeIterator"
          }
        },
        "path.planning.fastPlanNodeCostCalculator": {
          "id": "545",
          "groupCode": "path",
          "code": "path.planning.fastPlanNodeCostCalculator",
          "label": "fastPlan模式下的路径规划节点cost计算器",
          "desc": "fastPlan模式下的路径规划节点cost计算器",
          "i18nCode": "lang.rms.config.path.planning.fastPlanNodeCostCalculator",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultNodeCostCalculator"
            ],
            "defVal": "defaultNodeCostCalculator"
          }
        },
        "path.planning.fastPlanNodeBuilder": {
          "id": "546",
          "groupCode": "path",
          "code": "path.planning.fastPlanNodeBuilder",
          "label": "fastPlan模式下的路径规划节点构造器",
          "desc": "fastPlan模式下的路径规划节点构造器",
          "i18nCode": "lang.rms.config.path.planning.fastPlanNodeBuilder",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultNodeBuilder",
              "nodeAngleNodeBuilder"
            ],
            "defVal": "defaultNodeBuilder"
          }
        },
        "path.planning.bestPlanPlanner": {
          "id": "547",
          "groupCode": "path",
          "code": "path.planning.bestPlanPlanner",
          "label": "bestPlan模式下的路径规划器",
          "desc": "bestPlan模式下的路径规划器",
          "i18nCode": "lang.rms.config.path.planning.bestPlanPlanner",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "aStar"
            ],
            "defVal": "aStar"
          }
        },
        "path.planning.bestPlanNodeIterator": {
          "id": "548",
          "groupCode": "path",
          "code": "path.planning.bestPlanNodeIterator",
          "label": "bestPlan模式下的路径规划迭代器",
          "desc": "bestPlan模式下的路径规划迭代器",
          "i18nCode": "lang.rms.config.path.planning.bestPlanNodeIterator",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "simpleNodeIterator",
              "defaultNodeIterator"
            ],
            "defVal": "defaultNodeIterator"
          }
        },
        "path.planning.bestPlanNodeCostCalculator": {
          "id": "549",
          "groupCode": "path",
          "code": "path.planning.bestPlanNodeCostCalculator",
          "label": "bestPlan模式下的路径规划节点cost计算器",
          "desc": "bestPlan模式下的路径规划节点cost计算器",
          "i18nCode": "lang.rms.config.path.planning.bestPlanNodeCostCalculator",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultNodeCostCalculator"
            ],
            "defVal": "defaultNodeCostCalculator"
          }
        },
        "path.planning.bestPlanNodeBuilder": {
          "id": "550",
          "groupCode": "path",
          "code": "path.planning.bestPlanNodeBuilder",
          "label": "bestPlan模式下的路径规划节点构造器",
          "desc": "bestPlan模式下的路径规划节点构造器",
          "i18nCode": "lang.rms.config.path.planning.bestPlanNodeBuilder",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "defaultNodeBuilder",
              "nodeAngleNodeBuilder"
            ],
            "defVal": "nodeAngleNodeBuilder"
          }
        },
        "path.planning.trafficRangeOfDistance": {
          "id": "551",
          "groupCode": "path",
          "code": "path.planning.trafficRangeOfDistance",
          "label": "路径规划时机器人检测拥堵系数的距离",
          "desc": "路径规划时机器人检测拥堵系数的距离",
          "i18nCode": "lang.rms.config.path.planning.trafficRangeOfDistance",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "150.0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.planning.needRecordPlanningCode": {
          "id": "1057",
          "groupCode": "path",
          "code": "path.planning.needRecordPlanningCode",
          "label": "需要进行打印的路径规划结果code",
          "desc": "需要进行打印的路径规划结果code",
          "i18nCode": "lang.rms.config.path.planning.needRecordPlanningCode",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1,2,3"
          }
        },
        "path.dispatching.enabledCheckDeadLock": {
          "id": "1059",
          "groupCode": "path",
          "code": "path.dispatching.enabledCheckDeadLock",
          "label": "是否开启检查死锁",
          "desc": "是否开启检查死锁",
          "i18nCode": "lang.rms.config.path.dispatching.enabledCheckDeadLock",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "section": "DeadLock",
          "sectionLabel": "死锁检测",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.planning.enabledDeadLockLowPriority": {
          "id": "1060",
          "groupCode": "path",
          "code": "path.planning.enabledDeadLockLowPriority",
          "label": "是否开启重规划优先级高于解死锁",
          "desc": "是否开启重规划优先级高于解死锁",
          "i18nCode": "lang.rms.config.path.planning.enabledCheckDeadLock",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "section": "DeadLock",
          "sectionLabel": "死锁检测",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "path.planning.enabledDeadLockExeTaskTimeout": {
          "id": "1061",
          "groupCode": "path",
          "code": "path.planning.enabledDeadLockExeTaskTimeout",
          "label": "是否开启解死锁任务超时",
          "desc": "开启解死锁任务超时，为0表示不开启",
          "i18nCode": "lang.rms.config.path.planning.enabledDeadLockExeTaskTimeout",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "section": "DeadLock",
          "sectionLabel": "死锁检测",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "30",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.deadlock.enabledStandardDeadLockSolution": {
          "id": "1066",
          "groupCode": "path",
          "code": "path.deadlock.enabledStandardDeadLockSolution",
          "label": "是否启用标准死锁解决方案",
          "desc": "是否启用标准死锁解决方案",
          "i18nCode": "lang.rms.config.path.deadlock.enabledStandardDeadLockSolution",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "section": "DeadLock",
          "sectionLabel": "死锁检测",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.planning.replan.traffic.conflict.ratio": {
          "id": "1076",
          "groupCode": "path",
          "code": "path.planning.replan.traffic.conflict.ratio",
          "label": "解决冲突的机器人百分比",
          "desc": "解决冲突的机器人百分比",
          "i18nCode": "lang.rms.config.warehouse.path.planning.replan.traffic.conflict.ratio",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.2",
            "limitMin": "0",
            "limitMax": "1"
          }
        },
        "path.planning.reducePlanForStationQueueLeftPathNum": {
          "id": "1090",
          "groupCode": "path",
          "code": "path.planning.reducePlanForStationQueueLeftPathNum",
          "label": "工作站排队机器人减少规划频率",
          "desc": "工作站排队机器人减少规划频率",
          "i18nCode": "lang.rms.config.path.planning.reducePlanForStationQueueLeftPathNum",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "path.rsp.ctl.p40.adjustHeightCellCount": {
          "id": "1157",
          "groupCode": "path",
          "code": "path.rsp.ctl.p40.adjustHeightCellCount",
          "label": "到达工位前高度调整区间单元格个数",
          "desc": "路径调度机械高度参数调整，P40、S20到达工位前高度调整区间单元格个数",
          "i18nCode": "lang.rms.config.path.rsp.ctl.p40.adjustHeightCellCount",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "3",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.rsp.ctl.p40.keepHeightCellCount": {
          "id": "1158",
          "groupCode": "path",
          "code": "path.rsp.ctl.p40.keepHeightCellCount",
          "label": "P40离开工位后高度保持区间单元格个数",
          "desc": "路径调度机械高度参数调整，P40离开工位后高度保持区间单元格个数",
          "i18nCode": "lang.rms.config.path.rsp.ctl.p40.keepHeightCellCount",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "2",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "path.planning.enabledUseDistanceCache": {
          "id": "1164",
          "groupCode": "path",
          "code": "path.planning.enabledUseDistanceCache",
          "label": "是否启用距离缓存",
          "desc": "是否启用距离缓存",
          "i18nCode": "lang.rms.config.path.planning.enabledUseDistanceCache",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "path.planning.charger.distanceCalculatingStrategy": {
          "id": "1187",
          "groupCode": "path",
          "code": "path.planning.charger.distanceCalculatingStrategy",
          "label": "充电站距离计算策略",
          "desc": "充电站距离计算策略，曼哈顿：Manhattan，AStar：AStar",
          "i18nCode": "lang.rms.config.path.planning.charger.distanceCalculatingStrategy",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "Manhattan",
              "AStar"
            ],
            "defVal": "Manhattan"
          }
        },
        "warehouse.monitoring.enabledMonitorDeadlock": {
          "id": "460",
          "groupCode": "warehouse",
          "code": "warehouse.monitoring.enabledMonitorDeadlock",
          "label": "是否开启死锁环的异常监控",
          "desc": "是否开启死锁环的异常监控，默认为false不开启",
          "i18nCode": "lang.rms.config.warehouse.monitoring.enabledMonitorDeadlock",
          "path": "/path/planning",
          "pathLabel": "路径/路径规划",
          "section": "DeadLock",
          "sectionLabel": "死锁检测",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        }
      }
    },
    "system": {
      "monitor": {
        "stat.mapCell.enabledDelay": {
          "id": "374",
          "groupCode": "stat",
          "code": "stat.mapCell.enabledDelay",
          "label": "统计单元格一段时间的范围",
          "desc": "统计单元格一段时间的范围（分钟），整数。已废弃",
          "i18nCode": "lang.rms.config.stat.mapCell.enabledDelay",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "60",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "stat.mapCell.enabledStatCell": {
          "id": "375",
          "groupCode": "stat",
          "code": "stat.mapCell.enabledStatCell",
          "label": "统计单元格上所经过机器人流量",
          "desc": "是否开启统计一段时间内单元格上所经过的机器人流量",
          "i18nCode": "lang.rms.config.stat.mapCell.enabledStatCell",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "stat.robotTask.delay": {
          "id": "376",
          "groupCode": "stat",
          "code": "stat.robotTask.delay",
          "label": "统计机器人任务时间维度",
          "desc": "统计机器人任务一段时间的范围",
          "i18nCode": "lang.rms.config.stat.robotTask.delay",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "stat.robotTask.enabledStatRobotTask": {
          "id": "377",
          "groupCode": "stat",
          "code": "stat.robotTask.enabledStatRobotTask",
          "label": "统计机器人任务",
          "desc": "是否开启统计机器人任务",
          "i18nCode": "lang.rms.config.stat.robotTask.enabledStatRobotTask",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.efficiency.eventClearIntervalTime": {
          "id": "399",
          "groupCode": "system",
          "code": "system.efficiency.eventClearIntervalTime",
          "label": "效率指标超过指定时间清除内存",
          "desc": "效率指标超过指定时间清除内存",
          "i18nCode": "lang.rms.config.system.efficiency.eventClearIntervalTime",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "86400000",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "system.efficiencyStat.enabledEfficiencyStat": {
          "id": "400",
          "groupCode": "system",
          "code": "system.efficiencyStat.enabledEfficiencyStat",
          "label": "开启效率指标数据统计",
          "desc": "是否开启效率指标数据统计功能",
          "i18nCode": "lang.rms.config.system.efficiencyStat.enabledEfficiencyStat",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "warehouse.monitoring.robotAveragePowerThreshold": {
          "id": "402",
          "groupCode": "warehouse",
          "code": "warehouse.monitoring.robotAveragePowerThreshold",
          "label": "仓库监控机器人平均电量阈值",
          "desc": "仓库监控机器人平均电量阈值",
          "i18nCode": "lang.rms.config.warehouse.monitoring.robotAveragePowerThreshold",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "30",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "warehouse.monitoring.waitTimeout": {
          "id": "461",
          "groupCode": "warehouse",
          "code": "warehouse.monitoring.waitTimeout",
          "label": "监控机器人任务中停止报异常的时长",
          "desc": "监控机器人任务中停止报异常的时长，默认30s",
          "i18nCode": "lang.rms.config.warehouse.monitoring.waitTimeout",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "30",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "warehouse.monitoring.enabledMonitorCrashed": {
          "id": "462",
          "groupCode": "warehouse",
          "code": "warehouse.monitoring.enabledMonitorCrashed",
          "label": "是否开启碰撞的异常监控",
          "desc": "是否开启碰撞的异常监控，默认为false不开启",
          "i18nCode": "lang.rms.config.warehouse.monitoring.enabledMonitorCrashed",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "web.monitor.shelf.fullPushInterval": {
          "id": "524",
          "groupCode": "BackEndParam",
          "code": "web.monitor.shelf.fullPushInterval",
          "label": "地图监控推送全量货架数据给前端间隔时间",
          "desc": "地图监控每隔多久(毫秒),推送全量货架数据给前端,为0则不推送",
          "i18nCode": "lang.rms.config.web.monitor.shelf.fullPushInterval",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "60000",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "monitor.groups": {
          "id": "176",
          "groupCode": "monitor",
          "code": "monitor.groups",
          "label": "监控群组",
          "desc": "系统监控的任务群组",
          "i18nCode": "lang.rms.config.monitor.groups",
          "path": "/system/monitor",
          "pathLabel": "系统/监控",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "shelfExceptionMonitorPoller,robotDeviceExceptionMonitorPoller,robotPathPlanExceptionMonitorPoller,robotCommunicationExceptionMonitorPoller,chargerDeviceExceptionMonitorPoller,chargerCommunicationExceptionMonitorPoller,warehouseExceptionMonitorPoller,stopButtonExceptionMonitorPoller,robotTaskExceptionMonitorPoller,robotJobExceptionMonitorPoller"
          }
        }
      },
      "stop": {
        "system.stop.mode": {
          "id": "401",
          "groupCode": "system",
          "code": "system.stop.mode",
          "label": "系统急停模式",
          "desc": "系统急停模式，0：普通急停，1：紧急制动",
          "i18nCode": "lang.rms.config.system.stop.mode",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              0,
              1
            ],
            "defVal": "0"
          }
        },
        "robot.task.enabledAutoCancelWhenOnFirePass": {
          "id": "1074",
          "groupCode": "warehouse",
          "code": "robot.task.enabledAutoCancelWhenOnFirePass",
          "label": "消防急停时急停区内工作站任务是否取消",
          "desc": "消防急停时急停区内工作站任务是否取消",
          "i18nCode": "lang.rms.config.warehouse.robot.task.enabledAutoCancelWhenOnFirePass",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "path.dispatching.robotAwayCellNumberWhenFireStop": {
          "id": "1075",
          "groupCode": "warehouse",
          "code": "path.dispatching.robotAwayCellNumberWhenFireStop",
          "label": "消防急停机器人离开消防通道几个格子",
          "desc": "消防急停机器人离开消防通道几个格子",
          "i18nCode": "lang.rms.config.warehouse.path.dispatching.robotAwayCellNumberWhenFireStop",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "3",
              "4",
              "5",
              "6"
            ],
            "defVal": "3"
          }
        },
        "system.stop.controller.globalStopEnableAllAreaStop": {
          "id": "1083",
          "groupCode": "system",
          "code": "system.stop.controller.globalStopEnableAllAreaStop",
          "label": "急停控制器模式全局急停是否触发所有区域急停",
          "desc": "急停控制器模式下，全局急停是否触发所有区域急停",
          "i18nCode": "lang.rms.config.system.stop.controller.globalStopEnableAllAreaStop",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.stop.mode.enabledWhenCellInAreaEdge": {
          "id": "1089",
          "groupCode": "system",
          "code": "system.stop.mode.enabledWhenCellInAreaEdge",
          "label": "急停时机器人在区域边界，是否立即停止",
          "desc": "开启系统急停模式，当机器人所在单元格在区域边界时，是否可以立即停止",
          "i18nCode": "lang.rms.config.system.stop.mode.enabledWhenCellInAreaEdge",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.stop.controller.allAreaEnableGlobal": {
          "id": "104",
          "groupCode": "system",
          "code": "system.stop.controller.allAreaEnableGlobal",
          "label": "急停控制器模式下所有区域急停按钮是否触发全局急停",
          "desc": "急停控制器模式下，所有区域急停按钮是否触发全局急停",
          "i18nCode": "lang.rms.config.system.stop.controller.allAreaEnableGlobal",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.stop.controller.emailAlarm.addresses": {
          "id": "105",
          "groupCode": "system",
          "code": "system.stop.controller.emailAlarm.addresses",
          "label": "系统急停警报邮件的收件人地址",
          "desc": "系统急停警报邮件的收件人地址",
          "i18nCode": "lang.rms.config.system.stop.controller.emailAlarm.addresses",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "system.stop.controller.emailAlarm.clientName": {
          "id": "106",
          "groupCode": "system",
          "code": "system.stop.controller.emailAlarm.clientName",
          "label": "系统急停警报邮件的收件人名称",
          "desc": "系统急停警报邮件的收件人名称",
          "i18nCode": "lang.rms.config.system.stop.controller.emailAlarm.clientName",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "system.stop.controller.emailAlarm.language": {
          "id": "107",
          "groupCode": "system",
          "code": "system.stop.controller.emailAlarm.language",
          "label": "系统急停警报邮件所用语言",
          "desc": "系统急停警报邮件所用语言，中文：zh_cn；中文繁体：zh_hk；英文：en_us；日文：ja_jp",
          "i18nCode": "lang.rms.config.system.stop.controller.emailAlarm.language",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "zh_cn",
              "zh_hk",
              "en_us",
              "ja_jp"
            ],
            "defVal": "en_us"
          }
        },
        "system.stop.controller.enableEmailAlarm": {
          "id": "108",
          "groupCode": "system",
          "code": "system.stop.controller.enableEmailAlarm",
          "label": "是否启用邮件警告",
          "desc": "是否启用邮件警告（系统急停控制器）",
          "i18nCode": "lang.rms.config.system.stop.controller.enableEmailAlarm",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.stop.controller.mode": {
          "id": "109",
          "groupCode": "system",
          "code": "system.stop.controller.mode",
          "label": "是否使用急停控制器",
          "desc": "系统急停控制器控制模式：0，默认参数，表示不使用控制器，控制器不会触发任何消息，也无法触发急停；1：使用控制器，可使用控制器触发急停",
          "i18nCode": "lang.rms.config.system.stop.controller.mode",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              0,
              1
            ],
            "defVal": "0"
          }
        },
        "system.stop.enabledForceFireStop": {
          "id": "110",
          "groupCode": "system",
          "code": "system.stop.enabledForceFireStop",
          "label": "是否强制使用消防急停",
          "desc": "是否强制使用消防急停",
          "i18nCode": "lang.rms.config.system.stop.enabledForceFireStop",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.stop.enabledLevel": {
          "id": "111",
          "groupCode": "system",
          "code": "system.stop.enabledLevel",
          "label": "是否区分系统急停级别",
          "desc": "是否区分系统急停级别",
          "i18nCode": "lang.rms.config.system.stop.enabledLevel",
          "path": "/system/stop",
          "pathLabel": "系统/急停控制",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        }
      },
      "rms": {
        "warp.callback.mode": {
          "id": "403",
          "groupCode": "warp",
          "code": "warp.callback.mode",
          "label": "封装回调选择模式",
          "desc": "封装回调选择模式",
          "i18nCode": "lang.rms.config.warp.callback.mode",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "defaultWrapCallback"
          }
        },
        "dmp.enable.newVersion": {
          "id": "552",
          "groupCode": "dmp",
          "code": "dmp.enable.newVersion",
          "label": "是否启用新版DMP",
          "desc": "是否启用新版DMP",
          "i18nCode": "lang.rms.config.dmp.enable.newVersion",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.forcedTask.enabledAutoRecoverDispatching": {
          "id": "1132",
          "groupCode": "system",
          "code": "system.forcedTask.enabledAutoRecoverDispatching",
          "label": "操作超时自动恢复调度开关",
          "desc": "强制停止调度的子任务，操作超时自动恢复调度开关",
          "i18nCode": "lang.rms.config.system.forcedTask.enabledAutoRecoverDispatching",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.forcedTask.autoRecoverDispatchingWaitTime": {
          "id": "1133",
          "groupCode": "system",
          "code": "system.forcedTask.autoRecoverDispatchingWaitTime",
          "label": "操作超时自动恢复调度等待时间",
          "desc": "强制停止调度的子任务，操作超时自动恢复调度等待时间(默认300秒)",
          "i18nCode": "lang.rms.config.system.forcedTask.autoRecoverDispatchingWaitTime",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "300",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "rack.box.ctl.size": {
          "id": "1135",
          "groupCode": "rack",
          "code": "rack.box.ctl.size",
          "label": "货架默认尺寸",
          "desc": "货架默认尺寸，如{\"length\":720,\"width\":520}",
          "i18nCode": "lang.rms.config.rack.box.ctl.size",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "{\"length\":720,\"width\":520}"
          }
        },
        "system.http.timeout": {
          "id": "100",
          "groupCode": "system",
          "code": "system.http.timeout",
          "label": "通用Http接口超时时间",
          "desc": "通用Http接口超时时间",
          "i18nCode": "lang.rms.config.system.http.timeout",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "system.log.operationBatchSaveSize": {
          "id": "101",
          "groupCode": "system",
          "code": "system.log.operationBatchSaveSize",
          "label": "操作日志一次入库条数",
          "desc": "操作日志一次入库条数",
          "i18nCode": "lang.rms.config.system.log.operationBatchSaveSize",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "100",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "system.projectName": {
          "id": "102",
          "groupCode": "system",
          "code": "system.projectName",
          "label": "RMS当前对接的客户",
          "desc": "RMS当前对接的客户",
          "i18nCode": "lang.rms.config.system.projectName",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "system.safetyProgram.enabled": {
          "id": "103",
          "groupCode": "system",
          "code": "system.safetyProgram.enabled",
          "label": "是否启用安全方案",
          "desc": "是否启用安全方案",
          "i18nCode": "lang.rms.config.system.safetyProgram.enabled",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.warehouse.id": {
          "id": "112",
          "groupCode": "system",
          "code": "system.warehouse.id",
          "label": "仓库id",
          "desc": "仓库id，用于机器人事件上报，仓库模型建立后此配置需要被移除",
          "i18nCode": "lang.rms.config.system.warehouse.id",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "dmp.callback.handler": {
          "id": "140",
          "groupCode": "dmp",
          "code": "dmp.callback.handler",
          "label": "DMP消息回调处理器",
          "desc": "DMP消息回调处理器，无特殊需求勿改",
          "i18nCode": "lang.rms.config.dmp.callback.handler",
          "path": "/system/rms",
          "pathLabel": "系统/RMS参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "defaultDmpCallbackHandler"
          }
        }
      },
      "api": {
        "api.apollo.syncShelfScoreUrl": {
          "id": "407",
          "groupCode": "api",
          "code": "api.apollo.syncShelfScoreUrl",
          "label": "货架分数同步API",
          "desc": "货架分数同步API",
          "i18nCode": "lang.rms.config.api.apollo.syncShelfScoreUrl",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://127.0.0.1:8080/beetle/api/score/queryShelfScore"
          }
        },
        "api.callback.dmp.enabledNotifyStationStatusChanged": {
          "id": "1069",
          "groupCode": "api",
          "code": "api.callback.dmp.enabledNotifyStationStatusChanged",
          "label": "工作站状态变更是否通知DMP",
          "desc": "工作站状态变更是否通知DMP",
          "i18nCode": "lang.rms.config.api.callback.dmp.enabledNotifyStationStatusChanged",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "api.callback.dmp.callbackFlow": {
          "id": "1070",
          "groupCode": "api",
          "code": "api.callback.dmp.callbackFlow",
          "label": "DMP回调处理链",
          "desc": "DMP回调处理链",
          "i18nCode": "lang.rms.config.api.callback.dmp.callbackFlow",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "api.callback.msg.deleteAfterFinishByMsgTypes": {
          "id": "1078",
          "groupCode": "api",
          "code": "api.callback.msg.deleteAfterFinishByMsgTypes",
          "label": "callback调用完成后需要删除的类型",
          "desc": "callback调用完成后需要删除的类型，多个使用\",\"进行分隔",
          "i18nCode": "lang.rms.config.api.callback.msg.deleteAfterFinishByMsgTypes",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "api.callback.msg.deleteAfterFinishBySubMsgTypes": {
          "id": "1079",
          "groupCode": "api",
          "code": "api.callback.msg.deleteAfterFinishBySubMsgTypes",
          "label": "callback调用完成后需要删除的子类型",
          "desc": "callback调用完成后需要删除的类型，多个使用\",\"进行分隔",
          "i18nCode": "lang.rms.config.api.callback.msg.deleteAfterFinishBySubMsgTypes",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "api.callback.msg.needNotSaveToDbByMsgTypes": {
          "id": "1080",
          "groupCode": "api",
          "code": "api.callback.msg.needNotSaveToDbByMsgTypes",
          "label": "不需要存库的回调信息类型",
          "desc": "不需要存库的回调信息类型，仅打印日志，多个使用\",\"进行分隔",
          "i18nCode": "lang.rms.config.api.callback.msg.needNotSaveToDbByMsgTypes",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "api.callback.msg.needNotSaveToDbBySubMsgTypes": {
          "id": "1081",
          "groupCode": "api",
          "code": "api.callback.msg.needNotSaveToDbBySubMsgTypes",
          "label": "不需要存库的回调信息子类型",
          "desc": "不需要存库的回调信息子类型，仅打印日志，多个使用\",\"进行分隔",
          "i18nCode": "lang.rms.config.api.callback.msg.needNotSaveToDbBySubMsgTypes",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "api.callback.stop.enabledCallbackSortingInfo": {
          "id": "1084",
          "groupCode": "system",
          "code": "api.callback.stop.enabledCallbackSortingInfo",
          "label": "急停回调是否包含格口信息和工作站信息",
          "desc": " \r\n系统急停、消防急停、系统恢复时，给上游的回调消息是否包含格口信息和工作站信息，默认关闭，分拣场景需打开",
          "i18nCode": "lang.rms.config.api.callback.stop.enabledCallbackSortingInfo",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "api.https.callback.enabledIgnoreSSL": {
          "id": "1109",
          "groupCode": "api",
          "code": "api.https.callback.enabledIgnoreSSL",
          "label": "https回调是否忽略SSL认证",
          "desc": "https回调是否忽略SSL认证",
          "i18nCode": "lang.rms.config.api.https.callback.enabledIgnoreSSL",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "api.callback.enabledExceptionCallback": {
          "id": "115",
          "groupCode": "api",
          "code": "api.callback.enabledExceptionCallback",
          "label": "是否开启异常回调",
          "desc": "是否开启异常回调",
          "i18nCode": "lang.rms.config.api.callback.enabledExceptionCallback",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "api.callback.max.retry.times": {
          "id": "116",
          "groupCode": "api",
          "code": "api.callback.max.retry.times",
          "label": "回调失败最大尝试次数",
          "desc": "回调失败最大尝试次数",
          "i18nCode": "lang.rms.config.api.callback.max.retry.times",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "10",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "api.flow": {
          "id": "117",
          "groupCode": "api",
          "code": "api.flow",
          "label": "RMS API流程",
          "desc": "RMS API流程",
          "i18nCode": "lang.rms.config.api.flow",
          "path": "/system/api",
          "pathLabel": "系统/API参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        }
      },
      "robot": {
        "robot.supportedCrossFloorProtocolSeries": {
          "id": "1091",
          "groupCode": "system",
          "code": "robot.supportedCrossFloorProtocolSeries",
          "label": "支持跨楼层协议的机器人系列",
          "desc": "支持跨楼层协议的机器人系列",
          "i18nCode": "lang.rms.config.robot.supportedCrossFloorProtocolSeries",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "M,F"
          }
        },
        "robot.task.enabledSkyPickPalletSelfHealing": {
          "id": "1112",
          "groupCode": "skypick",
          "code": "robot.task.enabledSkyPickPalletSelfHealing",
          "label": "四向库区托盘位置自愈开关",
          "desc": "四向库区托盘位置自愈开关，默认为关",
          "i18nCode": "lang.rms.robot.task.enabledSkyPickPalletSelfHealing",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.error.codes": {
          "id": "41",
          "groupCode": "robot",
          "code": "robot.error.codes",
          "label": "需要处理的机器人异常code集合",
          "desc": "需要处理的机器人异常code集合",
          "i18nCode": "lang.rms.config.robot.error.codes",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "robot.host.updateTimeout": {
          "id": "42",
          "groupCode": "robot",
          "code": "robot.host.updateTimeout",
          "label": "上位机升级发送数据超时时间",
          "desc": "上位机升级发送数据超时时间",
          "i18nCode": "lang.rms.config.robot.host.updateTimeout",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "100",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.save.map.timeout": {
          "id": "56",
          "groupCode": "robot",
          "code": "robot.save.map.timeout",
          "label": "slam机器人保存地图接口超时时间",
          "desc": "给SLAM机器人下发保存地图指令时接口超时时间设置，单位:秒",
          "i18nCode": "lang.rms.config.robot.save.map.timeout",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "60",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "robot.send.model.config": {
          "id": "57",
          "groupCode": "robot",
          "code": "robot.send.model.config",
          "label": "下发模型数据开关",
          "desc": "给机器人下发模型数据的开关",
          "i18nCode": "lang.rms.config.robot.send.model.config",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.abnormal.intervalTime": {
          "id": "270",
          "groupCode": "robot",
          "code": "robot.abnormal.intervalTime",
          "label": "机器人异常在间隔时间内不会重复触发",
          "desc": "机器人异常在间隔时间内不会重复触发，默认5秒，单位秒。",
          "i18nCode": "lang.rms.config.robot.abnormal.intervalTime",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "5",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "robot.communication.offlineTimeout": {
          "id": "271",
          "groupCode": "robot",
          "code": "robot.communication.offlineTimeout",
          "label": "机器人通信离线超时不分配任务",
          "desc": "机器人通信离线超时不分配任务，默认300秒，单位：秒",
          "i18nCode": "lang.rms.config.robot.communication.offlineTimeout",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "300",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.event.message.enabledPublish": {
          "id": "276",
          "groupCode": "robot",
          "code": "robot.event.message.enabledPublish",
          "label": "是否对外发布机器人事件消息",
          "desc": "是否对外发布机器人事件消息",
          "i18nCode": "lang.rms.config.robot.event.message.enabledPublish",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.event.message.enabledSaveDatabase": {
          "id": "277",
          "groupCode": "robot",
          "code": "robot.event.message.enabledSaveDatabase",
          "label": "机器人事件消息启用保存数据库",
          "desc": "机器人事件消息启用保存数据库",
          "i18nCode": "lang.rms.config.robot.event.message.enabledSaveDatabase",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.frame.business.enabledLogPackageWith16Decimal": {
          "id": "278",
          "groupCode": "robot",
          "code": "robot.frame.business.enabledLogPackageWith16Decimal",
          "label": "是否开启存储机器人日志为16进制",
          "desc": "是否开启存储机器人日志为16进制",
          "i18nCode": "lang.rms.config.robot.frame.business.enabledLogPackageWith16Decimal",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.frame.business.enabledLogPacket": {
          "id": "279",
          "groupCode": "robot",
          "code": "robot.frame.business.enabledLogPacket",
          "label": "打印机器人业务日志",
          "desc": "打印机器人业务日志",
          "i18nCode": "lang.rms.config.robot.frame.business.enabledLogPacket",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "robot.frame.business.packetLength": {
          "id": "280",
          "groupCode": "robot",
          "code": "robot.frame.business.packetLength",
          "label": "机器人业务包大小",
          "desc": "机器人业务包大小，默认值256",
          "i18nCode": "lang.rms.config.robot.frame.business.packetLength",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "256",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.host.update.timeout": {
          "id": "281",
          "groupCode": "robot",
          "code": "robot.host.update.timeout",
          "label": "P系列机器人上位机升级等待超时时间",
          "desc": "P系列机器人上位机升级等待超时时间",
          "i18nCode": "lang.rms.config.robot.host.update.timeout",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "100",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.image.enabledUpload": {
          "id": "282",
          "groupCode": "robot",
          "code": "robot.image.enabledUpload",
          "label": "开启二维码机器人传图功能",
          "desc": "开启二维码机器人传图功能",
          "i18nCode": "lang.rms.config.robot.image.enabledUpload",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.path.enabledForwardSecurity": {
          "id": "284",
          "groupCode": "robot",
          "code": "robot.path.enabledForwardSecurity",
          "label": "行进过程中保持机构和机器人角度一致",
          "desc": "行进过程中保持机构和机器人角度一致，辊筒机器人适用",
          "i18nCode": "lang.rms.config.robot.path.enabledForwardSecurity",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.path.maxPointNum": {
          "id": "285",
          "groupCode": "robot",
          "code": "robot.path.maxPointNum",
          "label": "允许发给机器人的最大路径长度",
          "desc": "允许发给机器人的最大路径长度",
          "i18nCode": "lang.rms.config.robot.path.maxPointNum",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "150",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.position.enableConfirmation": {
          "id": "286",
          "groupCode": "robot",
          "code": "robot.position.enableConfirmation",
          "label": "机器人上报位置",
          "desc": "机器人上报位置",
          "i18nCode": "lang.rms.config.robot.position.enableConfirmation",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.slam.enabledStayUnderShelf": {
          "id": "287",
          "groupCode": "robot",
          "code": "robot.slam.enabledStayUnderShelf",
          "label": "M系列顶升机器人是否可以在货架下方休息",
          "desc": "M系列顶升机器人是否可以在货架下方休息",
          "i18nCode": "lang.rms.config.robot.slam.enabledStayUnderShelf",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.task.BoxDefaultDeliverHeight": {
          "id": "288",
          "groupCode": "robot",
          "code": "robot.task.BoxDefaultDeliverHeight",
          "label": "送货箱时默认的抱叉高度",
          "desc": "送货箱时默认的抱叉高度，单位mm",
          "i18nCode": "lang.rms.config.robot.task.BoxDefaultDeliverHeight",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "665",
            "limitMin": "0",
            "limitMax": "9999"
          }
        },
        "robot.type": {
          "id": "308",
          "groupCode": "robot",
          "code": "robot.type",
          "label": "机器人类型",
          "desc": "机器人类型",
          "i18nCode": "lang.rms.config.robot.type",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "robot.shelfModel.enabledSend2PSeries": {
          "id": "417",
          "groupCode": "robot",
          "code": "robot.shelfModel.enabledSend2PSeries",
          "label": "是否下发货架模型给P系列机器人",
          "desc": "是否下发货架模型给P系列机器人",
          "i18nCode": "lang.rms.config.robot.shelfModel.enabledSend2PSeries",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.defaultFloorIdWhenMulti": {
          "id": "444",
          "groupCode": "robot",
          "code": "robot.defaultFloorIdWhenMulti",
          "label": "SLAM机器人默认所在楼层",
          "desc": "多楼层地图，当SLAM机器人无二维码定位时，机器人默认所在楼层",
          "i18nCode": "lang.rms.config.robot.defaultFloorIdWhenMulti",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "9999",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.type.load.turnWithShelf": {
          "id": "447",
          "groupCode": "robot",
          "code": "robot.type.load.turnWithShelf",
          "label": "带货架一起旋转的机器人型号",
          "desc": "带货架一起旋转的机器人型号列表",
          "i18nCode": "lang.rms.config.robot.type.load.turnWithShelf",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "robot.calibration.banPointCollections": {
          "id": "473",
          "groupCode": "robot",
          "code": "robot.calibration.banPointCollections",
          "label": "禁止标定点位",
          "desc": "机器人禁止标定点位编码集合",
          "i18nCode": "lang.rms.config.robot.calibration.banPointCollections",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "robot.path.allowRobotKnockedAreaStop": {
          "id": "499",
          "groupCode": "robot",
          "code": "robot.path.allowRobotKnockedAreaStop",
          "label": "是否使用机器人碰撞急停区域",
          "desc": "是否使用机器人碰撞急停区域",
          "i18nCode": "lang.rms.robot.path.allowRobotKnockedAreaStop",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.path.knockedAreaStopWidth": {
          "id": "500",
          "groupCode": "robot",
          "code": "robot.path.knockedAreaStopWidth",
          "label": "碰撞区域急停范围",
          "desc": "碰撞区域急停范围，单位m",
          "i18nCode": "lang.rms.robot.path.knockedAreaStopWidth",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1",
            "limitMin": "0",
            "limitMax": "100"
          }
        },
        "robot.common.speed": {
          "id": "510",
          "groupCode": "robot",
          "code": "robot.common.speed",
          "label": "机器人速度",
          "desc": "机器人速度，单位m/s",
          "i18nCode": "lang.rms.config.robot.common.speed",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "1.5",
            "limitMin": "0",
            "limitMax": "9"
          }
        },
        "robot.task.enabledCancelOnFollowSingleLane": {
          "id": "1071",
          "groupCode": "robot",
          "code": "robot.task.enabledCancelOnFollowSingleLane",
          "label": "任务是否允许在可跟随的单行道上进行取消",
          "desc": "任务是否允许在可跟随的单行道上进行取消，true允许，false不允许，默认false",
          "i18nCode": "lang.rms.config.robot.task.enabledCancelOnFollowSingleLane",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.task.callback.ignoreMeaningLess": {
          "id": "1073",
          "groupCode": "robot",
          "code": "robot.task.callback.ignoreMeaningLess",
          "label": "机器人任务回调是否去掉无意义的拆分子任务开始执行的回调",
          "desc": "robotTask回调是否去掉无意义的拆分子任务的开始执行的回调，true忽略，false不忽略，默认false",
          "i18nCode": "lang.rms.config.robot.task.callback.ignoreMeaningLess",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "robot.event.message.disablePublishByEventGroup": {
          "id": "1082",
          "groupCode": "robot",
          "code": "robot.event.message.disablePublishByEventGroup",
          "label": "机器人事件不需要向eventLoop发布事件的eventGroup列表",
          "desc": "机器人事件不需要向eventLoop发布事件的eventGroup列表，多个使用\",\"进行分隔",
          "i18nCode": "lang.rms.config.robot.event.message.disablePublishByEventGroup",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "robot.type.turnInAdvance": {
          "id": "1086",
          "groupCode": "robot",
          "code": "robot.type.turnInAdvance",
          "i18nCode": "lang.rms.config.robot.type.turnInAdvance",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        },
        "robot.exception.stopMoving.timeThreshold": {
          "id": "1092",
          "groupCode": "robot",
          "code": "robot.exception.stopMoving.timeThreshold",
          "label": "机器人持续上报指定故障码时间阀值",
          "desc": "机器人持续上报指定故障码时间阀值，超过阀值将下发急停指令并释放预占路径，默认15s",
          "i18nCode": "lang.rms.robot.exception.stopMoving.timeThreshold",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "15",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.exception.stopMoving.errorCodes": {
          "id": "1093",
          "groupCode": "robot",
          "code": "robot.exception.stopMoving.errorCodes",
          "label": "机器人被判定为无法行走的故障码",
          "desc": "机器人被判定为无法行走的故障码，以逗号分隔。例如：400027,100008,100009,200014",
          "i18nCode": "lang.rms.robot.exception.stopMoving.errorCodes",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea",
            "defVal": "21026,21041,21042,21057,21059,21067,21068,21073,21085,21086,21087,21088,21089,21090,21091,21092,21093,21094,21095,21096,21097,21098"
          }
        },
        "robot.task.macSetSupportedSeries": {
          "id": "305",
          "groupCode": "robot",
          "code": "robot.task.macSetSupportedSeries",
          "label": "支持mac查询和设置的机器人系列",
          "desc": "支持mac查询和设置的机器人系列",
          "i18nCode": "lang.rms.config.robot.task.macSetSupportedSeries",
          "path": "/system/robot",
          "pathLabel": "系统/机器人参数",
          "type": "textarea",
          "options": {
            "componentName": "textarea"
          }
        }
      },
      "URL": {
        "robot.hostUpdateUrl": {
          "id": "43",
          "groupCode": "robot",
          "code": "robot.hostUpdateUrl",
          "label": "P系列机器人上位机升级URL",
          "desc": "P系列机器人上位机升级URL",
          "i18nCode": "lang.rms.config.robot.hostUpdateUrl",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}:5000/upper_upgrade/"
          }
        },
        "robot.https.server.cancelMapUri": {
          "id": "44",
          "groupCode": "robot",
          "code": "robot.https.server.cancelMapUri",
          "label": "slam机器人取消建图URI",
          "desc": "slam机器人取消建图URI",
          "i18nCode": "lang.rms.config.robot.https.server.cancelMapUri",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/https/cmd/cancel_scan_map"
          }
        },
        "robot.https.server.getMapUri": {
          "id": "45",
          "groupCode": "robot",
          "code": "robot.https.server.getMapUri",
          "label": "slam机器人获取正在创建的地图URI",
          "desc": "slam机器人获取正在创建的地图URI",
          "i18nCode": "lang.rms.config.robot.https.server.getMapUri",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/https/rms/realtime_scan_map"
          }
        },
        "robot.https.server.port": {
          "id": "46",
          "groupCode": "robot",
          "code": "robot.https.server.port",
          "label": "slam机器人http接口的端口号",
          "desc": "slam机器人http接口的端口号",
          "i18nCode": "lang.rms.config.robot.https.server.port",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "8884",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "robot.https.server.scanExtendMapUri": {
          "id": "47",
          "groupCode": "robot",
          "code": "robot.https.server.scanExtendMapUri",
          "label": "slam机器人开始扩展建图URI",
          "desc": "slam机器人开始扩展建图URI",
          "i18nCode": "lang.rms.config.robot.https.server.scanExtendMapUri",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/https/cmd/extend_map"
          }
        },
        "robot.https.server.scanMapUri": {
          "id": "48",
          "groupCode": "robot",
          "code": "robot.https.server.scanMapUri",
          "label": "slam机器人开始建图URI",
          "desc": "slam机器人开始建图URI",
          "i18nCode": "lang.rms.config.robot.https.server.scanMapUri",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/https/cmd/start_scan_map"
          }
        },
        "robot.https.server.sendModelUri": {
          "id": "49",
          "groupCode": "robot",
          "code": "robot.https.server.sendModelUri",
          "label": "给机器人下发模型数据的URI",
          "desc": "给机器人下发模型数据的URI",
          "i18nCode": "lang.rms.config.robot.https.server.sendModelUri",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}:8884/https/rms/config_perception_model"
          }
        },
        "robot.https.server.stopMapUri": {
          "id": "50",
          "groupCode": "robot",
          "code": "robot.https.server.stopMapUri",
          "label": "slam机器人停止建图并保存URI",
          "desc": "slam机器人停止建图并保存URI",
          "i18nCode": "lang.rms.config.robot.https.server.stopMapUri",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/https/rms/stop_scan_map"
          }
        },
        "robot.image.uploadPath": {
          "id": "51",
          "groupCode": "robot",
          "code": "robot.image.uploadPath",
          "label": "二维码机器人传图保存路径",
          "desc": "二维码机器人传图保存路径",
          "i18nCode": "lang.rms.config.robot.image.uploadPath",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/var/log/geekplus/tomcat-rms/athena/images/"
          }
        },
        "robot.init.locationConfigUrl": {
          "id": "52",
          "groupCode": "robot",
          "code": "robot.init.locationConfigUrl",
          "label": "SLAM机器人坐标下发地址",
          "desc": "SLAM机器人坐标下发地址",
          "i18nCode": "lang.rms.config.robot.init.locationConfigUrl",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}:8884/https/rms/initialize_localization"
          }
        },
        "robot.init.mapConfigUrl": {
          "id": "53",
          "groupCode": "robot",
          "code": "robot.init.mapConfigUrl",
          "label": "SLAM机器人地图下发地址",
          "desc": "SLAM机器人地图下发地址",
          "i18nCode": "lang.rms.config.robot.init.mapConfigUrl",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}:8884/https/rms/config_map"
          }
        },
        "robot.software.uploadPath": {
          "id": "58",
          "groupCode": "robot",
          "code": "robot.software.uploadPath",
          "label": "机器人软件上传路径",
          "desc": "机器人软件上传路径",
          "i18nCode": "lang.rms.config.robot.software.uploadPath",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "/var/log/geekplus/tomcat-rms/athena/software/"
          }
        },
        "robot.https.server.sendModelUriForRobot": {
          "id": "418",
          "groupCode": "robot",
          "code": "robot.https.server.sendModelUriForRobot",
          "label": "针对每个机器人设定下发货架模型的url",
          "desc": "针对每个机器人设定下发货架模型的url，JSON格式",
          "i18nCode": "lang.rms.config.robot.https.server.sendModelUriForRobot",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "robot.https.server.sendBoxModelUri": {
          "id": "478",
          "groupCode": "robot",
          "code": "robot.https.server.sendBoxModelUri",
          "label": "下发货箱模型URL",
          "desc": "下发货箱模型URL",
          "i18nCode": "lang.rms.config.robot.https.server.sendBoxModelUri",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}:5000/https/rms/config_bin_model"
          }
        },
        "robot.https.server.statusUri": {
          "id": "515",
          "groupCode": "robot",
          "code": "robot.https.server.statusUri",
          "label": "获取机器人实时信息的URI配置",
          "desc": "获取机器人实时信息的URI配置",
          "i18nCode": "lang.rms.config.robot.https.server.statusUri",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}:5000/https/rms/robotStatus"
          }
        },
        "robot.re.locationConfigUrl": {
          "id": "516",
          "groupCode": "robot",
          "code": "robot.re.locationConfigUrl",
          "label": "下发重定位位置信息给机器人的地址",
          "desc": "下发重定位位置信息给机器人的地址",
          "i18nCode": "lang.rms.config.robot.re.locationConfigUrl",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}:8884/https/rms/relocalization"
          }
        },
        "robot.param.getUrl": {
          "id": "54",
          "groupCode": "api",
          "code": "robot.param.getUrl",
          "label": "机器人参数获取url",
          "desc": "机器人参数获取url",
          "i18nCode": "lang.rms.config.robot.param.getUrl",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}:8884/https/rms/param/getParams"
          }
        },
        "robot.param.setUrl": {
          "id": "55",
          "groupCode": "api",
          "code": "robot.param.setUrl",
          "label": "机器人参数设置url",
          "desc": "机器人参数设置url",
          "i18nCode": "lang.rms.config.robot.param.setUrl",
          "path": "/system/URL",
          "pathLabel": "系统/URL",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "http://{ip}:8884/https/rms/param/setParams"
          }
        }
      }
    },
    "fesystem": {
      "hide": {
        "system.fe.curFloorID": {
          "id": "66",
          "groupCode": "fesystem",
          "code": "system.fe.curFloorID",
          "label": "当前默认显示楼层数",
          "desc": "当前默认显示楼层数，默认会显示1，2楼层",
          "i18nCode": "lang.rms.config.system.fe.curFloorID",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "text",
          "options": {
            "componentName": "text"
          }
        },
        "system.fe.disableChangeShelf": {
          "id": "67",
          "groupCode": "fesystem",
          "code": "system.fe.disableChangeShelf",
          "label": "是否隐藏更新位置按钮",
          "desc": "是否隐藏更新位置按钮",
          "i18nCode": "lang.rms.config.system.fe.disableChangeShelf",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.disableChangeShelfAngle": {
          "id": "68",
          "groupCode": "fesystem",
          "code": "system.fe.disableChangeShelfAngle",
          "label": "是否隐藏更新货架角度按钮",
          "desc": "是否隐藏更新货架角度按钮",
          "i18nCode": "lang.rms.config.system.fe.disableChangeShelfAngle",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.disableGoCharging": {
          "id": "69",
          "groupCode": "fesystem",
          "code": "system.fe.disableGoCharging",
          "label": "是否隐藏去充电按钮",
          "desc": "是否隐藏去充电按钮",
          "i18nCode": "lang.rms.config.system.fe.disableGoCharging",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.disableGoRemoveRobot": {
          "id": "70",
          "groupCode": "fesystem",
          "code": "system.fe.disableGoRemoveRobot",
          "label": "是否隐藏移除机器人按钮",
          "desc": "是否隐藏移除机器人按钮",
          "i18nCode": "lang.rms.config.system.fe.disableGoRemoveRobot",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.disableGoSomewhere": {
          "id": "71",
          "groupCode": "fesystem",
          "code": "system.fe.disableGoSomewhere",
          "label": "是否隐藏去某处按钮",
          "desc": "是否隐藏去某处按钮",
          "i18nCode": "lang.rms.config.system.fe.disableGoSomewhere",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.disableMoveShelf": {
          "id": "72",
          "groupCode": "fesystem",
          "code": "system.fe.disableMoveShelf",
          "label": "隐藏货架移动按钮",
          "desc": "隐藏货架移动按钮",
          "i18nCode": "lang.rms.config.system.fe.disableMoveShelf",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.disableRecoverRobot": {
          "id": "73",
          "groupCode": "fesystem",
          "code": "system.fe.disableRecoverRobot",
          "label": "隐藏机器人加入按钮",
          "desc": "隐藏机器人加入按钮",
          "i18nCode": "lang.rms.config.system.fe.disableRecoverRobot",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.disableSaveChange": {
          "id": "74",
          "groupCode": "fesystem",
          "code": "system.fe.disableSaveChange",
          "label": "隐藏单元格操作保存按钮",
          "desc": "隐藏单元格操作保存按钮",
          "i18nCode": "lang.rms.config.system.fe.disableSaveChange",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.disableShelfSide": {
          "id": "75",
          "groupCode": "fesystem",
          "code": "system.fe.disableShelfSide",
          "label": "货架面禁用",
          "desc": "货架面禁用",
          "i18nCode": "lang.rms.config.system.fe.disableShelfSide",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "L",
              "R"
            ],
            "defVal": "L,R"
          }
        },
        "system.fe.disableUpdateFloor": {
          "id": "76",
          "groupCode": "fesystem",
          "code": "system.fe.disableUpdateFloor",
          "label": "隐藏更新楼层按钮",
          "desc": "隐藏更新楼层按钮",
          "i18nCode": "lang.rms.config.system.fe.disableUpdateFloor",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.hideRobotScan": {
          "id": "77",
          "groupCode": "fesystem",
          "code": "system.fe.hideRobotScan",
          "label": "隐藏地面扫描按钮",
          "desc": "隐藏地面扫描按钮",
          "i18nCode": "lang.rms.config.system.fe.hideRobotScan",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.hideSysScramButton": {
          "id": "78",
          "groupCode": "fesystem",
          "code": "system.fe.hideSysScramButton",
          "label": "隐藏系统急停按钮",
          "desc": "隐藏系统急停按钮",
          "i18nCode": "lang.rms.config.system.fe.hideSysScramButton",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.propertyPageHide": {
          "id": "83",
          "groupCode": "fesystem",
          "code": "system.fe.propertyPageHide",
          "label": "隐藏热度管理页面",
          "desc": "隐藏热度管理页面",
          "i18nCode": "lang.rms.config.system.fe.propertyPageHide",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.robotRecyclePageHide": {
          "id": "84",
          "groupCode": "fesystem",
          "code": "system.fe.robotRecyclePageHide",
          "label": "隐藏维修调度页面",
          "desc": "隐藏维修调度页面",
          "i18nCode": "lang.rms.config.system.fe.robotRecyclePageHide",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.showCollection": {
          "id": "85",
          "groupCode": "fesystem",
          "code": "system.fe.showCollection",
          "label": "显示一键集合按钮",
          "desc": "显示一键集合按钮",
          "i18nCode": "lang.rms.config.system.fe.showCollection",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.showFireStop": {
          "id": "86",
          "groupCode": "fesystem",
          "code": "system.fe.showFireStop",
          "label": "是否显示消防急停按钮",
          "desc": "是否显示消防急停按钮",
          "i18nCode": "lang.rms.config.system.fe.showFireStop",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.showReloadMap": {
          "id": "87",
          "groupCode": "fesystem",
          "code": "system.fe.showReloadMap",
          "label": "是否显示重新加载地图按钮",
          "desc": "是否显示重新加载地图按钮",
          "i18nCode": "lang.rms.config.system.fe.showReloadMap",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.showRoad": {
          "id": "88",
          "groupCode": "fesystem",
          "code": "system.fe.showRoad",
          "label": "显示地图线路",
          "desc": "显示地图线路",
          "i18nCode": "lang.rms.config.system.fe.showRoad",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.showRobotAwakenButton": {
          "id": "89",
          "groupCode": "fesystem",
          "code": "system.fe.showRobotAwakenButton",
          "label": "显示一键唤醒按钮",
          "desc": "显示一键唤醒按钮",
          "i18nCode": "lang.rms.config.system.fe.showRobotAwakenButton",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.showRobotScan": {
          "id": "90",
          "groupCode": "fesystem",
          "code": "system.fe.showRobotScan",
          "label": "显示一键扫描",
          "desc": "显示一键扫描",
          "i18nCode": "lang.rms.config.system.fe.showRobotScan",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.showRobotSleepButton": {
          "id": "91",
          "groupCode": "fesystem",
          "code": "system.fe.showRobotSleepButton",
          "label": "显示一键休眠按钮",
          "desc": "显示一键休眠按钮",
          "i18nCode": "lang.rms.config.system.fe.showRobotSleepButton",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.showRobotsShutdownButton": {
          "id": "92",
          "groupCode": "fesystem",
          "code": "system.fe.showRobotsShutdownButton",
          "label": "显示一键关机按钮",
          "desc": "显示一键关机按钮",
          "i18nCode": "lang.rms.config.system.fe.showRobotsShutdownButton",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.showSelectFloors": {
          "id": "93",
          "groupCode": "fesystem",
          "code": "system.fe.showSelectFloors",
          "label": "是否显示选择楼层",
          "desc": "是否显示选择楼层",
          "i18nCode": "lang.rms.config.system.fe.showSelectFloors",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.showShelfReturnButton": {
          "id": "94",
          "groupCode": "fesystem",
          "code": "system.fe.showShelfReturnButton",
          "label": "显示一键归还货架按钮",
          "desc": "显示一键归还货架按钮",
          "i18nCode": "lang.rms.config.system.fe.showShelfReturnButton",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.showShelfTurningButton": {
          "id": "95",
          "groupCode": "fesystem",
          "code": "system.fe.showShelfTurningButton",
          "label": "显示一键转面按钮",
          "desc": "显示一键转面按钮",
          "i18nCode": "lang.rms.config.system.fe.showShelfTurningButton",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.showSystemStop": {
          "id": "96",
          "groupCode": "fesystem",
          "code": "system.fe.showSystemStop",
          "label": "是否显示系统急停按钮",
          "desc": "是否显示系统急停按钮",
          "i18nCode": "lang.rms.config.system.fe.showSystemStop",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.showTaskRecovery": {
          "id": "97",
          "groupCode": "fesystem",
          "code": "system.fe.showTaskRecovery",
          "label": "是否显示任务恢复按钮",
          "desc": "是否显示任务恢复按钮",
          "i18nCode": "lang.rms.config.system.fe.showTaskRecovery",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.showYunWeiBtn": {
          "id": "98",
          "groupCode": "fesystem",
          "code": "system.fe.showYunWeiBtn",
          "label": "显示机器人部调试按钮",
          "desc": "显示机器人部调试按钮",
          "i18nCode": "lang.rms.config.system.fe.showYunWeiBtn",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.enabledShowDeadLockWarning": {
          "id": "1134",
          "groupCode": "fe",
          "code": "system.fe.enabledShowDeadLockWarning",
          "label": "死锁警告显示开关",
          "desc": "死锁警告显示开关",
          "i18nCode": "lang.rms.config.system.fe.enabledShowDeadLockWarning",
          "path": "/fesystem/hide",
          "pathLabel": "前端/显隐设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        }
      },
      "base": {
        "system.fe.isBackSupport": {
          "id": "79",
          "groupCode": "fesystem",
          "code": "system.fe.isBackSupport",
          "label": "左导和登录使用模拟数据",
          "desc": "左导和登录使用模拟数据",
          "i18nCode": "lang.rms.config.system.fe.isBackSupport",
          "path": "/fesystem/base",
          "pathLabel": "前端/基础设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": false
          }
        },
        "system.fe.mapPort": {
          "id": "80",
          "groupCode": "fesystem",
          "code": "system.fe.mapPort",
          "label": "地图监控端口",
          "desc": "地图监控端口",
          "i18nCode": "lang.rms.config.system.fe.mapPort",
          "path": "/fesystem/base",
          "pathLabel": "前端/基础设置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "8890",
            "limitMin": "0",
            "limitMax": "99999999"
          }
        },
        "system.fe.mapRotateAngle": {
          "id": "81",
          "groupCode": "fesystem",
          "code": "system.fe.mapRotateAngle",
          "label": "地图监控页地图的旋转角度",
          "desc": "地图监控页地图的旋转角度",
          "i18nCode": "lang.rms.config.system.fe.mapRotateAngle",
          "path": "/fesystem/base",
          "pathLabel": "前端/基础设置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "0.0",
            "limitMin": "0",
            "limitMax": "360"
          }
        },
        "system.fe.openPermissionSystem": {
          "id": "82",
          "groupCode": "fesystem",
          "code": "system.fe.openPermissionSystem",
          "label": "是否开启权限系统",
          "desc": "是否开启权限系统",
          "i18nCode": "lang.rms.config.system.fe.openPermissionSystem",
          "path": "/fesystem/base",
          "pathLabel": "前端/基础设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "system.fe.touchSelectBin": {
          "id": "99",
          "groupCode": "fesystem",
          "code": "system.fe.touchSelectBin",
          "label": "配置工作站上架任务触控选择货位",
          "desc": "配置工作站上架任务触控选择货位",
          "i18nCode": "lang.rms.config.system.fe.touchSelectBin",
          "path": "/fesystem/base",
          "pathLabel": "前端/基础设置",
          "type": "checkbox",
          "options": {
            "componentName": "checkbox",
            "defVal": true
          }
        },
        "web.security.websocket.signSecretValue": {
          "id": 113,
          "groupCode": "web",
          "code": "web.security.websocket.signSecretValue",
          "label": "web前端与系统交互秘钥",
          "desc": "web前端与系统交互秘钥",
          "i18nCode": "lang.rms.config.web.security.websocket.signSecretValue",
          "path": "/fesystem/base",
          "pathLabel": "前端/基础设置",
          "type": "text",
          "options": {
            "componentName": "text",
            "defVal": "signature#gk"
          }
        },
        "web.security.websocket.signType": {
          "id": "114",
          "groupCode": "web",
          "code": "web.security.websocket.signType",
          "label": "web前端与系统交互加密类型",
          "desc": "web前端与系统交互加密类型。MD5=md5加密，默认default=不启用",
          "i18nCode": "lang.rms.config.web.security.websocket.signType",
          "path": "/fesystem/base",
          "pathLabel": "前端/基础设置",
          "type": "select",
          "options": {
            "componentName": "select",
            "selectList": [
              "MD5",
              "default"
            ],
            "defVal": "default"
          }
        }
      }
    }
  }
