var e=require("regenerator-runtime");import*as t from"three";import{OrbitControls as n,MapControls as r}from"three/examples/jsm/controls/OrbitControls";import{TransformControls as a}from"three/examples/jsm/controls/TransformControls.js";import o from"intersects";import{mergeBufferGeometries as i}from"three/examples/jsm/utils/BufferGeometryUtils.js";import{Line2 as s}from"three/examples/jsm/lines/Line2.js";import{LineMaterial as l}from"three/examples/jsm/lines/LineMaterial.js";import{LineGeometry as d}from"three/examples/jsm/lines/LineGeometry.js";import h from"mitt";import{v4 as u}from"uuid";import p from"@tweenjs/tween.js";import f from"stats.js";import m from"flatbush";import{FBXLoader as v}from"three/examples/jsm/loaders/FBXLoader";import{OBJLoader as g}from"three/examples/jsm/loaders/OBJLoader";import{GLTFLoader as A}from"three/examples/jsm/loaders/GLTFLoader.js";import{RoughnessMipmapper as b}from"three/examples/jsm/utils/RoughnessMipmapper.js";import{PointerLockControls as y}from"three/examples/jsm/controls/PointerLockControls";import x from"js-md5";function C(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function k(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?C(Object(n),!0).forEach((function(t){B(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):C(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _(e){return _="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_(e)}function w(e,t,n,r,a,o,i){try{var s=e[o](i),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,a)}function S(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var o=e.apply(t,n);function i(e){w(o,r,a,i,s,"next",e)}function s(e){w(o,r,a,i,s,"throw",e)}i(void 0)}))}}function M(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function E(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function D(e,t,n){return t&&E(e.prototype,t),n&&E(e,n),e}function B(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function L(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&R(e,t)}function T(e){return T=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},T(e)}function R(e,t){return R=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},R(e,t)}function I(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function O(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return I(e)}function P(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=T(e);if(t){var a=T(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return O(this,n)}}function H(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,a,o=[],i=!0,s=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);i=!0);}catch(e){s=!0,a=e}finally{try{i||null==n.return||n.return()}finally{if(s)throw a}}return o}(e,t)||N(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(e){return function(e){if(Array.isArray(e))return G(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||N(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function N(e,t){if(e){if("string"==typeof e)return G(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?G(e,t):void 0}}function G(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var F={SOUTH:0,NORTH:Math.PI,WEST:-Math.PI/2,EAST:Math.PI/2},z=["NULL_CELL","BLOCKED_CELL"],$=new(function(){function e(){M(this,e),this._mapData={},this._mapModelData=[],this._floorIds=[],this.curFloorId=null}return D(e,[{key:"mapData",get:function(){return this._mapData},set:function(e){for(var t in e){!this._floorIds.includes(t)&&(this._floorIds=this._floorIds.concat(t)),null===this.curFloorId&&(this.curFloorId=t),this._mapData[t]=e[t]}}},{key:"modelData",get:function(){return this._mapModelData},set:function(e){for(var t in e)this._mapModelData[t]=e[t]}},{key:"destory",value:function(){this._mapData={},this._mapModelData={},this.curFloorId=null,this._floorIds=[]}},{key:"addModelData",value:function(e,t){var n=this._mapModelData[e]||[];this._mapModelData[e]=n.concat(t)}},{key:"getModelData",value:function(e){return this._mapModelData[e]?[].concat(this._mapModelData[e]):null}},{key:"delModelData",value:function(e,t){var n=this._mapModelData[e]||[],r=n.find((function(e){return e.uuid===t.uuid}));n.splice(r,1)}},{key:"updateModelData",value:function(e,t){var n=this._mapModelData[e].findIndex((function(e){return e.uuid===t.uuid}));this._mapModelData[e].splice(n,1,t)}},{key:"updateCellsData",value:function(e){for(var t=this.mapData[this.curFloorId].cells,n=[].concat(e),r=n.map((function(e){return e.cellCode})),a=function(e){if("object"!==_(t[e])||!t[e].cellCode)return"continue";var a=r.findIndex((function(n){return n===t[e].cellCode}));if(!~a)return"continue";Object.assign(t[e],n[a])},o=0;o<t.length;o++)a(o)}},{key:"findCellByCellCode",value:function(e){for(var t=this.mapData[1].cells,n=null,r=0;r<t.length;r++)if(t[r].cellCode===e){n=t[r];break}return n}},{key:"findModelByCellCode",value:function(e,t){var n=this._mapModelData[e]||[];return[].concat(n.filter((function(e){return e.cellCode===t})))}},{key:"findModelByUuid",value:function(e,t){var n=this._mapModelData[e]||[];return Object.assign({},n.find((function(e){return e.uuid===t}))||{})}},{key:"findSelectCell",value:function(e){for(var t=e.x,n=e.y,r=this._mapData[this.curFloorId].cells,a=null,i=0;i<r.length;i++){var s=r[i],l=s.startBounds,c=s.length,d=s.width,h=s.cellType;if(!z.includes(h)&&o.pointBox(t,-n,l.x,l.y,c,d)){a=r[i];break}}return a}},{key:"checkVerifyFloorId",value:function(e){return this._floorIds.includes(String(e))}}]),e}()),V={THEME:{SCENE_COLOR:15921906,BG_Color:4671572,CRACK_LAYER:9406851,CRACK_LATTICE:2172205,CRACK_BOX:15959335,CRACK_BACK_BOX:16554755,RACK_LAYER:1118481,RACK_LATTICE:14145511,RACK_BOX:15959335,ROBOT_RACK_BOX:30704,RACK_BACK_BOX:5474994,RACK_TASK_BOX:16737843,STATION_RACK_TASK_BOX:4372867,SHELF_HOT_CONF:{0:7535619,1:10615811,2:12516355,3:16382979,4:16568323,5:16554755,6:16544259,7:16532227,8:16526339,9:16515843,10:16515843},ROBOT_PATH_COLOR:{0:16711680,1:3342489,2:10027263,3:16711935,4:9135368,5:6916898,6:10170623,7:15643682,8:16515843,9:16515843},HOVER_3D:16734739,SELECTED:16341337,LOCKED:7763574,STOPPED:15217970,NULL_CELL:4168955,BLOCKED_CELL:14540253,CHARGER_CELL:16768633,CHARGER_PI_CELL:14540253,DROP_CELL:9493919,ELEVATOR_CELL:14141439,OMNI_DIR_CELL:6929151,QUEUE_CELL:13558693,SHELF_CELL:26763,STATION_CELL:16482384,TURN_CELL:12580838,E2W_PATH_CELL:6929151,W2E_PATH_CELL:6929151,S2N_PATH_CELL:6929151,N2S_PATH_CELL:6929151,E2W_S2N_PATH_CELL:6929151,E2W_N2S_PATH_CELL:6929151,W2E_S2N_PATH_CELL:6929151,W2E_N2S_PATH_CELL:6929151,E2W_W2E_PATH_CELL:6929151,N2S_S2N_PATH_CELL:6929151,E2W_W2E_N2S_PATH_CELL:6929151,E2W_W2E_S2N_PATH_CELL:6929151,N2S_S2N_E2W_PATH_CELL:6929151,N2S_S2N_W2E_PATH_CELL:6929151}},W=function(){function e(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};M(this,e),this.floorGeo=new t.Group,this.floorInfo={},this._createFloorGeo(n)}return D(e,[{key:"selectCell",value:function(e,t){e&&"mergeSeq"in e&&this.changeCellColor([[e.mergeSeq,t]])}},{key:"hoverCell",value:function(e,t){e&&"mergeSeq"in e&&this.changeCellColor([[e.mergeSeq,t]])}},{key:"cancelCellColor",value:function(e){e&&"mergeSeq"in e&&this.changeCellColor([[e.mergeSeq,this.__getCellColor(e)]])}},{key:"dispose",value:function(){this.floorGeo.dispose(),this.floorGeo=null}},{key:"changeCellColor",value:function(e){for(var n,r=this.floorGeo.children[0].geometry.getAttribute("color"),a=new t.Color,o=function(e,t){if(!t||!t.length)throw new Error("changeCellColor >>> 传递格式 [[seq, color],....]");var n=H(t,2),o=n[0],i=n[1];a.set(i),new Array(4).fill(0).map((function(e,t){return a.toArray(r.array,(4*o+t)*r.itemSize)}))},i=0;n=e[i++];)o(0,n);r.needsUpdate=!0}},{key:"_createFloorGeo",value:function(e){var t=e.floor,n=e.cells;if(!n||!Array.isArray(n))throw"生成二维码地图需要传送node数据";var r=this._getFloorLocation(t,n);this.floorGeo.name="floorGeo",this.floorGeo.userData.floorId=r.floorId,this.floorGeo.userData.floorInfo=r;var a=this._createCellsGeo(n),o=this._createFloorPanelGeo(r);this.floorGeo.add(a),this.floorGeo.add(o),this.floorGeo.position.set(0,0,0),this.floorGeo.rotateX(-Math.PI/2),this.floorInfo=r}},{key:"__getCellColor",value:function(e){var t=e.cellFlag,n=e.cellType;return"LOCKED"===t?V.THEME.LOCKED:" STOPPED"===t?V.THEME.STOPPED:V.THEME[n]}},{key:"_createCellsGeo",value:function(e){for(var n=this,r=[],a=e.length,o=new t.Color,s=new t.Matrix4,l=function(a){var i=e[a],l=i.width,c=i.length,d=i.cellType,h=i.startBounds;if(z.includes(d))return"continue";e[a].mergeSeq=a,s.makeTranslation(h.x+c/2,h.y+l/2,0);var u=new t.PlaneGeometry(c-.1*c,l-.1*l);u.applyMatrix4(s);var p=new t.BufferAttribute(new Float32Array(12),3);o.set(n.__getCellColor(e[a])),new Array(4).fill(0).map((function(e,t){return o.toArray(p.array,t*p.itemSize)})),u.setAttribute("color",p),r.push(u)},c=0;c<a;c++)l(c);var d=i(r),h=new t.Mesh(d,new t.MeshBasicMaterial({vertexColors:!0}));return h.position.set(0,0,-.04),h.name="cellBox",r.map((function(e){return e.dispose()})),h}},{key:"_getFloorLocation",value:function(e,t){var n=e.resolution,r=e.splitImage,a=e.floorId,o=e.locationX,i=e.locationY,s=!!r,l={left:0,right:0,top:0,bottom:0};if(s){var c=o||0,d=i||0;l={bottom:d,left:c,right:r.originWidth*n+c,top:r.originHeight*n+d}}else{var h=t[0].location||{x:t[0].startBounds.x-t[0].length/2,y:t[0].startBounds.y-t[0].width/2};l=t.reduce((function(e,t){var n=t.location||{x:t.startBounds.x-t.length/2,y:t.startBounds.y-t.width/2},r=n.x,a=n.y;return e.left>r&&(e.left=r),e.right<r&&(e.right=r),e.top<a&&(e.top=a),e.bottom>a&&(e.bottom=a),e}),{left:h.x,right:h.x,top:h.y,bottom:h.y})}return k(k({},l),{},{floorId:a,isSlam:s})}},{key:"_createFloorPanelGeo",value:function(e){var n=e.left,r=e.right,a=e.top,o=e.bottom,i=Math.abs(r-n)+10,s=Math.abs(a-o)+10,l=new t.PlaneGeometry(i,s),c=new t.Mesh(l,new t.MeshBasicMaterial({color:V.THEME.BG_Color,roughness:1,metalness:0,side:t.DoubleSide}));return c.position.set((n+r)/2,(o+a)/2,-.05),c.name="floorBox",c}},{key:"_createSlamFloorPanelGeo",value:function(e,n){var r=new t.Group,a=e.left,o=e.right,i=e.top,s=e.bottom,l=new t.PlaneGeometry(o-a,i-s),c=new t.Mesh(l,new t.MeshBasicMaterial({color:V.THEME.BG_Color,side:t.DoubleSide}));c.position.set((a+o)/2,(s+i)/2,-.1),c.name="floorBox",r.add(c);for(var d=n.splitImage,h=n.resolution,u=d.imageItems,p=new t.TextureLoader,f=function(e){var n=u[e];p.load(n.imageText,(function(e){var o=n.width*h,i=n.height*h,l=new t.Mesh(new t.PlaneGeometry(o,i),new t.MeshBasicMaterial({map:e}));l.position.set(n.x*h+o/2+a,n.y*h+i/2+s,-.05),r.add(l)}))},m=0;m<u.length;m++)f(m);return r}}]),e}(),U=function(){function e(t){M(this,e),this.uuid=t.data.uuid}return D(e,[{key:"fit",value:function(e,n){var r=n.width,a=n.length,o=new t.BoxGeometry(a,170,r),i=new t.Mesh(o,new t.MeshBasicMaterial({color:16776960})),s=(new t.Box3).setFromObject(i),l=(new t.Box3).setFromObject(e),c=[Math.abs(s.max.x-s.min.x),Math.abs(s.max.y-s.min.y),Math.abs(s.max.z-s.min.z)],d=[Math.abs(l.max.x-l.min.x),Math.abs(l.max.y-l.min.y),Math.abs(l.max.z-l.min.z)],h=[c[0]/d[0],c[1]/d[1],c[2]/d[2]],u=Math.min.apply(Math,h);return(u-=0)*e.scale.x}}]),e}(),q=function(){function e(){M(this,e),this.Map3d=null,this.Store=null,this.EventInstance=null,this.$dom=null,this.Emitter=null,this.PluginName="basePlugin"}return D(e,[{key:"created",value:function(){}},{key:"activated",value:function(){}},{key:"deactivated",value:function(){}},{key:"destroyed",value:function(){}}],[{key:"getInstance",value:function(t){return new e(t)}}]),e}(),K=function e(t){return t.name.includes("Model-Group")?t:"Scene"===t.type?null:e(t.parent)},X=function(e){L(r,q);var n=P(r);function r(e){var a;return M(this,r),(a=n.call(this,e))._selectCallBackArr=[],a._unSelectCallBackArr=[],a._raycaster=new t.Raycaster,a._pointer=new t.Vector2,a._limitName=[],a._selectGeo=null,a._selectCellGeo=null,a.PluginName="selectPlugin",a.selectElementData=[],a.isAdsorbCell=!1,a}return D(r,[{key:"selectCategory",set:function(e){this._limitName=[].concat(e)}},{key:"deactivated",value:function(){this.EventInstance.off("selectElement")}},{key:"activated",value:function(){var e=this;this.EventInstance.add("selectElement",{clickHandle:function(t){return e._selectElement(t)}})}},{key:"destoryed",value:function(){this._limitName=null,this._selectCallBackArr=null,this._unSelectCallBackArr=null,this.selectElementData=null}},{key:"triggerSelect",value:function(e){var t=(e||{}).category;if(this.hide(),e&&this._limitName.includes(t))if("cell"===t)this._selectCallBackArr.map((function(t){return t([k(k({},e),{},{category:"cell"})])}));else{var n=[e];if(this.isAdsorbCell){var r=this.Store.findCellByCellCode(e.cellCode)||null;r&&(n=Object.assign(n,k(k({},r),{},{category:"cell"})))}this._selectCallBackArr.map((function(e){return e(n)}))}else this._unSelectCallBackArr.map((function(e){return e(null)}))}},{key:"triggerCancelSelect",value:function(){this.hide(),this._unSelectCallBackArr.map((function(e){return e()}))}},{key:"select",value:function(e){this._selectCallBackArr.push(e)}},{key:"unselect",value:function(e){this._unSelectCallBackArr.push(e)}},{key:"hide",value:function(){this._selectGeo&&(this.Map3d.scene.remove(this._selectGeo),this._selectGeo=null),this._selectCellGeo&&(this.Map3d.scene.remove(this._selectCellGeo),this._selectCellGeo=null)}},{key:"renderModelSelect",value:function(e){var n=new t.Box3;n.setFromObject(e);var r=new t.Box3Helper(n,6799930);r.name="select",this._selectGeo=r,this.Map3d.scene.add(r)}},{key:"updateModelSelect",value:function(e){var t=e.uuid;!!~this.selectElementData.find((function(e){return e.uuid===t}))&&(this.Map3d.scene.remove(this._selectGeo),this.renderModelSelect(this.Map3d.modelInstances[t].model))}},{key:"renderCellSelect",value:function(e){var t=e.width,n=e.length,r=e.startBounds,a=new d,o=function(e,t,n){e*=.5,t*=.5,n*=.5;var r=[];return r.push(-e,-t,-n,-e,t,-n,-e,t,-n,e,t,-n,e,t,-n,e,-t,-n,e,-t,-n,-e,-t,-n,-e,-t,n,-e,t,n,-e,t,n,e,t,n,e,t,n,e,-t,n,e,-t,n,-e,-t,n,-e,-t,-n,-e,-t,n,-e,t,-n,-e,t,n,e,t,-n,e,t,n,e,-t,-n,e,-t,n),r}(.9*n,.9*t,0);a.setPositions(o);var i=new l({color:16729088,linewidth:2});i.resolution.set(this.Map3d.$dom.offsetWidth,this.Map3d.$dom.offsetHeight);var c=new s(a,i);c.rotateX(-Math.PI/2),c.position.set(r.x+n/2,.01,-(r.y+t/2)),this._selectCellGeo=c,this.Map3d.scene.add(c)}},{key:"_selectByCell",value:function(e){var t=e.x,n=e.z;return this.Store.findSelectCell({x:t,y:n})}},{key:"_selectByModel",value:function(e){return!!e&&this.Store.findCellByCellCode(this.Map3d.modelInstances[e.userData.uuid]._data.cellCode)}},{key:"_selectElementNeedAdsorb",value:function(e){var t=this,n=[],r="floorBox"===e.object.name?this._selectByCell(e.point):this._selectByModel(K(e.object));if(!r)return n;for(var a=function(e){var a=t._limitName[e];if("cell"===a)n.push(k(k({},r),{},{category:a}));else{var o=t.Store.findModelByCellCode(a,r.cellCode)||[];o.length&&(n=n.concat(o.map((function(e){return k(k({},e),{},{category:a})}))))}},o=0;o<this._limitName.length;o++)a(o);return n}},{key:"_selectElementForbidAdsorb",value:function(e){var t=K(e.object);if(t){var n=this.Map3d.modelInstances[t.userData.uuid]._data,r=n.category,a=n.uuid;return this._limitName.includes(r)?[this.Store.findModelByUuid(r,a)]:[]}}},{key:"_selectElement",value:function(e){this._pointer.set(e.offsetX/this.$dom.offsetWidth*2-1,-e.offsetY/this.$dom.offsetHeight*2+1),this._raycaster.setFromCamera(this._pointer,this.Map3d.camera.get());var t=this._raycaster.intersectObjects([this.Map3d.scene.getObjectByName("floorBox")].concat(j(Object.values(this.Map3d.modelInstances).map((function(e){return e.model})))),!0);if(this.hide(),t.length){var n=t[0],r="floorBox"===n.object.name||this.isAdsorbCell?this._selectElementNeedAdsorb(n):this._selectElementForbidAdsorb(n);r.length?(this.selectElementData=r,this._selectCallBackArr.map((function(e){return e(r)}))):this._unSelectCallBackArr.map((function(e){return e(n.point)}))}else this._unSelectCallBackArr.map((function(e){return e(null)}))}}]),r}(),Z=new(function(e){L(r,q);var n=P(r);function r(e){var a;return M(this,r),(a=n.call(this,e)).raycaster=new t.Raycaster,a.pointer=new t.Vector2,a.hoverGeo=null,a.PluginName="hoverPlugin",a}return D(r,[{key:"deactivated",value:function(){this.EventInstance.off("hoveElement")}},{key:"destroyed",value:function(){this.addCategory="",this.EventInstance.off("hoveElement"),this.formatter=null}},{key:"activated",value:function(){var e=this;this.hoverGeo&&this.hidden(),!this.hoverGeo&&this._createGeo();var t=function(){return e.EventInstance.add("hoveElement",{moveHandle:function(t){return e._render(t)},dragoverHandle:function(t){return e._render(t)}})};this.Map3d.scene.getObjectByName("floorBox")?t():this.Emitter.on("after:initMap",(function(){return t()}))}},{key:"hoverByPoint",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(e){var n=this.Store.findSelectCell(e);if(n){var r=n.width,a=n.length,o=n.startBounds;this.hoverGeo.visible=!0,this.hoverGeo.geometry=new t.PlaneGeometry(.9*n.length,.9*n.width),this.hoverGeo.position.set(o.x+a/2,.01,-(o.y+r/2))}else this.hidden()}}},{key:"hidden",value:function(){this.hoverGeo.visible=!1}},{key:"_createGeo",value:function(){this.hoverGeo=new t.Mesh(new t.PlaneGeometry(1,1),new t.MeshBasicMaterial({color:16040979})),this.hoverGeo.name="hover",this.hoverGeo.rotateX(-Math.PI/2),this.hidden(),this.Map3d.scene.add(this.hoverGeo)}},{key:"_render",value:function(e){var n=this.Map3d.search.get([this.Map3d.scene.getObjectByName("floorBox")],e);if(n){var r=n.data[0];if(r){var a=r.width,o=r.length,i=r.startBounds;this.hoverGeo.visible=!0,this.hoverGeo.geometry=new t.PlaneGeometry(.9*r.length,.9*r.width),this.hoverGeo.position.set(i.x+o/2,.01,-(i.y+a/2))}else this.hidden()}}}]),r}()),Y=new(function(e){L(n,q);var t=P(n);function n(e){var r;return M(this,n),(r=t.call(this,e)).selectModel=null,r.selectData=null,r.active=!1,r.PluginName="movePlugin",r.isAdsorbCell=!1,r}return D(n,[{key:"activated",value:function(){this.active=!0;var e=this.Map3d.transformControl;e.addEventListener("mouseDown",this._moveStart.bind(this)),e.addEventListener("objectChange",this._move.bind(this)),e.addEventListener("mouseUp",this._moveEnd.bind(this))}},{key:"deactivated",value:function(){this._resetValue()}},{key:"destroyed",value:function(){this.active=!1,this._resetValue()}},{key:"begin",value:function(e){"cell"!==e.category&&this.active&&(this.selectData=e,this.selectModel=this.Map3d.modelInstances[this.selectData.uuid],this.Map3d.transformControl.attach(this.selectModel.model),this.isAdsorbCell=this.selectModel.isAdsorb)}},{key:"pause",value:function(){this.selectModel&&this.Map3d.transformControl.detach()}},{key:"recovery",value:function(){this.selectModel&&this.Map3d.transformControl.attach(this.selectModel.model)}},{key:"off",value:function(){this.selectModel&&(this.Map3d.transformControl.detach(),this.selectModel=null,this.selectData=null)}},{key:"isMoveStatus",value:function(e){return!(!this.active||!this.selectData)&&e.uuid===this.selectData.uuid}},{key:"_resetValue",value:function(){var e=this.Map3d.transformControl;e.removeEventListener("mouseDown",this._moveStart.bind(this)),e.removeEventListener("objectChange",this._move.bind(this)),e.removeEventListener("mouseUp",this._moveEnd.bind(this)),this.selectModel&&e.detach(),this.active=!1,this.selectModel=null,this.selectData=null}},{key:"_moveStart",value:function(){this.Map3d.disabledPlugin([Z.PluginName,J.PluginName]),J.hide()}},{key:"_move",value:function(){if(this.isAdsorbCell){var e=this.selectModel.model.position,t=e.x,n=e.z;Z.hoverByPoint({x:t,y:n})}}},{key:"_moveEnd",value:function(){var e=this;Z.hidden(),setTimeout((function(){e.Map3d.enablePlugin([Z.PluginName,J.PluginName])}),300);var t=this.selectModel.model.position,n=t.x,r=t.z,a=this.selectData,o=a.category,i=a.uuid,s=this.Store.findModelByUuid(o,i),l={cellCode:"",location:{x:n,y:-r},startBounds:{x:n,y:-r}};if(this.isAdsorbCell){var c,d=this.Store.findSelectCell({x:n,y:r});if(!d)return(c=this.selectModel.model.position).set.apply(c,j(this.selectModel.position)),void this.Emitter.emit("illegal:move");l=d}var h=l,u=h.cellCode,p=h.location,f=h.startBounds,m=k(k({},s),{},{cellCode:u,location:p,startBounds:f});this.Map3d.command.exec("update",{oldValue:s,newValue:m}),this.Emitter.emit("after:move",JSON.parse(JSON.stringify([m])))}}]),n}()),J=new X;J.selectCategory=["station","conveyor","cell"];var Q=function(e,t){var n=J.Map3d,r=e,a=n.modelInstances[r.uuid];J.renderModelSelect(a.model),Y.begin(r),J.Emitter.off("after:move"),J.Emitter.off("illegal:move"),J.Emitter.on("after:move",(function(e){var t=H(e,1)[0];J.triggerSelect(t)})),J.Emitter.on("illegal:move",(function(){J.triggerSelect(r)}))};J.select((function(e){var t=e.reduce((function(e,t){var n=e;return"cell"===t.category?n[0]=t:n[1]=t,n}),[null,null]);Y.off();var n=H(t,2),r=n[0],a=n[1];r&&function(e){J.renderCellSelect(e)}(r),a&&Q(a),J.Emitter.emit("after:select",JSON.parse(JSON.stringify(e)))})),J.unselect((function(e){J.Map3d.transformControl.detach(),J.Emitter.emit("after:unselect")}));var ee=function(e){L(r,U);var n=P(r);function r(e){var a;return M(this,r),(a=n.call(this,e))._object3d=e.object3d,a._data=e.data,a._group=new t.Group,a._group.name="".concat("Model-Group","-").concat(e.category),a._group.userData.uuid=e.data.uuid,a._group.userData.isAdsorb=e.isAdsorb,a._category=e.category,a._group.add(a._object3d),a.position=[],a.isAdsorb=e.isAdsorb,a._render(),a}return D(r,[{key:"model",get:function(){return this._group}},{key:"update",value:function(e){this._data=e,this._render(),$.updateModelData(this._category,e),J.updateModelSelect(e)}},{key:"_render",value:function(){var e,t=this._data,n=t.direction;t.cellCode;var r=t.location,a=(n||"NORTH").toLocaleUpperCase();this._group.rotation.y=F[a],this.position=[r.x,0,-r.y],(e=this._group.position).set.apply(e,j(this.position))}}]),r}(),te=new(function(){function e(){M(this,e),this.$dom=document,this.componentList={},this.exclusiveName="",this.isPause=!1}return D(e,[{key:"init",value:function(e){this.$dom=e,this._initHandle(),this._addEvent()}},{key:"add",value:function(e,t){this.componentList[e]=t}},{key:"off",value:function(e){this.exclusiveName="",e?delete this.componentList[e]:this.componentList={}}},{key:"exclusive",value:function(e){this.exclusiveName=e||e}},{key:"pause",value:function(){this.isPause=!0}},{key:"resume",value:function(){var e=this;setTimeout((function(){e.isPause=!1}),200)}},{key:"_addEvent",value:function(){this.$dom.addEventListener("mousedown",this._downHandle),this.$dom.addEventListener("mousemove",this._moveHandle),this.$dom.addEventListener("dragover",this._dragoverHandle),this.$dom.addEventListener("mouseup",this._upHandle),this.$dom.addEventListener("drop",this._dropHandle),this.$dom.addEventListener("mouseupoutside",this._outsideHandle),this.$dom.addEventListener("click",this._clickHandle),this.$dom.addEventListener("rightclick",this._rightClickHandle),document.addEventListener("keydown",this._keydownHandle),document.addEventListener("keyup",this._keyupHandle),this.$dom.addEventListener("contextmenu",this._rightClickHandle),this.component&&this.component.addHandle&&this.component.addHandle()}},{key:"_offEvent",value:function(){this.$dom.removeEventListener("mousedown",this._downHandle),this.$dom.removeEventListener("mousemove",this._moveHandle),this.$dom.removeEventListener("dragover",this._dragoverHandle),this.$dom.removeEventListener("mouseup",this._upHandle),this.$dom.removeEventListener("drop",this._dropHandle),this.$dom.removeEventListener("mouseupoutside",this._outsideHandle),this.$dom.removeEventListener("rightclick ",this._rightClickHandle),this.$dom.removeEventListener("click ",this._clickHandle),document.removeEventListener("keydown",this._keydownHandle),document.removeEventListener("keyup",this._keyupHandle),this.$dom.removeEventListener("contextmenu",this._rightClickHandle),this._runHandle("offHandle",""),this.componentList={}}},{key:"_initHandle",value:function(){this._downHandle=this.__downHandle.bind(this),this._moveHandle=this.__moveHandle.bind(this),this._upHandle=this.__upHandle.bind(this),this._outsideHandle=this.__outsideHandle.bind(this),this._rightClickHandle=this.__rightClickHandle.bind(this),this._keydownHandle=this.__keydownHandle.bind(this),this._keyupHandle=this.__keyupHandle.bind(this),this._clickHandle=this.__clickHandle.bind(this),this._dropHandle=this.__dropHandle.bind(this),this._dragoverHandle=this.__dragoverHandle.bind(this)}},{key:"__downHandle",value:function(e){this.isPause||this._runHandle("downHandle",e)}},{key:"__clickHandle",value:function(e){this.isPause||(e.preventDefault&&e.preventDefault(),this._runHandle("clickHandle",e))}},{key:"__moveHandle",value:function(e){this.isPause||this._runHandle("moveHandle",e)}},{key:"__upHandle",value:function(e){this.isPause||this._runHandle("upHandle",e)}},{key:"__outsideHandle",value:function(e){this.isPause||this._runHandle("outsideHandle",e)}},{key:"__rightClickHandle",value:function(e){console.log(e,this.isPause,12345),this.isPause||(e.preventDefault&&e.preventDefault(),this._runHandle("rightClickHandle",e))}},{key:"__keydownHandle",value:function(e){this.isPause||this._runHandle("keydownHandle",e)}},{key:"__keyupHandle",value:function(e){this.isPause||this._runHandle("keyupHandle",e)}},{key:"__dropHandle",value:function(e){this.isPause||this._runHandle("dropHandle",e)}},{key:"__dragoverHandle",value:function(e){this.isPause||(e.preventDefault(),this._runHandle("dragoverHandle",e))}},{key:"_runHandle",value:function(e,t){if(this.exclusiveName){var n=this.componentList[this.exclusiveName];n&&n[e]&&n[e](t)}else for(var r in this.componentList)if(this.componentList.hasOwnProperty(r)){var a=this.componentList[r];a&&a[e]&&a[e](t)}}}]),e}()),ne={},re=[],ae=function(){function e(t){M(this,e),te.init(t.dom),this.Emitter=h(),this._plugin=t.plugin||[]}return D(e,[{key:"getPlugin",value:function(e){return e?ne[e]:Object.values(ne)}},{key:"enablePlugin",value:function(e){for(var t=[].concat(e),n=function(e){var n=t[e],r=re.findIndex((function(e){return e===n}));if(!~r){var a=ne[n];a&&(re.push(n),a.activated())}},r=0;r<t.length;r++)n(r)}},{key:"disabledPlugin",value:function(e){for(var t=[].concat(e),n=function(e){var n=t[e],r=ne[n],a=re.findIndex((function(e){return e===n}));~a&&(re.splice(a,1),r.deactivated())},r=0;r<t.length;r++)n(r)}},{key:"registerPlugin",value:function(e){for(var t=[].concat(e),n=0;n<t.length;n++){var r=t[n];if(!r.PluginName)throw new Error(">>> [注册Map3D插件] 未提供PluginName,命名规则'{功能/行为....}Plugin'");if(!(r instanceof q))throw new Error(">>> 注册[Map3D]插件未扩展基类 class ".concat(key));r.Map3d=this,r.Store=$,r.EventInstance=te,r.$dom=this.$dom,r.Emitter=this.Emitter,r.created(),ne[r.PluginName]=r}}},{key:"removePlugins",value:function(e){for(var t=[].concat(e),n=function(e){var n=t[e],r=ne[n],a=re.findIndex((function(e){return e===n}));re.splice(a,1),r.destroyed(),delete ne[n]},r=0;r<t.length;r++)n(r)}}]),e}(),oe=[],ie=function(){function e(t){M(this,e),this.Map3d=t.Map3d,this.needSave=!1}return D(e,[{key:"undoStash",set:function(e){oe.push(e),this.needSave=!!oe.length}},{key:"destory",value:function(){oe=[]}},{key:"exec",value:function(e,t,n){e&&!e.includes("pure:")&&(this.undoStash={action:e,config:t}),this._dealAction(e,t,n)}},{key:"_dealAction",value:function(e,t,n){switch(e){case"update":this._update(t,n);break;case"add":this._add(t,n);break;case"del":this._del(t,n)}}},{key:"_update",value:function(e,t){var n=e.oldValue,r=e.newValue;this.Map3d.modelInstances[n.uuid].update&&this.Map3d.modelInstances[n.uuid].update(r,n),t&&t()}},{key:"_add",value:function(e,t){var n=e.value,r=n.category;this.Map3d._renderModel([n],{done:function(){t&&t()}}),$.addModelData(r,n)}},{key:"_del",value:function(e,t){for(var n=e.value,r=Array.isArray(n)?n:[n],a=0;a<r.length;a++){var o=r[a],i=o.category,s=this.Map3d.modelInstances[o.uuid];this.Map3d.scene.remove(s.model),delete this.Map3d.modelInstances[o.uuid],$.delModelData(i,n)}t&&t()}}]),e}(),se=0,le=function(){function e(){M(this,e),this.deltaTime=0,this.maxFPS=20,this._prevTime=0,this._animalStash=[],this.clock=new t.Clock,this.renderT=1/this.maxFPS,this.stats=null,this._render()}return D(e,[{key:"add",value:function(e){this._animalStash.push(e)}},{key:"addOnce",value:function(){}},{key:"remove",value:function(e){var t=this._animalStash.findIndex((function(t){return t===e}));~t&&this._animalStash.splice(t,1)}},{key:"start",value:function(){}},{key:"stop",value:function(){}},{key:"destroy",value:function(){this._animalStash=null}},{key:"__lodStats",value:function(){this.stats=new f,this.stats.showPanel(0),document.body.appendChild(this.stats.dom)}},{key:"_render",value:function(){var e=this;this.stats&&this.stats.begin();var t=this.clock.getDelta();(se+=t)>this.renderT&&(this._animalStash&&this._animalStash.forEach((function(e){e&&e()})),se=0),this.stats&&this.stats.end(),window.requestAnimationFrame((function(){return e._render()}))}}]),e}(),ce=new WeakMap,de=function(e){if(e.hitArea)return e.hitArea;var t=e.startBounds,n=e.width,r=e.length,a=t.x,o=t.y;return[a,o,a+r,o+n]},he=function(){function e(n){M(this,e),this.Map3d=n,this.raycaster=new t.Raycaster,this.pointer=new t.Vector2,ce=new WeakMap}return D(e,[{key:"add",value:function(e,t){var n=t.data,r=t.neighbors,a=void 0===r?.01:r;ce.set(e,function(e){for(var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.01,r=new m(e.length),a=0;t=e[a++];){var o=H(de(t),4),i=o[0],s=o[1],l=o[2],c=o[3];r.add(i,s,l,c)}return r.finish(),function(t){var a=r.neighbors(t.x,t.y,n,n);return a.length?a.map((function(t){return e[t]})):[]}}(n,a))}},{key:"update",value:function(e,t){this.add(e,t)}},{key:"remove",value:function(e){}},{key:"get",value:function(e,t){if(!e||!e.length)throw new Error("map-edit-3d: search方法object3d必传！");this.pointer.set(t.offsetX/this.Map3d.$dom.offsetWidth*2-1,-t.offsetY/this.Map3d.$dom.offsetHeight*2+1),this.raycaster.setFromCamera(this.pointer,this.Map3d.camera.get());var n=this.raycaster.intersectObjects([].concat(e),!0);if(n.length){var r=n[0],a=r.point,o=r.object,i={x:a.x,y:-a.z},s=ce.get(o);return s?{data:s(i),mesh:o,point:i}:{point:i,mesh:o}}return null}},{key:"destory",value:function(){ce=null}}]),e}(),ue={SOUTH:0,NORTH:180,WEST:-90,EAST:90},pe=function(e,t){var n=e[2],r=t[2],a=n<0,o=r<0,i=Math.abs(n)+Math.abs(r),s=Math.PI-Math.abs(n)+(Math.PI-Math.abs(r));if(a&&o||!a&&!o||i<=s)return t;var l=a?2*Math.PI+n:n,c=(o?2*Math.PI+r:r)-l;return[t[0],t[1],(Math.abs(n)+Math.abs(c))*(a?-1:1)]},fe=new p.Group;p.removeAll;var me=function(e,t,n,r){var a=JSON.parse(JSON.stringify(n));new p.Tween(a).easing(p.Easing.Linear.None).onUpdate((function(n){e.position.set(a[0],a[1],a[2]),t.target.set(a[3],a[4],a[5]),t.update()})).to(r,1e3).start()},ve=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:150,r=e.mesh,a=e.target,o=[r.userData.y||0],i=new p.Tween(o);if(Number(o).toFixed(2)!==Number(a).toFixed(2)){var s=o<a,l=r.getObjectByName("robot-rs-tray"),c=(new t.Box3).setFromObject(l.clone());i.easing(p.Easing.Linear.None).onUpdate((function(e){var t=o[0];r.position.y=t})).to([a],n).start().onStop((function(){r.userData.y=a,l.rotation.y=s?-Math.PI/2:0,l.position.z=s?-Math.abs(c.max.z-c.min.z)/2:0,l.position.x=s?-Math.abs(c.max.x-c.min.x)/2:0})).onComplete((function(e){r.userData.y=a,l.rotation.y=s?-Math.PI/2:0,l.position.z=s?-Math.abs(c.max.z-c.min.z)/2:0,l.position.x=s?-Math.abs(c.max.x-c.min.x)/2:0}))}},ge=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:150,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=e.mesh,a=e.location,o=r.userData.curLocation,i=n?new p.Tween(o,fe):new p.Tween(o);if(o.join(",")!==a.join(",")){var s=o&&o.length?pe(o,a):a;i.easing(p.Easing.Linear.None).onUpdate((function(e){var t=H(o,3),n=t[0],a=t[1],i=t[2];r.rotation.y=-i,r.position.set(n,0,-a)})).to(s,t).start().onStop((function(){var e=H(a,3),t=e[0],n=e[1],o=e[2];r.rotation.y=-o,r.position.set(t,0,-n),r.userData.curLocation=[t,n,o]})).onComplete((function(e){var t=H(a,3),n=t[0],o=t[1],i=t[2];r.rotation.y=-i,r.position.set(n,0,-o),r.userData.curLocation=[n,o,i]}))}},Ae=function(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))},be=function(){function e(t){M(this,e),this.camera=null,this.Map3d=t,this._initCamera()}return D(e,[{key:"changeCameraAspt",value:function(){if(this.camera){var e=this.Map3d.$dom,t=e.offsetWidth/e.offsetHeight;this.camera.aspect=t,this.camera.updateProjectionMatrix()}}},{key:"get",value:function(){return this.camera}},{key:"update",value:function(){}},{key:"reset",value:function(){}},{key:"flyTo",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:60;if(e&&e.length){var n=this.Map3d.OrbitControls,r=[],a=Object.values(this.camera.position).concat(Object.values(n.target));if(this.__caleViewHeight(),[0,90].includes(t))r=[e[0],this.__caleViewHeight(),e[2]].concat(j(e));else if(t>0&&t<90){var o=[e[0]-20,0,e[2]+20],i=Ae({x:e[0],y:e[2]},{x:o[0],y:o[2]})/Math.tan(t*(Math.PI/180));o[1]=i,r=[].concat(o,j(e))}me(this.camera,n,a,r)}}},{key:"__caleViewHeight",value:function(){var e=this.Map3d.floor,t=this.camera.fov*Math.PI/180,n=this.camera.aspect,r=e.floorInfo,a=r.right,o=r.left,i=r.bottom,s=r.top,l=Math.abs(a-o)+10,c=Math.abs(s-i)+10,d=l/(2*Math.tan(t/2)*n),h=c/(2*Math.tan(t/2));return Math.ceil(Math.max(d,h))}},{key:"_initCamera",value:function(){var e=this.Map3d.$dom,n=e.offsetWidth/e.offsetHeight;this.camera=new t.PerspectiveCamera(45,n,.1,1e3),this.camera.position.set(0,20,40),this.camera.lookAt(0,0,0)}}]),e}(),ye=function(e,n){var r=new t.ImageLoader,a=n.next,o=e.getObjectByName("screen");e.scale.set(.001,.001,.001),e.position.y=0;var i=new t.Group;i.name="station-load-group",i.add(e),r.load("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAALQAAAAqCAMAAAAtf4EzAAACEFBMVEUAAAAAjNYBjNcCjNcAjNcBjNcAjNYCjNcBjNcBjNcBjNcBjNcAjNcBjNcDjNcAjNcBjNcCjNcBjNcBjNcEjNcBjNcBjNcDjNcBjNcBjNcDjNcDjNcBjNcAjNYAjNYBjNcCjNcBjNcBjNcCjNcBjNcDjNcCjNcBjNcCjNYCjNcDjNcAjNcBjNcCjNcAjNYAjNYCjNcAjNcCjNcCjNcBjNcBjNcAjNcAjNcCjNcEjNfoMg0AjNYBjNcBjNcBjNcCjNcCjNcDjNcCjNcAi9bpMQ0DjNf/4gAEjNcCjNcBjNcBjNcEjNcAjNYCjNf/4wEojs32pgBuuSv/4gAAjNfoMg0BjNcCjNduuSv/4QH/4wAEjNcCjNcCjNdvuCv1qQLsTAzgqS0AjNYDjNf5vABvuSsAjNdtuCvoMg0BjNf/4QGjyA3/4QBiuiz/4wFuuSv/4QDoMg3oMg1tuSttuStuuSv/4QHoMg1uuSvsrh/qRQz6wAH+vAD0oQLsTQzfpDHlqC36vAHgqS/oMg3wRgptuCv/4QL5uwGbtyHouStuuSubtCb/4QJuuSvvVg01kckAgujkIAxuuSvoMg1vuCu1gx7oMg3/4QJGoI02zTH+0QXoMg3/4QLoMg0EjNcAjNf/4QHoMg1uuSsKi9f2rAAKi9b7rABtuCsAi9vnJwxguiz2rwH/4wCgtCBxuCsYi9OitBzbpwQPAAAAnXRSTlMA/hl02hL67GAMDhbw4QbkKwlnNNqhbSAdBDo4MN3UkXlYT0Qa9qeJVUAo+OnKta6aPSSsqYx/S0f7+fPHvpRb5ru4Y1VK4t7QzcS1soM2+vjW1sGoo3ELC/vzyZw2/fb0nob14dLh4NaVBeja08a2mV9aTDAiIBkI+fn5+Pf19fX09PLv7+zq5OPhwrqwp6WigHV0c3NgXDQ0MxcW6kUWhAAACX1JREFUWMO1WYW/EkEQvlUBKUGQUAQURQThiQrPwO7u7u7u7u7uXRWx61/0ZmbvuAP0h4qfPh+wx953s99+M7MqZoT9nVOBmaNGjS+M7jjU61D+B7qkE5NUDA0prUDXPsWclXHGuAohmGfmYr/SevQdy3iFMdZLaQESM5cjYQG8GdIW7nU9Wk66wxSuzs5ER+WfEQ64YCouVNLwAsCZGu719laT7sRw+n8n3WdTGSgz+RdfC47Mx3lbTLqbOqsQ/066v4tUIUnLWHOB/0T7tDjSQLrZSIdnLZiejzTkvFxGWca4zJE+Tc7F2KHdW0y6wnhzpL0xYOEaUz8yzCYFDL8802cE8gPHxZL6YzCeDbWWdLlZTXddwJFFtmftyOBunIN+K+rwDF8PC16dSaS2cs4h5oLPcrR6I5abI52uSMWmagbs/Zgm5lx/ZCyR6ZVVaf/KUbuGM5lwl9+GyTu0c+feg/o20jRvjvQYKyMB9KsdGA7MAHlnzVDPGZBoGnAOT1g0ze3xuHOBMRalMXrEc2NdyWTJ5m7zWWoj3exG7G1FL6gj3WGktOXhs+rjZh/Nhtdz9o4eO1zbp9YpvToodeiebkuqt5N5ytop6DBHmpsjbXESwvY60igCUUu6t2AIMbproyWOt9el4VkljjmIcxQ8n5qouyQ+QJqnkA40zm/0aWGWxwS3y2azZW3Jqd4GpAUUFf3MrGbSXubTLUpT6DESn10lDD9oldZe5jUalMMB5CxkqNwJozxMpBM2ekJhG1rnxfDwQrAa0pnlNK9tUnOch3qAMeYd4oxLPcto5AkPDUh1SP6uhE6am+QR8cj84OrfQNN4Kedm0p1l2i40lz+GjWWsTFyQL6fX1qAhzjYYkEEm+ylDvspGNNI4yiVprzQTbvU1yHofJO6YPh5N909OaIpzBhKUAD15VgbH+OJbZb7npUGKRGia+hbFMyXeu0d6QsCmvkb20/vqpKuRDufUUbjcbKxLENufzCF8v310KQFHx3FUR6fBTZFeJ+Psag91B5Ow+KagCgSfrvlDUeDWK5dmdeiORjK4wAQVBqnukjSuEpJ2jJc7lhcVHUfu7Z6H2Hn9zNq1a8+tXXv66q75iF17Hm9XlBhDjU7v0lQtaKO8PlWvsh0phuD6atsojlOG6aFfiVUdEEuTe3A9udgXMQo0W1T1roPz3rx/A/j26cc7iROvdGx7oWTJBvo11SiNptvFMpr1DGtT35Kr2SgzpWj3jdXkYgm6QfjIbMWgmnq6S1xSZm2WqjJ2q3zfv0fSX9+9RazesOyLRnrZNgV2jarRlaaUMtTXuQYT+tvVIiWKBaBLM5p0vwFIB+4bneVEjlEIe3VjDhvPBLk58/SySJ/W5dGOO1mdYqQh1x94Q3hvJH0WSRN2KDYMlFhnJB3KCQF7npyIkPWqXkZvAiQlb34sLjrAleoprYOsLkeLHY7b1Dfkf4v8xtoDS1PfAJqeT80oVTyAMBNqI024pEyhnTXQRHoa2ZWgECH3pB8CAz42oDMmvaIbLqKMF9Dz3WJ60HbKdFt1r54KK2UumHyJ5eot4AadTP3z/vdS0u/r5SEjPZKhaVJoJMIrkDN1ANKSPRmlawA3OsjVsjiKXCrwEOOGVXdxHkkPhzTRcyC29pCJXEWnsd0iIbd5yMZ5dphixJHNwPg3kd6j9OOYkz1+E2k5HYzhKwGeaB8iOLqjvfc0pA9rNHzkGGNaKvAK5IlBirMXCA9JDx81qLY01Rslxuvbkv0XIdTvaSN+Jhz/opO+MVtpL4MExPCOZtKcoziAM02+wKmSrsDLqK8AHyH4pqC5MCugYgYk0iPlIQTnnRbrq2h3WqixFTCTQN4D6hPhgZubr23eqGLnlbmEk5cnS8zfdki1XnB+9dujHAZNxyhEJBIVFcHyXRR7QXCILadGHcLfHpZsLH0H94QSrR9+wTrNBb9gimTeS+1Rb197fuCooF4wcRWg6dJipQ7bD65CPH144dQawLFbz2YTDsG4s5PA7xof2FmcOXDgEPg7ZEgn8iSuDnePo/y13Vd2FaVleGeMmp7b1A28PgVMwENlDbhAK9wW0ZIthkiX6ck41YrL8c6NMfGjxF3z5ynZ0EQzBqN2SNhDU+mUbGwE3AAFS8bOrHm/PgXFDby+N6mG7Fu4Ojq1PrSEZVMpjZFGjB6FuZjxUu9fkx7xmrCwpj5OAmlKSvXoaKUIDQGD8JeAEDK0tg3SLSNjw5Iu2RnIuWTNBh4ysNpDx3kZPoo6gTSR9Q2OYa2smsvQPyXdNUBbTYhCPetJJTwsFFYMhmWclOKABf1NbQQ4CXeHINPLvK4+VqyzocR24T0qASqYZBqPuEl63JNunjRhkOrxVLGP99YWSG7Z5Y0ijwgiHcGmGLKuA6wZ1j4gu7cKx4sKDsNqRmWu6oOk6U1HKMDkVo/2bJ40oYjfA7m6FxMbSSdIfVCZD5Dr13eTQKOyLgopEv4hnHO0W7qvZSTpmY2q8ujshpXgFT6jOyYX0lhHXAFB945l/pC0E+4je5AV7WmKUBdvcBxODozyikQQG02V+DRfB6iV/Sm3tvPWa80Gdf2CR4MUgWEBa4ULIGeLUEbEJ6cmYEIS7qCOrhjcNGmC3yOtDKKYnTpjZXx0YaSbzBSI5yy6rWDNDrDGhsRXTvfANSQth1a/LoJhKmEDs9rjC1ACWIEF9TTO9bO8IGVcwcaFf0d6b4POL8tlMqhoW5/yOK602yA4bxQ+x0jqR8JAemSo2pJNFTiHBrxchUhVaw9DY9uL7sPZwAbudXgLcR6xr1G/6ibWKmkD6DN3H/NhpUxl2hW4SOOJMyFNR/2AslwscMT13XXSCEm6S1G7JmCvJ/aIQr3wqNIA/gXq/AjSCWZqRM5f44JRCDL1O4I82VZ0mE0HaFEQtMwo3NA5G5sA0VGTXF7IGzc6Lrq/5fyILXtfKg1hWU+VIlk2xA+z7IB1dVLrWcCxan8wbmjdJTOQKBCWhyND/OYDSHWoo+5SULDgDVPdGyhk4r7nyi8RGe2hMlVUT1naEo1axTFtVk3Q3Dqj0RGkwzeNRE8/43sbYtghyrn5WMw5XuWMNWG78sfwBmdmDZsnFu9j+cWypHu1xbIuT2xmxx6/uCQ8JoBHBSI5rWaaDm5WBtq9DGd/WIYI/nf/T2cP9QkWAwPbhqxb3znSofvvrnSGvCGL/XfH086ek/r37+O1dKlZqJ49IpFIjx59jVFQP4A/aX8zRxk/AUBGzSCj3S3ZAAAAAElFTkSuQmCC",(function(e){var n=new t.Texture(e);n.needsUpdate=!0,o.material=new t.MeshBasicMaterial({map:n}),a&&a(i)}))},xe=["obj"],Ce=["glft","glb"],ke=["fbx"],_e=function(){function e(t){M(this,e),this.waitExec=[],this.cache=[],this.status={},this.modelMap=t.config.modelMap,this.baseUrl=t.baseUrl,this.Map3d=t,this.__setStatus()}return D(e,[{key:"get",value:function(e){return this.cache[e]}},{key:"getLazy",value:function(e,t,n){var r=this.cache[e];if(r)return t(r);this.waitExec[e]=(this.waitExec[e]||[]).concat([[t,n]]),this.load(e)}},{key:"load",value:function(e){for(var t,n=this,r=[].concat(e),a=function(e,t){var r=n.status[t];if(!r||["loading","success"].includes(r))return"continue";n.status[t]="loading";var a=n.modelMap[t].path;a||(a=n.modelMap[t]),Promise.all([].concat(a).map((function(e){return n.__fetchObj(e)}))).then((function(e){return n.__loadSuccess.apply(n,[t,a,e])}),(function(){return n.__loadFail.call(n,t)}))},o=0;t=r[o++];)a(0,t)}},{key:"destory",value:function(){this.waitExec=[],this.cache=[],this.status={}}},{key:"__loadSuccess",value:function(e,t,n){var r=this;this.status[e]="success";var a=Array.isArray(t)?n:n[0];this.__extendModel(e,a,(function(t){r.cache[e]=t;var n=r.waitExec[e];n&&n.length&&(n&&n.forEach((function(e){var n=H(e,1)[0];return n&&n(t)})),r.waitExec[e]=[])}))}},{key:"__extendModel",value:function(e,n,r){var a=this.modelMap[e],o=a.insetShape,i=a.scale,s=a.setAttributes;if("station2"===o)ye(n,{next:r});else if("station6"===o)!function(e,n){ye(e,{screenImg:n.screenImg,next:function(e){var r=new t.ImageLoader,a=n.next;r.load("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAC0AgMAAADQnFUrAAAADFBMVEX//wAA/wD/AACAgIDsSniYAAAAHklEQVQoz2NYtWrBsMD//38gG4eGBgwLTEkYDBMAAOY06mE5dYlzAAAAAElFTkSuQmCC",(function(n){var r=new t.Texture(n);r.needsUpdate=!0;var o=new t.Mesh(new t.CylinderGeometry(.2,.2,3,13,1,!0),new t.MeshBasicMaterial({map:r,side:t.DoubleSide}));o.position.y=7.5,o.position.x=-1,o.position.z=1,e.add(o),a&&a(e)}))}})}(n,{next:r});else if("P40"===o)!function(e,n){var r=H(e,2),a=r[0].scene,o=r[1].scene,i=new t.Group;i.name="robot-p40-group";var s=(new t.Box3).setFromObject(a.clone()),l=s.max.y-s.min.y;a.name="robot-p40",i.add(a),o.scale.y=.7,o.name="robot-p40-plant",i.add(o);var c=new t.Mesh(new t.BoxBufferGeometry(s.max.x,.2,s.max.z),new t.MeshStandardMaterial({color:30704,side:t.FrontSide,roughness:1,metalness:0}));c.name="robot-p40-box",c.visible=!1,c.position.y=l+l/2,i.add(c),i.rotation.y=Math.PI/180*-90,n.next&&n.next(i)}(n,{next:r});else if("RS"===o)!function(e,n){var r=H(e,4),a=r[0].scene,o=r[1].scene,i=r[2].scene,s=r[3].scene,l=new t.Group;l.name="robot-rs-group",a.name="robot-rs-body",l.add(a),s.name="robot-rs-lattice",s.visible=!1,l.add(s);var c=new t.Group;c.name="robot-rs-liftGroup",o.name="robot-rs-tray",c.add(o),i.name="robot-rs-lift",c.add(i);var d=new t.Mesh(new t.BoxBufferGeometry(.3,.3,.3),new t.MeshStandardMaterial({color:30704,side:t.DoubleSide,roughness:1,metalness:0,transparent:!0,opacity:1}));d.name="robot-rs-boxMesh",d.visible=!1,c.add(d),l.add(c),l.rotation.y=Math.PI/180*-90,n.next&&n.next(l)}(n,{next:r});else if("P800"===o)!function(e,n){var r=H(e,2),a=r[0].scene,o=r[1].scene,i=new t.Group;i.name="robot-p800-group",a.name="robot-p800",i.add(a),o.name="robot-p40-tray",i.add(o),n.next&&n.next(i)}(n,{next:r});else{var l=n.scene||n;i&&l.scale.set(i,i,i),r&&r(s?s(l):l)}}},{key:"__loadFail",value:function(e){this.status[e]="fail",this.waitExec[e].forEach((function(t){var n=H(t,2);n[0];var r=n[1];return r&&r(e)})),this.waitExec[e]=[]}},{key:"__fetchObj",value:function(e){var n=this;return new Promise((function(r,a){var o=n.__getLoader(e);o.load(e,(function(e){if(o instanceof A){var a=new b(n.Map3d.renderer);e.scene.traverse((function(e){var n;e.isMesh&&(a.generateMipmaps(e.material),e.frustumCulled=!1,e.castShadow=!1,e.material.emissive=e.material.color,e.material.emissiveMap=e.material.map,e.material.emissiveIntensity=.8,"MeshStandardMaterial"==e.material.type?(e.material.metalness&&(e.material.metalness*=.1),e.material.glossiness&&(e.material.glossiness*=.25),n=new t.Color(12,12,12)):"MeshPhongMaterial"==c.material.type&&(e.material.shininess=.1,n=new t.Color(20,20,20)),e.material.specular&&e.material.specular.isColor&&(e.material.specular=n))})),a.dispose()}r(e)}),null,a)}))}},{key:"__getLoader",value:function(e){if(e){var t=null,n=e.split(".").pop().toLowerCase();return xe.includes(n)?t=new g:ke.includes(n)?t=new v:Ce.includes(n)&&(t=new A),t}}},{key:"__setStatus",value:function(){this.status=Object.keys(this.modelMap).reduce((function(e,t){return Object.assign(e,B({},t,"ready"))}),{})}}]),e}(),we=function(e){L(r,q);var n=P(r);function r(e){var a;return M(this,r),(a=n.call(this,e)).pointerLockControl=null,a._moveForward=!1,a._moveLeft=!1,a._moveBackward=!1,a._moveRight=!1,a.origin=null,a._effect=["selectPlugin","hoverPlugin","selectMapCellPlugin"],a._isAuto=!1,a._velocity=new t.Vector3,a._direction=new t.Vector3,a.PluginName="pointerLockPlugin",a}return D(r,[{key:"effectPlugins",set:function(e){this._effect=this._effect.concat(e)}},{key:"autoFollow",set:function(e){void 0===e&&(e=!1),this._isAuto=e}},{key:"activated",value:function(){var e=this,t=this;if(this._handlePlugin(),this._control(!1),this._resetCamera(),!this.pointerLockControl){var n=this.Map3d,r=n.camera,a=n.$dom,o=n.scene;this.pointerLockControl=new y(r.get(),a);var i=this.pointerLockControl.getObject();o.add(i)}this.pointerLockControl.lock(),this.EventInstance.add("PointerLock",{keydownHandle:function(e){switch(e.code){case"ArrowUp":case"KeyW":t._moveForward=!0;break;case"ArrowLeft":case"KeyA":t._moveLeft=!0;break;case"ArrowDown":case"KeyS":t._moveBackward=!0;break;case"ArrowRight":case"KeyD":t._moveRight=!0}},keyupHandle:function(e){switch(e.code){case"ArrowUp":case"KeyW":t._moveForward=!1;break;case"ArrowLeft":case"KeyA":t._moveLeft=!1;break;case"ArrowDown":case"KeyS":t._moveBackward=!1;break;case"ArrowRight":case"KeyD":t._moveRight=!1}}}),this.Map3d.ticker.add(this._handleMove.bind(this)),this.pointerLockControl.addEventListener("unlock",(function(){return e._cleanPointLock()}))}},{key:"deactivated",value:function(){this._cleanPointLock()}},{key:"destroyed",value:function(){this._cleanPointLock()}},{key:"_handlePlugin",value:function(e){var t=this._effect;e?this.Map3d.enablePlugin(t):this.Map3d.disabledPlugin(t)}},{key:"_control",value:function(e){this.Map3d.OrbitControls&&(this.Map3d.OrbitControls.enabled=e)}},{key:"_resetCamera",value:function(){var e=this.Map3d.scene.getObjectByName("floorGeo").userData.floorInfo||{left:0,bottom:0},t=e.left,n=e.bottom;e.right,e.top,this.Map3d.camera.flyTo([t,2,n])}},{key:"_cleanPointLock",value:function(){this.pointerLockControl&&(this.pointerLockControl.dispose(),this.pointerLockControl=null,this.Map3d.ticker.remove(this._handleMove.bind(this))),this.EventInstance.off("PointerLock"),this._control(!0),this._handlePlugin(!0),this.Map3d.resetCamera()}},{key:"_handleMove",value:function(){if(this.pointerLockControl.isLocked){var e=this.Map3d.ticker.deltaTime;this._velocity.x-=10*this._velocity.x*e,this._velocity.z-=10*this._velocity.z*e,this._direction.z=Number(this._moveForward)-Number(this._moveBackward),this._direction.x=Number(this._moveRight)-Number(this._moveLeft),(this._moveForward||this._moveBackward)&&(this._velocity.z-=200*this._direction.z*e),(this._moveLeft||this._moveRight)&&(this._velocity.x-=200*this._direction.x*e),this.pointerLockControl.moveRight(-this._velocity.x*e),this.pointerLockControl.moveForward(-this._velocity.z*e)}}}]),r}(),Se=function(e,t){var n,r=e,a=!0;return function(){var e=arguments,o=this;return a?(r.apply(o,e),a=!1):!n&&void(n=setTimeout((function(){clearTimeout(n),n=null,r.apply(o,e)}),t||500))}},Me=function e(t,n){return t.name.includes(n)?t:"Scene"===t.type?null:e(t.parent,n)},Ee=function(){return new Promise((function(e){setTimeout(e,34)}))},De=function(e){var n=null;Object.defineProperty(e,"color",{get:function(){return n},set:function(r){n!==r&&(e.traverse((function(e){if("Mesh"==e.type||"SkinnedMesh"==e.type){var n=[];Array.isArray(e.material)?n=e.material:n.push(e.material);var a=n[0];r?(e.userData.materials=a,e.material=new t.MeshStandardMaterial,e.material.color.setHex(r)):(e.material.dispose(),e.material=e.userData.materials,e.userData.materials.dispose(),e.userData.materials=null)}})),n=r)}}),Object.defineProperty(e,"status",{get:function(){return e.userData.__status},set:function(n){e.userData.__status=n;var r=e.getObjectByName("status");if("normal"===String(n).trim())return r&&(r.visible=!1);if(r)return r.visible=!0;var a=(new t.Box3).setFromObject(e.clone()),o=Math.abs(a.max.x-a.min.x),i=Math.abs(a.max.z-a.min.z),s=new t.RingGeometry(Math.min(o,i),1.4*Math.min(o,i),64),l=new t.Mesh(s,new t.MeshStandardMaterial({color:30704,side:t.DoubleSide,roughness:1,metalness:0}));l.name="status",l.rotation.x=-Math.PI/2,l.visible=!0,e.add(l)}})},Be=function(){function n(e){M(this,n),this.monitor=e.monitor,this.dispatch=e.dispatch||{renderRack:function(){},selectLattices:function(){},selectBoxs:function(){},updateRack:function(){}},this.rackGroup=new t.Group,this.rackGroup.name="goup-rack",this.config={lockColor:V.THEME.LOCKED,boxColor:V.THEME.RACK_BOX,layerColor:V.THEME.RACK_LAYER,latticeColor:V.THEME.RACK_LATTICE,hoverColor:V.THEME.HOVER_3D,selectColor:V.THEME.SELECTED,taskRackColor:V.THEME.RACK_TASK_BOX,stationRackColor:V.THEME.STATION_RACK_TASK_BOX,boxH:.3,latticeH:.04,layerH:.02,h:.5},this.racks=null,this.latticesSeq={},this.boxSeq={},this.latticesSearch=null,this.isHeatMode=!1,this.isTaskMode=!1}var r,a;return D(n,[{key:"create",value:(a=S(e.mark((function t(n){return e.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.racks=n,e.next=3,this.__handleRacks(n);case 3:this.rackGroup.rotateX(-Math.PI/2),this.dispatch.renderRack&&this.dispatch.renderRack(this.rackGroup);case 5:case"end":return e.stop()}}),t,this)}))),function(e){return a.apply(this,arguments)})},{key:"update",value:function(e){this.__updateLattice(e)}},{key:"reRenderBox",value:function(){for(var e,t=Object.values(this.racks).reduce((function(e,t){return e.concat(t.lattices||[])}),[]),n=this.rackGroup.children.filter((function(e){return e.name.includes("rack-box")})).sort((function(e,t){return e.userData.layerId-t.userData.layerId})),r=0;e=t[r++];){var a=e,o=a.layer,i=a.latticeCode,s=a.mergeBoxSeq;this.__hasBox(o,i)&&this.__changePanelColor(n[o],s,this.__getBoxColor(e),this.__getBoxAlpha(e))}}},{key:"destory",value:function(){this.racks=null,this.latticesSeq={},this.boxSeq={},this.latticesSearch=null}},{key:"hoverLattices",value:function(e,t){var n=this.searchByLatticeCode(e),r=this.getLayersMesh(n.layer);this.__changePanelColor(r,n.mergeLatticesSeq,t)}},{key:"selectLattices",value:function(e,t){var n=this.searchByLatticeCode(e),r=this.getLayersMesh(n.layer);this.__changePanelColor(r,n.mergeLatticesSeq,t),this.dispatch.selectLattices&&this.dispatch.selectLattices(n)}},{key:"cancelLatticesColor",value:function(e){var t=this.searchByLatticeCode(e),n=this.getLayersMesh(t.layer),r=this.__isLockLattices(t)?this.config.lockColor:this.config.latticeColor;this.__changePanelColor(n,t.mergeLatticesSeq,r)}},{key:"hoverBoxs",value:function(e,t){var n=this.searchByLatticeCode(e);if(this.__hasBox(n.layer,e)){var r=this.getBoxMesh(n.layer);this.__changePanelColor(r,n.mergeBoxSeq,t)}}},{key:"selectBoxs",value:function(e,t){var n=this.searchByLatticeCode(e);if(this.__hasBox(n.layer,e)){var r=this.getBoxMesh(n.layer);this.__changePanelColor(r,n.mergeBoxSeq,t),this.dispatch.selectBoxs&&this.dispatch.selectBoxs(n.relateBox)}}},{key:"cancelBoxsColor",value:function(e){var t=this.searchByLatticeCode(e);if(this.__hasBox(t.layer,e)){var n=this.getBoxMesh(t.layer),r=this.__getBoxColor(t);this.__changePanelColor(n,t.mergeBoxSeq,r)}}},{key:"showLayerId",value:function(e){var t=this.getLayersMesh(e);t&&(t.visible=!0)}},{key:"showBoxLayerId",value:function(e){var t=this.getBoxMesh(e);t&&(t.visible=!0)}},{key:"hideLayerId",value:function(e){var t=this.getLayersMesh(e);t&&(t.visible=!1)}},{key:"hideBoxLayerId",value:function(e){var t=this.getBoxMesh(e);t&&(t.visible=!1)}},{key:"getLayersMesh",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1;return~e?this.rackGroup.getObjectByName("rack-layer-".concat(e)):this.rackGroup.children.filter((function(e){return e.name.includes("rack-layer-")}))}},{key:"getBoxMesh",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1;return~e?this.rackGroup.getObjectByName("rack-box-".concat(e)):this.rackGroup.children.filter((function(e){return e.name.includes("rack-box-")}))}},{key:"getRackLayers",value:function(){return this.rackGroup.children.filter((function(e){return e.name.includes("rack-layer-")})).map((function(e,t){return t}))}},{key:"searchByLatticeCode",value:function(e){var t=this.racks[this.latticesSeq[e]];return!!t&&t.lattices.find((function(t){return String(t.latticeCode)===String(e)}))}},{key:"searchByBoxCode",value:function(e){var t=this.boxSeq[e];if(!t)return!1;var n=H(t,2),r=n[0],a=n[1],o=this.racks[r];if(!o)return!1;var i=o.lattices.find((function(e){return String(e.latticeCode)===String(a)}));return i&&i.relateBox||!1}},{key:"__hasBox",value:function(e,t){var n;return null===(n=this.latticesSearch[e][t])||void 0===n?void 0:n.boxVisible}},{key:"__isLockBox",value:function(e){return e.relateBox&&!!e.relateBox.lockState}},{key:"__isLockLattices",value:function(e){return"NORMAL"!==e.latticeFlag}},{key:"__boxHeatColor",value:function(e){if(!e.relateBox)return!1;var t=Math.floor((e.relateBox.boxHeat||0)/10);return Object.values(V.THEME.SHELF_HOT_CONF)[t]}},{key:"__getBoxAlpha",value:function(e){var t=e.relateBox;return!t&&e.relateBoxCode?.5:t?this.isTaskMode?"jobIds"in t&&t.jobIds?1:0:1:0}},{key:"__getBoxColor",value:function(e){var t=e.relateBox;if(!t)return!1;var n="jobIds"in t&&t.jobIds;if(this.isTaskMode&&n){var r=t.goFetchJobs&&!e.layer?"stationRackColor":"taskRackColor";return this.config[r]}return this.isHeatMode?this.__boxHeatColor(e):this.config[this.__isLockBox(e)?"lockColor":"boxColor"]}},{key:"__updateLattice",value:function(e){var t=this,n=Object.values(e);if(n&&n.length){for(var r=[],a=0,o=n.length;a<o;a++){var i,s=n[a],l=null===(i=this.racks[s.rackCode])||void 0===i?void 0:i.lattices,c=s.lattices;if(l&&l.length)for(var d,h=function(e,n){var a,o=c.find((function(e){return e.latticeCode===n.latticeCode}));if(n.relateBox&&(delete t.boxSeq[n.relateBox.boxCode],r[n.layer]=Object.assign(r[n.layer]||{},B({},n.latticeCode,[n.mergeBoxSeq,!1,n]))),Object.assign(n,o),!(o.relateBoxCode||!!o.relateBox&&!!o.relateBox.boxCode))return"continue";r[o.layer]=Object.assign(r[o.layer]||{},B({},n.latticeCode,[n.mergeBoxSeq,!0,o])),t.boxSeq[(null===(a=o.relateBox)||void 0===a?void 0:a.boxCode)||o.relateBoxCode]=[s.rackCode,o.latticeCode]},u=0;d=l[u++];)h(0,d)}for(var p,f=function(e,n){if(!e.name.includes("rack-box"))return"continue";var a=r[e.userData.layerId];if(!a)return"continue";Object.values(a).map((function(n){return t.__changePanelColor(e,n[0],t.__getBoxColor(n[2]),n[1]?t.__getBoxAlpha(n[2]):0)}))},m=0;p=this.rackGroup.children[m++];)f(p);for(var v,g=function(e,n){if(!e)return"continue";Object.keys(e).map((function(r){t.latticesSearch[n-1][r]?t.latticesSearch[n-1][r].boxVisible=e[r][1]:t.latticesSearch[n-1][r]={boxVisible:e[r][1]}}))},A=0;v=r[A++];)g(v,A);this.dispatch.updateRack&&this.dispatch.updateRack()}}},{key:"__handleRacks",value:(r=S(e.mark((function n(r){var a,o,i,s,l,c,d,h,u,p,f,m,v,g,A,b,y,x,C,k;return e.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:a=Object.values(r),o=new t.Matrix4,i=(new t.Color).set(this.config.boxColor),s=(new t.Color).set(this.config.layerColor),l=(new t.Color).set(this.config.latticeColor),c=(new t.Color).set(this.config.lockColor),d=this.config.h,h=[],u=[],p=[],f=0;case 11:if(!(m=a[f++])){e.next=33;break}v=this.__formatRack(m),g=this.__formatLattices(m.lattices),A=0,b=g.length;case 15:if(!(A<b)){e.next=31;break}if(y=g[A]){e.next=19;break}return e.abrupt("continue",28);case 19:x=d*(A+1),h[C=A]||(h[C]=[]),u[C]||(u[C]=[]),p[C]||(p[C]={}),k=[v,y,o,x,c],this.__renderLayer.apply(this,k.concat([s,h[C]])),this.__renderLattices.apply(this,k.concat([l,h[C],p[C]])),this.__renderBox.apply(this,k.concat([i,u[C]]));case 28:A++,e.next=15;break;case 31:e.next=11;break;case 33:return this.latticesSearch=p,e.next=36,Ee();case 36:return this.__renderGeo(h,u,p),e.abrupt("return",Promise.resolve());case 38:case"end":return e.stop()}}),n,this)}))),function(e){return r.apply(this,arguments)})},{key:"__formatRack",value:function(e){var t=e.location,n=e.width,r=e.length,a=e.degAngle,o=e.rackCode,i=[90,270].includes(a);if(i){var s=r;r=n,n=s}var l=.8*r,c=.8*n,d=.1*l,h=.1*c,u=t.x,p=t.y;return{rackCode:o,isVertical:i,degAngle:a,rackType:e.rackType||1,location:{location:{x:u,y:p},l:l,w:c},left:{location:{x:u-l/4-d/4,y:p},l:(l-d)/2,w:c},right:{location:{x:u+l/4+d/4,y:p},l:(l-d)/2,w:c},top:{location:{x:u,y:p+c/4+h/4},l:l,w:(c-h)/2},bottom:{location:{x:u,y:p-c/4-h/4},l:l,w:(c-h)/2}}}},{key:"__getIsOuter",value:function(e,t){var n=e.degAngle,r=t.isOuter;return[90,180].includes(n)?r?0:1:r}},{key:"__formatLattices",value:function(e){return e&&e.length?[].concat(e).reduce((function(e,t){var n=t.isOuter,r=t.layer;return e[r]?(e[r]&&r&&(e[r]=n?e[r].concat(t):[t].concat(e[r])),e):(e[r]=[t],e)}),[]):[]}},{key:"__renderLayer",value:function(e,t,n,r,a,o,i){if(t.length>1&&t[0].layer){var s=e.location,l=s.location,c=s.l,d=s.w;n.makeTranslation(l.x,l.y,r),i.push(this.__createPanel(c,d,this.config.layerH,n,o,.8))}else{var h=1===e.rackType?e.location:(e.isVertical?[e.bottom,e.top]:[e.left,e.right])[this.__getIsOuter(e,t[0])],u=h.location,p=h.l,f=h.w;n.makeTranslation(u.x,u.y,r),i.push(this.__createPanel(p,f,this.config.layerH,n,o,.8))}}},{key:"__renderLattices",value:function(e,t,n,r,a,o,i,s){for(var l,c=1===e.rackType?[e.location]:e.isVertical?[e.bottom,e.top]:[e.left,e.right],d=r+this.config.layerH+this.config.latticeH/2,h=0;l=t[h++];){var u;if(!(h>1)||l.layer){l.mergeLatticesSeq=i.length;var p=c[this.__getIsOuter(e,l)]||c[0],f=p.location,m=p.l,v=p.w,g=l.latticeCode;n.makeTranslation(f.x,f.y,d);var A=this.__isLockLattices(l)?a:o;i.push(this.__createPanel(m,v,this.config.latticeH,n,A,1)),s[g]={startBounds:{x:f.x-m/2,y:f.y-v/2},width:v,length:m,latticeCode:g,boxVisible:!(null===(u=l.relateBox)||void 0===u||!u.boxCode)},this.latticesSeq[g]=e.rackCode}}}},{key:"__renderBox",value:function(e,t,n,r,a,o,i){for(var s,l=1===e.rackType?[e.location]:e.isVertical?[e.bottom,e.top]:[e.left,e.right],c=r+this.config.layerH+this.config.latticeH+this.config.boxH/2,d=0;s=t[d++];){var h;if(s){var u=!s.relateBox,p=null===(h=s.relateBox)||void 0===h?void 0:h.boxCode,f=l[this.__getIsOuter(e,s)]||l[0],m=f.location,v=f.l,g=f.w;n.makeTranslation(m.x,m.y,c),s.mergeBoxSeq=i.length,s.isCacheBox=0===s.layer;var A=this.__isLockBox(s)?a:o;i.push(this.__createPanel(v,g,this.config.boxH,n,A,u?0:1)),p&&(this.boxSeq[p]=[e.rackCode,t.latticeCode])}}}},{key:"__renderGeo",value:function(e,n,r){for(var a=this,o=new t.MeshStandardMaterial({vertexColors:!0,roughness:1,metalness:.2,side:t.FrontSide,transparent:!0,alphaTest:.8}),s=function(n,s){var l=e[n];if(!l||!l.length)return"continue";var c=new t.Mesh(i(l),o);c.name="rack-layer-".concat(n),c.userData.layerId=n,a.rackGroup.add(c),setTimeout((function(){a.monitor.Map3d.search.add(c,{data:Object.values(r[n])})}),200)},l=0,c=e.length;l<c;l++)s(l);for(var d=function(e,s){var l=n[e];if(!l||!l.length)return"continue";var c=new t.Mesh(i(l),o);c.name="rack-box-".concat(e),c.userData.layerId=e,a.rackGroup.add(c),setTimeout((function(){a.monitor.Map3d.search.add(c,{data:Object.values(r[e])})}),300)},h=0,u=n.length;h<u;h++)d(h)}},{key:"__changePanelColor",value:function(e,n,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:-1,o=(new t.Color).set(r),i=e.geometry.getAttribute("color");new Array(24).fill(0).map((function(e,t){var r=(24*n+t)*i.itemSize;o.toArray(i.array,r),~a&&(i.array[r+3]=a)})),i.needsUpdate=!0}},{key:"__createPanel",value:function(e,n,r,a,o){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,s=new t.BoxBufferGeometry(e,n,r);s.applyMatrix4(a);var l=s.getAttribute("position").count,c=new t.BufferAttribute(new Float32Array(4*l),4);return new Array(l).fill(0).map((function(e,t){o.toArray(c.array,t*c.itemSize),c.array[t*c.itemSize+3]=i})),s.setAttribute("color",c),s}}]),n}(),Le=function(){function e(t){M(this,e),this.Map3d=t}return D(e,[{key:"getRobotMesh",value:function(e){return this.Map3d.modelInstances[e.uuid].model.children[0].children.filter((function(e){return!e.name.includes("robot-p40-box")})).map((function(t){return t.userData.uuid=e.uuid,t}))}},{key:"afterRender",value:function(e){var n=this.Map3d.modelInstances[e.uuid].model;this.__visibleBox(e,n);var r=n.getObjectByName("robot-p40"),a=(new t.Box3).setFromObject(r.clone()),o=new t.Box3Helper(a,V.THEME.HOVER_3D);o.name="hover",o.visible=!1,r.parent.add(o);var i=new t.Box3Helper(a,V.THEME.SELECTED);i.name="select",i.visible=!1,r.parent.add(i),n.rotation.y=-e.radAngle,De(r)}},{key:"update",value:function(e,t){if(this.Map3d.modelInstances[e.uuid]){var n=this.Map3d.modelInstances[e.uuid].model,r=t.taskPhase,a=e.location,o=e.radAngle;n.userData.curLocation=[a.x,a.y,o],this.__renderRobotStatus(t,n),this.__visibleBox(t,n),this.__useRobotAnimal(t,n),"BOX_ARRIVED"===r&&this.__useRobotLift(t,n)}}},{key:"__visibleBox",value:function(e,t){var n,r=((null===(n=e.onloadRack)||void 0===n?void 0:n.lattices)||[])[0];t.getObjectByName("robot-p40-box").visible=r&&"OCCUPIED"===r.latticeStatus}},{key:"__renderRobotStatus",value:function(e,t){t.getObjectByName("robot-p40").status=e.taskId&&!["GO_SOMEWHERE_TO_STAY"].includes(e.taskType)?"work":"normal"}},{key:"__useRobotAnimal",value:function(e,t){var n=e.location,r=e.radAngle;ge({mesh:t,location:[n.x,n.y,r]})}},{key:"__useRobotLift",value:function(e,t){var n=e.obstacleCount,r=t.getObjectByName("robot-p40-plant"),a=t.getObjectByName("robot-p40-box");700===n&&(r.scale.y=1,a.position.y=.495),282===n&&(r.scale.y=.7,a.position.y=.23+.115)}}]),e}(),Te=function(){function e(t){M(this,e),this.Map3d=t}return D(e,[{key:"getRobotMesh",value:function(e){return this.Map3d.modelInstances[e.uuid].model.children[0].children.map((function(t){return t.userData.uuid=e.uuid,t}))}},{key:"afterRender",value:function(e){var n=this.Map3d.modelInstances[e.uuid].model,r=n.getObjectByName("robot-p800"),a=(new t.Box3).setFromObject(r.clone()),o=new t.Box3Helper(a,V.THEME.HOVER_3D);o.name="hover",o.visible=!1,r.parent.add(o);var i=new t.Box3Helper(a,V.THEME.SELECTED);i.name="select",i.visible=!1,r.parent.add(i),n.rotation.y=-e.radAngle,De(r)}},{key:"update",value:function(e,t){if(this.Map3d.modelInstances[e.uuid]){var n=this.Map3d.modelInstances[e.uuid].model,r=e.location,a=e.radAngle;n.userData.curLocation=[r.x,r.y,a],this.__renderRobotStatus(t,n),this.__useRobotAnimal(t,n)}}},{key:"__renderRobotStatus",value:function(e,t){t.getObjectByName("robot-p800").status=e.taskId&&!["GO_SOMEWHERE_TO_STAY"].includes(e.taskType)?"work":"normal"}},{key:"__useRobotAnimal",value:function(e,t){var n=e.location,r=e.radAngle;ge({mesh:t,location:[n.x,n.y,r]})}}]),e}(),Re=function(){function e(t){M(this,e),this.Map3d=t,this.config={rackLayerHeight:.5,rsbaseHeight:.5}}return D(e,[{key:"getRobotMesh",value:function(e){return this.Map3d.modelInstances[e.uuid].model.children[0].children.filter((function(e){return!e.name.includes("robot-rs-boxMesh")})).map((function(t){return t.userData.uuid=e.uuid,t}))}},{key:"afterRender",value:function(e){var n=this.Map3d.modelInstances[e.uuid].model,r=n.getObjectByName("robot-rs-body"),a=(new t.Box3).setFromObject(r.clone()),o=new t.Box3Helper(a,V.THEME.HOVER_3D);o.name="hover",o.visible=!1,o.updateMatrixWorld(!0),r.parent.add(o);var i=new t.Box3Helper(a,V.THEME.SELECTED);i.name="select",i.visible=!1,i.updateMatrixWorld(!0),r.parent.add(i),n.rotation.y=-e.radAngle,De(r)}},{key:"update",value:function(e,t){if(this.Map3d.modelInstances[e.uuid]){var n=this.Map3d.modelInstances[e.uuid].model;this.__renderRobotStatus(t,n),this.__renderRobotFetchReturnAction(t,n),n.userData.isRenderLattice?this.__renderBox(t,n):this.__renderLattice(t,n);var r=e.location,a=e.radAngle;n.userData.curLocation=[r.x,r.y,a],this.__useRobotAnimal(t,n)}}},{key:"__useRobotAnimal",value:function(e,t){var n=e.location,r=e.radAngle;ge({mesh:t,location:[n.x,n.y,r]})}},{key:"__renderLattice",value:function(e,n){var r=this,a=e.onloadRack,o=n.getObjectByName("robot-rs-lattice"),i=n.getObjectByName("robot-rs-tray"),s=n.getObjectByName("robot-rs-boxMesh"),l=n.getObjectByName("robot-rs-liftGroup"),c=(new t.Box3).setFromObject(o.clone()),d=(new t.Box3).setFromObject(i.clone()),h=this.config.rackLayerHeight;a.lattices.forEach((function(e){var t=!e.layer,a=r.config.rsbaseHeight+h*(e.layer-1);if(!t){var i=o.clone();i.position.y=a,i.visible=!0,i.name="robot-rs-lattice-".concat(e.layer),n.children[0].add(i)}var u=s.clone();u.name="robot-rs-boxMesh-".concat(e.layer),u.visible=!!e.relateBox,t?(u.position.y=d.max.y,u.position.z=d.min.z/2,l.add(u)):(u.position.y=a+c.max.y+.15,u.position.z=c.max.z/2+.15,n.children[0].add(u))})),n.userData.isRenderLattice=!0}},{key:"__renderRobotStatus",value:function(e,t){t.getObjectByName("robot-rs-body").status=e.taskId&&!["GO_SOMEWHERE_TO_STAY"].includes(e.taskType)?"work":"normal"}},{key:"__renderRobotFetchReturnAction",value:function(e,n){var r=n.getObjectByName("robot-rs-liftGroup"),a=n.getObjectByName("robot-rs-body"),o=(new t.Box3).setFromObject(a.clone()).max.y/2,i="GO_RETURN_BOX",s="GO_FETCH_BOX",l=r.userData.robotPathMode,c=e.robotPathMode,d={mesh:r,target:o};c===i?l===i||ve(d):c===s?l===s||ve(d):[i,s].includes(l)&&![i,s].includes(c)&&ve(Object.assign(d,{target:0})),r.userData.robotPathMode=c}},{key:"__renderBox",value:function(e,t){e.onloadRack.lattices.forEach((function(e){t.getObjectByName("robot-rs-boxMesh-".concat(e.layer)).visible=!!e.relateBox}))}}]),e}();function Ie(e,t,n){var r=void 0===t?null:t,a=function(e,t){var n=atob(e);if(t){for(var r=new Uint8Array(n.length),a=0,o=n.length;a<o;++a)r[a]=n.charCodeAt(a);return String.fromCharCode.apply(null,new Uint16Array(r.buffer))}return n}(e,void 0!==n&&n),o=a.indexOf("\n",10)+1,i=a.substring(o)+(r?"//# sourceMappingURL="+r:""),s=new Blob([i],{type:"application/javascript"});return URL.createObjectURL(s)}var Oe,Pe,He,je,Ne=new(Oe="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",Pe=null,He=!1,function(e){return je=je||Ie(Oe,Pe,He),new Worker(je,e)}),Ge=function(){function e(t){M(this,e),this.Map3d=t,this.canvasEle=null,this.canvasTexture=null,this.lw=1}return D(e,[{key:"create",value:function(){var e=Object.assign({},this.Map3d.floor.floorInfo);e.w=Math.abs(e.right-e.left),e.h=Math.abs(e.top-e.bottom),this.__createCanvas(e),this.__createPanel(e),Ne.addEventListener("message",this.__loadImage.bind(this))}},{key:"clearPath",value:function(){Ne.postMessage({action:"clearRefresh"})}},{key:"drawPath",value:function(e){var t=this;Ne.postMessage({action:"clear"});var n=this.Map3d.floor.floorInfo,r=n.left,a=n.bottom,o=e.filter((function(e){return e.paths&&e.paths.length})).map((function(e){return[e.color].concat(j(e.paths.map((function(e){return[e.x-r+t.lw,e.y-a+t.lw]}))))}));Ne.postMessage({action:"draw",value:o})}},{key:"destory",value:function(){this.canvasEle&&(this.canvasEle=null),Ne&&Ne.postMessage({action:"destory"}),Ne.removeEventListener("message",this.__loadImage.bind(this))}},{key:"__loadImage",value:function(e){var t=e.data;if(this.canvasEle){var n=this.canvasEle.getContext("bitmaprenderer");"draw"===t.action&&(n&&n.transferFromImageBitmap(t.imageBitmap),this.canvasTexture.needsUpdate=!0)}}},{key:"__createCanvas",value:function(e){var t=document.createElement("canvas"),n=2*this.lw;t.width=16*(e.w+n),t.height=16*(e.h+n),t.style.width=16*(e.w+n),t.style.height=16*(e.h+n),Ne.postMessage({action:"init",value:{floorInfo:e,space:n,config:{scale:16}}}),this.canvasEle=t}},{key:"__createPanel",value:function(e){var n=e.left,r=e.right,a=e.top,o=e.bottom,i=e.w,s=e.h,l=new t.CanvasTexture(this.canvasEle);l.minFilter=t.NearestFilter,l.magFilter=t.NearestFilter,l.anisotropy=this.Map3d.renderer.capabilities.getMaxAnisotropy(),l.needsUpdate=!0;var c=2*this.lw;this.canvasTexture=l;var d=new t.Mesh(new t.PlaneBufferGeometry(i+c,s+c),new t.MeshBasicMaterial({map:l,transparent:!0,side:t.BackSide}));d.rotateX(Math.PI/2),d.position.set((n+r)/2,.01,-(o+a)/2),this.Map3d.scene.add(d)}}]),e}(),Fe=function(){function e(t){M(this,e),this.monitor=t.monitor,this.dispatch=t.dispatch||{updateRobot:function(){},renderRobot:function(){}},this.curFloorId=1,this.isPath=!1;var n=this.monitor.Map3d;this.robotInstance={P40:new Le(n),RS:new Re(n),P800:new Te(n)},this.robotPath=new Ge(n),this.boxHelper=null,this.selectRobotData=null}return D(e,[{key:"create",value:function(e){var t=this;this.monitor.Emitter.on("after:renderModel",this.__afterRenderRobots.bind(this)),e.map((function(e){return t.__formatRobots(e)})),this.__renderRobot(e),this.robotPath.create()}},{key:"update",value:function(e){this.__updateRobot(e)}},{key:"destory",value:function(){this.monitor.Emitter.off("after:renderModel",this.__afterRenderRobots.bind(this)),this.robotPath.destory(),this.robotInstance=null,this.robotPath=null,this.boxHelper=null,this.selectRobotData=null}},{key:"getRobotMesh",value:function(){var e=this,t=this.monitor,n=t.Store;return t.Map3d,n.getModelData("ROBOT").reduce((function(t,n){var r=e.robotInstance[n.robotType];return r?t.concat(r.getRobotMesh(n)):t}),[])}},{key:"cancelHoverRobot",value:function(e){if(e){var t=e.getObjectByName("hover");t&&(t.visible=!1)}}},{key:"hoverRobot",value:function(e){if(e){var t=e.getObjectByName("hover");t&&(t.visible=!0)}}},{key:"selectRobot",value:function(e){if(e){var t=this.monitor.Store,n=e.getObjectByName("select");n&&(n.visible=!0);var r=t.findModelByUuid("ROBOT",e.userData.uuid);r.id||(this.selectRobotData=null),r.id&&(this.selectRobotData=r)}}},{key:"cancelSelectRobot",value:function(e){if(e){var t=e.getObjectByName("select");t&&(t.visible=!1),this.selectRobotData=null}}},{key:"__formatRobots",value:function(e){var t="Normal",n=e.radAngle;e.taskId&&(t="Work"),"DISCONNECTED"===e.robotState&&(t="Offline"),e.errorCode&&(t="Error");var r="P800";-1!==e.robotSeries.indexOf("RS")&&(r="RS","P40"===e.robotType&&(r="P40"));var a=Math.PI/180*2;return Math.abs(Math.abs(n)-0)<=a&&(n=0),Math.abs(Math.abs(n)-Math.PI/2)<=a&&(n=Math.PI/2*(n<0?-1:1)),Math.abs(Math.abs(n)-Math.PI)<=a&&(n=Math.PI*(n<0?-1:1)),Object.assign(e,{robotState:t,robotType:r,radAngle:n,floorId:e.location.z})}},{key:"__afterRenderRobots",value:function(e){var t=e.category,n=e.data;if("ROBOT"===t&&n.length){for(var r,a=0;r=n[a++];){var o=this.robotInstance[r.robotType];o&&o.afterRender(r)}this.dispatch.renderRobot&&this.dispatch.renderRobot()}}},{key:"__renderRobot",value:function(e){if(e&&e.length){var t=this.monitor,n=t.Store,r=t.Map3d,a=n.getModelData("ROBOT"),o=!a||!a.length,i=V.THEME.ROBOT_PATH_COLOR,s=[e=e.map((function(e){return e.pathColor?e:k(k({},e),{},{pathColor:i[Math.floor(10*Math.random())]})})),{category:"ROBOT",useModelName:function(e){return e.robotType}}];o?r.initModel.apply(r,s):r.appendModelData.apply(r,s)}}},{key:"__updateRobot",value:function(e){for(var t,n=this,r=this.monitor.Store,a=e.map((function(e){return e.id})),o=r.getModelData("ROBOT"),i=function(t,o){var i=a.findIndex((function(e){return e===o.id}));if(!~i)return n.__handleDelRobot(o),"continue";var s=n.__formatRobots(e[i]);n.robotInstance[o.robotType]&&n.robotInstance[o.robotType].update(o,s),r.updateModelData("ROBOT",k(k({},o),s)),delete e[i]},s=0;t=o[s++];)i(0,t);this.__renderRobot(e.filter((function(e){return e}))),this.__renderPath(r.getModelData("ROBOT")),this.dispatch.updateRobot&&this.dispatch.updateRobot()}},{key:"__handleDelRobot",value:function(e){var t=this.monitor.Map3d,n=t.modelInstances[e.uuid];t.scene.remove(n.model),delete t.modelInstances[e.uuid]}},{key:"__renderPath",value:function(e){if(!this.selectRobotData||this.isPath){if(!this.isPath)return this.robotPath.clearPath();this.robotPath.drawPath(e.map((function(e){return{color:e.pathColor,paths:e.path}})))}else{var t=this.selectRobotData,n=t.pathColor,r=t.path;this.robotPath.drawPath([{color:n,paths:r}])}}}]),e}(),ze=function(){function e(t){M(this,e),this.isHeatMode=!1,this.monitor=t.monitor,this.dispatch=t.dispatch||{renderShelf:function(){}},this.robotUUid={}}return D(e,[{key:"create",value:function(e){this.monitor.Emitter.on("after:renderModel",this.__afterRenderShelf.bind(this)),this.__renderShelves(e)}},{key:"destory",value:function(){this.monitor.Emitter.off("after:renderModel",this.__afterRenderShelf.bind(this)),this.robotUUid={}}},{key:"delete",value:function(e){for(var t,n=this.monitor,r=n.Store,a=n.Map3d,o=r.getModelData("SHELF"),i=[],s=0;t=o[s++];)e.includes(t.shelfCode)&&(a.scene.remove(a.modelInstances[t.uuid].model),delete a.modelInstances[t.uuid],i.push(t));r.delModelData("SHELF",i)}},{key:"update",value:function(e){this.__updateShelves(e)}},{key:"getShelfMesh",value:function(){var e=this.monitor,t=e.Store,n=e.Map3d;return t.getModelData("SHELF").reduce((function(e,t){var r=n.modelInstances[t.uuid].model.getObjectByName("shelf_a_lod0");return r&&(r.userData.uuid=t.uuid,e.push(r)),e}),[])}},{key:"renderShelfHot",value:function(){var e=this.monitor,t=e.Store,n=e.Map3d,r=t.getModelData("SHELF"),a=n.modelInstances,o=V.THEME.SHELF_HOT_CONF;if(r)for(var i,s=0;i=r[s++];){var l=a[i.uuid],c=Math.floor((i.score||0)/10);l.model.color=this.isHeatMode?o[c]:null}}},{key:"__afterRenderShelf",value:function(e){var t=e.category,n=e.data;if("SHELF"===t&&n.length){for(var r,a=0;r=n[a++];)this.__afterRender(r);this.dispatch.renderShelf&&this.dispatch.renderShelf()}}},{key:"__renderShelves",value:function(e){if(e&&e.length){var t=this.monitor,n=t.Store,r=t.Map3d,a=n.getModelData("SHELF"),o=[e,{category:"SHELF",useModelName:"SHELF"}];!a||!a.length?r.initModel.apply(r,o):r.appendModelData.apply(r,o)}}},{key:"__updateShelves",value:function(e){var t=this,n=this.monitor.Store,r=n.getModelData("SHELF"),a=n.getModelData("ROBOT");this.robotUUid=a.reduce((function(e,t){return Object.assign(e,B({},t.id,t.uuid))}),{});for(var o,i=e,s=function(e,r){var a=i.findIndex((function(e){return e.shelfCode===r.shelfCode}));if(!~a)return"continue";var o=i[a];t.__shelfMove(r,o),i.splice(a,1),n.updateModelData("SHELF",k(k({},r),o))},l=0;o=r[l++];)s(0,o);i.length&&this.__renderShelves(i)}},{key:"__shelfMove",value:function(e,t){var n,r=this.monitor.Map3d,a=r.modelInstances[e.uuid].model,o=(null===(n=r.modelInstances[this.robotUUid[t.robotId]])||void 0===n?void 0:n.model)||null;if(!t.robotId||!~t.robotId)return a.removeFromParent(),a.rotation.y=-t.radAngle*(Math.PI/180),a.position.set(t.location.x,0,-t.location.y),void r.scene.add(a);o&&(a.rotation.y=-(o.rotation.y+t.radAngle*(Math.PI/180)),a.parent!==o&&(a.position.set(0,0,0),o.add(a)))}},{key:"__afterRender",value:function(e){var n=this.monitor.Map3d.modelInstances[e.uuid].model,r=n.getObjectByName("shelf_a_lod0"),a=(new t.Box3).setFromObject(r),o=new t.Box3Helper(a,V.THEME.HOVER_3D);o.name="hover",o.visible=!1,r.parent.add(o);var i=new t.Box3Helper(a,V.THEME.SELECTED);i.name="select",i.visible=!1,r.parent.add(i),De(n),n.rotation.y=-e.radAngle*(Math.PI/180)}}]),e}(),$e=function(){function e(t){M(this,e),this.isHeatMode=!1,this.monitor=t.monitor,this.dispatch=t.dispatch||{renderCRack:function(){}},this.robotUUid={},this.modelCache={},this.shelfModelObject3D={},this.cracks=[],this.config={space:function(e){return.9*e},latticeSpace:function(e){return.05*e},layerColor:V.THEME.CRACK_LAYER,latticeColor:V.THEME.CRACK_LATTICE,boxFColor:V.THEME.CRACK_BOX,boxBColor:V.THEME.CRACK_BACK_BOX,layerH:.02,latticeH:.04,boxH:.3,h:.6}}return D(e,[{key:"destory",value:function(){this.robotUUid={},this.modelCache={},this.shelfModelObject3D={},this.cracks=[]}},{key:"create",value:function(e){this.cracks=e,this.__renderCRacks(e)}},{key:"update",value:function(e){this.__updateCRacks(e)}},{key:"delete",value:function(e){for(var t,n=this,r=this.monitor.Map3d,a=function(e,t){var a=n.shelfModelObject3D[t.shelfCode];if(!a)return"continue";r.scene.remove(a),delete n.shelfModelObject3D[t.shelfCode];var o=n.cracks.findIndex((function(e){return e.shelfCode===t.shelfCode}));n.cracks.splice(o,1)},o=0;t=e[o++];)a(0,t)}},{key:"__updateCRacks",value:function(e){var t=this,n=this.monitor.Store.getModelData("ROBOT");this.robotUUid=n.reduce((function(e,t){return Object.assign(e,B({},t.id,t.uuid))}),{});for(var r,a=e,o=this.cracks,i=function(e,n){var r=a.findIndex((function(e){return e.shelfCode===n.shelfCode}));if(!~r)return"continue";var o=a[r];t.__CRackMove(n,o),Object.assign(n,o),a.splice(r,1)},s=0;r=o[s++];)i(0,r);a.length&&(this.cracks.concat(a),this.__renderCRacks(a))}},{key:"__renderCRacks",value:function(e){for(var t,n=this.monitor.Map3d,r=0;t=e[r++];){var a=t,o=a.layerCount,i=a.latticeNum,s=a.layerLattices,l=a.length,c=a.width,d=a.location,h=i/o/2,u=o+2*i,p=this.__createCRack(u,o,h,l,c),f=s.FRONT,m=s.BACK;this.__renderBoxVisible(p,f,"F"),this.__renderBoxVisible(p,m,"B"),p.children[0].instanceMatrix.needsUpdate=!0,p.position.set(d.x,0,-d.y),p.rotation.y=t.radAngle*(Math.PI/180),n.scene.add(p),this.shelfModelObject3D[t.shelfCode]=p}}},{key:"__renderBoxVisible",value:function(e,n,r){var a=new t.Matrix4,o=e.userData.seq,i=function(t){n[t].displayLatticeList.forEach((function(n,i){var s=H(o["".concat(t,"-").concat(r,"-").concat(i,"-box")],7),l=s[0],c=s[1],d=s[2],h=s[3],u=s[4],p=s[5],f=s[6],m=n.relateBox;m&&m.boxCode?a.makeScale(c,d,h):a.makeScale(0,0,0),a.setPosition(u,p,f),e.children[0].setMatrixAt(l,a)}))};for(var s in n)i(s)}},{key:"__CRackMove",value:function(e,t){var n=this.monitor.Map3d,r=this.shelfModelObject3D[e.shelfCode];if(!t.robotId||!~t.robotId)return r.removeFromParent(),r.rotation.y=-t.radAngle*(Math.PI/180),r.position.set(t.location.x,0,-t.location.y),void n.scene.add(r);var a=n.modelInstances[this.robotUUid[t.robotId]].model;a&&(r.userData.curLocation=[0,0,e.radAngle*(Math.PI/180)],ge({mesh:r,location:[0,0,t.radAngle*(Math.PI/180)]}),r.parent!==a&&(r.position.set(0,0,0),a.add(r)))}},{key:"__createCRack",value:function(e,n,r,a,o){var i="".concat(e,"-").concat(n,"-").concat(r,"-").concat(a,"-").concat(o),s=this.modelCache[i]||null;if(s)return s.clone();var l=new t.Group;l.name="Model-Group-CRack";for(var c=new t.Matrix4,d=new t.Color,h=new t.BoxGeometry(1,1,1),u=new t.MeshStandardMaterial({color:16777215,transparent:!0}),p=this.config,f=p.latticeH,m=p.boxH,v=p.layerH,g=p.h,A=p.layerColor,b=p.latticeColor,y=p.boxFColor,x=p.boxBColor,C=new t.InstancedMesh(h,u,e),k=this.config.space(a),_=this.config.space(o),w=this.config.latticeSpace(a),S=this.config.latticeSpace(o),M={},E=-1,D=0;D<n;D++){var B=g*(D+1),L=B+v+f/2,T=B+v+f+m/2,R=(_-S)/2,I=k/2,O=(k-(r-1)*w)/3;++E,c.makeScale(a,o,v),c.setPosition(0,0,B),C.setMatrixAt(E,c),C.setColorAt(E,d.set(A));for(var P=0;P<r;P++){var H=P*(O+w)-I+O/2,j=R-(R-S)/2,N=(R-S)/2-R;++E,c.makeScale(O,R,f),c.setPosition(H,j,L),C.setMatrixAt(E,c),C.setColorAt(E,d.set(b)),M["".concat(D,"-F-").concat(P)]=[E,O,R,f,H,j,L],++E,c.makeScale(O,R,m),c.setPosition(H,j,T),C.setMatrixAt(E,c),C.setColorAt(E,d.set(y)),M["".concat(D,"-F-").concat(P,"-box")]=[E,O,R,m,H,j,T],++E,c.makeScale(O,R,f),c.setPosition(H,N,L),C.setMatrixAt(E,c),C.setColorAt(E,d.set(b)),M["".concat(D,"-B-").concat(P)]=[E,O,R,f,H,N,L],++E,c.makeScale(O,R,m),c.setPosition(H,N,T),C.setMatrixAt(E,c),C.setColorAt(E,d.set(x)),M["".concat(D,"-B-").concat(P,"-box")]=[E,O,R,m,H,N,T]}}return C.rotation.x=-Math.PI/2,C.rotation.z=-Math.PI,l.add(C),this.modelCache[i]=l,l.userData.seq=M,l}}]),e}(),Ve=function(){function e(t){M(this,e),this.Map3d=t,this.config={boxColor:V.THEME.CRACK_BOX,latticeColor:V.THEME.CRACK_LATTICE,boxH:.2,latticeH:.02,latticeL:.3,latticeW:.4,h:.6}}return D(e,[{key:"getStationMesh",value:function(e){return this.Map3d.modelInstances[e.uuid].model.children[0]}},{key:"afterRender",value:function(e){var n=this.Map3d.modelInstances[e.uuid].model;n.rotation.y=-e.radAngle,this.__renderLattices(n,e);var r=n.getObjectByName("Scene"),a=(new t.Box3).setFromObject(r),o=new t.Box3Helper(a,V.THEME.HOVER_3D);o.name="hover",o.visible=!1,r.parent.add(o);var i=new t.Box3Helper(a,V.THEME.SELECTED);i.name="select",i.visible=!1,r.parent.add(i),n.children[1].instanceMatrix.needsUpdate=!0}},{key:"update",value:function(e,t){var n,r=this.Map3d.modelInstances[e.uuid].model.getObjectByName("lattices"),a=null===(n=t.virtualRacks)||void 0===n?void 0:n.stationRackLattices;if(a){for(var o in a)for(var i,s=a[o],l=0;i=s[l++];)this.__changeBoxVisible(r,o,l-1,i);r.instanceMatrix.needsUpdate=!0}}},{key:"__renderLattices",value:function(e,n){if(!e.userData.isRenderLattices&&n.virtualRacks&&n.virtualRacks.stationRackLattices){var r=n.virtualRacks.stationRackLattices,a=new t.Matrix4,o=new t.Color,i=Object.values(r).reduce((function(e,t){return e+2*t.length}),0),s=new t.BoxGeometry(1,1,1),l=new t.MeshStandardMaterial({color:16777215,transparent:!0}),c=new t.InstancedMesh(s,l,i),d=-1,h={},u=this.config,p=u.boxH,f=u.latticeH,m=u.latticeL,v=u.latticeW,g=u.h,A=u.latticeColor,b=u.boxColor;for(var y in r)for(var x=r[y],C=.6-Number(y)*g,k=0;x[k++];){var _=2*m-m/2-(k-1)*(m+.01),w=C+f/2;++d,a.makeScale(m,v,f),a.setPosition(_,-.1,w),c.setMatrixAt(d,a),c.setColorAt(d,o.set(A)),h["".concat(y,"-").concat(k-1)]=[d,m,v,f,_,-.1,w];var S=w+f/2+p/2;++d,a.makeScale(m,v,p),a.setPosition(_,-.1,S),c.setMatrixAt(d,a),c.setColorAt(d,o.set(b)),h["".concat(y,"-").concat(k-1,"-box")]=[d,m,v,p,_,-.1,S]}c.name="lattices",c.userData.seq=h,c.rotation.x=-Math.PI/2,c.rotation.z=-Math.PI/2,e.add(c)}}},{key:"__changeBoxVisible",value:function(e,n,r,a){var o=e.userData.seq["".concat(n,"-").concat(r,"-box")]||[],i=new t.Matrix4;"OCCUPIED"===a.latticeStatus?i.makeScale(o[1],o[2],o[3]):i.makeScale(0,0,0),i.setPosition(o[4],o[5],o[6]),e.setMatrixAt(o[0],i)}}]),e}(),We=function(){function t(e){M(this,t),this.monitor=e.monitor;var n=this.monitor.Map3d;this.dispatch=e.dispatch||{renderStation:function(){}},this.stationInstance={"station-7":new Ve(n)}}var n;return D(t,[{key:"create",value:(n=S(e.mark((function t(n){var r=this;return e.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.monitor.Emitter.on("after:renderModel",this.__afterRenderStation.bind(this)),n.map((function(e){return r.__formatStations(e)})),this.__renderStations(n);case 3:case"end":return e.stop()}}),t,this)}))),function(e){return n.apply(this,arguments)})},{key:"destory",value:function(){this.monitor.Emitter.off("after:renderModel",this.__afterRenderStation.bind(this)),this.stationInstance={}}},{key:"update",value:function(e){var t=this;e.map((function(e){return t.__formatStations(e)})),this.__updateStations(e)}},{key:"getStationMesh",value:function(){var e=this;return this.monitor.Store.getModelData("STATION").filter((function(e){return 7===e.stationTypeValue})).reduce((function(t,n){var r=e.stationInstance["station-".concat(n.stationTypeValue)];return r?t.concat(r.getStationMesh(n)):t}),[])}},{key:"hoverStation",value:function(e){if(e){var t=e.getObjectByName("hover");t&&(t.visible=!0)}}},{key:"cancelHoverStation",value:function(e){if(e){var t=e.getObjectByName("hover");t&&(t.visible=!1)}}},{key:"selectStation",value:function(e){if(e){var t=e.getObjectByName("select");t&&(t.visible=!0)}}},{key:"cancelSelectStation",value:function(e){if(e){var t=e.getObjectByName("select");t&&(t.visible=!1)}}},{key:"__formatStations",value:function(e){var t={};7===e.stationTypeValue?t.location=e.location3D||e.location:(e.stationTypeValue=2,e.placeDir&&(e.direction=ue[e.placeDir])),t.radAngle=e.direction*(Math.PI/180),delete e.direction,Object.assign(e,t)}},{key:"__afterRenderStation",value:function(e){var t=e.category,n=e.data;if("STATION"===t&&n.length){for(var r,a=0;r=n[a++];){var o=this.stationInstance["station-".concat(r.stationTypeValue)];o&&o.afterRender(r)}this.dispatch.renderStation&&this.dispatch.renderStation()}}},{key:"__renderStations",value:function(e){if(e&&e.length){var t=this.monitor,n=t.Store,r=t.Map3d,a=n.getModelData("STATION"),o=[e,{category:"STATION",useModelName:function(e){return"STATION-".concat(e.stationTypeValue)}}];!a||!a.length?r.initModel.apply(r,o):r.appendModelData.apply(r,o)}}},{key:"__updateStations",value:function(e){var t=this,n=this.monitor.Store,r=e.map((function(e){return e.stationId})),a=n.getModelData("STATION");if(a)for(var o,i=function(a,o){var i=r.findIndex((function(e){return e===o.stationId}));if(!~i)return"continue";var s=e[i],l="station-".concat(o.stationTypeValue);t.stationInstance[l]&&t.stationInstance[l].update(o,s),n.updateModelData("STATION",k(k({},o),s))},s=0;o=a[s++];)i(0,o)}}]),t}(),Ue=function(){function t(e){M(this,t),this.monitor=e.monitor}var n;return D(t,[{key:"create",value:(n=S(e.mark((function t(n){var r=this;return e.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:n.map((function(e){return r.__formatCharges(e)})),this.__renderCharges(stations);case 2:case"end":return e.stop()}}),t,this)}))),function(e){return n.apply(this,arguments)})},{key:"destory",value:function(){}},{key:"__formatCharges",value:function(e){var t={};t.radAngle=ue[e.chargerDir]*(Math.PI/180),Object.assign(station,t)}},{key:"__renderStations",value:function(e){if(e&&e.length){var t=this.monitor,n=t.Store,r=t.Map3d,a=n.getModelData("CHARGE"),o=[e,{category:"CHARGE",useModelName:"charger"}];!a||!a.length?r.initModel.apply(r,o):r.appendModelData.apply(r,o)}}}]),t}(),qe=function(){function e(t){M(this,e),this.monitor=t.monitor,this.dispatch=t.dispatch||{hoverEvent:function(){}},this.actionType="box",this.config={hoverColor:V.THEME.HOVER_3D},this.actionTypeMesh={},this.preSelect=null,this.curSelect=null}return D(e,[{key:"hover",value:function(e){var t=this.monitor.Map3d;if(this.actionTypeMesh[this.actionType]){var n=this.actionTypeMesh[this.actionType].filter((function(e){return e.visible}));if(n&&n.length){var r=t.search.get(n,e),a=this.monitor.behaviorSelect.getCurSelectUniqKey();if(this.preSelect=this.curSelect,a)this.getCurHoverUniqKey(this.preSelect)===this.monitor.behaviorSelect.getCurSelectUniqKey()&&(this.preSelect=null);this.curSelect=r,this.__handlerHover()}}}},{key:"triggerHover",value:function(){}},{key:"destory",value:function(){this.actionTypeMesh={},this.preSelect=null,this.curSelect=null,this.actionType="box"}},{key:"clearHover",value:function(){this.preSelect=this.curSelect,this.curSelect=null,this.__handlerHover(),this.preSelect=null}},{key:"addMesh",value:function(e,t){this.actionTypeMesh[e]=t}},{key:"getCurHoverUniqKey",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.curSelect;return!!(e&&e.data&&e.data.length&&e.mesh)&&("box"===this.actionType||"lattice"===this.actionType?e.data[0].latticeCode:"cell"===this.actionType?!!e.data.length&&e.data[0].cellCode:"robot"===this.actionType?Me(e.mesh,"Model-Group-ROBOT"):"station"===this.actionType?Me(e.mesh,"Model-Group-STATION"):void 0)}},{key:"__handlerBoxHover",value:function(e,t,n){e&&e.data&&e.data.length&&this.monitor.rack.cancelBoxsColor(e.data[0].latticeCode,n),t&&t.data&&t.data.length&&this.monitor.rack.hoverBoxs(t.data[0].latticeCode,n)}},{key:"__handlerLatticeHover",value:function(e,t,n){e&&e.data&&e.data.length&&this.monitor.rack.cancelLatticesColor(e.data[0].latticeCode,n),t&&t.data&&t.data.length&&this.monitor.rack.hoverLattices(t.data[0].latticeCode,n)}},{key:"__handlerCellHover",value:function(e,t,n){var r=this.monitor.Map3d.floor;e&&r.cancelCellColor(e.data[0],n),t&&r.hoverCell(t.data[0],n)}},{key:"__handlerRobotHover",value:function(e,t,n){e&&e.mesh&&this.monitor.robot.cancelHoverRobot(Me(e.mesh,"Model-Group-ROBOT"),n),t&&t.mesh&&this.monitor.robot.hoverRobot(Me(t.mesh,"Model-Group-ROBOT"),n)}},{key:"__handlerStationHover",value:function(e,t,n){e&&e.mesh&&this.monitor.station.cancelHoverStation(Me(e.mesh,"Model-Group-STATION"),n),t&&t.mesh&&this.monitor.station.hoverStation(Me(t.mesh,"Model-Group-STATION"),n)}},{key:"__handlerHover",value:function(){var e=this.curSelect,t=this.preSelect,n=this.config.hoverColor;"box"===this.actionType&&this.__handlerBoxHover(t,e,n),"lattice"===this.actionType&&this.__handlerLatticeHover(t,e,n),"cell"===this.actionType&&this.__handlerCellHover(t,e,n),"robot"===this.actionType&&this.__handlerRobotHover(t,e,n),"station"===this.actionType&&this.__handlerStationHover(t,e,n)}}]),e}(),Ke=function(){function e(t){M(this,e),this.monitor=t.monitor,this.dispatch=t.dispatch||{selectEvent:function(){},unSelectEvent:function(){},clearSelectEvent:function(){}},this.actionType="box",this.config={selectColor:V.THEME.SELECTED},this.actionTypeMesh={},this.preSelect=null,this.curSelect=null}return D(e,[{key:"select",value:function(e){var t=this.monitor.Map3d;if(this.actionTypeMesh[this.actionType]){var n=this.actionTypeMesh[this.actionType].filter((function(e){return e.visible}));if(n&&n.length){var r=t.search.get(n,e);this.preSelect=this.curSelect,this.curSelect=r,this.__handlerSelect()}}}},{key:"selectByCode",value:function(e){this.preSelect=this.curSelect,this.curSelect=e,this.__handlerSelect()}},{key:"trigger",value:function(){this.__handlerSelect()}},{key:"clearSelect",value:function(){this.preSelect=this.curSelect,this.curSelect=null,this.__handlerSelect(),this.preSelect=null,this.dispatch.clearSelectEvent&&this.dispatch.clearSelectEvent(this.actionType)}},{key:"destory",value:function(){this.actionTypeMesh={},this.preSelect=null,this.curSelect=null,this.actionType="box"}},{key:"addMesh",value:function(e,t){this.actionTypeMesh[e]=t}},{key:"getCurSelectUniqKey",value:function(){return!!(this.curSelect&&this.curSelect.data&&this.curSelect.mesh)&&("box"===this.actionType||"lattice"===this.actionType?this.curSelect.data[0].latticeCode:"cell"===this.actionType?!!this.curSelect.data.length&&this.curSelect.data[0].cellCode:"robot"===this.actionType?Me(this.curSelect.mesh,"Model-Group-ROBOT"):"station"===this.actionType?Me(this.curSelect.mesh,"Model-Group-STATION"):void 0)}},{key:"__handlerBoxSelect",value:function(e,t,n){var r=null;if(e&&e.data&&this.monitor.rack.cancelBoxsColor(e.data[0].latticeCode,n),t&&t.data){var a=t.data[0].latticeCode;this.monitor.rack.selectBoxs(a,n);var o=this.monitor.rack.searchByLatticeCode(a);if(!o.relateBox)return;r=Object.assign(o.relateBox,{layer:o.layer})}return r}},{key:"__handlerLatticeSelect",value:function(e,t,n){var r=null;if(e&&this.monitor.rack.cancelLatticesColor(e.data[0].latticeCode,n),t&&t.data){var a=t.data[0].latticeCode;this.monitor.rack.selectLattices(a,n),r=this.monitor.rack.searchByLatticeCode(a)}return r}},{key:"__handlerCellSelect",value:function(e,t,n){var r=null,a=this.monitor.Map3d.floor;return e&&a.cancelCellColor(e.data[0],n),t&&(a.selectCell(t.data[0],n),r=t.data[0]),r}},{key:"__handlerRobotSelect",value:function(e,t,n){var r=null;if(e&&e.mesh&&this.monitor.robot.cancelSelectRobot(Me(e.mesh,"Model-Group-ROBOT"),n),t&&t.mesh){var a=Me(t.mesh,"Model-Group-ROBOT");this.monitor.robot.selectRobot(a,n),r=this.monitor.Store.findModelByUuid("ROBOT",a.userData.uuid)}return r}},{key:"__handlerStationSelect",value:function(e,t,n){var r=null;if(e&&e.mesh&&this.monitor.station.cancelSelectStation(Me(e.mesh,"Model-Group-STATION"),n),t&&t.mesh){var a=Me(t.mesh,"Model-Group-STATION");this.monitor.station.selectStation(a,n),r=this.monitor.Store.findModelByUuid("STATION",a.userData.uuid)}return r}},{key:"__handlerSelect",value:function(){var e=this.curSelect,t=this.preSelect,n=this.config.selectColor,r=null;"box"===this.actionType&&(r=this.__handlerBoxSelect(t,e,n)),"lattice"===this.actionType&&(r=this.__handlerLatticeSelect(t,e,n)),"cell"===this.actionType&&(r=this.__handlerCellSelect(t,e,n)),"robot"===this.actionType&&(r=this.__handlerRobotSelect(t,e,n)),"station"===this.actionType&&(r=this.__handlerStationSelect(t,e,n)),t&&(t.mesh||t.data)&&this.dispatch.unSelectEvent(this.actionType,t.data),e&&(e.mesh||e.data)&&this.dispatch.selectEvent(this.actionType,r),e||this.dispatch.clearSelectEvent&&this.dispatch.clearSelectEvent(this.actionType)}}]),e}(),Xe=function(){function e(t){M(this,e),this.dispatch=t||{FloorIdsReady:function(){},FloorsDataReady:function(){},DisplayDataReady:function(){},DisplayDataUpdate:function(){}},this.currentFloorIds=[1],this.cellCodesSize={},this.floorChanging=!0,this.floorMapInit=!1,this.initDisplay={isFinish:!1}}return D(e,[{key:"dispatchFloorChange",value:function(){this.floorChanging=!0,this.floorMapInit=!1,this.initDisplay={isFinish:!1}}},{key:"MapInitResponseMsg",value:function(e){var t=e.response,n=t.header,r=t.body;if(!this.__isResponseError("MapInitResponseMsg",n)){var a=n.ext;if(!this.floorMapInit){var o=r.map;if("MapInfo"!==a||!o)throw new Error("ws MapInitResponseMsg:: 没有返回map数据呀");return this.floorMapInit=!0,void this.__installMapFloors(o)}var i=!!a.includes("MapFinished");this.__installDisplay(r,i),i&&(this.floorChanging=!1)}}},{key:"MapUpdateResponseMsg",value:function(e){var t=e.response,n=t.header,r=t.body;this.__isResponseError("MapUpdateResponseMsg",n)||this.floorChanging||this.__UpdateDisplay(r)}},{key:"__isResponseError",value:function(e,t){return 0!==t.code&&(console.error(e,t),!0)}},{key:"__installMapFloors",value:function(e){if(!e.floorIds)throw new Error("ws MapInitResponseMsg:: map数据结构中没有floorIds数据呢");if(!e.floors)throw new Error("ws MapInitResponseMsg:: map数据结构中没有floors数据呢");var t=e.floorIds||[],n=e.floors||{},r=this.__formatFloorsData(n);this.currentFloorIds=r.curFloorIds,this.dispatch.FloorIdsReady&&this.dispatch.FloorIdsReady(t),this.dispatch.FloorsDataReady&&this.dispatch.FloorsDataReady(r)}},{key:"__installDisplay",value:function(e,t){if(this.initDisplay.isFinish=t,e.displayRobots&&(this.initDisplay.robots=(this.initDisplay.robots||[]).concat(this.__formatRobots(e.displayRobots))),e.displayRacks&&(this.initDisplay.racks=k(k({},this.initDisplay.racks||{}),this.__formatRacks(e.displayRacks))),e.displayShelves){var n=this.__formatShelves(e.displayShelves),r=n.shelves,a=n.cRacks;this.initDisplay.shelves=(this.initDisplay.shelves||[]).concat(r),this.initDisplay.cRacks=(this.initDisplay.cRacks||[]).concat(a)}e.displayWorkStations&&(this.initDisplay.stations=(this.initDisplay.stations||[]).concat(this.__formatStations(e.displayWorkStations))),t&&(this.dispatch.DisplayDataReady&&this.dispatch.DisplayDataReady(this.initDisplay),this.initDisplay={isFinish:!1})}},{key:"__UpdateDisplay",value:function(e){var t={};if(e.displayRobots&&(t.robots=this.__formatRobots(e.displayRobots)),e.displayRacks&&(t.racks=this.__formatRacks(e.displayRacks)),e.displayCells&&(t.cells=e.displayCells),e.delShelves&&(t.delShelves=e.delShelves),e.displayShelves){var n=this.__formatShelves(e.displayShelves),r=n.shelves,a=n.cRacks;t.shelves=r,t.cRacks=a}e.displayWorkStations&&(t.stations=this.__formatStations(e.displayWorkStations)),this.dispatch.DisplayDataReady&&this.dispatch.DisplayDataUpdate(t)}},{key:"__formatRobots",value:function(e){var t=this.currentFloorIds,n=[];for(var r in e){var a=e[r];a.location?a.location.z&&t.includes(String(a.location.z))&&n.push(a):console.error("displayRobots 找不到位置:",a)}return n}},{key:"__formatRacks",value:function(e){var t=this.currentFloorIds,n={};for(var r in e){var a=e[r],o=a.location,i=a.locationCode;o?a.location.z&&t.includes(String(a.location.z))&&(a.width="width"in a?a.width:this.cellCodesSize[i].width||1,a.length="length"in a?a.length:this.cellCodesSize[i].length||1,n[r]=a):console.error("displayRacks 找不到位置:",a)}return n}},{key:"__formatShelves",value:function(e){var t=[],n=[],r=this.currentFloorIds;for(var a in e){var o=e[a];"UN_BUILT"!==o.shelfStatus&&(o.location&&o.location.z&&r.includes(String(o.location.z))&&("popPickShelf"in o&&o.popPickShelf?n.push(o):t.push(o)))}return{shelves:t,cRacks:n}}},{key:"__formatStations",value:function(e){var t=this.currentFloorIds,n=[];for(var r in e){var a=e[r];a.location?a.location.z&&t.includes(String(a.location.z))&&n.push(a):console.error("displayWorkStations 找不到位置：",a)}return n}},{key:"__formatFloorsData",value:function(e){var t=this,n={},r=[];for(var a in e){var o=e[a]||{},i=o.floorId,s=o.resolution,l=o.mapCells,c=o.background;o.mask;var d={resolution:s,floorId:i,splitImage:!1,locationX:c.leftBottomPoint.x,locationY:c.leftBottomPoint.y};l&&l.length&&(r.push(a),l.map((function(e){var n=e.cellCode,r=e.width,a=e.length;t.cellCodesSize[n]={width:r,length:a}})),n[i]={floor:d,cells:l.filter((function(e){return!z.includes(e.cellType)}))})}return{curFloorIds:r,floorsData:n}}},{key:"destroy",value:function(){this.currentFloorIds=[1],this.cellCodesSize={},this.floorChanging=!0,this.floorMapInit=!1}}]),e}(),Ze=function(){function e(t,n){M(this,e),this.ws=null,this.wsStatus=0,this.wsUrl=t,this.initMsgParams=null,this.isDestroy=!1,this.responseFlag=!1,this.responseTimmer=null,this.restarTimmer=null,this.WebSocketMessage=new Xe(n),this._createSocket(t)}return D(e,[{key:"reqFloorMap",value:function(e){this.ws&&2===this.wsStatus&&(this.ws.send(e),this._heartbeatDetect()),this.initMsgParams=e}},{key:"reqUpdateMap",value:function(e){this.ws?(this.ws.send(e),this._heartbeatDetect()):console.log(">>>>>> socket error: 断开，等待重连")}},{key:"reqChangeFloor",value:function(e){this.ws&&(this.ws.send(e),this._heartbeatDetect()),this.WebSocketMessage.dispatchFloorChange()}},{key:"_heartbeatDetect",value:function(){var e=this;this.responseFlag=!1,this.isDestroy||(this.responseTimmer&&(clearTimeout(this.responseTimmer),this.responseTimmer=null),this.responseTimmer=setTimeout((function(){e.responseFlag||(e.ws&&e.ws.close(),clearTimeout(e.responseTimmer),e.responseTimmer=null)}),6e4))}},{key:"_createSocket",value:function(e){var t=this.wsStatus;if(0===t||3===t){console.log(">>>>>> socket create: "+Date.now());var n=new WebSocket(e);n.onopen=this.onopen.bind(this),n.onmessage=this.onmessage.bind(this),n.onclose=this.onclose.bind(this),this.ws=n}}},{key:"onopen",value:function(e){this.wsStatus=2,console.log(">>>>>> socket connected: "+Date.now()),this.initMsgParams&&(this.ws.send(this.initMsgParams),this._heartbeatDetect())}},{key:"onmessage",value:function(e){this.responseFlag=!0;var t=JSON.parse(e.data);if(t){var n=t.msgType;n&&this.WebSocketMessage[n]&&this.WebSocketMessage[n](t)}}},{key:"onclose",value:function(e){var t=this;this.wsStatus=3,console.error(">>>>>> websocket closed:",e),this.ws.onopen=void 0,this.ws.onmessage=void 0,this.ws.onclose=void 0,this.ws=null,this.isDestroy||(this.restarTimmer&&(clearTimeout(this.restarTimmer),this.restarTimmer=null),this.restarTimmer=setTimeout((function(){t._createSocket(t.wsUrl)}),3e3))}},{key:"destroy",value:function(){this.isDestroy=!0,this.restarTimmer&&(clearTimeout(this.restarTimmer),this.restarTimmer=null),this.responseTimmer&&(clearTimeout(this.responseTimmer),this.responseTimmer=null),this.ws&&this.ws.close(),this.WebSocketMessage&&this.WebSocketMessage.destroy(),this.WebSocketMessage=null,this.initMsgParams=null}}]),e}(),Ye=function(){function e(t,n){M(this,e),this.floorIds=[],this.cellCodes=[],this.WebWorker=new Ze(t,n)}return D(e,[{key:"reqFloorMap",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.floorIds=e;var t="MapInitRequestMsg",n=this.__formatWsData(t,{floorIds:e,clientFor3D:!0});this.WebWorker.reqFloorMap(n)}},{key:"reqUpdateMap",value:function(){var e=this.floorIds,t=this.__formatWsData("MapUpdateRequestMsg",{floorIds:e,robotIds:[],shelfCodes:[],rackCodes:[],cellCodes:this.cellCodes,clientFor3D:!0});this.WebWorker.reqUpdateMap(t)}},{key:"reqChangeFloor",value:function(e){if(!e)throw new Error("没有传floorId:".concat(e));this.cellCodes=[],this.floorIds=e;var t=this.__formatWsData("InitRequestMsg",{floorIds:e,clientFor3D:!0});this.WebWorker.reqChangeFloor(t)}},{key:"setCellCodes",value:function(e){this.cellCodes=e||[]}},{key:"removeCellCode",value:function(e){var t=this.cellCodes.findIndex((function(t){return String(t)===String(e)}));~t&&this.cellCodes.splice(t,1)}},{key:"destory",value:function(){this.WebWorker&&this.WebWorker.destroy(),this.shelfCodes=[],this.floorIds=[],this.WebWorker=null}},{key:"__formatWsData",value:function(e,t){var n={id:"GEEK",msgType:e,request:{header:{requestId:u(),clientCode:"GEEK",warehouseCode:"GEEK"},body:t}},r=null,a=!1,o=JSON.stringify(n);return $utils&&$utils.Data&&(r=$utils.Data.getRMSConfig()||{}),r&&(a=r.md5Switch||!1),a?"".concat(o,"@@@").concat(x(o+"signature#gk")):o}}]),e}(),Je="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAABF1BMVEUAAACas+Obs+Obs+Obs+OZseKbs+OctOObs+OcteWbs+PcXXybsuObtOSbs+SasuOcs+OasOabs+Obs+Obs+ObtOSbsuObs+ObtOWZtOOct+Wbs+Sbs+Obs+Obs+Obs+ObsuOas+Kbs+T/NUqbs+Ocs+Ocs+ObsuObtOObseH/NEibs+aZsOObtOSbs+P/NEr/NEr/NUqbs+SbtOSbsuH/NUn/NUr/NUqas+P/NEqcs+P/NEqbs+WXr9//QFC2tv8XPnMwQ3T/NUr/NEr/NUuZsuL/NUj/Nkv/Okr/MU6bs+P/NUoHPHJ+nc1qjL4WSH6nN1h8mctdgbRGb6IyX5NEOmhmOWJ4OWCOOFyeOFq9N1XdNk/pNU0CJWmkAAAASnRSTlMAUM/Nx4DcPzET8MClj2pgNhf6yq6eh3hXLRzhv5WKg0Io6efVtq+ZZkUsIgv148u5pXxwcFP6kYmDbF86IAgH+fDYcWpGQz0fGuiASvUAAAJ0SURBVFjD7ZZpUxpBEIZlOSUgCiIeIIJggiAIEY9ozH09g0fu8///jgwsgZmpWViSqlQlxfOFnZ7tpne6991emDPnX2AlFlv5Xd9YLh9JI0lvhbZ3ZvXeSFbQKCdqM7gvFuP0iTt7yeSe4y4obPr1X4ogcU43RvmcOki2Hvjz30aSXdaNy3eRFPz4rwKrlkOL9UNkFqf6x4El+9aLMhCd4r8FXIyX3dZuW+mKClCa6F8FGuPl/q4Q4lzZD0Jzccr5qad3JiSH6h0HUPX2jwL3VcNT0WdfsaSAe5MKEFbXXTHgTLWFIejlvw4R7QHbYkBLtT3KeKcQMh7g9bFwealaA54pRM2dZ2LIiWbOejVDBzqaofUrwO6lkULH6wk21PUrMaKrvasQsooPOJrhRIxoaxt5sMlUwKjhZb8Lf9zefpc/x2+MSgZsKgBH6vpcOn7pST7Li+fqTsr+uq1BSl0fCvGtN+Cr0c5RWLMESOiJ7cv//eQG+Gi080NIWALk9fLekTxxAzzuX6u3Qn56BiXgvRvgA1AyM5h+BlW4cgNcQ9U8Ax9VSAHXN73ezTtkaLMKPvqgUUZy9RZJueGjDzahohkKjCiYPW/9xjiqnEl2GLFjvAuOlyAWNUOGIRlTd7atAWqmHuQYkjP1oOZPkeppBqTrpiL51cQEAxIWTfSnyusMWPeryrLFjApHkESMW2QCnhSBut7eRtsewOrCBJKAcgwxJDH9nCuNhUkE9WcOB4NhpSxN7E1o5EjOvhWIG99eO5lJE0rEz6CV9Z6RErNMaaFcaaROuZA7pf2FOXE8qTpoNLO1mafdo/GsXLz4o2l9zv/PT4gtriRYXxVZAAAAAElFTkSuQmCC",Qe=0,et=new(function(e){L(r,q);var n=P(r);function r(e){var t;return M(this,r),(t=n.call(this,e)).compassDomBox=document.createElement("div"),t.compassDom=document.createElement("embed"),t._formatStyleToDom(t.compassDom),t.compassDomBox.appendChild(t.compassDom),t._formatStyleToDom(t.compassDomBox,{position:"absolute",left:"10px",bottom:"10px",width:"40px",height:"40px",dispaly:"none",background:"#fff","border-radius":"4px","box-shadow":"0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)","z-index":1,"pointer-events":"none","user-select":"none"}),t.PluginName="compassPlugin",t.handleChange=null,t}return D(r,[{key:"activated",value:function(){var e=this;if(this._formatStyleToDom(this.compassDomBox,{display:"block"}),!Qe){var t=Je.includes("data:image")?Je:"".concat(this.Map3d.baseUrl).concat(Je.replace("../",""));this.compassDom.src=t,this.$dom.appendChild(this.compassDomBox)}this.handleChange=Se((function(){return e._listen()}),500),this.Map3d.OrbitControls.addEventListener("change",this.handleChange),Qe++}},{key:"deactivated",value:function(){this._formatStyleToDom(this.compassDomBox,{display:"none"});var e=this.Map3d.OrbitControls;e&&e.removeEventListener("change",this.handleChange),this.handleChange=null}},{key:"destroyed",value:function(){Qe=0,this.compassDomBox&&this.compassDomBox.parentElement.removeChild(this.compassDomBox);var e=this.Map3d.OrbitControls;e&&e.removeEventListener("change",this.handleChange),this.handleChange=null}},{key:"_listen",value:function(){var e=new t.Vector3,n=new t.Spherical;this.Map3d.camera.get().getWorldDirection(e),n.setFromVector3(e),this.compassDom.style.transform="rotate(".concat(t.Math.radToDeg(n.theta)-180,"deg)")}},{key:"_formatStyleToDom",value:function(e,t){if("[object Object]"===Object.prototype.toString.call(t)){var n=e.getAttribute("style")?Object.fromEntries(e.getAttribute("style").split(";").map((function(e){return e.split(":")}))):{},r=Object.entries(k(k({},n),t)).map((function(e){return e.join(":")})).join(";");e.setAttribute("style",r)}}}]),r}()),tt=["compassPlugin"],nt=function(t){L(r,q);var n=P(r);function r(e){var t;return M(this,r),(t=n.call(this,e)).PluginName="monitorPlugin",t.actionCategory="box",t.rack=null,t.robot=null,t.mapData=null,t.wsUrl=e.wsUrl,t}return D(r,[{key:"created",value:function(){this.Map3d.registerPlugin([et]),this.__instanceHover(),this.__instanceSelect(),this.__instanceRack(),this.__instanceRobot(),this.__instanceStation(),this.__instanceShelf(),this.__instanceCRack(),this.__instanceCharge(),this.__loadMapData()}},{key:"activated",value:function(){var e=this;this.Map3d.enablePlugin(tt),this.EventInstance.add("monitor",{moveHandle:Se((function(t){return e.__handleHover(t)}),32),clickHandle:Se((function(t){return e.__handleSelect(t)}),64)})}},{key:"deactivated",value:function(){this.EventInstance.off("monitor"),this.Map3d.disabledPlugin(tt)}},{key:"destroyed",value:function(){this.rack&&(this.rack.destory(),this.rack=null),this.robot&&(this.robot.destory(),this.robot=null),this.crack&&(this.crack.destory(),this.crack=null),this.shelf&&(this.shelf.destory(),this.shelf=null),this.station&&(this.station.destory(),this.station=null),this.behaviorHover&&(this.behaviorHover.destory(),this.behaviorHover=null),this.behaviorSelect&&(this.behaviorSelect.destory(),this.behaviorSelect=null),this.mapData&&(this.mapData.destory(),this.mapData=null),this.EventInstance.off("monitor")}},{key:"showBoxHot",value:function(e){this.rack.isHeatMode=e,this.rack.reRenderBox(),this.shelf.isHeatMode=e,this.shelf.renderShelfHot()}},{key:"showRobotPath",value:function(e){this.robot.isPath=e}},{key:"zoomIn",value:function(){}},{key:"zoomOut",value:function(){}},{key:"showRacksLattices",value:function(e){var t=this;this.rack.getRackLayers().forEach((function(n){return e?t.rack.showLayerId(n):t.rack.hideLayerId(n)}))}},{key:"showTaskBoxs",value:function(e){this.rack.isTaskMode=e,this.rack.reRenderBox()}},{key:"showTopView",value:function(){var e=this.Map3d,t=e.camera,n=e.floor.floorInfo,r=n.left,a=n.right,o=n.top,i=n.bottom,s=t.get().position,l=s.x,c=s.z,d=[];d=l>r&&l<a&&c>-o&&c<-i?[l,0,c]:[(a+r)/2,0,-(i+o)/2],this.Map3d.camera.flyTo(d,90)}},{key:"showRackLayers",value:function(e){var t=this,n=[].concat(e);this.rack.getRackLayers().forEach((function(e){return t.rack.hideBoxLayerId(e)})),n.forEach((function(e){return t.rack.showBoxLayerId(e)}))}},{key:"showRackLatticeLayer",value:function(e){var t=this,n=[].concat(e);this.rack.getRackLayers().forEach((function(e){return t.rack.hideLayerId(e)})),n.forEach((function(e){return t.rack.showLayerId(e)}))}},{key:"changeActionType",value:function(e){this.behaviorSelect.clearSelect(),this.behaviorHover.clearHover(),this.behaviorHover&&(this.behaviorHover.actionType=e),this.behaviorSelect&&(this.behaviorSelect.actionType=e)}},{key:"select",value:function(e){if(e.data){if("box"===e.actionType&&this.behaviorSelect.selectByCode({data:[{latticeCode:e.data}]}),"lattice"===e.actionType&&this.behaviorSelect.selectByCode({data:[{latticeCode:e.data}]}),"robot"===e.actionType){var t=this.Store.getModelData("ROBOT").find((function(t){return String(t.id)===String(e.data)}));if(!t)return;var n=this.Map3d.modelInstances[t.uuid];if(!n)return;this.behaviorSelect.selectByCode({mesh:n.model})}if("cell"===e.actionType&&this.behaviorSelect.selectByCode({data:[this.Store.findCellByCellCode(e.data)]}),"station"===e.actionType){var r=this.Store.getModelData("STATION").find((function(t){return String(t.stationId)===String(e.data)}));if(!r)return;var a=this.Map3d.modelInstances[r.uuid];if(!a)return;this.behaviorSelect.selectByCode({mesh:a.model})}}}},{key:"clearSelect",value:function(){this.behaviorSelect.clearSelect()}},{key:"render",value:function(){}},{key:"__handleHover",value:function(e){this.behaviorHover&&this.behaviorHover.hover(e)}},{key:"__handleSelect",value:function(e){this.behaviorSelect&&this.behaviorSelect.select(e)}},{key:"__instanceRack",value:function(){var e=this;this.rack=new Be({monitor:this,dispatch:{renderRack:function(t){e.Map3d.scene.add(t),e.Emitter.emit("after:renderRack",{layers:e.rack.getRackLayers()}),e.behaviorHover.addMesh("box",e.rack.getBoxMesh()),e.behaviorHover.addMesh("lattice",e.rack.getLayersMesh()),e.behaviorSelect.addMesh("box",e.rack.getBoxMesh()),e.behaviorSelect.addMesh("lattice",e.rack.getLayersMesh())}}})}},{key:"__instanceRobot",value:function(){var e=this;this.robot=new Fe({monitor:this,dispatch:{renderRobot:function(){e.behaviorHover.addMesh("robot",e.robot.getRobotMesh()),e.behaviorSelect.addMesh("robot",e.robot.getRobotMesh())}}})}},{key:"__instanceShelf",value:function(){this.shelf=new ze({monitor:this,dispatch:{renderShelf:function(){}}})}},{key:"__instanceCRack",value:function(){this.crack=new $e({monitor:this,dispatch:{renderCRack:function(){}}})}},{key:"__instanceCharge",value:function(){this.charge=new Ue({monitor:this})}},{key:"__instanceStation",value:function(){var e=this;this.station=new We({monitor:this,dispatch:{renderStation:function(){e.behaviorHover.addMesh("station",e.station.getStationMesh()),e.behaviorSelect.addMesh("station",e.station.getStationMesh())}}})}},{key:"__instanceHover",value:function(){this.behaviorHover=new qe({monitor:this,dispatch:{}})}},{key:"__instanceSelect",value:function(){var e=this;this.behaviorSelect=new Ke({monitor:e,dispatch:{selectEvent:function(t,n){e.Emitter.emit("selected:element",{actionType:t,data:n}),"cell"===t&&n&&e.mapData.setCellCodes([n.cellCode])},unSelectEvent:function(t,n){"cell"===t&&n&&n[0]&&e.mapData.removeCellCode(n[0].cellCode)},clearSelectEvent:function(t){e.Emitter.emit("selected:element",{actionType:t,data:null})}}})}},{key:"__loadMapData",value:function(){var t=this;this.mapData=new Ye(this.wsUrl,{FloorIdsReady:function(e){t.Emitter.emit("floorIds:ready",e)},FloorsDataReady:function(e){t.Map3d.initMap(e.floorsData),t.behaviorHover.addMesh("cell",[t.Map3d.floor.floorGeo.children[1]]),t.behaviorSelect.addMesh("cell",[t.Map3d.floor.floorGeo.children[1]])},DisplayDataReady:function(n){return S(e.mark((function r(){return e.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n.racks&&t.rack.create(n.racks),e.next=3,Ee();case 3:return n.robots&&t.robot.create(n.robots),e.next=6,Ee();case 6:return n.shelves&&n.shelves.length&&t.shelf.create(n.shelves),n.cRacks&&n.cRacks.length&&t.crack.create(n.cRacks),e.next=10,Ee();case 10:return n.stations&&n.stations.length&&t.station.create(n.stations),n.charges&&n.charges.length&&t.charge.create(n.charges),e.next=14,Ee();case 14:n.isFinish&&(t.Emitter.emit("after:renderReady"),t.Map3d.ticker.add(t.__refreshData.bind(t)));case 15:case"end":return e.stop()}}),r)})))()},DisplayDataUpdate:function(e){e.racks&&t.rack&&t.rack.update(e.racks),e.robots&&e.robots.length&&t.robot&&t.robot.update(e.robots),e.shelves&&e.shelves.length&&t.shelf.update(e.shelves),e.cRacks&&e.cRacks.length&&t.crack.update(e.cRacks),e.cells&&e.cells.length&&t.Store.updateCellsData(e.cells),e.stations&&e.stations.length&&t.station.update(e.stations),e.delShelves&&e.delShelves.length&&(t.shelf.delete(e.delShelves),t.crack.delete(e.delShelves)),t.behaviorSelect&&t.behaviorSelect.trigger()}}),this.mapData.reqFloorMap([1])}},{key:"__refreshData",value:function(){var e=this,t=p.getAll();t&&t.length||Se((function(){var t;return null===(t=e.mapData)||void 0===t?void 0:t.reqUpdateMap()}),34)()}}]),r}(),rt=new(function(e){L(r,q);var n=P(r);function r(e){var a;return M(this,r),(a=n.call(this,e)).addCategory="",a.formatter=null,a.raycaster=new t.Raycaster,a.pointer=new t.Vector2,a.PluginName="addPlugin",a}return D(r,[{key:"activated",value:function(){var e=this;this.EventInstance.add("addElement",{upHandle:function(t){return e._addElement(t)},dropHandle:function(t){return e._addElement(t)}})}},{key:"deactivated",value:function(){this.addCategory="",this.formatter=null,this.EventInstance.off("addElement")}},{key:"destroyed",value:function(){this.addCategory="",this.EventInstance.off("addElement"),this.formatter=null}},{key:"_addElement",value:function(e){var t=this,n=this.Map3d.search.get([this.Map3d.scene.getObjectByName("floorBox")],e);if(n){var r={location:n.point,startBounds:n.point},a=n.data[0];a&&(r=a),this.formatter&&(r=this.formatter(r)),r.uuid=u(),this.Map3d.command.exec("add",{value:r},(function(){return t.Map3d.Emitter.emit("after:add",r)}))}}}]),r}()),at=function(e){L(r,q);var n=P(r);function r(e){var a;return M(this,r),(a=n.call(this,e))._raycaster=new t.Raycaster,a._pointer=new t.Vector2,a.PluginName="selectMapCellPlugin",a}return D(r,[{key:"activated",value:function(){var e=this;this.EventInstance.add("SelectMapCell",{clickHandle:function(t){return e._selectMapCell(t)}})}},{key:"deactivated",value:function(){this.EventInstance.off("SelectMapCell")}},{key:"destroyed",value:function(){this.EventInstance.off("SelectMapCell")}},{key:"_selectMapCell",value:function(e){this._pointer.set(e.offsetX/this.$dom.offsetWidth*2-1,-e.offsetY/this.$dom.offsetHeight*2+1),this._raycaster.setFromCamera(this._pointer,this.Map3d.camera.get());var t=this._raycaster.intersectObject(this.Map3d.scene.getObjectByName("floorBox"),!1);if(t.length){var n=t[0].point,r=n.x,a=n.z,o=this.Store.findSelectCell({x:r,y:a})||{cellCode:null};this.Emitter.emit("after:selectCell",o)}}}]),r}(),ot=new at,it=["selectPlugin","addPlugin","hoverPlugin","movePlugin","selectMapCellPlugin","compassPlugin"],st=function(e){L(n,q);var t=P(n);function n(e){var r;return M(this,n),(r=t.call(this,e)).PluginName="editPlugin",r}return D(n,[{key:"created",value:function(){var e=[J,Z,rt,et,Y,ot];this.Map3d.registerPlugin(e),this.Map3d.loadObj.load(Object.keys(this.Map3d.config.modelMap))}},{key:"activated",value:function(){this.Map3d.enablePlugin(it)}},{key:"deactivated",value:function(){this.Map3d.disabledPlugin(it)}}]),n}(),lt=[{icon:"icon-full-screen",text:"lang.rms.fed.fullScreen",event:"fullScreen",active:!1,desc:"全屏",isShow:!0},{icon:"icon-daolu",text:"lang.rms.fed.displayAllPath",event:"isShowRobotPath",active:!1,desc:"机器人路径",isShow:!0},{icon:"icon-konghuowei",text:"显示隐藏货位",event:"isShowLattice",active:!0,desc:"显示隐藏货位",isShow:!0},{icon:"icon-gis_fushi",text:"俯视角",event:"isShowTopView",active:!1,desc:"俯视角",isShow:!0},{icon:"icon-box",text:"lang.rms.fed.showTaskBox",event:"isShowTaskBox",active:!1,desc:"显示有任务货箱",isShow:!0},{icon:"icon-shelf-heat-display",text:"lang.rms.fed.shelfHeatDisplay",event:"toggleMapShelfHeat",active:!1,desc:"显示货架/货箱热度",isShow:!0},{icon:"icon-show-help-doc",text:"lang.rms.fed.showHelpDoc",event:"isShowLegend",active:!1,desc:"显示帮助说明",isShow:!0},{icon:"icon-to2D",text:"2D",event:"to2D",active:!1,desc:"2D",isShow:!0}],ct={name:"svgIcon",props:{symbolId:{type:String,required:!0}},data:()=>({}),computed:{xlinkHref(){return`#${this.symbolId}`}}};function dt(e,t,n,r,a,o,i,s,l,c){"boolean"!=typeof i&&(l=s,s=i,i=!1);const d="function"==typeof n?n.options:n;let h;if(e&&e.render&&(d.render=e.render,d.staticRenderFns=e.staticRenderFns,d._compiled=!0,a&&(d.functional=!0)),r&&(d._scopeId=r),o?(h=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,l(e)),e&&e._registeredComponents&&e._registeredComponents.add(o)},d._ssrRegister=h):t&&(h=i?function(e){t.call(this,c(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,s(e))}),h)if(d.functional){const e=d.render;d.render=function(t,n){return h.call(n),e(t,n)}}else{const e=d.beforeCreate;d.beforeCreate=e?[].concat(e,h):[h]}return n}const ht="undefined"!=typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());function ut(e){return(e,t)=>function(e,t){const n=ht?t.media||"default":e,r=ft[n]||(ft[n]={ids:new Set,styles:[]});if(!r.ids.has(e)){r.ids.add(e);let n=t.source;if(t.map&&(n+="\n/*# sourceURL="+t.map.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(t.map))))+" */"),r.element||(r.element=document.createElement("style"),r.element.type="text/css",t.media&&r.element.setAttribute("media",t.media),void 0===pt&&(pt=document.head||document.getElementsByTagName("head")[0]),pt.appendChild(r.element)),"styleSheet"in r.element)r.styles.push(n),r.element.styleSheet.cssText=r.styles.filter(Boolean).join("\n");else{const e=r.ids.size-1,t=document.createTextNode(n),a=r.element.childNodes;a[e]&&r.element.removeChild(a[e]),a.length?r.element.insertBefore(t,a[e]):r.element.appendChild(t)}}}(e,t)}let pt;const ft={};const mt=ct;var vt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("i",{staticClass:"svg-icon"},[n("svg",{staticClass:"icon",attrs:{"aria-hidden":"true"}},[n("use",{attrs:{"xlink:href":e.xlinkHref}})])])};vt._withStripped=!0;const gt={name:"ToolBar",components:{svgIcon:dt({render:vt,staticRenderFns:[]},(function(e){e&&e("data-v-3e1d58f3_0",{source:".svg-icon .icon[data-v-3e1d58f3] {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n\n/*# sourceMappingURL=svgIcon.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\svgIcon.vue","svgIcon.vue"],names:[],mappings:"AA4BA;EACA,UAAA;EACA,WAAA;EACA,uBAAA;EACA,kBAAA;EACA,gBAAA;AC3BA;;AAEA,sCAAsC",file:"svgIcon.vue",sourcesContent:['<template>\r\n  <i class="svg-icon">\r\n    <svg class="icon" aria-hidden="true">\r\n      <use :xlink:href="xlinkHref"></use>\r\n    </svg>\r\n  </i>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: "svgIcon",\r\n  props: {\r\n    symbolId: {\r\n      type: String,\r\n      required: true,\r\n    },\r\n  },\r\n  data() {\r\n    return {};\r\n  },\r\n  computed: {\r\n    xlinkHref() {\r\n      return `#${this.symbolId}`;\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n<style scoped lang="scss">\r\n.svg-icon {\r\n  .icon {\r\n    width: 1em;\r\n    height: 1em;\r\n    vertical-align: -0.15em;\r\n    fill: currentColor;\r\n    overflow: hidden;\r\n  }\r\n}\r\n</style>\r\n',".svg-icon .icon {\n  width: 1em;\n  height: 1em;\n  vertical-align: -0.15em;\n  fill: currentColor;\n  overflow: hidden;\n}\n\n/*# sourceMappingURL=svgIcon.vue.map */"]},media:void 0})}),mt,"data-v-3e1d58f3",false,undefined,!1,ut,void 0,void 0)},props:["floorIds","rackLayers","defaultCurrentFloor"],data:()=>({btnList:[],isVisible:!0,currentFloor:1,props:{label:"name",children:"children"},rackLayerVisible:!1}),computed:{btnListClass(){return this.isVisible?"btn-list-max-width":""},rackLayerTreeData(){let e=this.rackLayers,t=[];return e.forEach((e=>{t.push({name:e})})),[{name:"ALL",children:t}]}},mounted(){this.btnList=this.formatToolBar(lt),this.currentFloor=this.defaultCurrentFloor},destroyed(){this.btnList=null},methods:{ctrlVisible(){this.isVisible=!this.isVisible},triggerMapEvent(e){switch(e.event){case"zoomOut":case"zoomIn":case"fullScreen":case"isShowTopView":e.active=!1;break;case"to2D":case"isShowRobotPath":case"isShowRack":case"toggleMapShelfHeat":case"isShowLegend":case"isShowTaskBox":case"isShowLattice":e.active=!e.active}this.$emit("handleToolBar",e)},changeFloor(e){this.$emit("changeFloor",e)},formatToolBar(e){let t=JSON.parse(JSON.stringify(e));return t.forEach((e=>{"isShowRack"!==e.event&&"isShowTaskBox"!==e.event||(e.isShow=null!==this.rackLayers)})),t},handleCheckChange(e,t){const n=this.$refs.rackLayerTreeDom.getCheckedKeys().filter((e=>"ALL"!==e));this.$emit("MapRackLayerChange",{layer:n})}}};var At=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"tool-bar-content"},[n("div",{staticClass:"ctrl-btn",on:{click:e.ctrlVisible}},[n("i",{class:e.isVisible?"el-icon-s-fold":"el-icon-s-unfold"})]),e._v(" "),n("div",{class:["btn-list",e.btnListClass],attrs:{id:"toolBtnList"}},[e._l(e.btnList,(function(t,r){return n("el-tooltip",{directives:[{name:"show",rawName:"v-show",value:t.isShow,expression:"item.isShow"}],key:r,attrs:{effect:"dark",content:e.$t(t.text),placement:"bottom"}},[n("div",{class:["btn-item",t.active?"btn-active":""],on:{click:function(n){return e.triggerMapEvent(t)}}},[n("svg-icon",{attrs:{symbolId:t.icon}})],1)])})),e._v(" "),n("div",{staticClass:"floor-select"},[n("span",{staticClass:"title"},[e._v(e._s(e.$t("lang.rms.fed.floor")))]),e._v(" "),n("el-select",{staticStyle:{width:"80px"},attrs:{size:"mini",placeholder:e.$t("lang.rms.fed.pleaseChoose")},on:{change:e.changeFloor},model:{value:e.currentFloor,callback:function(t){e.currentFloor=t},expression:"currentFloor"}},e._l(e.floorIds,(function(e){return n("el-option",{key:e,attrs:{label:e,value:e}})})),1),e._v(" "),e.rackLayers&&e.rackLayers.length>0?[n("el-popover",{attrs:{placement:"bottom",width:"100",trigger:"manual"},model:{value:e.rackLayerVisible,callback:function(t){e.rackLayerVisible=t},expression:"rackLayerVisible"}},[n("el-tree",{ref:"rackLayerTreeDom",attrs:{props:e.props,"node-key":"name",data:e.rackLayerTreeData,"default-checked-keys":e.rackLayers,"show-checkbox":"","default-expand-all":!0},on:{check:e.handleCheckChange}}),e._v(" "),n("el-button",{staticStyle:{"margin-left":"10px"},attrs:{slot:"reference",size:"mini",type:"primary"},on:{click:function(t){e.rackLayerVisible=!e.rackLayerVisible}},slot:"reference"},[e._v("\n            "+e._s(e.$t("lang.rms.fed.choose")+e.$t("lang.rms.fed.rackShelfLayer"))+"\n          ")])],1)]:e._e()],2)],2)])};At._withStripped=!0;const bt=dt({render:At,staticRenderFns:[]},(function(e){e&&e("data-v-3f949731_0",{source:".flex-row[data-v-3f949731] {\n  display: flex;\n  flex-direction: row;\n}\n.btn-active[data-v-3f949731] {\n  background: #6486f3 !important;\n  color: #ffffff;\n  font-weight: bold;\n}\n.btn-style[data-v-3f949731] {\n  height: 30px;\n  width: 30px;\n  min-width: 30px;\n  border-radius: 3px;\n  line-height: 30px;\n  text-align: center;\n  font-size: 20px;\n  cursor: pointer;\n}\n.btn-style[data-v-3f949731]:hover {\n  color: #ffffff;\n  font-weight: bold;\n  background: #97aef8;\n}\n.tool-bar-content[data-v-3f949731] {\n  position: absolute;\n  top: 3px;\n  left: 3px;\n  height: 40px;\n  background: #ffffff;\n  color: #575b5f;\n  border-radius: 4px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);\n  display: flex;\n  flex-direction: row;\n  justify-content: left;\n  align-items: center;\n  padding: 0 5px;\n}\n.tool-bar-content .ctrl-btn[data-v-3f949731] {\n  flex: 0 0 30px;\n  margin: 0 5px;\n  height: 30px;\n  width: 30px;\n  min-width: 30px;\n  border-radius: 3px;\n  line-height: 30px;\n  text-align: center;\n  font-size: 20px;\n  cursor: pointer;\n}\n.tool-bar-content .ctrl-btn[data-v-3f949731]:hover {\n  color: #ffffff;\n  font-weight: bold;\n  background: #97aef8;\n}\n.tool-bar-content .btn-list-max-width[data-v-3f949731] {\n  width: 100% !important;\n}\n.tool-bar-content .btn-list[data-v-3f949731] {\n  overflow: hidden;\n  width: 0;\n  transition: width 0.3s ease-in-out;\n  display: flex;\n  flex-direction: row;\n}\n.tool-bar-content .btn-list .floor-select[data-v-3f949731] {\n  border-left: 2px solid #95a0ac;\n  padding-left: 10px;\n  margin-left: 10px;\n  display: flex;\n  flex-direction: row;\n}\n.tool-bar-content .btn-list .floor-select .title[data-v-3f949731] {\n  height: 30px;\n  width: auto;\n  min-width: 30px;\n  line-height: 30px;\n  text-align: center;\n  font-size: 13px;\n  cursor: pointer;\n  padding-right: 5px;\n  padding-left: 5px;\n}\n.tool-bar-content .btn-list .btn-item[data-v-3f949731] {\n  margin: 0 5px;\n  height: 30px;\n  width: 30px;\n  min-width: 30px;\n  border-radius: 3px;\n  line-height: 30px;\n  text-align: center;\n  font-size: 20px;\n  cursor: pointer;\n}\n.tool-bar-content .btn-list .btn-item[data-v-3f949731]:hover {\n  color: #ffffff;\n  font-weight: bold;\n  background: #97aef8;\n}\n.floor-content[data-v-3f949731] {\n  width: 100px;\n  padding: 10px;\n}\n\n/*# sourceMappingURL=index.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\tool-bar\\index.vue","index.vue"],names:[],mappings:"AA0JA;EACA,aAAA;EACA,mBAAA;ACzJA;AD4JA;EACA,8BAAA;EACA,cAAA;EACA,iBAAA;ACzJA;AD4JA;EACA,YAAA;EACA,WAAA;EACA,eAAA;EACA,kBAAA;EACA,iBAAA;EACA,kBAAA;EACA,eAAA;EACA,eAAA;ACzJA;AD2JA;EACA,cAAA;EACA,iBAAA;EACA,mBAAA;ACzJA;AD6JA;EACA,kBAAA;EACA,QAAA;EACA,SAAA;EACA,YAAA;EACA,mBAAA;EACA,cAAA;EACA,kBAAA;EACA,gBAAA;EACA,sEAAA;EACA,aAAA;EACA,mBAAA;EACA,qBAAA;EACA,mBAAA;EACA,cAAA;AC1JA;AD4JA;EACA,cAAA;EACA,aAAA;EACA,YAAA;EACA,WAAA;EACA,eAAA;EACA,kBAAA;EACA,iBAAA;EACA,kBAAA;EACA,eAAA;EACA,eAAA;AC1JA;AD4JA;EACA,cAAA;EACA,iBAAA;EACA,mBAAA;AC1JA;AD8JA;EACA,sBAAA;AC5JA;AD+JA;EACA,gBAAA;EACA,QAAA;EACA,kCAAA;EACA,aAAA;EACA,mBAAA;AC7JA;AD+JA;EACA,8BAAA;EACA,kBAAA;EACA,iBAAA;EACA,aAAA;EACA,mBAAA;AC7JA;AD8JA;EACA,YAAA;EACA,WAAA;EACA,eAAA;EACA,iBAAA;EACA,kBAAA;EACA,eAAA;EACA,eAAA;EACA,kBAAA;EACA,iBAAA;AC5JA;ADgKA;EACA,aAAA;EACA,YAAA;EACA,WAAA;EACA,eAAA;EACA,kBAAA;EACA,iBAAA;EACA,kBAAA;EACA,eAAA;EACA,eAAA;AC9JA;ADgKA;EACA,cAAA;EACA,iBAAA;EACA,mBAAA;AC9JA;ADoKA;EACA,YAAA;EACA,aAAA;ACjKA;;AAEA,oCAAoC",file:"index.vue",sourcesContent:['<template>\r\n  <div class="tool-bar-content">\r\n    <div class="ctrl-btn" @click="ctrlVisible">\r\n      <i :class="isVisible ? \'el-icon-s-fold\' : \'el-icon-s-unfold\'" />\r\n    </div>\r\n    <div id="toolBtnList" :class="[\'btn-list\', btnListClass]">\r\n      <el-tooltip\r\n        v-for="(item, index) in btnList"\r\n        v-show="item.isShow"\r\n        :key="index"\r\n        effect="dark"\r\n        :content="$t(item.text)"\r\n        placement="bottom"\r\n      >\r\n        <div :class="[\'btn-item\', item.active ? \'btn-active\' : \'\']" @click="triggerMapEvent(item)">\r\n          <svg-icon :symbolId="item.icon"></svg-icon>\r\n        </div>\r\n      </el-tooltip>\r\n      <div class="floor-select">\r\n        <span class="title">{{ $t("lang.rms.fed.floor") }}</span>\r\n        <el-select\r\n          v-model="currentFloor"\r\n          size="mini"\r\n          :placeholder="$t(\'lang.rms.fed.pleaseChoose\')"\r\n          style="width: 80px"\r\n          @change="changeFloor"\r\n        >\r\n          <el-option v-for="item in floorIds" :key="item" :label="item" :value="item" />\r\n        </el-select>\r\n\r\n        \x3c!-- 货架层 过滤选择 --\x3e\r\n        <template v-if="rackLayers && rackLayers.length > 0">\r\n          <el-popover v-model="rackLayerVisible" placement="bottom" width="100" trigger="manual">\r\n            <el-tree\r\n              ref="rackLayerTreeDom"\r\n              :props="props"\r\n              node-key="name"\r\n              :data="rackLayerTreeData"\r\n              :default-checked-keys="rackLayers"\r\n              show-checkbox\r\n              :default-expand-all="true"\r\n              @check="handleCheckChange"\r\n            >\r\n            </el-tree>\r\n            <el-button\r\n              slot="reference"\r\n              size="mini"\r\n              type="primary"\r\n              style="margin-left: 10px"\r\n              @click="rackLayerVisible = !rackLayerVisible"\r\n            >\r\n              {{ $t("lang.rms.fed.choose") + $t("lang.rms.fed.rackShelfLayer") }}\r\n            </el-button>\r\n          </el-popover>\r\n        </template>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport toolBarList from "./config/toolBarList.js";\r\nimport svgIcon from "../svgIcon.vue";\r\n\r\nexport default {\r\n  name: "ToolBar",\r\n  components: { svgIcon },\r\n  props: ["floorIds", "rackLayers", "defaultCurrentFloor"],\r\n  data() {\r\n    return {\r\n      btnList: [],\r\n      isVisible: true,\r\n      currentFloor: 1,\r\n      props: {\r\n        label: "name",\r\n        children: "children",\r\n      },\r\n      rackLayerVisible: false,\r\n    };\r\n  },\r\n  computed: {\r\n    btnListClass() {\r\n      return this.isVisible ? "btn-list-max-width" : "";\r\n    },\r\n    rackLayerTreeData() {\r\n      let racklayers = this.rackLayers;\r\n      let data = [];\r\n      racklayers.forEach(item => {\r\n        data.push({\r\n          name: item,\r\n        });\r\n      });\r\n      return [{ name: "ALL", children: data }];\r\n    },\r\n  },\r\n  mounted() {\r\n    this.btnList = this.formatToolBar(toolBarList);\r\n    this.currentFloor = this.defaultCurrentFloor;\r\n  },\r\n  destroyed() {\r\n    this.btnList = null;\r\n  },\r\n  methods: {\r\n    ctrlVisible() {\r\n      this.isVisible = !this.isVisible;\r\n    },\r\n    // 触发地图事件\r\n    triggerMapEvent(item) {\r\n      const event = item.event;\r\n      switch (event) {\r\n        case "zoomOut":\r\n        case "zoomIn":\r\n        case "fullScreen":\r\n        case "isShowTopView":\r\n          item.active = false;\r\n          break;\r\n        case "to2D":\r\n        case "isShowRobotPath":\r\n        case "isShowRack":\r\n        case "toggleMapShelfHeat":\r\n        case "isShowLegend":\r\n        case "isShowTaskBox":\r\n        case "isShowLattice":\r\n          item.active = !item.active;\r\n          break;\r\n      }\r\n      this.$emit("handleToolBar", item);\r\n    },\r\n\r\n    // 切换楼层\r\n    changeFloor(floor) {\r\n      this.$emit("changeFloor", floor);\r\n    },\r\n\r\n    formatToolBar(list) {\r\n      let toolbar = JSON.parse(JSON.stringify(list));\r\n      toolbar.forEach(item => {\r\n        if (item.event === "isShowRack" || item.event === "isShowTaskBox") {\r\n          item.isShow = this.rackLayers !== null;\r\n        }\r\n      });\r\n      return toolbar;\r\n    },\r\n\r\n    handleCheckChange(data, checked) {\r\n      const selectData = this.$refs.rackLayerTreeDom.getCheckedKeys();\r\n      const layers = selectData.filter(item => item !== "ALL");\r\n      this.$emit("MapRackLayerChange", { layer: layers });\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.flex-row {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n.btn-active {\r\n  background: #6486f3 !important;\r\n  color: #ffffff;\r\n  font-weight: bold;\r\n}\r\n\r\n.btn-style {\r\n  height: 30px;\r\n  width: 30px;\r\n  min-width: 30px;\r\n  border-radius: 3px;\r\n  line-height: 30px;\r\n  text-align: center;\r\n  font-size: 20px;\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    color: #ffffff;\r\n    font-weight: bold;\r\n    background: #97aef8;\r\n  }\r\n}\r\n\r\n.tool-bar-content {\r\n  position: absolute;\r\n  top: 3px;\r\n  left: 3px;\r\n  height: 40px;\r\n  background: #ffffff;\r\n  color: #575b5f;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: left;\r\n  align-items: center;\r\n  padding: 0 5px;\r\n\r\n  .ctrl-btn {\r\n    flex: 0 0 30px;\r\n    margin: 0 5px;\r\n    height: 30px;\r\n    width: 30px;\r\n    min-width: 30px;\r\n    border-radius: 3px;\r\n    line-height: 30px;\r\n    text-align: center;\r\n    font-size: 20px;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: #ffffff;\r\n      font-weight: bold;\r\n      background: #97aef8;\r\n    }\r\n  }\r\n\r\n  .btn-list-max-width {\r\n    width: 100% !important;\r\n  }\r\n\r\n  .btn-list {\r\n    overflow: hidden;\r\n    width: 0;\r\n    transition: width 0.3s ease-in-out;\r\n    display: flex;\r\n    flex-direction: row;\r\n\r\n    .floor-select {\r\n      border-left: 2px solid #95a0ac;\r\n      padding-left: 10px;\r\n      margin-left: 10px;\r\n      display: flex;\r\n      flex-direction: row;\r\n      .title {\r\n        height: 30px;\r\n        width: auto;\r\n        min-width: 30px;\r\n        line-height: 30px;\r\n        text-align: center;\r\n        font-size: 13px;\r\n        cursor: pointer;\r\n        padding-right: 5px;\r\n        padding-left: 5px;\r\n      }\r\n    }\r\n\r\n    .btn-item {\r\n      margin: 0 5px;\r\n      height: 30px;\r\n      width: 30px;\r\n      min-width: 30px;\r\n      border-radius: 3px;\r\n      line-height: 30px;\r\n      text-align: center;\r\n      font-size: 20px;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        color: #ffffff;\r\n        font-weight: bold;\r\n        background: #97aef8;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.floor-content {\r\n  width: 100px;\r\n  padding: 10px;\r\n}\r\n</style>\r\n',".flex-row {\n  display: flex;\n  flex-direction: row;\n}\n\n.btn-active {\n  background: #6486f3 !important;\n  color: #ffffff;\n  font-weight: bold;\n}\n\n.btn-style {\n  height: 30px;\n  width: 30px;\n  min-width: 30px;\n  border-radius: 3px;\n  line-height: 30px;\n  text-align: center;\n  font-size: 20px;\n  cursor: pointer;\n}\n.btn-style:hover {\n  color: #ffffff;\n  font-weight: bold;\n  background: #97aef8;\n}\n\n.tool-bar-content {\n  position: absolute;\n  top: 3px;\n  left: 3px;\n  height: 40px;\n  background: #ffffff;\n  color: #575b5f;\n  border-radius: 4px;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);\n  display: flex;\n  flex-direction: row;\n  justify-content: left;\n  align-items: center;\n  padding: 0 5px;\n}\n.tool-bar-content .ctrl-btn {\n  flex: 0 0 30px;\n  margin: 0 5px;\n  height: 30px;\n  width: 30px;\n  min-width: 30px;\n  border-radius: 3px;\n  line-height: 30px;\n  text-align: center;\n  font-size: 20px;\n  cursor: pointer;\n}\n.tool-bar-content .ctrl-btn:hover {\n  color: #ffffff;\n  font-weight: bold;\n  background: #97aef8;\n}\n.tool-bar-content .btn-list-max-width {\n  width: 100% !important;\n}\n.tool-bar-content .btn-list {\n  overflow: hidden;\n  width: 0;\n  transition: width 0.3s ease-in-out;\n  display: flex;\n  flex-direction: row;\n}\n.tool-bar-content .btn-list .floor-select {\n  border-left: 2px solid #95a0ac;\n  padding-left: 10px;\n  margin-left: 10px;\n  display: flex;\n  flex-direction: row;\n}\n.tool-bar-content .btn-list .floor-select .title {\n  height: 30px;\n  width: auto;\n  min-width: 30px;\n  line-height: 30px;\n  text-align: center;\n  font-size: 13px;\n  cursor: pointer;\n  padding-right: 5px;\n  padding-left: 5px;\n}\n.tool-bar-content .btn-list .btn-item {\n  margin: 0 5px;\n  height: 30px;\n  width: 30px;\n  min-width: 30px;\n  border-radius: 3px;\n  line-height: 30px;\n  text-align: center;\n  font-size: 20px;\n  cursor: pointer;\n}\n.tool-bar-content .btn-list .btn-item:hover {\n  color: #ffffff;\n  font-weight: bold;\n  background: #97aef8;\n}\n\n.floor-content {\n  width: 100px;\n  padding: 10px;\n}\n\n/*# sourceMappingURL=index.vue.map */"]},media:void 0})}),gt,"data-v-3f949731",false,undefined,!1,ut,void 0,void 0);const yt={props:{theme:{type:Object,default:()=>({})}},name:"LegendBox",data(){const e=this.theme.MapTheme;return{legendRenderArr:[{title:this.$t("lang.rms.fed.initialization.box")+"/"+this.$t("lang.rms.fed.lattice"),arr:[{t:this.$t("lang.rms.charger.status.occupied"),c:e.RACK_BOX.toString(16)},{t:this.$t("lang.rms.fed.selected"),c:e.SELECTED.toString(16)},{t:this.$t("lang.rms.fed.buttonLock"),c:e.LOCKED.toString(16)}]},{title:this.$t("lang.rms.fed.cell"),arr:[{t:this.$t("lang.rms.fed.selected"),c:e.SELECTED.toString(16)},{t:this.$t("lang.rms.fed.buttonLock"),c:e.LOCKED.toString(16)},{t:this.$t("lang.rms.fed.pause"),c:e.STOPPED.toString(16)}]},{title:this.$t("lang.rms.fed.deliverBox"),arr:[{t:this.$t("lang.rms.charger.status.occupied"),c:e.RACK_BOX.toString(16)}]}]}}};var xt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{staticClass:"legend-content"},[n("h3",{staticClass:"legend-title"},[e._v("图例")]),e._v(" "),n("div",{staticClass:"legend-inner"},e._l(e.legendRenderArr,(function(t,r){return n("section",{key:r,staticClass:"legend-tab"},[n("h3",{staticClass:"legend-tab-title"},[e._v(e._s(t.title))]),e._v(" "),e._l(t.arr,(function(t,r){return n("div",{key:r,staticClass:"legend-item"},[n("div",{class:t.class?t.class:"legend-box",style:{borderColor:"#"+t.c}}),e._v(" "),n("div",{staticClass:"legend-name"},[e._v(e._s(t.t))])])}))],2)})),0)])};xt._withStripped=!0;const Ct=dt({render:xt,staticRenderFns:[]},(function(e){e&&e("data-v-025e6e9a_0",{source:".flex-row[data-v-025e6e9a], .legend-content .legend-inner .legend-tab .legend-item[data-v-025e6e9a], .legend-content .legend-inner[data-v-025e6e9a] {\n  display: flex;\n  flex-direction: row;\n}\n.flex-column[data-v-025e6e9a], .legend-content .legend-inner .legend-tab[data-v-025e6e9a] {\n  display: flex;\n  flex-direction: column;\n}\n.legend-content[data-v-025e6e9a] {\n  position: absolute;\n  right: 10px;\n  bottom: 10px;\n  z-index: 10;\n  padding: 10px;\n  background: #edeff1;\n  border-radius: 6px;\n  color: #95a0ac;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);\n}\n.legend-content .legend-title[data-v-025e6e9a] {\n  line-height: 30px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #676767;\n}\n.legend-content .legend-inner[data-v-025e6e9a] {\n  width: 300px;\n}\n.legend-content .legend-inner .legend-tab[data-v-025e6e9a] {\n  flex: 1;\n}\n.legend-content .legend-inner .legend-tab .legend-tab-title[data-v-025e6e9a] {\n  font-size: 12px;\n  line-height: 20px;\n}\n.legend-content .legend-inner .legend-tab .legend-item[data-v-025e6e9a] {\n  justify-content: center;\n  align-items: center;\n}\n.legend-content .legend-inner .legend-tab .legend-item .legend-box[data-v-025e6e9a] {\n  border: 8px solid #fff;\n  border-radius: 2px;\n}\n.legend-content .legend-inner .legend-tab .legend-item .legend-circle[data-v-025e6e9a] {\n  height: 16px;\n  width: 16px;\n  box-sizing: border-box;\n  border: 2px solid #fff;\n  border-radius: 50%;\n}\n.legend-content .legend-inner .legend-tab .legend-item .legend-name[data-v-025e6e9a] {\n  flex: 1;\n  font-size: 12px;\n  padding-left: 10px;\n  line-height: 30px;\n}\n\n/*# sourceMappingURL=help-panel.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\help-panel.vue","help-panel.vue"],names:[],mappings:"AAgFA;EACA,aAAA;EACA,mBAAA;AC/EA;ADkFA;EACA,aAAA;EACA,sBAAA;AC/EA;ADkFA;EACA,kBAAA;EACA,WAAA;EACA,YAAA;EACA,WAAA;EACA,aAAA;EACA,mBAAA;EACA,kBAAA;EACA,cAAA;EACA,gBAAA;EACA,sEAAA;AC/EA;ADiFA;EACA,iBAAA;EACA,eAAA;EACA,iBAAA;EACA,cAAA;AC/EA;ADkFA;EACA,YAAA;AChFA;ADmFA;EACA,OAAA;ACjFA;ADoFA;EACA,eAAA;EACA,iBAAA;AClFA;ADqFA;EAEA,uBAAA;EACA,mBAAA;ACpFA;ADsFA;EACA,sBAAA;EACA,kBAAA;ACpFA;ADuFA;EACA,YAAA;EACA,WAAA;EACA,sBAAA;EACA,sBAAA;EACA,kBAAA;ACrFA;ADwFA;EACA,OAAA;EACA,eAAA;EACA,kBAAA;EACA,iBAAA;ACtFA;;AAEA,yCAAyC",file:"help-panel.vue",sourcesContent:['<template>\r\n  <section class="legend-content">\r\n    <h3 class="legend-title">图例</h3>\r\n    <div class="legend-inner">\r\n      <section v-for="(item, index) in legendRenderArr" :key="index" class="legend-tab">\r\n        <h3 class="legend-tab-title">{{ item.title }}</h3>\r\n        <div v-for="(i, index) in item.arr" :key="index" class="legend-item">\r\n          <div :class="i.class ? i.class : \'legend-box\'" :style="{ borderColor: `#${i.c}` }"></div>\r\n          <div class="legend-name">{{ i.t }}</div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    theme: {\r\n      type: Object,\r\n      default() {\r\n        return {};\r\n      },\r\n    },\r\n  },\r\n  name: "LegendBox",\r\n  data() {\r\n    const ConfigColor = this.theme.MapTheme;\r\n    return {\r\n      legendRenderArr: [\r\n        {\r\n          title: this.$t("lang.rms.fed.initialization.box") + "/" + this.$t("lang.rms.fed.lattice"),\r\n          arr: [\r\n            {\r\n              t: this.$t("lang.rms.charger.status.occupied"), // 占用\r\n              c: ConfigColor["RACK_BOX"].toString(16),\r\n            },\r\n            {\r\n              t: this.$t("lang.rms.fed.selected"),\r\n              c: ConfigColor["SELECTED"].toString(16),\r\n            },\r\n            {\r\n              t: this.$t("lang.rms.fed.buttonLock"),\r\n              c: ConfigColor["LOCKED"].toString(16),\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          title: this.$t("lang.rms.fed.cell"),\r\n          arr: [\r\n            {\r\n              t: this.$t("lang.rms.fed.selected"),\r\n              c: ConfigColor["SELECTED"].toString(16),\r\n            },\r\n            {\r\n              t: this.$t("lang.rms.fed.buttonLock"),\r\n              c: ConfigColor["LOCKED"].toString(16),\r\n            },\r\n            {\r\n              t: this.$t("lang.rms.fed.pause"),\r\n              c: ConfigColor["STOPPED"].toString(16),\r\n            },\r\n          ],\r\n        },\r\n        {\r\n          title: this.$t("lang.rms.fed.deliverBox"),\r\n          arr: [\r\n            {\r\n              t: this.$t("lang.rms.charger.status.occupied"),\r\n              c: ConfigColor["RACK_BOX"].toString(16),\r\n            },\r\n          ],\r\n        },\r\n      ],\r\n    };\r\n  },\r\n};\r\n<\/script>\r\n\r\n<style scoped lang="scss">\r\n.flex-row {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n.flex-column {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.legend-content {\r\n  position: absolute;\r\n  right: 10px;\r\n  bottom: 10px;\r\n  z-index: 10;\r\n  padding: 10px;\r\n  background: #edeff1;\r\n  border-radius: 6px;\r\n  color: #95a0ac;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);\r\n\r\n  .legend-title {\r\n    line-height: 30px;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #676767;\r\n  }\r\n\r\n  .legend-inner {\r\n    width: 300px;\r\n    @extend .flex-row;\r\n\r\n    .legend-tab {\r\n      flex: 1;\r\n      @extend .flex-column;\r\n\r\n      .legend-tab-title {\r\n        font-size: 12px;\r\n        line-height: 20px;\r\n      }\r\n\r\n      .legend-item {\r\n        @extend .flex-row;\r\n        justify-content: center;\r\n        align-items: center;\r\n\r\n        .legend-box {\r\n          border: 8px solid #fff;\r\n          border-radius: 2px;\r\n        }\r\n\r\n        .legend-circle {\r\n          height: 16px;\r\n          width: 16px;\r\n          box-sizing: border-box;\r\n          border: 2px solid #fff;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .legend-name {\r\n          flex: 1;\r\n          font-size: 12px;\r\n          padding-left: 10px;\r\n          line-height: 30px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n',".flex-row, .legend-content .legend-inner .legend-tab .legend-item, .legend-content .legend-inner {\n  display: flex;\n  flex-direction: row;\n}\n\n.flex-column, .legend-content .legend-inner .legend-tab {\n  display: flex;\n  flex-direction: column;\n}\n\n.legend-content {\n  position: absolute;\n  right: 10px;\n  bottom: 10px;\n  z-index: 10;\n  padding: 10px;\n  background: #edeff1;\n  border-radius: 6px;\n  color: #95a0ac;\n  overflow: hidden;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);\n}\n.legend-content .legend-title {\n  line-height: 30px;\n  font-size: 16px;\n  font-weight: bold;\n  color: #676767;\n}\n.legend-content .legend-inner {\n  width: 300px;\n}\n.legend-content .legend-inner .legend-tab {\n  flex: 1;\n}\n.legend-content .legend-inner .legend-tab .legend-tab-title {\n  font-size: 12px;\n  line-height: 20px;\n}\n.legend-content .legend-inner .legend-tab .legend-item {\n  justify-content: center;\n  align-items: center;\n}\n.legend-content .legend-inner .legend-tab .legend-item .legend-box {\n  border: 8px solid #fff;\n  border-radius: 2px;\n}\n.legend-content .legend-inner .legend-tab .legend-item .legend-circle {\n  height: 16px;\n  width: 16px;\n  box-sizing: border-box;\n  border: 2px solid #fff;\n  border-radius: 50%;\n}\n.legend-content .legend-inner .legend-tab .legend-item .legend-name {\n  flex: 1;\n  font-size: 12px;\n  padding-left: 10px;\n  line-height: 30px;\n}\n\n/*# sourceMappingURL=help-panel.vue.map */"]},media:void 0})}),yt,"data-v-025e6e9a",false,undefined,!1,ut,void 0,void 0);const kt={name:"OrderGroupGrid",props:{type:{type:String,default:"form"}}};var _t=function(){var e=this,t=e.$createElement;return(e._self._c||t)("table",{staticClass:"flex-grid"},[e._t("default")],2)};_t._withStripped=!0;const wt=dt({render:_t,staticRenderFns:[]},(function(e){e&&e("data-v-7af73842_0",{source:".flex-grid[data-v-7af73842] {\n  width: 100%;\n  font-size: 14px;\n  line-height: 2;\n  word-break: break-word;\n}\n\n/*# sourceMappingURL=order-group-grid.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\right-panel\\common\\order-group-grid.vue","order-group-grid.vue"],names:[],mappings:"AAmBA;EACA,WAAA;EACA,eAAA;EACA,cAAA;EACA,sBAAA;AClBA;;AAEA,+CAA+C",file:"order-group-grid.vue",sourcesContent:['<template>\r\n  <table class="flex-grid">\r\n    <slot></slot>\r\n  </table>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: "OrderGroupGrid",\r\n  props: {\r\n    type: {\r\n      type: String,\r\n      default: "form", // form,info\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.flex-grid {\r\n  width: 100%;\r\n  font-size: 14px;\r\n  line-height: 2;\r\n  word-break: break-word;\r\n}\r\n</style>\r\n',".flex-grid {\n  width: 100%;\n  font-size: 14px;\n  line-height: 2;\n  word-break: break-word;\n}\n\n/*# sourceMappingURL=order-group-grid.vue.map */"]},media:void 0})}),kt,"data-v-7af73842",false,undefined,!1,ut,void 0,void 0);const St=dt({},(function(e){e&&e("data-v-093c3886_0",{source:"tr[data-v-093c3886] {\n  border-bottom: 1px solid #eee;\n  line-height: 1.2;\n}\ntr > td[data-v-093c3886] {\n  padding: 6px;\n  vertical-align: middle;\n}\ntr[data-v-093c3886]:nth-child(2n) {\n  background: #f6f6f6;\n}\n.item-label[data-v-093c3886] {\n  color: #606266;\n  text-align: right;\n  font-weight: 700;\n  font-size: 13px;\n  user-select: text !important;\n  width: 86px;\n  border-right: 1px solid #eee;\n  vertical-align: middle;\n}\n.item-value[data-v-093c3886] {\n  user-select: text !important;\n}\n.item-value[data-v-093c3886]  .el-tag {\n  font-size: 14px;\n  letter-spacing: 0.5px;\n  word-break: break-all;\n  height: auto;\n  white-space: unset;\n}\n.item-value > input[data-v-093c3886] {\n  width: 100%;\n}\n.item-value > button.submit[data-v-093c3886] {\n  float: right;\n}\n\n/*# sourceMappingURL=order-group-grid-item.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\right-panel\\common\\order-group-grid-item.vue","order-group-grid-item.vue"],names:[],mappings:"AAuEA;EACA,6BAAA;EACA,gBAAA;ACtEA;ADwEA;EACA,YAAA;EACA,sBAAA;ACtEA;ADwEA;EACA,mBAAA;ACtEA;AD0EA;EACA,cAAA;EACA,iBAAA;EACA,gBAAA;EACA,eAAA;EACA,4BAAA;EACA,WAAA;EACA,4BAAA;EACA,sBAAA;ACvEA;AD0EA;EAEA,4BAAA;ACxEA;AD0EA;EACA,eAAA;EACA,qBAAA;EACA,qBAAA;EACA,YAAA;EACA,kBAAA;ACxEA;AD2EA;EACA,WAAA;ACzEA;AD4EA;EACA,YAAA;AC1EA;;AAEA,oDAAoD",file:"order-group-grid-item.vue",sourcesContent:['<script>\r\nexport default {\r\n  name: "OrderGroupGridItem",\r\n  render(h) {\r\n    const colspan = this.label ? 1 : 2;\r\n    let valueText = this.value;\r\n\r\n    const type = Object.prototype.toString.call(valueText);\r\n    if (type === "[object Object]" || type === "[object Array]") {\r\n      valueText = JSON.stringify(valueText);\r\n    } else if (type === "[object Number]") {\r\n      valueText = valueText.toString();\r\n    }\r\n\r\n    return h(\r\n      "tr", // 标签名称\r\n      [\r\n        colspan === 1 &&\r\n          h(\r\n            "td",\r\n            {\r\n              class: {\r\n                "item-label": true,\r\n              },\r\n            },\r\n            this.label,\r\n          ),\r\n        valueText === "" &&\r\n          h(\r\n            "td",\r\n            {\r\n              class: {\r\n                "item-value": true,\r\n              },\r\n              attrs: {\r\n                colspan: colspan,\r\n              },\r\n            },\r\n            this.$slots.default,\r\n          ),\r\n\r\n        valueText !== "" &&\r\n          h(\r\n            "td",\r\n            {\r\n              class: {\r\n                "item-value": true,\r\n              },\r\n              attrs: {\r\n                colspan: colspan,\r\n              },\r\n            },\r\n            valueText,\r\n          ),\r\n      ],\r\n    );\r\n  },\r\n  props: {\r\n    label: {\r\n      type: String,\r\n      default: "",\r\n    },\r\n    value: {\r\n      // eslint-disable-next-line vue/require-prop-type-constructor\r\n      type: String | Number | Object | Array,\r\n      default: "",\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n<style lang="scss" scoped>\r\ntr {\r\n  border-bottom: 1px solid #eee;\r\n  line-height: 1.2;\r\n\r\n  > td {\r\n    padding: 6px;\r\n    vertical-align: middle;\r\n  }\r\n  &:nth-child(2n) {\r\n    background: #f6f6f6;\r\n  }\r\n}\r\n\r\n.item-label {\r\n  color: #606266;\r\n  text-align: right;\r\n  font-weight: 700;\r\n  font-size: 13px;\r\n  user-select: text !important;\r\n  width: 86px;\r\n  border-right: 1px solid #eee;\r\n  vertical-align: middle;\r\n}\r\n\r\n.item-value {\r\n  //width: 100%;\r\n  user-select: text !important;\r\n\r\n  ::v-deep .el-tag {\r\n    font-size: 14px;\r\n    letter-spacing: 0.5px;\r\n    word-break: break-all;\r\n    height: auto;\r\n    white-space: unset;\r\n  }\r\n\r\n  > input {\r\n    width: 100%;\r\n  }\r\n\r\n  > button.submit {\r\n    float: right;\r\n  }\r\n}\r\n</style>\r\n',"tr {\n  border-bottom: 1px solid #eee;\n  line-height: 1.2;\n}\ntr > td {\n  padding: 6px;\n  vertical-align: middle;\n}\ntr:nth-child(2n) {\n  background: #f6f6f6;\n}\n\n.item-label {\n  color: #606266;\n  text-align: right;\n  font-weight: 700;\n  font-size: 13px;\n  user-select: text !important;\n  width: 86px;\n  border-right: 1px solid #eee;\n  vertical-align: middle;\n}\n\n.item-value {\n  user-select: text !important;\n}\n.item-value ::v-deep .el-tag {\n  font-size: 14px;\n  letter-spacing: 0.5px;\n  word-break: break-all;\n  height: auto;\n  white-space: unset;\n}\n.item-value > input {\n  width: 100%;\n}\n.item-value > button.submit {\n  float: right;\n}\n\n/*# sourceMappingURL=order-group-grid-item.vue.map */"]},media:void 0})}),{name:"OrderGroupGridItem",render(e){const t=this.label?1:2;let n=this.value;const r=Object.prototype.toString.call(n);return"[object Object]"===r||"[object Array]"===r?n=JSON.stringify(n):"[object Number]"===r&&(n=n.toString()),e("tr",[1===t&&e("td",{class:{"item-label":!0}},this.label),""===n&&e("td",{class:{"item-value":!0},attrs:{colspan:t}},this.$slots.default),""!==n&&e("td",{class:{"item-value":!0},attrs:{colspan:t}},n)])},props:{label:{type:String,default:""},value:{type:String|Number|Object|Array,default:""}}},"data-v-093c3886",undefined,undefined,!1,ut,void 0,void 0);const Mt={name:"DetailBox",components:{OrderGroupGrid:wt,GridItem:St},props:{detailData:{type:Object,require:!0},detailTitle:{type:String,require:!0}}};var Et=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-card",{directives:[{name:"show",rawName:"v-show",value:e.detailData.boxCode,expression:"detailData.boxCode"}],staticClass:"component-operate-detail",attrs:{shadow:"never"}},[n("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[n("span",[e._v(e._s(e.detailTitle+e.$t("lang.rms.fed.textInformationOverview")))])]),e._v(" "),n("order-group-grid",[n("grid-item",{attrs:{label:e.$t("lang.rms.fed.boxCode"),value:e.detailData.boxCode||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.boxStatus"),value:e.detailData.boxStatus||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.boxCurrentLocation"),value:e.detailData.location||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.placeLatticeCode"),value:e.detailData.placeLatticeCode||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.currentLatticeCode"),value:e.detailData.currentLatticeCode||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.robotId"),value:e.detailData.robotId||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.locationCode"),value:e.detailData.locationCode||"--"}})],1)],1)};Et._withStripped=!0;const Dt=dt({render:Et,staticRenderFns:[]},(function(e){e&&e("data-v-cd45926c_0",{source:".component-operate-detail[data-v-cd45926c] {\n  background: #fbfbfb;\n}\n.component-operate-detail[data-v-cd45926c]  tr > td {\n  padding-bottom: 0;\n}\n\n/*# sourceMappingURL=detail-box.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\right-panel\\components\\detail-box.vue","detail-box.vue"],names:[],mappings:"AAkDA;EACA,mBAAA;ACjDA;ADmDA;EACA,iBAAA;ACjDA;;AAEA,yCAAyC",file:"detail-box.vue",sourcesContent:['<template>\r\n  <el-card v-show="detailData.boxCode" shadow="never" class="component-operate-detail">\r\n    <div slot="header" class="header">\r\n      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>\r\n    </div>\r\n    <order-group-grid>\r\n      <grid-item :label="$t(\'lang.rms.fed.boxCode\')" :value="detailData.boxCode || \'--\'" />\r\n      <grid-item :label="$t(\'lang.rms.fed.boxStatus\')" :value="detailData.boxStatus || \'--\'" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.boxCurrentLocation\')"\r\n        :value="detailData.location || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.placeLatticeCode\')"\r\n        :value="detailData.placeLatticeCode || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.currentLatticeCode\')"\r\n        :value="detailData.currentLatticeCode || \'--\'"\r\n      />\r\n      <grid-item :label="$t(\'lang.rms.fed.robotId\')" :value="detailData.robotId || \'--\'" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.locationCode\')"\r\n        :value="detailData.locationCode || \'--\'"\r\n      />\r\n    </order-group-grid>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport OrderGroupGrid from "../common/order-group-grid.vue";\r\nimport GridItem from "../common/order-group-grid-item.vue";\r\n\r\nexport default {\r\n  name: "DetailBox",\r\n  components: { OrderGroupGrid, GridItem },\r\n  props: {\r\n    detailData: {\r\n      type: Object,\r\n      require: true,\r\n    },\r\n    detailTitle: {\r\n      type: String,\r\n      require: true,\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.component-operate-detail {\r\n  background: #fbfbfb;\r\n\r\n  ::v-deep tr > td {\r\n    padding-bottom: 0;\r\n  }\r\n}\r\n</style>\r\n',".component-operate-detail {\n  background: #fbfbfb;\n}\n.component-operate-detail ::v-deep tr > td {\n  padding-bottom: 0;\n}\n\n/*# sourceMappingURL=detail-box.vue.map */"]},media:void 0})}),Mt,"data-v-cd45926c",false,undefined,!1,ut,void 0,void 0);const Bt={name:"DetailLattice",components:{OrderGroupGrid:wt,GridItem:St},props:{detailData:{type:Object,require:!0},detailTitle:{type:String,require:!0}}};var Lt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-card",{directives:[{name:"show",rawName:"v-show",value:e.detailData.latticeCode,expression:"detailData.latticeCode"}],staticClass:"component-operate-detail",attrs:{shadow:"never"}},[n("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[n("span",[e._v(e._s(e.detailTitle+e.$t("lang.rms.fed.textInformationOverview")))])]),e._v(" "),n("order-group-grid",[n("grid-item",{attrs:{label:e.$t("lang.rms.fed.latticeCode"),value:e.detailData.latticeCode||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.latticeStatus"),value:e.detailData.latticeStatus||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.latticeAvailableStatus"),value:e.detailData.latticeFlag||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.layer"),value:e.detailData.layer||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.height"),value:e.detailData.height||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.fetchDirs"),value:e.detailData.fetchDirs||"--"}})],1)],1)};Lt._withStripped=!0;const Tt=dt({render:Lt,staticRenderFns:[]},(function(e){e&&e("data-v-b2399ec2_0",{source:".component-operate-detail[data-v-b2399ec2] {\n  background: #fbfbfb;\n}\n.component-operate-detail[data-v-b2399ec2]  tr > td {\n  padding-bottom: 0;\n}\n.component-operate-detail .item-label[data-v-b2399ec2] {\n  width: 50px;\n}\n\n/*# sourceMappingURL=detail-lattice.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\right-panel\\components\\detail-lattice.vue","detail-lattice.vue"],names:[],mappings:"AA4CA;EACA,mBAAA;AC3CA;AD6CA;EACA,iBAAA;AC3CA;AD8CA;EACA,WAAA;AC5CA;;AAEA,6CAA6C",file:"detail-lattice.vue",sourcesContent:['<template>\r\n  <el-card v-show="detailData.latticeCode" shadow="never" class="component-operate-detail">\r\n    <div slot="header" class="header">\r\n      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>\r\n    </div>\r\n\r\n    <order-group-grid>\r\n      <grid-item :label="$t(\'lang.rms.fed.latticeCode\')" :value="detailData.latticeCode || \'--\'" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.latticeStatus\')"\r\n        :value="detailData.latticeStatus || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.latticeAvailableStatus\')"\r\n        :value="detailData.latticeFlag || \'--\'"\r\n      />\r\n      <grid-item :label="$t(\'lang.rms.fed.layer\')" :value="detailData.layer || \'--\'" />\r\n      <grid-item :label="$t(\'lang.rms.fed.height\')" :value="detailData.height || \'--\'" />\r\n      <grid-item :label="$t(\'lang.rms.fed.fetchDirs\')" :value="detailData.fetchDirs || \'--\'" />\r\n    </order-group-grid>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport OrderGroupGrid from "../common/order-group-grid.vue";\r\nimport GridItem from "../common/order-group-grid-item.vue";\r\n\r\nexport default {\r\n  name: "DetailLattice",\r\n  components: { OrderGroupGrid, GridItem },\r\n  props: {\r\n    detailData: {\r\n      type: Object,\r\n      require: true,\r\n    },\r\n    detailTitle: {\r\n      type: String,\r\n      require: true,\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.component-operate-detail {\r\n  background: #fbfbfb;\r\n\r\n  ::v-deep tr > td {\r\n    padding-bottom: 0;\r\n  }\r\n\r\n  .item-label {\r\n    width: 50px;\r\n  }\r\n}\r\n</style>\r\n',".component-operate-detail {\n  background: #fbfbfb;\n}\n.component-operate-detail ::v-deep tr > td {\n  padding-bottom: 0;\n}\n.component-operate-detail .item-label {\n  width: 50px;\n}\n\n/*# sourceMappingURL=detail-lattice.vue.map */"]},media:void 0})}),Bt,"data-v-b2399ec2",false,undefined,!1,ut,void 0,void 0);const Rt={name:"RobotDetail",components:{OrderGroupGrid:wt,GridItem:St},props:{detailData:{type:Object,require:!0},detailTitle:{type:String,require:!0}},computed:{confirmState(){let e=this.detailData.hasOwnProperty("posConfirmed");return e?e?this.$t("lang.rms.fed.confirmed"):this.$t("lang.rms.fed.unconfirmed"):"--"}}};var It=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-card",{staticClass:"component-operate-detail",attrs:{shadow:"never"}},[n("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[n("span",[e._v(e._s(e.detailTitle+e.$t("lang.rms.fed.textInformationOverview")))])]),e._v(" "),n("order-group-grid",[n("grid-item",{attrs:{label:e.$t("lang.rms.fed.robotId"),value:e.detailData.id||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.type"),value:e.detailData.robotType||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textNodeCode"),value:e.detailData.locationCell||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.web.monitor.robot.location"),value:e.detailData.location||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.web.monitor.robot.robotPathMode"),value:e.detailData.robotPathMode||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.confirmState"),value:e.confirmState}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.errorState"),value:e.detailData.hasOwnProperty("errorCode")?e.detailData.errorCode:"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.taskId"),value:e.detailData.taskId?e.detailData.taskId.toString():"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.taskType"),value:e.detailData.taskType||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.shelfCoding"),value:e.detailData.onloadShelfCode||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.power"),value:e.detailData.hasOwnProperty("powerPercent")?e.detailData.powerPercent+"%":"--"}}),e._v(" "),n("grid-item",{directives:[{name:"show",rawName:"v-show",value:"P40"===e.detailData.robotType,expression:"detailData.robotType === 'P40'"}],attrs:{label:e.$t("lang.rms.fed.obstacleCount"),value:e.detailData.hasOwnProperty("obstacleCount")?e.detailData.obstacleCount:"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.boxHoldingTaskId"),value:e.detailData.hasOwnProperty("jobIds")?e.detailData.jobIds:"--"}})],1)],1)};It._withStripped=!0;const Ot=dt({render:It,staticRenderFns:[]},(function(e){e&&e("data-v-5fc60a8c_0",{source:".component-operate-detail[data-v-5fc60a8c] {\n  background: #fbfbfb;\n}\n.component-operate-detail[data-v-5fc60a8c]  tr > td {\n  padding-bottom: 0;\n}\n\n/*# sourceMappingURL=detail-robot.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\right-panel\\components\\detail-robot.vue","detail-robot.vue"],names:[],mappings:"AAqFA;EACA,mBAAA;ACpFA;ADsFA;EACA,iBAAA;ACpFA;;AAEA,2CAA2C",file:"detail-robot.vue",sourcesContent:['<template>\r\n  <el-card shadow="never" class="component-operate-detail">\r\n    <div slot="header" class="header">\r\n      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>\r\n    </div>\r\n    <order-group-grid>\r\n      <grid-item :label="$t(\'lang.rms.fed.robotId\')" :value="detailData.id || \'--\'" />\r\n      <grid-item :label="$t(\'lang.rms.fed.type\')" :value="detailData.robotType || \'--\'" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textNodeCode\')"\r\n        :value="detailData.locationCell || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.web.monitor.robot.location\')"\r\n        :value="detailData.location || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.web.monitor.robot.robotPathMode\')"\r\n        :value="detailData.robotPathMode || \'--\'"\r\n      />\r\n      <grid-item :label="$t(\'lang.rms.fed.confirmState\')" :value="confirmState" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.errorState\')"\r\n        :value="detailData.hasOwnProperty(\'errorCode\') ? detailData.errorCode : \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.taskId\')"\r\n        :value="detailData.taskId ? detailData.taskId.toString() : \'--\'"\r\n      />\r\n      <grid-item :label="$t(\'lang.rms.fed.taskType\')" :value="detailData.taskType || \'--\'" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.shelfCoding\')"\r\n        :value="detailData.onloadShelfCode || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.power\')"\r\n        :value="detailData.hasOwnProperty(\'powerPercent\') ? `${detailData.powerPercent}%` : \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.obstacleCount\')"\r\n        v-show="detailData.robotType === \'P40\'"\r\n        :value="detailData.hasOwnProperty(\'obstacleCount\') ? detailData.obstacleCount : \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.boxHoldingTaskId\')"\r\n        :value="detailData.hasOwnProperty(\'jobIds\') ? detailData.jobIds : \'--\'"\r\n      />\r\n    </order-group-grid>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport OrderGroupGrid from "../common/order-group-grid.vue";\r\nimport GridItem from "../common/order-group-grid-item.vue";\r\n\r\nexport default {\r\n  name: "RobotDetail",\r\n  components: { OrderGroupGrid, GridItem },\r\n  props: {\r\n    detailData: {\r\n      type: Object,\r\n      require: true,\r\n    },\r\n    detailTitle: {\r\n      type: String,\r\n      require: true,\r\n    },\r\n  },\r\n  computed: {\r\n    confirmState() {\r\n      let value = this.detailData.hasOwnProperty("posConfirmed");\r\n      if (!value) return "--";\r\n      else {\r\n        if (value) {\r\n          return this.$t("lang.rms.fed.confirmed");\r\n        } else {\r\n          return this.$t("lang.rms.fed.unconfirmed");\r\n        }\r\n      }\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.component-operate-detail {\r\n  background: #fbfbfb;\r\n\r\n  ::v-deep tr > td {\r\n    padding-bottom: 0;\r\n  }\r\n}\r\n</style>\r\n',".component-operate-detail {\n  background: #fbfbfb;\n}\n.component-operate-detail ::v-deep tr > td {\n  padding-bottom: 0;\n}\n\n/*# sourceMappingURL=detail-robot.vue.map */"]},media:void 0})}),Rt,"data-v-5fc60a8c",false,undefined,!1,ut,void 0,void 0);const Pt={name:"ShelfDetail",components:{OrderGroupGrid:wt,GridItem:St},props:{detailData:{type:Object,require:!0},detailTitle:{type:String,require:!0}}};var Ht=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-card",{staticClass:"component-operate-detail",attrs:{shadow:"never"}},[n("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[n("span",[e._v(e._s(e.detailTitle+e.$t("lang.rms.fed.textInformationOverview")))])]),e._v(" "),n("order-group-grid",[n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textNodeCode"),value:e.detailData.cellCode||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textIndexCoordinates"),value:e.detailData.index||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textAbsoluteCoordinate"),value:e.detailData.location||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textNodeType"),value:e.detailData.cellType||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textNodeStatus"),value:e.detailData.cellStatus||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textLoadDirectionMatrix"),value:e.detailData.direction||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textUnloadDirectionMatrix"),value:e.detailData.dirUnload||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textLoadDirectionNode"),value:e.detailData.loadAdjacentCells||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textUnloadDirectionNode"),value:e.detailData.unloadAdjacentCells||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textAllocatedRobotID"),value:-1!==e.detailData.allocatedRobotId?e.detailData.allocatedRobotId:"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textOccupiedRobotID"),value:-1!==e.detailData.occupyRobotId?e.detailData.occupyRobotId:"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.web.monitor.cell.fieldPrefix.occupiedShelfCode"),value:e.detailData.occupiedShelfCode||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textLogicArea"),value:e.detailData.logicId||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textLength"),value:e.detailData.length||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textWidth"),value:e.detailData.width||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.web.monitor.robot.workStationId"),value:-1!==e.detailData.stationId?e.detailData.stationId:"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.chargerId"),value:-1!==e.detailData.chargerId?e.detailData.chargerId:"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textLockStatus"),value:e.detailData.cellFlag||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.textOriginNode"),value:e.detailData.startBounds||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.laneId"),value:e.detailData.laneId||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.rsLogicId"),value:e.detailData.rsLogicId||"--"}})],1)],1)};Ht._withStripped=!0;const jt=dt({render:Ht,staticRenderFns:[]},(function(e){e&&e("data-v-0d51541e_0",{source:".component-operate-detail[data-v-0d51541e] {\n  background: #fbfbfb;\n}\n.component-operate-detail[data-v-0d51541e]  tr > td {\n  padding-bottom: 0;\n}\n\n/*# sourceMappingURL=detail-cell.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\right-panel\\components\\detail-cell.vue","detail-cell.vue"],names:[],mappings:"AA2FA;EACA,mBAAA;AC1FA;AD4FA;EACA,iBAAA;AC1FA;;AAEA,0CAA0C",file:"detail-cell.vue",sourcesContent:['<template>\r\n  <el-card shadow="never" class="component-operate-detail">\r\n    <div slot="header" class="header">\r\n      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>\r\n    </div>\r\n    <order-group-grid>\r\n      <grid-item :label="$t(\'lang.rms.fed.textNodeCode\')" :value="detailData.cellCode || \'--\'" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textIndexCoordinates\')"\r\n        :value="detailData.index || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textAbsoluteCoordinate\')"\r\n        :value="detailData.location || \'--\'"\r\n      />\r\n      <grid-item :label="$t(\'lang.rms.fed.textNodeType\')" :value="detailData.cellType || \'--\'" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textNodeStatus\')"\r\n        :value="detailData.cellStatus || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textLoadDirectionMatrix\')"\r\n        :value="detailData.direction || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textUnloadDirectionMatrix\')"\r\n        :value="detailData.dirUnload || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textLoadDirectionNode\')"\r\n        :value="detailData.loadAdjacentCells || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textUnloadDirectionNode\')"\r\n        :value="detailData.unloadAdjacentCells || \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textAllocatedRobotID\')"\r\n        :value="detailData.allocatedRobotId !== -1 ? detailData.allocatedRobotId : \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textOccupiedRobotID\')"\r\n        :value="detailData.occupyRobotId !== -1 ? detailData.occupyRobotId : \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.web.monitor.cell.fieldPrefix.occupiedShelfCode\')"\r\n        :value="detailData.occupiedShelfCode || \'--\'"\r\n      />\r\n      <grid-item :label="$t(\'lang.rms.fed.textLogicArea\')" :value="detailData.logicId || \'--\'" />\r\n      <grid-item :label="$t(\'lang.rms.fed.textLength\')" :value="detailData.length || \'--\'" />\r\n      <grid-item :label="$t(\'lang.rms.fed.textWidth\')" :value="detailData.width || \'--\'" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.web.monitor.robot.workStationId\')"\r\n        :value="detailData.stationId !== -1 ? detailData.stationId : \'--\'"\r\n      />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.chargerId\')"\r\n        :value="detailData.chargerId !== -1 ? detailData.chargerId : \'--\'"\r\n      />\r\n      <grid-item :label="$t(\'lang.rms.fed.textLockStatus\')" :value="detailData.cellFlag || \'--\'" />\r\n      <grid-item\r\n        :label="$t(\'lang.rms.fed.textOriginNode\')"\r\n        :value="detailData.startBounds || \'--\'"\r\n      />\r\n      <grid-item :label="$t(\'lang.rms.fed.laneId\')" :value="detailData.laneId || \'--\'" />\r\n      <grid-item :label="$t(\'lang.rms.fed.rsLogicId\')" :value="detailData.rsLogicId || \'--\'" />\r\n    </order-group-grid>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport OrderGroupGrid from "../common/order-group-grid.vue";\r\nimport GridItem from "../common/order-group-grid-item.vue";\r\n\r\nexport default {\r\n  name: "ShelfDetail",\r\n  components: { OrderGroupGrid, GridItem },\r\n  props: {\r\n    detailData: {\r\n      type: Object,\r\n      require: true,\r\n    },\r\n    detailTitle: {\r\n      type: String,\r\n      require: true,\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.component-operate-detail {\r\n  background: #fbfbfb;\r\n\r\n  ::v-deep tr > td {\r\n    padding-bottom: 0;\r\n  }\r\n}\r\n</style>\r\n',".component-operate-detail {\n  background: #fbfbfb;\n}\n.component-operate-detail ::v-deep tr > td {\n  padding-bottom: 0;\n}\n\n/*# sourceMappingURL=detail-cell.vue.map */"]},media:void 0})}),Pt,"data-v-0d51541e",false,undefined,!1,ut,void 0,void 0);const Nt={name:"RobotDetail",components:{OrderGroupGrid:wt,GridItem:St},props:{detailData:{type:Object,require:!0},detailTitle:{type:String,require:!0}},computed:{virtualRacks(){if(this.detailData.hasOwnProperty("virtualRacks")){const e=this.detailData.virtualRacks,t=[];if(e.hasOwnProperty("stationRackLattices")){let n=e.stationRackLattices;for(const e in n)if(Object.hasOwnProperty.call(n,e)){const r=n[e];for(let e=0;e<r.length;e++){const n=r[e],a={latticeCode:n.latticeCode,relativeIndex:n.relativeIndex};if(t.push(a),n.hasOwnProperty("relateBox")){const e=n.relateBox;a.boxCode=e.boxCode}}}return t.length>0?JSON.stringify(t):null}return null}return null}}};var Gt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-card",{staticClass:"component-operate-detail",attrs:{shadow:"never"}},[n("div",{staticClass:"header",attrs:{slot:"header"},slot:"header"},[n("span",[e._v(e._s(e.detailTitle+e.$t("lang.rms.fed.textInformationOverview")))])]),e._v(" "),n("order-group-grid",[n("grid-item",{attrs:{label:e.$t("lang.rms.web.monitor.robot.workStationId"),value:e.detailData.stationId||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.web.monitor.robot.location"),value:e.detailData.location||"--"}}),e._v(" "),n("grid-item",{attrs:{label:e.$t("lang.rms.fed.lattice"),value:e.virtualRacks}})],1)],1)};Gt._withStripped=!0;const Ft={name:"Monitor3DMapRightPanel",components:{BoxDetail:Dt,LatticeDetail:Tt,RobotDetail:Ot,CellDetail:jt,StationDetail:dt({render:Gt,staticRenderFns:[]},(function(e){e&&e("data-v-7415bbfd_0",{source:".component-operate-detail[data-v-7415bbfd] {\n  background: #fbfbfb;\n}\n.component-operate-detail[data-v-7415bbfd]  tr > td {\n  padding-bottom: 0;\n}\n\n/*# sourceMappingURL=detail-station.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\right-panel\\components\\detail-station.vue","detail-station.vue"],names:[],mappings:"AA8FA;EACA,mBAAA;AC7FA;AD+FA;EACA,iBAAA;AC7FA;;AAEA,6CAA6C",file:"detail-station.vue",sourcesContent:['<template>\r\n  <el-card shadow="never" class="component-operate-detail">\r\n    <div slot="header" class="header">\r\n      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>\r\n    </div>\r\n\r\n    <order-group-grid>\r\n      <grid-item\r\n        :label="$t(\'lang.rms.web.monitor.robot.workStationId\')"\r\n        :value="detailData.stationId || \'--\'"\r\n      />\r\n      \x3c!-- <grid-item :label="$t(\'lang.rms.fed.type\')" :value="detailData.robotType || \'--\'" /> --\x3e\r\n\r\n      <grid-item\r\n        :label="$t(\'lang.rms.web.monitor.robot.location\')"\r\n        :value="detailData.location || \'--\'"\r\n      />\r\n\r\n      \x3c!-- <template v-if="virtualRacks && virtualRacks.length > 0"> --\x3e\r\n      \x3c!-- <grid-item\r\n        v-for="(item, index) in virtualRacks"\r\n        :key="index"\r\n        :label="item.latticeCode"\r\n        :value="item.boxCode"\r\n      /> --\x3e\r\n      \x3c!-- </template> --\x3e\r\n\r\n      <grid-item :label="$t(\'lang.rms.fed.lattice\')" :value="virtualRacks" />\r\n    </order-group-grid>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport OrderGroupGrid from "../common/order-group-grid.vue";\r\nimport GridItem from "../common/order-group-grid-item.vue";\r\n\r\nexport default {\r\n  name: "RobotDetail",\r\n  components: { OrderGroupGrid, GridItem },\r\n  props: {\r\n    detailData: {\r\n      type: Object,\r\n      require: true,\r\n    },\r\n    detailTitle: {\r\n      type: String,\r\n      require: true,\r\n    },\r\n  },\r\n  computed: {\r\n    virtualRacks() {\r\n      let value = this.detailData.hasOwnProperty("virtualRacks");\r\n      if (!value) return null;\r\n      else {\r\n        const rack = this.detailData.virtualRacks;\r\n        const result = [];\r\n        if (rack.hasOwnProperty("stationRackLattices")) {\r\n          let layers = rack.stationRackLattices;\r\n          for (const key in layers) {\r\n            if (Object.hasOwnProperty.call(layers, key)) {\r\n              const layerData = layers[key];\r\n              for (let i = 0; i < layerData.length; i++) {\r\n                const lattice = layerData[i];\r\n                const box = {\r\n                  latticeCode: lattice.latticeCode,\r\n                  relativeIndex: lattice.relativeIndex,\r\n                };\r\n                result.push(box);\r\n                if (lattice.hasOwnProperty("relateBox")) {\r\n                  const curBox = lattice.relateBox;\r\n                  box["boxCode"] = curBox.boxCode;\r\n                } else {\r\n                  continue;\r\n                }\r\n              }\r\n            } else {\r\n              continue;\r\n            }\r\n          }\r\n          if (result.length > 0) {\r\n            return JSON.stringify(result);\r\n          } else {\r\n            return null;\r\n          }\r\n        } else {\r\n          return null;\r\n        }\r\n      }\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n\r\n<style lang="scss" scoped>\r\n.component-operate-detail {\r\n  background: #fbfbfb;\r\n\r\n  ::v-deep tr > td {\r\n    padding-bottom: 0;\r\n  }\r\n}\r\n</style>\r\n',".component-operate-detail {\n  background: #fbfbfb;\n}\n.component-operate-detail ::v-deep tr > td {\n  padding-bottom: 0;\n}\n\n/*# sourceMappingURL=detail-station.vue.map */"]},media:void 0})}),Nt,"data-v-7415bbfd",false,undefined,!1,ut,void 0,void 0)},props:["rackLayers","selectInfo","detailData"],data(){return{isCollapse:!1,searchType:this.rackLayers?"box":"robot",searchValue:""}},computed:{detailTitle(){switch(this.searchType){case"box":return this.$t("lang.rms.fed.optionBox");case"lattice":return this.$t("lang.rms.fed.lattice");case"robot":return this.$t("lang.rms.fed.robot");case"cell":return this.$t("lang.rms.fed.cell");case"station":return this.$t("lang.rms.fed.station");default:return""}},currentTabName(){switch(this.searchType){case"box":return"BoxDetail";case"lattice":return"LatticeDetail";case"robot":return"RobotDetail";case"cell":return"CellDetail";case"station":return"StationDetail";default:return""}}},watch:{detailData(e,t){const n=this.searchType;if(!e)return this.searchValue="";let r=e;switch(n){case"box":r.boxCode&&this.searchValue!==r.boxCode&&(this.searchValue=r.boxCode);break;case"lattice":r.latticeCode&&this.searchValue!==r.latticeCode&&(this.searchValue=r.latticeCode);break;case"cell":r.cellCode&&this.searchValue!==r.cellCode&&(this.searchValue=r.cellCode);break;case"robot":case"poppickStation":r.id&&this.searchValue!==r.id&&(this.searchValue=r.id.toString())}}},methods:{onSelect(){console.log("onSelect",this.searchType);switch(this.searchType){case"box":this._getRackInfoByBox(this.searchValue);break;case"lattice":case"cell":case"robot":case"station":this.$emit("searchSelect",{actionType:this.searchType,data:this.searchValue})}},clearSearch(){this.searchValue="",this.$emit("clearSelect")},changeSearchType(){this.clearSearch(),this.$emit("changeSearchType",this.searchType)},async querySearch(e,t){console.log("querySearch",e);switch(this.searchType){case"box":case"cell":case"lattice":case"station":t([{value:e}]);break;case"robot":if(e.length<1)t([]);else try{let n=await this._getRobotQueryList(e);t((n.data||[]).map((e=>({value:e.toString()}))))}catch(e){t([])}}},_getRobotQueryList:e=>$req.get("/athena/robotStatus/queryRobotIds",{info:e,robotStates:""}),_getRackInfoByBox(e){$req.get("/athena/box/getRackInfoByBox",{boxCode:e}).then((e=>{const{latticeCode:t}=e.data||{};t?this.$emit("searchSelect",{actionType:this.searchType,data:t}):console.error("SearchBoxError:","未收到当前rackCode的latticeCode返回值,api-/athena/box/getRackInfoByBox")}))},_getRackInfoByLattice(e){$req.get("/athena/box/getRackInfoByLattic",{latticCode:e}).then((e=>{e.data}))}}};var zt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("section",{staticClass:"right-panel-content",class:{"is-collapse":e.isCollapse}},[n("h3",[e._v(e._s(e.$t("lang.rms.fed.textInformationOverview")))]),e._v(" "),n("el-autocomplete",{staticClass:"autocomplete-input",attrs:{"fetch-suggestions":e.querySearch,"trigger-on-focus":!1,placeholder:e.$t("lang.rms.fed.pleaseEnterContent"),clearable:""},on:{select:e.onSelect,clear:e.clearSearch},model:{value:e.searchValue,callback:function(t){e.searchValue=t},expression:"searchValue"}},[n("el-select",{staticStyle:{"min-width":"80px"},attrs:{slot:"prepend",placeholder:e.$t("lang.rms.fed.pleaseChoose")},on:{change:e.changeSearchType},slot:"prepend",model:{value:e.searchType,callback:function(t){e.searchType=t},expression:"searchType"}},[e.rackLayers?n("el-option",{attrs:{label:e.$t("lang.rms.fed.initialization.box"),value:"box"}}):e._e(),e._v(" "),e.rackLayers?n("el-option",{attrs:{label:e.$t("lang.rms.fed.lattice"),value:"lattice"}}):e._e(),e._v(" "),n("el-option",{attrs:{label:e.$t("lang.rms.fed.robot"),value:"robot"}}),e._v(" "),n("el-option",{attrs:{label:e.$t("lang.rms.fed.cell"),value:"cell"}}),e._v(" "),n("el-option",{attrs:{label:e.$t("lang.rms.fed.station"),value:"station"}})],1)],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:e.detailData,expression:"detailData"}],staticClass:"panel-detail"},[n(e.currentTabName,{tag:"component",attrs:{"detail-title":e.detailTitle,"detail-data":e.detailData||{}}})],1),e._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:!e.detailData,expression:"!detailData"}],staticClass:"no-data"}),e._v(" "),n("div",{staticClass:"icon-arrow",class:{active:e.isCollapse},on:{click:function(t){t.stopPropagation(),e.isCollapse=!e.isCollapse}}},[n("i",{class:[e.isCollapse?"el-icon-arrow-left":"el-icon-arrow-right"]})])],1)};zt._withStripped=!0;const $t=dt({render:zt,staticRenderFns:[]},(function(e){e&&e("data-v-10b5e58f_0",{source:".right-panel-content[data-v-10b5e58f] {\n  position: absolute;\n  right: 0;\n  top: 0;\n  max-height: 100%;\n  padding-bottom: 10px;\n  border-radius: 4px;\n  background: #fff;\n  box-shadow: 0 3px 5px #d9d6d6;\n  z-index: 10;\n  transition-duration: 0.2s;\n  width: 280px;\n}\n.right-panel-content.is-collapse[data-v-10b5e58f] {\n  transform: translateX(100%);\n}\n.right-panel-content > h3[data-v-10b5e58f] {\n  padding: 10px;\n  border-bottom: 1px solid #ddd;\n  color: #4a8bce;\n  background: #f5f7fa;\n  font-weight: 600;\n  font-size: 15px;\n}\n.right-panel-content > .autocomplete-input[data-v-10b5e58f] {\n  width: 100%;\n  padding: 10px 10px 0 10px;\n}\n.panel-detail[data-v-10b5e58f] {\n  padding: 0 10px;\n  height: calc(100% - 100px);\n  overflow-y: auto;\n}\n.panel-detail > h5[data-v-10b5e58f] {\n  font-size: 13px;\n  font-weight: 600;\n  color: #666;\n}\n.no-data[data-v-10b5e58f] {\n  min-height: 80px;\n  background: url(./icon/no-message.png) no-repeat 50% 50%;\n  background-size: 68px;\n}\n[data-v-10b5e58f] .component-operate-detail {\n  margin-top: 12px;\n  font-size: 14px;\n  color: rgba(0, 0, 0, 0.65);\n  width: 260px;\n}\n[data-v-10b5e58f] .component-operate-detail .el-card__header {\n  padding: 0 10px;\n  background: #f7f9fa;\n}\n[data-v-10b5e58f] .component-operate-detail .el-card__header > .header {\n  display: flex;\n  height: 36px;\n  line-height: 36px;\n  justify-content: space-between;\n  letter-spacing: 1px;\n}\n[data-v-10b5e58f] .component-operate-detail .el-card__header > .header > a {\n  color: rgba(0, 0, 0, 0.65);\n}\n[data-v-10b5e58f] .component-operate-detail .el-card__header > .header > a:hover {\n  color: #409eff;\n}\n[data-v-10b5e58f] .component-operate-detail .el-card__body {\n  padding: 5px;\n}\n.icon-arrow[data-v-10b5e58f] {\n  position: absolute;\n  top: 46px;\n  right: 280px;\n  width: 16px;\n  height: 32px;\n  line-height: 32px;\n  background: #fff;\n  text-align: center;\n  box-shadow: -5px -5px 5px -4px #d9d6d6, -5px 5px 5px -4px #d9d6d6;\n  border-radius: 5px 0 0 5px;\n  cursor: pointer;\n}\n.icon-arrow i[data-v-10b5e58f] {\n  font-size: 14px;\n}\n.icon-arrow.active[data-v-10b5e58f] {\n  width: 30px;\n}\n\n/*# sourceMappingURL=index.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\components\\right-panel\\index.vue","index.vue"],names:[],mappings:"AAmPA;EACA,kBAAA;EACA,QAAA;EACA,MAAA;EACA,gBAAA;EACA,oBAAA;EACA,kBAAA;EACA,gBAAA;EACA,6BAAA;EACA,WAAA;EACA,yBAAA;EACA,YAAA;AClPA;ADoPA;EACA,2BAAA;AClPA;ADqPA;EACA,aAAA;EACA,6BAAA;EACA,cAAA;EACA,mBAAA;EACA,gBAAA;EACA,eAAA;ACnPA;ADsPA;EACA,WAAA;EACA,yBAAA;ACpPA;ADwPA;EACA,eAAA;EACA,0BAAA;EAQA,gBAAA;AC5PA;ADsPA;EACA,eAAA;EACA,gBAAA;EACA,WAAA;ACpPA;AD0PA;EACA,gBAAA;EACA,wDAAA;EACA,qBAAA;ACvPA;AD0PA;EACA,gBAAA;EACA,eAAA;EACA,0BAAA;EACA,YAAA;ACvPA;ADyPA;EACA,eAAA;EACA,mBAAA;ACvPA;ADyPA;EACA,aAAA;EACA,YAAA;EACA,iBAAA;EACA,8BAAA;EACA,mBAAA;ACvPA;ADyPA;EACA,0BAAA;ACvPA;ADyPA;EACA,cAAA;ACvPA;AD6PA;EACA,YAAA;AC3PA;AD+PA;EACA,kBAAA;EACA,SAAA;EACA,YAAA;EACA,WAAA;EACA,YAAA;EACA,iBAAA;EACA,gBAAA;EACA,kBAAA;EACA,iEAAA;EACA,0BAAA;EACA,eAAA;AC5PA;AD8PA;EACA,eAAA;AC5PA;AD+PA;EACA,WAAA;AC7PA;;AAEA,oCAAoC",file:"index.vue",sourcesContent:['<template>\r\n  <section class="right-panel-content" :class="{ \'is-collapse\': isCollapse }">\r\n    <h3>{{ $t("lang.rms.fed.textInformationOverview") }}</h3>\r\n\r\n    <el-autocomplete\r\n      v-model="searchValue"\r\n      class="autocomplete-input"\r\n      :fetch-suggestions="querySearch"\r\n      :trigger-on-focus="false"\r\n      :placeholder="$t(\'lang.rms.fed.pleaseEnterContent\')"\r\n      clearable\r\n      @select="onSelect"\r\n      @clear="clearSearch"\r\n    >\r\n      <el-select\r\n        slot="prepend"\r\n        v-model="searchType"\r\n        :placeholder="$t(\'lang.rms.fed.pleaseChoose\')"\r\n        style="min-width: 80px"\r\n        @change="changeSearchType"\r\n      >\r\n        <el-option v-if="rackLayers" :label="$t(\'lang.rms.fed.initialization.box\')" value="box" />\r\n        <el-option v-if="rackLayers" :label="$t(\'lang.rms.fed.lattice\')" value="lattice" />\r\n        <el-option :label="$t(\'lang.rms.fed.robot\')" value="robot" />\r\n        <el-option :label="$t(\'lang.rms.fed.cell\')" value="cell" />\r\n        <el-option :label="$t(\'lang.rms.fed.station\')" value="station" />\r\n      </el-select>\r\n    </el-autocomplete>\r\n\r\n    <div v-show="detailData" class="panel-detail">\r\n      <component :is="currentTabName" :detail-title="detailTitle" :detail-data="detailData || {}" />\r\n    </div>\r\n    <div v-show="!detailData" class="no-data" />\r\n\r\n    <div class="icon-arrow" :class="{ active: isCollapse }" @click.stop="isCollapse = !isCollapse">\r\n      <i :class="[!isCollapse ? \'el-icon-arrow-right\' : \'el-icon-arrow-left\']"></i>\r\n    </div>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport BoxDetail from "./components/detail-box.vue"; // 货箱\r\nimport LatticeDetail from "./components/detail-lattice.vue"; // 货位\r\nimport RobotDetail from "./components/detail-robot.vue"; // 机器人\r\nimport CellDetail from "./components/detail-cell.vue"; // 单元格\r\nimport StationDetail from "./components/detail-station.vue"; // ppp工作站\r\nexport default {\r\n  name: "Monitor3DMapRightPanel",\r\n  components: {\r\n    BoxDetail,\r\n    LatticeDetail,\r\n    RobotDetail,\r\n    CellDetail,\r\n    StationDetail,\r\n  },\r\n  props: ["rackLayers", "selectInfo", "detailData"],\r\n  data() {\r\n    return {\r\n      isCollapse: false,\r\n\r\n      searchType: this.rackLayers ? "box" : "robot",\r\n      searchValue: "",\r\n    };\r\n  },\r\n  computed: {\r\n    detailTitle() {\r\n      switch (this.searchType) {\r\n        case "box":\r\n          return this.$t("lang.rms.fed.optionBox");\r\n        case "lattice":\r\n          return this.$t("lang.rms.fed.lattice");\r\n        case "robot":\r\n          return this.$t("lang.rms.fed.robot");\r\n        case "cell":\r\n          return this.$t("lang.rms.fed.cell");\r\n        case "station":\r\n          return this.$t("lang.rms.fed.station");\r\n        default:\r\n          return "";\r\n      }\r\n    },\r\n    currentTabName() {\r\n      switch (this.searchType) {\r\n        case "box":\r\n          return "BoxDetail";\r\n        case "lattice":\r\n          return "LatticeDetail";\r\n        case "robot":\r\n          return "RobotDetail";\r\n        case "cell":\r\n          return "CellDetail";\r\n        case "station":\r\n          return "StationDetail";\r\n        default:\r\n          return "";\r\n      }\r\n    },\r\n  },\r\n  watch: {\r\n    detailData(nv, ov) {\r\n      const searchType = this.searchType;\r\n      if (!nv) return (this.searchValue = "");\r\n      let data = nv;\r\n      switch (searchType) {\r\n        case "box":\r\n          if (data["boxCode"] && this.searchValue !== data["boxCode"]) {\r\n            this.searchValue = data.boxCode;\r\n          }\r\n          break;\r\n        case "lattice":\r\n          if (data["latticeCode"] && this.searchValue !== data["latticeCode"]) {\r\n            this.searchValue = data.latticeCode;\r\n          }\r\n          break;\r\n        case "cell":\r\n          if (data["cellCode"] && this.searchValue !== data["cellCode"]) {\r\n            this.searchValue = data.cellCode;\r\n          }\r\n          break;\r\n        case "robot":\r\n          if (data["id"] && this.searchValue !== data["id"]) {\r\n            this.searchValue = data.id.toString();\r\n          }\r\n          break;\r\n        case "poppickStation":\r\n          if (data["id"] && this.searchValue !== data["id"]) {\r\n            this.searchValue = data.id.toString();\r\n          }\r\n          break;\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    onSelect() {\r\n      console.log("onSelect", this.searchType);\r\n      const searchType = this.searchType;\r\n      switch (searchType) {\r\n        case "box":\r\n          this._getRackInfoByBox(this.searchValue);\r\n          break;\r\n        case "lattice":\r\n          // 后台接口404 感觉无用啊\r\n          // this._getRackInfoByLattice(this.searchValue);\r\n          this.$emit("searchSelect", { actionType: this.searchType, data: this.searchValue });\r\n          break;\r\n        case "cell":\r\n        case "robot":\r\n        case "station":\r\n          this.$emit("searchSelect", { actionType: this.searchType, data: this.searchValue });\r\n          break;\r\n      }\r\n    },\r\n    clearSearch() {\r\n      this.searchValue = "";\r\n      this.$emit("clearSelect");\r\n    },\r\n    changeSearchType() {\r\n      this.clearSearch();\r\n      this.$emit("changeSearchType", this.searchType);\r\n    },\r\n    async querySearch(queryString, cb) {\r\n      console.log("querySearch", queryString);\r\n      const searchType = this.searchType;\r\n      switch (searchType) {\r\n        case "box":\r\n        case "cell":\r\n        case "lattice":\r\n        case "station":\r\n          cb([{ value: queryString }]);\r\n          break;\r\n        case "robot":\r\n          if (queryString.length < 1) {\r\n            cb([]);\r\n          } else {\r\n            try {\r\n              let res = await this._getRobotQueryList(queryString);\r\n              const robotQueryList = res.data || [];\r\n              cb(\r\n                robotQueryList.map(item => {\r\n                  return { value: item.toString() };\r\n                }),\r\n              );\r\n            } catch (e) {\r\n              cb([]);\r\n            }\r\n          }\r\n          break;\r\n      }\r\n    },\r\n    _getRobotQueryList(queryString) {\r\n      const url = "/athena/robotStatus/queryRobotIds";\r\n      return $req.get(url, {\r\n        info: queryString,\r\n        robotStates: "", // 模糊查询出来的机器人状态，传空默认所有状态\r\n      });\r\n    },\r\n    _getRackInfoByBox(boxCode) {\r\n      $req\r\n        .get("/athena/box/getRackInfoByBox", {\r\n          boxCode,\r\n        })\r\n        .then(res => {\r\n          const { latticeCode } = res.data || {};\r\n          if (latticeCode) {\r\n            this.$emit("searchSelect", { actionType: this.searchType, data: latticeCode });\r\n          } else {\r\n            console.error(\r\n              "SearchBoxError:",\r\n              "未收到当前rackCode的latticeCode返回值,api-/athena/box/getRackInfoByBox",\r\n            );\r\n          }\r\n        });\r\n    },\r\n    _getRackInfoByLattice(latticeCode) {\r\n      $req\r\n        .get("/athena/box/getRackInfoByLattic", {\r\n          latticCode: latticeCode,\r\n        })\r\n        .then(res => {\r\n          const { rackCode } = res.data || {};\r\n          if (rackCode) {\r\n            // EventBus3D.emit("MonitorMap3D", "MapElementSelectTrigger", {\r\n            //   code: latticeCode,\r\n            //   type: "rack_lattice",\r\n            //   rackCode,\r\n            // });\r\n            // EventBus3D.emit("MonitorMap3D", "WorkerDataQuery", {\r\n            //   code: rackCode,\r\n            //   latticeCode,\r\n            //   type: "rack",\r\n            // });\r\n            // EventBus3D.emit("MonitorMap3D", "MapFilterElement", {\r\n            //   code: latticeCode,\r\n            //   type: "lattice",\r\n            // });\r\n          }\r\n        });\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n\r\n<style scoped lang="scss">\r\n.right-panel-content {\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  max-height: calc(100%);\r\n  padding-bottom: 10px;\r\n  border-radius: 4px;\r\n  background: #fff;\r\n  box-shadow: 0 3px 5px #d9d6d6;\r\n  z-index: 10;\r\n  transition-duration: 0.2s;\r\n  width: 280px;\r\n\r\n  &.is-collapse {\r\n    transform: translateX(100%);\r\n  }\r\n\r\n  > h3 {\r\n    padding: 10px;\r\n    border-bottom: 1px solid #ddd;\r\n    color: #4a8bce;\r\n    background: #f5f7fa;\r\n    font-weight: 600;\r\n    font-size: 15px;\r\n  }\r\n\r\n  > .autocomplete-input {\r\n    width: 100%;\r\n    padding: 10px 10px 0 10px;\r\n  }\r\n}\r\n\r\n.panel-detail {\r\n  padding: 0 10px;\r\n  height: calc(100% - 100px);\r\n\r\n  > h5 {\r\n    font-size: 13px;\r\n    font-weight: 600;\r\n    color: #666;\r\n  }\r\n\r\n  overflow-y: auto;\r\n}\r\n\r\n.no-data {\r\n  min-height: 80px;\r\n  background: url(./icon/no-message.png) no-repeat 50% 50%;\r\n  background-size: 68px;\r\n}\r\n\r\n::v-deep .component-operate-detail {\r\n  margin-top: 12px;\r\n  font-size: 14px;\r\n  color: rgba(0, 0, 0, 0.65);\r\n  width: 260px;\r\n\r\n  .el-card__header {\r\n    padding: 0 10px;\r\n    background: #f7f9fa;\r\n\r\n    > .header {\r\n      display: flex;\r\n      height: 36px;\r\n      line-height: 36px;\r\n      justify-content: space-between;\r\n      letter-spacing: 1px;\r\n\r\n      > a {\r\n        color: rgba(0, 0, 0, 0.65);\r\n\r\n        &:hover {\r\n          color: #409eff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-card__body {\r\n    padding: 5px;\r\n  }\r\n}\r\n\r\n.icon-arrow {\r\n  position: absolute;\r\n  top: 46px;\r\n  right: 280px;\r\n  width: 16px;\r\n  height: 32px;\r\n  line-height: 32px;\r\n  background: #fff;\r\n  text-align: center;\r\n  box-shadow: -5px -5px 5px -4px #d9d6d6, -5px 5px 5px -4px #d9d6d6;\r\n  border-radius: 5px 0 0 5px;\r\n  cursor: pointer;\r\n\r\n  i {\r\n    font-size: 14px;\r\n  }\r\n\r\n  &.active {\r\n    width: 30px;\r\n  }\r\n}\r\n</style>\r\n',".right-panel-content {\n  position: absolute;\n  right: 0;\n  top: 0;\n  max-height: 100%;\n  padding-bottom: 10px;\n  border-radius: 4px;\n  background: #fff;\n  box-shadow: 0 3px 5px #d9d6d6;\n  z-index: 10;\n  transition-duration: 0.2s;\n  width: 280px;\n}\n.right-panel-content.is-collapse {\n  transform: translateX(100%);\n}\n.right-panel-content > h3 {\n  padding: 10px;\n  border-bottom: 1px solid #ddd;\n  color: #4a8bce;\n  background: #f5f7fa;\n  font-weight: 600;\n  font-size: 15px;\n}\n.right-panel-content > .autocomplete-input {\n  width: 100%;\n  padding: 10px 10px 0 10px;\n}\n\n.panel-detail {\n  padding: 0 10px;\n  height: calc(100% - 100px);\n  overflow-y: auto;\n}\n.panel-detail > h5 {\n  font-size: 13px;\n  font-weight: 600;\n  color: #666;\n}\n\n.no-data {\n  min-height: 80px;\n  background: url(./icon/no-message.png) no-repeat 50% 50%;\n  background-size: 68px;\n}\n\n::v-deep .component-operate-detail {\n  margin-top: 12px;\n  font-size: 14px;\n  color: rgba(0, 0, 0, 0.65);\n  width: 260px;\n}\n::v-deep .component-operate-detail .el-card__header {\n  padding: 0 10px;\n  background: #f7f9fa;\n}\n::v-deep .component-operate-detail .el-card__header > .header {\n  display: flex;\n  height: 36px;\n  line-height: 36px;\n  justify-content: space-between;\n  letter-spacing: 1px;\n}\n::v-deep .component-operate-detail .el-card__header > .header > a {\n  color: rgba(0, 0, 0, 0.65);\n}\n::v-deep .component-operate-detail .el-card__header > .header > a:hover {\n  color: #409eff;\n}\n::v-deep .component-operate-detail .el-card__body {\n  padding: 5px;\n}\n\n.icon-arrow {\n  position: absolute;\n  top: 46px;\n  right: 280px;\n  width: 16px;\n  height: 32px;\n  line-height: 32px;\n  background: #fff;\n  text-align: center;\n  box-shadow: -5px -5px 5px -4px #d9d6d6, -5px 5px 5px -4px #d9d6d6;\n  border-radius: 5px 0 0 5px;\n  cursor: pointer;\n}\n.icon-arrow i {\n  font-size: 14px;\n}\n.icon-arrow.active {\n  width: 30px;\n}\n\n/*# sourceMappingURL=index.vue.map */"]},media:void 0})}),Ft,"data-v-10b5e58f",false,undefined,!1,ut,void 0,void 0);window._iconfont_svg_string_2976199='<svg><symbol id="icon-gis_fushi" viewBox="0 0 1024 1024"><path d="M602.496 778.048l45.248 45.248L512 959.04l-135.744-135.744 45.248-45.248 90.432 90.56 90.56-90.56zM512 352a160 160 0 1 1 0 320 160 160 0 0 1 0-320z m-311.296 24.256l45.248 45.248L155.52 512l90.496 90.496-45.248 45.248L64.96 512l135.744-135.744z m622.592 0L959.04 512l-135.744 135.744-45.248-45.248 90.56-90.432-90.56-90.56 45.248-45.248zM512 416a96 96 0 1 0 0 192 96 96 0 0 0 0-192z m0-351.04l135.744 135.744-45.248 45.248-90.432-90.56-90.56 90.56-45.248-45.248L512 64.96z" fill="#2C374D" ></path></symbol><symbol id="icon-konghuowei" viewBox="0 0 1024 1024"><path d="M891.050667 248.149333A101.888 101.888 0 0 0 820.352 213.333333H205.056a114.773333 114.773333 0 0 0-73.088 33.536C112.426667 266.410667 0 447.914667 0 486.144v244.053333c0.170667 18.432 7.68 36.053333 20.821333 48.896 13.397333 12.970667 31.317333 20.181333 49.92 20.053334h882.56c18.56 0.128 36.522667-7.082667 49.877334-20.096 13.141333-12.885333 20.650667-30.378667 20.821333-48.810667v-244.053333c0-38.272-132.992-238.037333-132.992-238.037334z m-227.669334 216.362667a32.981333 32.981333 0 0 0-33.792 23.765333v4.992a115.285333 115.285333 0 0 1-230.4-6.570666 36.437333 36.437333 0 0 0-32.682666-22.442667H76.288l98.901333-167.253333s19.797333-32.426667 38.613334-32.213334h616.021333c13.866667 7.296 25.344 18.346667 33.237333 31.914667l97.066667 167.808h-296.874667 0.128z"  ></path></symbol><symbol id="icon-rack" viewBox="0 0 1024 1024"><path d="M164.38571167 859.61428833m0-43.45178604l0-608.32500458q0-43.45178603 43.45178604-43.45178604l0 0q43.45178603 0 43.45178604 43.45178604l0 608.32500458q0 43.45178603-43.45178604 43.45178604l0 0q-43.45178603 0-43.45178604-43.45178604Z"  ></path><path d="M772.71071625 859.61428833m0-43.45178604l0-608.32500458q0-43.45178603 43.45178604-43.45178604l0 0q43.45178603 0 43.45178604 43.45178604l0 608.32500458q0 43.45178603-43.45178604 43.45178604l0 0q-43.45178603 0-43.45178604-43.45178604Z"  ></path><path d="M200.59553315 787.19464469V671.32321572h622.8089337v115.87142897zM200.59553315 461.30624939V345.43482041h622.8089337v115.87142898z"  ></path></symbol><symbol id="icon-box" viewBox="0 0 1024 1024"><path d="M1015.7056 481.0752C1021.2352 483.84 1024 488.6528 1024 495.5136c0 6.9632-2.7648 11.6736-8.2944 14.4384L858.8288 592.4864l0 233.2672c0 6.9632-2.7648 11.6736-8.2944 14.4384L520.2944 1021.952 518.144 1021.952C516.8128 1023.2832 514.7648 1024 512 1024c-2.7648 0-4.8128-0.7168-6.144-2.048l-2.048 0L173.4656 840.2944C167.936 837.5296 165.1712 832.7168 165.1712 825.856L165.1712 592.4864 8.2944 509.952C2.7648 507.1872 0 502.3744 0 495.5136c0-6.8608 2.7648-11.6736 8.2944-14.4384l125.952-68.096-125.952-68.096C2.7648 342.016 0 337.2032 0 330.3424c0-6.8608 2.7648-11.6736 8.2944-14.4384L355.1232 134.144c5.5296-1.3312 11.0592-1.3312 16.4864 0L512 210.5344 652.3904 134.144c5.5296-1.3312 11.0592-1.3312 16.4864 0l346.8288 181.6576C1021.2352 318.6688 1024 323.4816 1024 330.3424c0 6.9632-2.7648 11.6736-8.2944 14.4384L889.856 412.8768 1015.7056 481.0752zM169.2672 394.3424l307.6096-165.1712L363.3152 167.2192 51.6096 330.3424 169.2672 394.3424zM51.6096 495.5136l311.7056 163.1232 113.5616-61.952L169.2672 431.5136 51.6096 495.5136zM198.144 815.5136l297.2672 163.1232L495.4112 625.5616l-123.904 66.048c-1.3312 1.3312-4.096 2.048-8.2944 2.048-4.096 0-6.8608-0.7168-8.2944-2.048L198.144 609.0752 198.144 815.5136zM819.6096 412.8768 512 247.7056 204.3904 412.8768 512 578.048 819.6096 412.8768zM825.856 609.0752 668.8768 691.6096c-1.3312 1.3312-4.096 2.048-8.2944 2.048s-6.8608-0.7168-8.2944-2.048l-123.904-66.048 0 353.0752 297.2672-163.1232L825.6512 609.0752zM547.1232 229.1712l307.6096 165.1712 117.6576-64L660.6848 167.2192 547.1232 229.1712zM972.3904 495.5136 854.7328 431.5136 547.1232 596.6848l113.5616 61.952L972.3904 495.5136z"  ></path></symbol><symbol id="icon-daolu" viewBox="0 0 1024 1024"><path d="M512 85.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v128a42.666667 42.666667 0 1 1-85.333334 0V128a42.666667 42.666667 0 0 1 42.666667-42.666667z m0 298.666667a42.666667 42.666667 0 0 1 42.666667 42.666667v170.666666a42.666667 42.666667 0 1 1-85.333334 0v-170.666666a42.666667 42.666667 0 0 1 42.666667-42.666667z m0 341.333333a42.666667 42.666667 0 0 1 42.368 37.674667L554.666667 768v128a42.666667 42.666667 0 1 1-85.333334 0v-128a42.666667 42.666667 0 0 1 37.674667-42.368L512 725.333333zM228.650667 88.917333l8.490666 1.066667a42.666667 42.666667 0 0 1 37.12 47.530667L180.693333 899.797333a42.666667 42.666667 0 0 1-47.573333 37.12l-8.448-1.024a42.666667 42.666667 0 0 1-37.162667-47.530666L181.12 126.08a42.666667 42.666667 0 0 1 47.530667-37.12z m581.205333-0.938666a42.666667 42.666667 0 0 1 47.530667 37.162666l93.610666 762.282667a42.666667 42.666667 0 0 1-37.162666 47.530667l-8.490667 1.066666a42.666667 42.666667 0 0 1-47.530667-37.162666L764.202667 136.576a42.666667 42.666667 0 0 1 37.162666-47.530667z"  ></path></symbol><symbol id="icon-show-help-doc" viewBox="0 0 1024 1024"><path d="M931.5 345.47a450.16 450.16 0 1 0 35.3 174.75 447.33 447.33 0 0 0-35.3-174.75zM517.9 904.13c-212 0-383.9-171.88-383.9-383.9s171.88-383.9 383.9-383.9 383.9 171.88 383.9 383.9-171.87 383.9-383.9 383.9z"  ></path><path d="M515.68 241A173.18 173.18 0 0 0 342.5 413.87H406a108.87 108.87 0 1 1 169.7 90.29A140.73 140.73 0 0 0 555.18 515l-1 0.43v0.24A141 141 0 0 0 489.94 634c0 1.95 0 3.89 0.13 5.81v71.41h64.46V634a76.37 76.37 0 0 1 30.58-61.17A173.2 173.2 0 0 0 515.68 241z"  ></path><path d="M522.24 781.07m-32.18 0a32.18 32.18 0 1 0 64.36 0 32.18 32.18 0 1 0-64.36 0Z"  ></path></symbol><symbol id="icon-full-screen" viewBox="0 0 1024 1024"><path d="M604.84266667 580.29155553c-8.192 0-13.65333333 2.73066667-19.11466667 8.19200001-10.92266667 10.92266667-10.92266667 27.30666667 0 38.22933333l234.83733333 234.83733333c10.92266667 10.92266667 27.30666667 10.92266667 38.22933334 0s10.92266667-27.30666667 0-38.22933333L623.95733333 588.48355554c-5.46133333-5.46133333-13.65333333-8.192-19.11466666-8.19200001zM845.14133333 692.24888886c-16.384 0-27.30666667 10.92266667-27.30666666 27.30666667v101.03466667H716.8c-16.384 0-27.30666667 10.92266667-27.30666667 27.30666667s10.92266667 27.30666667 27.30666667 27.30666666h128.34133333c16.384 0 27.30666667-10.92266667 27.30666667-27.30666666V719.55555553c0-16.384-10.92266667-27.30666667-27.30666667-27.30666667zM181.58933333 159.76888887c-8.192 0-13.65333333 2.73066667-19.11466666 8.19199999-10.92266667 10.92266667-10.92266667 27.30666667 0 38.22933334l234.83733333 234.83733334c10.92266667 10.92266667 27.30666667 10.92266667 38.22933333 0s10.92266667-27.30666667 0-38.22933334L200.704 167.96088886c-5.46133333-5.46133333-10.92266667-8.192-19.11466667-8.19199999zM307.2 154.30755553H178.85866667c-16.384 0-27.30666667 10.92266667-27.30666667 27.30666667V309.95555553c0 16.384 10.92266667 27.30666667 27.30666667 27.30666668s27.30666667-10.92266667 27.30666666-27.30666668V208.92088887H307.2c16.384 0 27.30666667-10.92266667 27.30666667-27.30666667s-10.92266667-27.30666667-27.30666667-27.30666667z m532.48 2.73066667c-8.192 0-13.65333333 2.73066667-19.11466667 8.19200001L585.728 400.06755553c-10.92266667 10.92266667-10.92266667 27.30666667 0 38.22933333s27.30666667 10.92266667 38.22933333 0L858.79466667 203.45955553c10.92266667-10.92266667 10.92266667-27.30666667 0-38.22933332-5.46133333-5.46133333-13.65333333-8.192-19.11466667-8.19200001z m5.46133333-2.73066667H716.8c-16.384 0-27.30666667 10.92266667-27.30666667 27.30666667s10.92266667 27.30666667 27.30666667 27.30666667h101.03466667V309.95555553c0 16.384 10.92266667 27.30666667 27.30666666 27.30666668s27.30666667-10.92266667 27.30666667-27.30666668V181.6142222c0-16.384-10.92266667-27.30666667-27.30666667-27.30666667zM419.15733333 580.29155553c-8.192 0-13.65333333 2.73066667-19.11466666 8.19200001L162.47466667 823.32088887c-10.92266667 10.92266667-10.92266667 27.30666667 0 38.22933333s27.30666667 10.92266667 38.22933333 0l234.83733333-234.83733333c10.92266667-10.92266667 10.92266667-27.30666667 0-38.22933333-2.73066667-5.46133333-10.92266667-8.192-16.384-8.19200001zM178.85866667 692.24888886c-16.384 0-27.30666667 10.92266667-27.30666667 27.30666667v128.34133334c0 16.384 10.92266667 27.30666667 27.30666667 27.30666666H307.2c16.384 0 27.30666667-10.92266667 27.30666667-27.30666666s-10.92266667-27.30666667-27.30666667-27.30666667h-101.03466667V719.55555553c0-16.384-13.65333333-27.30666667-27.30666666-27.30666667z"  ></path></symbol><symbol id="icon-shelf-heat-display" viewBox="0 0 1024 1024"><path d="M519.770353 85.33333332c-108.784941 0-197.330824 88.545882-197.330824 197.330824 0 66.620235 34.695529 129.144471 91.075765 165.406117v374.302118C413.515294 880.92109832 461.221647 928.62745132 519.770353 928.62745132s106.255059-47.706353 106.255059-106.255059V448.07027432c56.380235-36.261647 91.075765-98.846118 91.075764-165.406117C717.161412 173.87921532 628.615529 85.33333332 519.770353 85.33333332z m0 812.935529c-41.863529 0-75.896471-34.032941-75.896471-75.89647V430.90321532l-7.529411-4.397176c-52.284235-30.418824-83.546353-84.208941-83.546353-143.781647 0-92.099765 74.872471-166.972235 166.972235-166.972235s166.972235 74.872471 166.972235 166.972235c0 59.572706-31.201882 113.302588-83.546353 143.781647l-7.529411 4.336941v391.529412c0 41.863529-34.032941 75.896471-75.896471 75.89647z m-199.439059-60.717176c-7.469176 0-13.492706-6.023529-13.492706-13.492706s6.023529-13.492706 13.492706-13.492706h77.342118v26.985412h-77.342118z m0-215.883294c-7.469176 0-13.492706-6.023529-13.492706-13.492706s6.023529-13.492706 13.492706-13.492706h77.342118v26.985412h-77.342118z m18.010353 161.91247c-7.469176 0-13.492706-6.023529-13.492706-13.492705s6.023529-13.492706 13.492706-13.492706h59.392v26.985411h-59.392z m0-53.970823c-7.469176 0-13.492706-6.023529-13.492706-13.492706s6.023529-13.492706 13.492706-13.492706h59.392v26.985412h-59.392z m0-53.970824c-7.469176 0-13.492706-6.023529-13.492706-13.492706s6.023529-13.492706 13.492706-13.492705h59.392V675.63921532h-59.392z m266.179765 129.024H434.898824v50.657883s59.151059 58.187294 84.871529 58.127059c25.720471 0 84.751059-58.127059 84.751059-58.127059v-50.657883z"  ></path></symbol><symbol id="icon-zoom-out" viewBox="0 0 1024 1024"><path d="M513.138 126.862c51.863 0 102.115 10.145 149.428 30.151 45.7 19.343 86.755 47.028 122.027 82.3 35.27 35.27 62.957 76.326 82.299 122.026 20.006 47.313 30.151 97.565 30.151 149.428s-10.145 102.116-30.151 149.429c-19.342 45.7-47.028 86.755-82.3 122.026-35.27 35.271-76.325 62.957-122.026 82.3-47.218 20.1-97.565 30.34-149.428 30.34s-102.116-10.24-149.428-30.246c-45.701-19.342-86.756-47.028-122.027-82.299-35.271-35.271-62.957-76.326-82.3-122.027-20.005-47.312-30.15-97.564-30.15-149.428s10.145-102.21 30.15-149.523c19.343-45.7 47.029-86.755 82.3-122.026 35.271-35.272 76.326-62.957 122.027-82.3 47.312-20.006 97.659-30.15 149.428-30.15m0-42.667c-235.615 0-426.667 191.051-426.667 426.666S277.523 937.53 513.138 937.53s426.666-191.052 426.666-426.667S748.847 84.196 513.138 84.196z"  ></path><path d="M712.344 532.196H314.12c-11.757 0-21.333-9.577-21.333-21.334s9.576-21.333 21.333-21.333h398.223c11.757 0 21.333 9.576 21.333 21.333s-9.576 21.334-21.333 21.334z"  ></path><path d="M513.233 731.307c-11.757 0-21.334-9.577-21.334-21.334V311.751c0-11.757 9.577-21.333 21.334-21.333s21.333 9.576 21.333 21.333v398.222c0 11.757-9.576 21.334-21.333 21.334z"  ></path></symbol><symbol id="icon-zoom-in" viewBox="0 0 1024 1024"><path d="M510.293 126.862c51.864 0 102.116 10.145 149.428 30.151 45.701 19.343 86.756 47.028 122.027 82.3 35.271 35.27 62.957 76.326 82.3 122.026 20.005 47.313 30.15 97.565 30.15 149.428s-10.145 102.116-30.15 149.429c-19.343 45.7-47.029 86.755-82.3 122.026-35.271 35.271-76.326 62.957-122.027 82.3-47.217 20.1-97.564 30.34-149.428 30.34s-102.115-10.24-149.428-30.246c-45.7-19.342-86.755-47.028-122.026-82.299-35.272-35.271-62.958-76.326-82.3-122.027-20.006-47.312-30.15-97.564-30.15-149.428s10.144-102.21 30.15-149.523c19.342-45.7 47.028-86.755 82.3-122.026 35.27-35.272 76.325-62.957 122.026-82.3 47.313-20.006 97.66-30.15 149.428-30.15m0-42.667c-235.614 0-426.666 191.051-426.666 426.666S274.679 937.53 510.293 937.53 936.96 746.477 936.96 510.862 746.003 84.196 510.293 84.196z"  ></path><path d="M709.5 532.196H311.276c-11.757 0-21.333-9.577-21.333-21.334s9.576-21.333 21.333-21.333h398.222c11.757 0 21.334 9.576 21.334 21.333s-9.577 21.334-21.334 21.334z"  ></path></symbol><symbol id="icon-to2D" viewBox="0 0 1024 1024"><path d="M534.65 935.69c0.19-0.23 0.36-0.46 0.55-0.69 0.14-0.18 0.28-0.35 0.41-0.53 0.2-0.26 0.38-0.53 0.57-0.8 0.11-0.16 0.22-0.31 0.33-0.47 0.19-0.28 0.36-0.56 0.54-0.84l0.3-0.48c0.17-0.28 0.33-0.56 0.49-0.85 0.1-0.17 0.2-0.35 0.29-0.52l0.42-0.81 0.3-0.6c0.12-0.25 0.23-0.5 0.34-0.75 0.1-0.23 0.21-0.47 0.31-0.71 0.09-0.22 0.18-0.45 0.26-0.67 0.1-0.27 0.21-0.54 0.31-0.82 0.07-0.19 0.13-0.39 0.2-0.59 0.1-0.3 0.2-0.61 0.3-0.92l0.15-0.52c0.09-0.33 0.19-0.66 0.27-1 0.04-0.16 0.07-0.32 0.11-0.49 0.08-0.35 0.16-0.69 0.23-1.04l0.09-0.49 0.18-1.04c0.03-0.19 0.05-0.38 0.07-0.56 0.04-0.32 0.09-0.65 0.12-0.98l0.06-0.75 0.06-0.81c0.02-0.47 0.03-0.94 0.04-1.41v-0.17-0.17c0-0.47-0.01-0.94-0.04-1.41-0.01-0.27-0.04-0.54-0.06-0.81-0.02-0.25-0.03-0.5-0.06-0.75-0.03-0.33-0.08-0.65-0.12-0.98-0.02-0.19-0.04-0.38-0.07-0.56-0.05-0.35-0.11-0.7-0.18-1.04l-0.09-0.49c-0.07-0.35-0.15-0.69-0.23-1.04-0.04-0.16-0.07-0.32-0.11-0.49-0.08-0.33-0.18-0.67-0.27-1l-0.15-0.52c-0.09-0.31-0.19-0.61-0.3-0.92-0.07-0.2-0.13-0.39-0.2-0.59-0.1-0.27-0.2-0.54-0.31-0.82-0.09-0.22-0.17-0.45-0.26-0.67-0.1-0.24-0.2-0.47-0.31-0.71-0.11-0.25-0.22-0.5-0.34-0.75l-0.3-0.6c-0.14-0.27-0.27-0.54-0.42-0.81-0.09-0.18-0.19-0.35-0.29-0.52-0.16-0.28-0.32-0.57-0.49-0.85l-0.3-0.48c-0.18-0.28-0.35-0.56-0.54-0.84-0.11-0.16-0.22-0.32-0.33-0.47-0.19-0.27-0.37-0.53-0.57-0.8-0.13-0.18-0.27-0.35-0.41-0.53-0.18-0.23-0.36-0.47-0.55-0.69-0.2-0.24-0.4-0.47-0.6-0.7-0.14-0.16-0.27-0.32-0.42-0.48-0.36-0.4-0.73-0.79-1.11-1.17L442 802.24c-12.5-12.5-32.76-12.5-45.25 0-12.5 12.5-12.5 32.76 0 45.25l25.46 25.46c-65.12-15.79-124.86-49.14-173.44-97.73C179.02 705.48 140.6 612.74 140.6 514.1c0-17.67-14.33-32-32-32s-32 14.33-32 32c0 58.48 11.46 115.23 34.06 168.67 21.83 51.6 53.06 97.94 92.85 137.72s86.12 71.02 137.72 92.85c53.44 22.6 110.19 34.06 168.67 34.06 0.53 0 1.06-0.01 1.58-0.04 0.25-0.01 0.5-0.04 0.75-0.05l0.81-0.06c0.31-0.03 0.62-0.07 0.93-0.11 0.2-0.03 0.41-0.05 0.61-0.08 0.34-0.05 0.67-0.11 1-0.17l0.53-0.09c0.34-0.07 0.68-0.14 1.01-0.22l0.51-0.12c0.33-0.08 0.65-0.17 0.97-0.26 0.18-0.05 0.37-0.1 0.55-0.16 0.3-0.09 0.6-0.19 0.89-0.29 0.2-0.07 0.41-0.14 0.61-0.21 0.27-0.09 0.53-0.2 0.79-0.3 0.23-0.09 0.46-0.18 0.7-0.27 0.23-0.09 0.45-0.2 0.68-0.3 0.26-0.11 0.52-0.23 0.77-0.35 0.2-0.09 0.39-0.19 0.58-0.29 0.28-0.14 0.56-0.28 0.83-0.43 0.17-0.09 0.33-0.19 0.5-0.28 0.29-0.16 0.58-0.33 0.87-0.5 0.15-0.09 0.3-0.19 0.46-0.29 0.29-0.18 0.58-0.36 0.86-0.55 0.15-0.1 0.3-0.21 0.46-0.32 0.27-0.19 0.55-0.38 0.81-0.58 0.17-0.13 0.34-0.27 0.51-0.4 0.24-0.18 0.48-0.37 0.71-0.56 0.23-0.19 0.46-0.39 0.69-0.59 0.16-0.14 0.33-0.28 0.5-0.43 0.79-0.71 1.54-1.46 2.25-2.25l0.42-0.48c0.21-0.21 0.41-0.44 0.61-0.68zM909.14 345.43c-21.83-51.6-53.06-97.94-92.85-137.72s-86.12-71.02-137.72-92.85C625.13 92.26 568.38 80.8 509.9 80.8h-0.08c-0.5 0-1 0.01-1.5 0.04-0.26 0.01-0.51 0.04-0.77 0.06-0.26 0.02-0.53 0.03-0.79 0.06-0.32 0.03-0.63 0.07-0.95 0.11-0.2 0.03-0.39 0.05-0.59 0.08-0.34 0.05-0.68 0.11-1.01 0.17l-0.52 0.09c-0.34 0.07-0.68 0.14-1.01 0.22l-0.51 0.12c-0.33 0.08-0.65 0.17-0.97 0.26-0.18 0.05-0.37 0.1-0.55 0.16-0.3 0.09-0.6 0.19-0.89 0.29-0.2 0.07-0.41 0.13-0.61 0.21-0.27 0.09-0.53 0.2-0.79 0.3-0.23 0.09-0.47 0.18-0.7 0.27-0.23 0.09-0.45 0.19-0.68 0.29-0.26 0.11-0.52 0.23-0.78 0.35-0.19 0.09-0.38 0.19-0.58 0.29-0.28 0.14-0.56 0.28-0.84 0.43-0.17 0.09-0.33 0.18-0.49 0.28-0.29 0.16-0.59 0.33-0.87 0.5-0.15 0.09-0.3 0.18-0.44 0.28-0.29 0.18-0.59 0.37-0.87 0.56-0.15 0.1-0.29 0.2-0.44 0.3-0.28 0.19-0.56 0.39-0.83 0.59-0.17 0.12-0.33 0.25-0.49 0.38-0.25 0.19-0.49 0.38-0.73 0.58-0.22 0.18-0.43 0.37-0.65 0.56-0.18 0.15-0.36 0.3-0.53 0.46-0.79 0.71-1.53 1.46-2.25 2.25l-0.45 0.51c-0.19 0.22-0.39 0.44-0.57 0.67-0.19 0.23-0.37 0.47-0.56 0.71-0.13 0.17-0.27 0.34-0.4 0.51-0.2 0.27-0.39 0.54-0.57 0.81a20.026 20.026 0 0 0-0.86 1.31c-0.1 0.16-0.2 0.31-0.29 0.47-0.17 0.28-0.33 0.57-0.49 0.85-0.1 0.17-0.19 0.34-0.29 0.52-0.14 0.27-0.28 0.54-0.42 0.82l-0.3 0.6c-0.12 0.25-0.23 0.5-0.34 0.75-0.1 0.23-0.21 0.47-0.3 0.7-0.09 0.22-0.18 0.45-0.27 0.67-0.1 0.27-0.21 0.54-0.31 0.81-0.07 0.2-0.13 0.39-0.2 0.59-0.1 0.3-0.2 0.61-0.29 0.91-0.05 0.17-0.1 0.35-0.15 0.53l-0.27 0.99c-0.04 0.16-0.08 0.33-0.11 0.49-0.08 0.34-0.16 0.69-0.23 1.04l-0.09 0.5c-0.06 0.34-0.12 0.69-0.17 1.03-0.03 0.19-0.05 0.38-0.07 0.58l-0.12 0.96c-0.03 0.26-0.04 0.52-0.06 0.78l-0.06 0.78c-0.02 0.5-0.04 1-0.04 1.5V112.9c0 0.5 0.01 1 0.04 1.49 0.01 0.26 0.04 0.52 0.06 0.77 0.02 0.26 0.03 0.52 0.06 0.78 0.03 0.32 0.07 0.63 0.11 0.95 0.03 0.2 0.05 0.4 0.08 0.59 0.05 0.34 0.11 0.68 0.17 1.02 0.03 0.17 0.06 0.34 0.09 0.52 0.07 0.34 0.14 0.68 0.22 1.02 0.04 0.17 0.07 0.34 0.12 0.51 0.08 0.33 0.17 0.65 0.26 0.98l0.15 0.54c0.09 0.3 0.19 0.6 0.29 0.9 0.07 0.2 0.13 0.41 0.21 0.61 0.09 0.26 0.2 0.53 0.3 0.79 0.09 0.23 0.18 0.47 0.28 0.7 0.09 0.23 0.19 0.45 0.29 0.68 0.11 0.26 0.23 0.52 0.35 0.78 0.09 0.19 0.19 0.38 0.28 0.57 0.14 0.28 0.28 0.57 0.43 0.85l0.27 0.48c0.17 0.3 0.33 0.6 0.51 0.89 0.09 0.14 0.18 0.28 0.27 0.43 0.19 0.3 0.37 0.6 0.57 0.89 0.09 0.14 0.19 0.27 0.29 0.41 0.2 0.29 0.4 0.58 0.61 0.86 0.11 0.15 0.23 0.3 0.34 0.44 0.2 0.26 0.4 0.52 0.61 0.78 0.16 0.19 0.32 0.37 0.48 0.56l0.54 0.63c0.29 0.32 0.59 0.64 0.9 0.95 0.06 0.06 0.12 0.13 0.19 0.2L577.81 226c6.25 6.25 14.44 9.37 22.63 9.37s16.38-3.12 22.63-9.37c12.5-12.5 12.5-32.76 0-45.25l-25.46-25.46c65.12 15.79 124.86 49.14 173.44 97.73 69.74 69.7 108.15 162.44 108.15 261.08 0 17.67 14.33 32 32 32s32-14.33 32-32c0-58.48-11.46-115.23-34.06-168.67z" fill="#666666" ></path><path d="M299.9 688.16h160.54c8.84 0 16-7.16 16-16s-7.16-16-16-16H315.9V527.54H460.44c8.28 0 15.1-6.3 15.92-14.36 0.05-0.54 0.08-1.08 0.08-1.64V350.93c0-8.84-7.16-16-16-16H299.9c-8.84 0-16 7.16-16 16s7.16 16 16 16h144.54v128.62H299.9c-8.84 0-16 7.16-16 16v160.62c0 8.83 7.16 15.99 16 15.99zM620.11 334.95c-0.15 0-0.29-0.02-0.43-0.02h-97.84c-0.42 0-0.83 0.03-1.25 0.06-0.41-0.03-0.83-0.06-1.25-0.06-8.84 0-16 7.16-16 16v317.28c0 8.84 7.16 16 16 16 0.42 0 0.83-0.03 1.25-0.06 0.41 0.03 0.83 0.06 1.25 0.06h97.84c0.15 0 0.29-0.02 0.43-0.02 32.18-0.46 61.87-19.53 83.71-53.85 20.69-32.5 32.08-75.39 32.08-120.77 0-45.38-11.39-88.27-32.08-120.77-21.84-34.32-51.53-53.39-83.71-53.85z m56.72 278.2c-16.03 25.19-36.6 39.06-57.91 39.06h-83.58V366.93h83.58c21.31 0 41.87 13.87 57.91 39.06 17.46 27.43 27.07 64.22 27.07 103.58 0 39.37-9.62 76.15-27.07 103.58z" fill="#666666" ></path></symbol></svg>',function(e){var t,n=(t=(t=document.getElementsByTagName("script"))[t.length-1]).getAttribute("data-injectcss");if(!(t=t.getAttribute("data-disable-injectsvg"))){var r,a,o,i,s;if(n&&!e.__iconfont__svg__cssinject__){e.__iconfont__svg__cssinject__=!0;try{document.write("<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>")}catch(t){console&&console.log(t)}}r=function(){var t,n=document.createElement("div");n.innerHTML=e._iconfont_svg_string_2976199,(n=n.getElementsByTagName("svg")[0])&&(n.setAttribute("aria-hidden","true"),n.style.position="absolute",n.style.width=0,n.style.height=0,n.style.overflow="hidden",n=n,(t=document.body).firstChild?function(e,t){t.parentNode.insertBefore(e,t)}(n,t.firstChild):t.appendChild(n))},document.addEventListener?~["complete","loaded","interactive"].indexOf(document.readyState)?setTimeout(r,0):(a=function(){document.removeEventListener("DOMContentLoaded",a,!1),r()},document.addEventListener("DOMContentLoaded",a,!1)):document.attachEvent&&(o=r,i=e.document,s=!1,function e(){try{i.documentElement.doScroll("left")}catch(t){return void setTimeout(e,50)}l()}(),i.onreadystatechange=function(){"complete"==i.readyState&&(i.onreadystatechange=null,l())})}function l(){s||(s=!0,o())}}(window);let Vt=null;const Wt={props:{baseUrl:{type:String,required:!0,default:()=>"/"},modelPath:{type:Object,required:!0,validator(e){const t=Object.keys(e);return!["P40","P800","shelf","conveyor"].some((e=>!t.includes(e)))}},token:{required:!0,type:[Boolean,String],default:()=>!1},theme:Object},name:"MapThreeMonitor",components:{ToolBar:bt,HelpPanel:Ct,RightPanel:$t},data:()=>({mapLoading:!0,isHelpPanelShow:!1,floorIds:[],rackLayers:[],curShowRackLayers:[],currentFloor:1,detailValue:null}),mounted(){const e=this.modelPath,t=this.theme;Vt=new Xt({dom:this.$refs.mapBox,baseUrl:this.baseUrl,config:{modelMap:{P40:{path:[e.P40.body,e.P40.plant],insetShape:"P40"},RS:{path:[e.RS.body,e.RS.tray,e.RS.lifting,e.RS.lattice],insetShape:"RS"},P800:{path:[e.P800.body,e.P800.tray],insetShape:"P800"},SHELF:{path:e.shelf,scale:4,setAttributes:e=>(e.rotation.y=Math.PI/180*90,e)},charger:{path:e.charger,scale:.1},"STATION-2":{path:e.monitor,insetShape:"station2"},"STATION-7":{path:e.conveyor,setAttributes:e=>(e.position.y=.3,e.rotation.y=-Math.PI/2,e)}},constants:{theme:{...t.MapTheme,...t.Theme}},controlMode:"Map"}}),Vt.registerPlugin(new Jt({wsUrl:this.getWsUrl()})),this.listenEvent()},destroyed(){Vt&&Vt.desotry(),Vt=null},methods:{listenEvent(){Vt.Emitter.on("floorIds:ready",(e=>this.floorIds=e)),Vt.Emitter.on("after:renderRack",(e=>{this.rackLayers=e.layers,this.curShowRackLayers=e.layers})),Vt.Emitter.on("selected:element",(e=>{this.detailValue=e.data})),Vt.Emitter.on("after:renderReady",(()=>{this.mapLoading=!1,Vt.enablePlugin("monitorPlugin")}))},getWsUrl(){let e="http:"===window.location.protocol?"ws":"wss",t=window.location.host;$req&&$req.isDev&&(t=new URL($req.API_URL).hostname);return`${e}://${t}/athena-monitor${this.token?`?token=${this.token}`:""}`},changeFloor(e){this.currentFloor=e},handleToolBar(e){let{event:t,active:n}=e;const r=Vt.getPlugin("monitorPlugin");switch(t){case"to2D":this.$emit("redirect2D");break;case"fullScreen":const t=document.querySelector("#mapBox");this.launchFullScreen(t);break;case"zoomIn":r.zoomIn();break;case"zoomOut":r.zoomOut();break;case"isShowRobotPath":r.showRobotPath(e.active);break;case"toggleMapShelfHeat":r.showBoxHot(e.active);break;case"isShowTaskBox":r.showTaskBoxs(e.active);break;case"isShowLattice":r.showRacksLattices(e.active);break;case"isShowTopView":r.showTopView();break;case"isShowLegend":this.isHelpPanelShow=n}},handlerRackLayerChange({layer:e}){const t=Vt.getPlugin("monitorPlugin");this.curShowRackLayers=e,t.showRackLayers(e),t.showRackLatticeLayer(e)},handleChangeSearchType(e){Vt.getPlugin("monitorPlugin").changeActionType(e)},handleSearchSelect(e){const t=Vt.getPlugin("monitorPlugin");if(t.select(e),["box","lattice"].includes(e.actionType)){const{layer:e=-1}=this.detailValue;~e&&(t.showRackLayers([e]),t.showRackLatticeLayer([e]))}setTimeout((()=>{const e=this.detailValue.location;e&&Vt.camera.flyTo([e.x,0,-e.y],45)}),500),setTimeout((()=>{t&&t.showRackLayers(this.curShowRackLayers),t&&t.showRackLatticeLayer(this.curShowRackLayers)}),3500)},handleClearSelect(e){Vt.getPlugin("monitorPlugin").clearSelect(e)},launchFullScreen(e){e.requestFullscreen?e.requestFullscreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.webkitRequestFullscreen?e.webkitRequestFullscreen():e.msRequestFullscreen&&e.msRequestFullscreen()}}};var Ut=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"ui-mapEdit3D"},[n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.mapLoading,expression:"mapLoading"}],staticClass:"edit3d-panel",attrs:{"element-loading-text":"拼命加载中","element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(255, 255, 255, 0.4)"}},[n("div",{staticClass:"mian-box"},[n("div",{ref:"mapBox",staticClass:"ui-mapEdit3D__box",attrs:{id:"mapBox"}}),e._v(" "),e.mapLoading?e._e():n("ToolBar",{attrs:{"floor-ids":e.floorIds,"rack-layers":e.rackLayers,"default-current-floor":e.currentFloor},on:{changeFloor:e.changeFloor,handleToolBar:e.handleToolBar,MapRackLayerChange:e.handlerRackLayerChange}}),e._v(" "),e.isHelpPanelShow?n("help-panel",{attrs:{theme:e.theme}}):e._e(),e._v(" "),e.mapLoading?e._e():n("right-panel",{ref:"rightPanel",attrs:{"rack-layers":e.rackLayers,"select-info":null,"detail-data":e.detailValue},on:{changeSearchType:e.handleChangeSearchType,searchSelect:e.handleSearchSelect,clearSelect:e.handleClearSelect}})],1)])])};Ut._withStripped=!0;const qt=dt({render:Ut,staticRenderFns:[]},(function(e){e&&e("data-v-6f552c96_0",{source:".ui-mapEdit3D[data-v-6f552c96],\n.ui-mapEdit3D__box[data-v-6f552c96] {\n  height: 100%;\n  position: relative;\n}\n.ui-mapEdit3D[data-v-6f552c96] {\n  margin: -12px;\n}\n.ui-mapEdit3D .header[data-v-6f552c96] {\n  background: #fff;\n}\n.ui-mapEdit3D .mian-box[data-v-6f552c96] {\n  height: calc(100vh - 46px);\n  display: flex;\n  position: relative;\n  overflow: hidden;\n}\n.ui-mapEdit3D .mian-box .ui-mapEdit3D__box[data-v-6f552c96] {\n  flex: 1;\n  background: #fff;\n}\n.ui-mapEdit3D .mian-box .aside[data-v-6f552c96] {\n  width: 250px;\n  background: #fff;\n  overflow-x: hidden;\n  overflow-y: auto;\n  border-left: 1px solid rgb(238, 238, 238);\n  box-shadow: -3px 6px 10px 0 rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n.edit3d-panel[data-v-6f552c96] {\n  overflow-y: hidden;\n}\n\n/*# sourceMappingURL=MapThreeMonitor.vue.map */",map:{version:3,sources:["D:\\coding\\rms相关库\\map-edit-3d\\package\\MapThreeMonitor\\MapThreeMonitor.vue","MapThreeMonitor.vue"],names:[],mappings:"AAiRA;;EAEA,YAAA;EACA,kBAAA;AChRA;ADkRA;EACA,aAAA;AC/QA;ADgRA;EACA,gBAAA;AC9QA;ADgRA;EACA,0BAAA;EACA,aAAA;EACA,kBAAA;EACA,gBAAA;AC9QA;AD+QA;EACA,OAAA;EACA,gBAAA;AC7QA;AD+QA;EACA,YAAA;EACA,gBAAA;EACA,kBAAA;EACA,gBAAA;EACA,yCAAA;EACA,8CAAA;EACA,kBAAA;AC7QA;ADiRA;EACA,kBAAA;AC9QA;;AAEA,8CAA8C",file:"MapThreeMonitor.vue",sourcesContent:['<template>\r\n  <div class="ui-mapEdit3D">\r\n    <div\r\n      class="edit3d-panel"\r\n      v-loading="mapLoading"\r\n      element-loading-text="拼命加载中"\r\n      element-loading-spinner="el-icon-loading"\r\n      element-loading-background="rgba(255, 255, 255, 0.4)"\r\n    >\r\n      <div class="mian-box">\r\n        <div id="mapBox" ref="mapBox" class="ui-mapEdit3D__box"></div>\r\n        \x3c!-- 操作工具bar --\x3e\r\n        <ToolBar\r\n          v-if="!mapLoading"\r\n          :floor-ids="floorIds"\r\n          :rack-layers="rackLayers"\r\n          :default-current-floor="currentFloor"\r\n          @changeFloor="changeFloor"\r\n          @handleToolBar="handleToolBar"\r\n          @MapRackLayerChange="handlerRackLayerChange"\r\n        />\r\n        \x3c!-- 帮助面板 --\x3e\r\n        <help-panel v-if="isHelpPanelShow" :theme="theme" />\r\n        <right-panel\r\n          v-if="!mapLoading"\r\n          ref="rightPanel"\r\n          :rack-layers="rackLayers"\r\n          :select-info="null"\r\n          :detail-data="detailValue"\r\n          @changeSearchType="handleChangeSearchType"\r\n          @searchSelect="handleSearchSelect"\r\n          @clearSelect="handleClearSelect"\r\n        ></right-panel>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport MAPEDIT3D, { MointorPlugin } from "../../src/index";\r\nimport ToolBar from "./components/tool-bar/index.vue";\r\nimport HelpPanel from "./components/help-panel.vue";\r\nimport RightPanel from "./components/right-panel/index.vue";\r\nimport "./components/tool-bar/icon/iconfont.js";\r\nlet map = null;\r\n// const baseUrl = process.env.NODE_ENV === "production" ? "./static/" : "/";\r\n\r\nexport default {\r\n  props: {\r\n    baseUrl: {\r\n      type: String,\r\n      required: true,\r\n      default() {\r\n        return "/";\r\n      },\r\n    },\r\n    modelPath: {\r\n      type: Object,\r\n      required: true,\r\n      validator(val) {\r\n        const requireKey = ["P40", "P800", "shelf", "conveyor"];\r\n        const keys = Object.keys(val);\r\n        return !requireKey.some(k => !keys.includes(k));\r\n      },\r\n    },\r\n    token: {\r\n      required: true,\r\n      type: [Boolean, String],\r\n      default() {\r\n        return false;\r\n      },\r\n    },\r\n    theme: Object,\r\n  },\r\n  name: "MapThreeMonitor",\r\n  components: { ToolBar, HelpPanel, RightPanel },\r\n  data() {\r\n    return {\r\n      mapLoading: true,\r\n      isHelpPanelShow: false,\r\n      floorIds: [],\r\n      rackLayers: [],\r\n      curShowRackLayers: [],\r\n      currentFloor: 1,\r\n      detailValue: null,\r\n    };\r\n  },\r\n  mounted() {\r\n    const GeekEditor3DMapConfig = this.modelPath;\r\n    const GeekEditor3dGlobalConfig = this.theme;\r\n    map = new MAPEDIT3D({\r\n      dom: this.$refs.mapBox,\r\n      baseUrl: this.baseUrl,\r\n      config: {\r\n        // 加载模型支持 modelMap，或者调用map.registerModel\r\n        modelMap: {\r\n          P40: {\r\n            path: [GeekEditor3DMapConfig.P40.body, GeekEditor3DMapConfig.P40.plant],\r\n            insetShape: "P40",\r\n          },\r\n          RS: {\r\n            path: [\r\n              GeekEditor3DMapConfig.RS.body,\r\n              GeekEditor3DMapConfig.RS.tray,\r\n              GeekEditor3DMapConfig.RS.lifting,\r\n              GeekEditor3DMapConfig.RS.lattice,\r\n            ],\r\n            insetShape: "RS",\r\n          },\r\n          P800: {\r\n            path: [GeekEditor3DMapConfig.P800.body, GeekEditor3DMapConfig.P800.tray],\r\n            insetShape: "P800",\r\n          },\r\n          SHELF: {\r\n            path: GeekEditor3DMapConfig.shelf,\r\n            scale: 4,\r\n            setAttributes(model) {\r\n              model.rotation.y = 90 * (Math.PI / 180);\r\n              return model;\r\n            },\r\n          },\r\n          charger: {\r\n            path: GeekEditor3DMapConfig.charger,\r\n            scale: 0.1,\r\n          },\r\n          [`STATION-2`]: {\r\n            path: GeekEditor3DMapConfig.monitor,\r\n            insetShape: "station2",\r\n          },\r\n          [`STATION-7`]: {\r\n            path: GeekEditor3DMapConfig.conveyor,\r\n            setAttributes(model) {\r\n              model.position.y = 0.3;\r\n              model.rotation.y = -Math.PI / 2;\r\n              return model;\r\n            },\r\n          },\r\n        },\r\n        constants: {\r\n          theme: {\r\n            ...GeekEditor3dGlobalConfig.MapTheme,\r\n            ...GeekEditor3dGlobalConfig.Theme,\r\n          },\r\n        },\r\n        controlMode: "Map", // Orbit\r\n      },\r\n    });\r\n    // 加载mointor\r\n    map.registerPlugin(new MointorPlugin({ wsUrl: this.getWsUrl() }));\r\n    this.listenEvent();\r\n  },\r\n  destroyed() {\r\n    map && map.desotry();\r\n    map = null;\r\n  },\r\n  methods: {\r\n    listenEvent() {\r\n      // map.Emitter.on("after:initMap", () => (this.mapLoading = false));\r\n      map.Emitter.on("floorIds:ready", floorIds => (this.floorIds = floorIds));\r\n      map.Emitter.on("after:renderRack", data => {\r\n        this.rackLayers = data.layers;\r\n        this.curShowRackLayers = data.layers;\r\n      });\r\n      map.Emitter.on("selected:element", data => {\r\n        this.detailValue = data.data;\r\n      });\r\n      map.Emitter.on("after:renderReady", () => {\r\n        this.mapLoading = false;\r\n        map.enablePlugin("monitorPlugin");\r\n      });\r\n    },\r\n    getWsUrl() {\r\n      let protocol = window.location.protocol === "http:" ? "ws" : "wss";\r\n      let hostname = window.location.host;\r\n      if ($req && $req.isDev) {\r\n        hostname = new URL($req.API_URL).hostname;\r\n      }\r\n      const token = this.token ? `?token=${this.token}` : "";\r\n      return `${protocol}://${hostname}/athena-monitor${token}`;\r\n    },\r\n    // 切换楼层\r\n    changeFloor(floor) {\r\n      this.currentFloor = floor;\r\n    },\r\n    // 切换toolbar\r\n    handleToolBar(item) {\r\n      let { event, active } = item;\r\n      const monitorPlugin = map.getPlugin("monitorPlugin");\r\n      switch (event) {\r\n        case "to2D": // 切换为2D\r\n          this.$emit("redirect2D");\r\n          break;\r\n        case "fullScreen":\r\n          const monitor = document.querySelector("#mapBox");\r\n          this.launchFullScreen(monitor);\r\n          break;\r\n        case "zoomIn":\r\n          monitorPlugin.zoomIn();\r\n          break;\r\n        case "zoomOut":\r\n          monitorPlugin.zoomOut();\r\n          break;\r\n        case "isShowRobotPath":\r\n          monitorPlugin.showRobotPath(item.active);\r\n          break;\r\n        case "toggleMapShelfHeat":\r\n          monitorPlugin.showBoxHot(item.active);\r\n          break;\r\n        case "isShowTaskBox":\r\n          monitorPlugin.showTaskBoxs(item.active);\r\n          break;\r\n        case "isShowLattice":\r\n          monitorPlugin.showRacksLattices(item.active);\r\n          break;\r\n        case "isShowTopView":\r\n          monitorPlugin.showTopView();\r\n          break;\r\n        case "isShowLegend":\r\n          this.isHelpPanelShow = active;\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    handlerRackLayerChange({ layer }) {\r\n      const monitor = map.getPlugin("monitorPlugin");\r\n      this.curShowRackLayers = layer;\r\n      monitor.showRackLayers(layer);\r\n      monitor.showRackLatticeLayer(layer);\r\n    },\r\n    handleChangeSearchType(type) {\r\n      const monitor = map.getPlugin("monitorPlugin");\r\n      monitor.changeActionType(type);\r\n    },\r\n    handleSearchSelect(data) {\r\n      const monitor = map.getPlugin("monitorPlugin");\r\n      monitor.select(data);\r\n      if (["box", "lattice"].includes(data.actionType)) {\r\n        const { layer = -1 } = this.detailValue;\r\n        if (!!~layer) {\r\n          monitor.showRackLayers([layer]);\r\n          monitor.showRackLatticeLayer([layer]);\r\n        }\r\n      }\r\n      setTimeout(() => {\r\n        const location = this.detailValue.location;\r\n        if (!location) return;\r\n        map.camera.flyTo([location.x, 0, -location.y], 45);\r\n      }, 500);\r\n      setTimeout(() => {\r\n        monitor && monitor.showRackLayers(this.curShowRackLayers);\r\n        monitor && monitor.showRackLatticeLayer(this.curShowRackLayers);\r\n      }, 3500);\r\n    },\r\n    handleClearSelect(data) {\r\n      const monitor = map.getPlugin("monitorPlugin");\r\n      monitor.clearSelect(data);\r\n    },\r\n    // 全屏\r\n    launchFullScreen(element) {\r\n      if (element.requestFullscreen) {\r\n        element.requestFullscreen();\r\n      } else if (element.mozRequestFullScreen) {\r\n        element.mozRequestFullScreen();\r\n      } else if (element.webkitRequestFullscreen) {\r\n        element.webkitRequestFullscreen();\r\n      } else if (element.msRequestFullscreen) {\r\n        element.msRequestFullscreen();\r\n      }\r\n    },\r\n  },\r\n};\r\n<\/script>\r\n<style lang="scss" scoped>\r\n.ui-mapEdit3D,\r\n.ui-mapEdit3D__box {\r\n  height: 100%;\r\n  position: relative;\r\n}\r\n.ui-mapEdit3D {\r\n  margin: -12px;\r\n  .header {\r\n    background: #fff;\r\n  }\r\n  .mian-box {\r\n    height: calc(100vh - 46px);\r\n    display: flex;\r\n    position: relative;\r\n    overflow: hidden;\r\n    .ui-mapEdit3D__box {\r\n      flex: 1;\r\n      background: #fff;\r\n    }\r\n    .aside {\r\n      width: 250px;\r\n      background: #fff;\r\n      overflow-x: hidden;\r\n      overflow-y: auto;\r\n      border-left: 1px solid rgb(238, 238, 238);\r\n      box-shadow: -3px 6px 10px 0 rgba(0, 0, 0, 0.1);\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n.edit3d-panel {\r\n  overflow-y: hidden;\r\n}\r\n</style>\r\n',".ui-mapEdit3D,\n.ui-mapEdit3D__box {\n  height: 100%;\n  position: relative;\n}\n\n.ui-mapEdit3D {\n  margin: -12px;\n}\n.ui-mapEdit3D .header {\n  background: #fff;\n}\n.ui-mapEdit3D .mian-box {\n  height: calc(100vh - 46px);\n  display: flex;\n  position: relative;\n  overflow: hidden;\n}\n.ui-mapEdit3D .mian-box .ui-mapEdit3D__box {\n  flex: 1;\n  background: #fff;\n}\n.ui-mapEdit3D .mian-box .aside {\n  width: 250px;\n  background: #fff;\n  overflow-x: hidden;\n  overflow-y: auto;\n  border-left: 1px solid rgb(238, 238, 238);\n  box-shadow: -3px 6px 10px 0 rgba(0, 0, 0, 0.1);\n  position: relative;\n}\n\n.edit3d-panel {\n  overflow-y: hidden;\n}\n\n/*# sourceMappingURL=MapThreeMonitor.vue.map */"]},media:void 0})}),Wt,"data-v-6f552c96",false,undefined,!1,ut,void 0,void 0);qt.install=function(e){e.component(qt.name,qt)};var Kt=[qt];var Xt=function(e){L(i,ae);var o=P(i);function i(e){var t;return M(this,i),(t=o.call(this,e)).config=e.config,t.$dom=e.dom,t.baseUrl=e.baseUrl,t.scene=null,t.renderer=null,t.OrbitControls=null,t.modelInstances={},t._initConstants(e.config.constants),t._initThreeScene(),t.command=new ie({Map3d:I(t)}),t.ticker=new le(I(t)),t.camera=new be(I(t)),t.search=new he(I(t)),t.loadObj=new _e(I(t)),t.floor=null,t._initeControls(),t._render(),t}return D(i,[{key:"initMap",value:function(e){$.mapData=e;var t=Object.keys(e)[0];$.curFloorId=t,this._doRenderMap(e[t]),this.Emitter.emit("after:initMap")}},{key:"initModel",value:function(e,t){if(e&&Array.isArray(e)&&e.length){var n=t.category,r=t.useModelName,a=e.map((function(e){return k(k({},e),{},{uuid:u(),category:n,useModelName:"function"==typeof r?r(e):r})}));$.modelData=B({},n,a),this._renderModel(a)}}},{key:"getModelData",value:function(e){return $.getModelData(e)}},{key:"showFloorId",value:function(e){if(this.Emitter.emit("before:changeFloorId"),!$.checkVerifyFloorId(e))return!1;this.hiddenFloorId($.curFloorId),$.curFloorId=e;var t=$.mapData[e];this._doRenderMap(t);var n=Object.values($.modelData).flat().filter((function(t){return String(t.floorId)===String(e)}));return this._renderModel(n),this.Emitter.emit("after:changeFloorId",!0),!0}},{key:"hiddenFloorId",value:function(){var e=this.scene.getObjectByName("floorGeo");for(var t in this.scene.remove(e),this.modelInstances)this.scene.remove(this.modelInstances[t].model),delete this.modelInstances[t];this.modelInstances={}}},{key:"appendFloorMapData",value:function(e){$.mapData=e}},{key:"appendModelData",value:function(e,t){if(e&&Array.isArray(e)&&e.length){var n=t.category,r=t.useModelName,a=e.map((function(e){return k(k({},e),{},{uuid:u(),category:n,useModelName:"function"==typeof r?r(e):r})}));$.addModelData(n,a),this._renderModel(a)}}},{key:"addModelAction",value:function(e,t){var n=this,r=this.getPlugin("addPlugin"),a=this.getPlugin("selectPlugin");r.addCategory=e,r.formatter=t,this.enablePlugin(r.PluginName),this.Emitter.on("after:add",(function(e){n.disabledPlugin(r.PluginName),a.triggerSelect(e)}))}},{key:"updateModel",value:function(e){var t=e.uuid;if(!this.modelInstances[t])throw new Error("当前数据不存在，请使用addModel");var n=this.modelInstances[t]._data;this.command.exec("update",{oldValue:n,newValue:e}),this.Emitter.emit("after:updateModel")}},{key:"deleteModel",value:function(e){var t=e.uuid;if(!this.modelInstances[t])throw new Error("当前数据不存在！");this.command.exec("del",{value:e}),this.getPlugin("selectPlugin").triggerCancelSelect(),this.Emitter.emit("after:deleteModel")}},{key:"deleteBatchModels",value:function(e){if(!e||!e.length)throw new Error("[deleteModels]>>> 缺少传参！");this.command.exec("del",{value:e}),this.getPlugin("selectPlugin").triggerCancelSelect(),this.Emitter.emit("after:deleteModel")}},{key:"desotry",value:function(){var e=this.getPlugin();this.removePlugins(e.map((function(e){return e.PluginName}))),this.Emitter.all.clear(),this.Emitter=null,this.command.destory(),this.command=null,this.ticker&&this.ticker.destroy(),this.ticker=null,this.loadObj&&this.loadObj.destory(),this.loadObj=null,this.search&&this.search.destory(),this.search=null,$&&$.destory()}},{key:"resetCamera",value:function(){this.floor.floorInfo&&this.__resetView()}},{key:"_doRenderMap",value:function(e){this.floor=new W(e),this.scene.add(this.floor.floorGeo),this.search.add(this.floor.floorGeo.children[1],{data:e.cells}),this.resetCamera()}},{key:"_renderModel",value:function(e){for(var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.length,a=n.done,o=0,i=function(n){var i=e[n],s=i.category,l=i.useModelName,c=(t.config.modelMap[l]||{}).isAdsorb||!1;t.loadObj.getLazy(l,(function(n){var l=new ee({object3d:n.clone(),data:i,category:s,isAdsorb:c});t.scene.add(l.model),t.modelInstances[i.uuid]=l,++o===r&&(o=0,t.Emitter.emit("after:renderModel",{category:s,data:e}),a&&a())}))},s=0;s<r;s++)i(s)}},{key:"__resetView",value:function(){var e=this.floor.floorInfo,t=(e.right+e.left)/2,n=-(e.bottom+e.top)/2;this.camera.flyTo([t,0,n],90),this.scene.getObjectByName("directionLight1").position.set(t,100,n)}},{key:"_initThreeScene",value:function(){this.scene=new t.Scene,this.scene.background=new t.Color(V.THEME.SCENE_COLOR);var e=new t.AmbientLight(4210752);this.scene.add(e);var n=new t.DirectionalLight(6316128,.65);n.name="directionLight1",n.position.set(50,50,-50),this.scene.add(n);var r=new t.HemisphereLight(16777147,6316128,.8);r.position.set(50,50,-50),this.scene.add(r),this.renderer=new t.WebGLRenderer({antialias:!0,logarithmicDepthBuffer:!0,powerPreference:"high-performance",premultipliedAlpha:"false"}),this.renderer.setSize(this.$dom.offsetWidth,this.$dom.offsetHeight),this.$dom.appendChild(this.renderer.domElement);var a=this;window.addEventListener("resize",Se((function(){a.camera.changeCameraAspt(),a.renderer.setSize(a.$dom.offsetWidth,a.$dom.offsetHeight)}),500))}},{key:"_initeControls",value:function(){var e=new("Orbit"===this.config.controlMode?n:r)(this.camera.get(),this.renderer.domElement);e.maxPolarAngle=.5*Math.PI,e.maxDistance=500,e.minDistance=1,this.OrbitControls=e,this.transformControl=new a(this.camera.get(),this.renderer.domElement),this.transformControl.showY=!1,this.transformControl.addEventListener("dragging-changed",(function(t){return e.enabled=!t.value})),this.scene.add(this.transformControl)}},{key:"_initConstants",value:function(e){if(e){var t=(e||{}).theme,n=void 0===t?null:t;n&&(V.THEME=Object.assign(V.THEME,n))}}},{key:"_createHelper",value:function(){var e=new t.GridHelper(600,300,8421504,8421504);e.position.y=-.5,this.scene.add(e)}},{key:"_render",value:function(){var e=this;this.ticker.add((function(){p.update(),e.renderer.render(e.scene,e.camera.get())}))}}]),i}();Xt.install=function(e){for(var t,n=0;t=Kt[n++];)e.component(t.name,t)};var Zt=q,Yt=we,Jt=nt,Qt=st;export{Qt as EditPlugin,Jt as MointorPlugin,Zt as PluginBase,Yt as PointerLockPlugin,Xt as default};
