<template>
  <div>
    <el-dialog
      :title="dialogMapAddCopy.title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      class="geek-dialog-form"
      width="400px"
      @close="close"
    >
      <el-form
        ref="mapForm"
        label-position="right"
        label-width="100px"
        :model="mapForm"
      >
        <el-form-item
          :label="rowData ? $t('lang.rms.fed.mapName') : $t('lang.rms.web.map.newName')"
          prop="mapName"
          :rules="[{required: true,message: $t('lang.rms.api.result.monitor.map.targetMapNameIsNull')}, {max: 25, message: $t('lang.rms.fed.map.nameTooLong')}]"
        >
          <el-input v-model="mapForm.mapName" :placeholder="$t('lang.rms.fed.pleaseEnter')" @keyup.enter.native="submit" />
          <input type="text" v-show="false">
        </el-form-item> 
      </el-form>

      <div slot="footer">
        <el-button @click="visible = false">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button
          type="primary"
          :disabled="disable"
          @click="submit"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'DialogMapAddCopy',
  data() {
    return {
      label: this.$t('lang.rms.web.map.version.newMapName'),
      disable: false,
      mapForm: {
        mapName: ''
      }
    }
  },
  computed: {
    ...mapState('mapManagement', ['dialogMapAddCopy']),
    visible: {
      get() {
        return this.dialogMapAddCopy.visible
      },
      set(val) {
        const { visible } = this.dialogMapAddCopy
        if (!val && val !== visible) {
          this.hideDialog()
        }
      }
    },
    rowData() {
      return this.dialogMapAddCopy.rowData
    }
  },
  methods: {
    ...mapMutations('mapManagement', ['hideDialog']),
    close() {
      const $mapForm = this.$refs['mapForm']
      $mapForm.resetFields()

      // this.mapForm.mapName = ''
    },
    submit() {
      const $mapForm = this.$refs['mapForm']
      $mapForm.validate((res) => {
        if (res) {
          this.disable = true
          if (!this.rowData) this.mapCreate()
          else this.mapCopy()
        }
      })
    },
    mapCreate() {
      $req
        .postParams('/athena/map/draw/createMap', {
          mapName: this.mapForm.mapName
        })
        .then(res => {
          this.reqSuccess(res.msg)
        })
        .catch(() => {
          this.disable = false
        })
    },
    mapCopy() {
      $req
        .post('/athena/map/version/clone', {
          mapId: this.rowData.id,
          targetMapName: this.mapForm.mapName
        })
        .then(res => {
          this.reqSuccess(res.msg)
        })
        .catch(() => {
          this.disable = false
        })
    },
    reqSuccess(msg) {
      this.disable = false
      this.visible = false
      this.$emit('refreshList')
      msg = $utils.Tools.transMsgLang(msg)
      this.$success(msg)
    }
  }
}
</script>

<style lang="less" scoped></style>
