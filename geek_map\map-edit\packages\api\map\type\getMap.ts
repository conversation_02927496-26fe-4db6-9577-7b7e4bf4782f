export interface MapSplitImage {
  originHeight: number;
  originWidth: number;
  splitSize: number;
  xarraySize: number;
  yarraySize: number;
  imageItems: MapSplitImageItem[];
}

export interface MapSplitImageItem {
  width: number;
  height: number;
  indexX: number;
  indexY: number;
  x: number;
  y: number;
  imageText: string;
}

export interface GetMapParams {
  mapId: string | number;
  floorId: string | number;
}

export interface GetMapResult {
  base64Text: string | null;
  cellLength?: number;
  cellNumX?: number;
  cellNumY?: number;
  cellWidth?: number;
  locationX?: number;
  locationY?: number;
  displayType: string; // "SPLIT"
  mapId: number;
  floorId: number;
  imageDisplayAngle: number | null;
  released: boolean;
  resolution: number;
  scale: number;
  splitImage: MapSplitImage;
  yaw: number;
}
