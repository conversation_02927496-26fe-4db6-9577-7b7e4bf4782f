/* ! <AUTHOR> at 2023/04/20 */
namespace MRender {
  export interface Main {
    /** 初始化 */
    init(): void;

    /**
     * 初始化Map Floor数据
     * @param floorsData
     */
    renderFloors(floorsData: floorsData): void;

    /**
     * 初始化display数据
     * @param displays
     */
    renderDisplays(displays: displays): void;

    /**
     * 更新display数据
     * 更新的2d display数据中会包含cells，不包含racks，因为racks不需要更新位置渲染
     * @param displays
     */
    updateDisplays(displays: displays): void;

    /**
     * 设置元素居中
     * @param params
     */
    setEleCenter(params: { layer: string; code: number | string }): void;

    /**
     * 开启 多选模式
     * @param flag: true | false
     */
    enableMultiClick(flag: boolean, layerNames?: Array<MRender.layerName>): void;

    getSelectCodes(type: "all" | "cell" | "robot"): Array<code>;

    /**  删除地图选中元素 默认type为all */
    clearSelects(type?: MRender.layerName | "all", codes?: Array<any>): void;

    /**
     * 触发type click 点击事件
     * @param type
     * @param data 要触发的元素codes map集合
     */
    trigger(type: "click", data: layerElements): void;
    /**
     * 触发type rect 框选事件
     * @param type
     * @param cb 框选callback
     */
    trigger(type: "rect" | "ranging" | "stop", cb?: callback): void;

    /**
     * 地图资源加载完成准备好了
     * @param cb
     */
    ready(cb: () => void): void;
    /**
     * 为点击绑定事件
     * @param cb
     */
    click(cb: (data: clickParams) => void): void;
    /**
     * 当前帧渲染完成
     * @param cb
     */
    rendered(cb: (renderType: MRender.renderedType) => void): void;
    /**
     * 地图缩放
     * @param val
     */
    zoom(val: number): void;

    /**  是否开启地图获取 elements 屏幕坐标 */
    enableScreenPositions(
      enable: boolean,
      params: { type: "stations" | "robots"; codes?: Array<code> },
    ): void;
    /** 渲染Area  */
    renderArea(areaType: string, areaData: Array<any>, isInit?: boolean): void;
    /** 渲染业务相关可选性  */
    renderFeature(type: "deadRobotPath", data: any): void;

    /**
     * 如果显示（隐藏）就隐藏（显示）地图层。
     * @param layerNames
     * @param isShow
     */
    toggleLayer(
      layerName: MRender.toggleLayerName,
      isShow: boolean,
      data?: shelfHeatApiData | Array<code>,
    ): void;

    /**
     * 地图快速查找过滤显示
     * @param data
     */
    triggerFastFilter(
      type: MRender.mapSearchFilterType,
      data: {
        action: "remove" | "render";
        cellCodes?: Array<code>;
        sizeType?: string;
        areaType?: string;
        areasData?: Array<any>;
      },
    ): void;
    /**
     * 地图 layerNames 可点击
     * @param layerNames
     */
    triggerLayers(layerNames: Array<MRender.layerName>): void;

    /**
     * 改变地图地图元素的颜色
     * @param params
     */
    renderFeatureColor(params: MRender.mapColorParam): void;
    /** 渲染单元格文字 */
    renderCellText(isRender: boolean, params?: MRender.mapCellTextParam): void;
    /** 获取地图的color配色 */
    getMapColors(): MRender.MapColorConfig;

    /**
     * 设置地图位置
     * @param mapPosition
     */
    setMapPosition(mapPosition?: MRender.mapPosition): void;
    /** 设置地图中心点 */
    setMapCenter(): void;
    /** 地图跟随windows、dom大小 resize  */
    resize(): void;
    /** 全部重新渲染一下 */
    rerender(): void;
    /** 地图重绘 */
    repaint(): void;
    /** 地图销毁 */
    destroy(): void;
  }
}
