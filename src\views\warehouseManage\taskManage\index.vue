<template>
  <geek-main-structure class="task-manage-operation">
    <div class="form-content">
      <geek-customize-form
        :form-config="formConfig"
        @on-query="onQuery"
        @on-reset="onReset"
        @on-export="onExport"
      />
    </div>

    <div v-if="tabList.length" class="tabs-content mt10 mt10">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane v-for="tab in tabList" :key="tab" :label="tab" :name="tab"></el-tab-pane>
      </el-tabs>
    </div>
    <div class="table-content">
      <!-- :tree-props 通过错误的 配置 去掉默认的 样式 -->
      <el-table
        :key="curTimer"
        ref="myTable"
        :data="tableData"
        row-key="uuid"
        style="width: 100%"
        lazy
        :load="loadChildTask"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        @expand-change="recordExpandChange"
      >
        <!-- 上游任务ID -->
        <el-table-column prop="jobId" :label="$t('lang.rms.fed.taskId')" width="100">
          <template slot-scope="scope">
            <span v-if="!scope.row.jobStageId">
              {{ scope.row.jobId || "" }}
            </span>
          </template>
        </el-table-column>
        <!-- 任务ID -->
        <el-table-column prop="jobStageId" :label="$t('lang.rms.fed.subTaskId')" width="105" />
        <!-- 任务类型 -->
        <el-table-column prop="jobType" :label="$t('lang.rms.fed.taskType')" width="100" />
        <!-- 优先级 -->
        <el-table-column prop="priority" :label="$t('lang.rms.web.monitor.robot.robotPriority')" />
        <!-- 任务状态 -->
        <el-table-column prop="jobState" :label="$t('lang.rms.web.monitor.robot.taskState')" />
        <!-- 任务指令 -->
        <el-table-column prop="jobInstruction" :label="$t('lang.rms.fed.taskInstruction')" />
        <!-- 任务阶段 -->
        <el-table-column prop="jobPhase" :label="$t('lang.rms.web.monitor.robot.taskPhase')" />
        <!-- 机器人ID -->
        <el-table-column prop="robotId" :label="$t('lang.rms.fed.listRobotId')" />
        <!-- 货架ID -->
        <el-table-column prop="shelfCode" :label="$t('lang.rms.fed.containerId')" />
        <!-- 工作站ID -->
        <el-table-column prop="stationId" :label="$t('lang.rms.fed.stationId')" />
        <!-- 异常信息 -->
        <el-table-column
          prop="excetpionInfo"
          width="120px"
          :label="$t('lang.rms.web.monitor.exception.info')"
        >
          <template slot-scope="scope">
            <template v-if="scope.row.excetpionInfo">
              <el-tooltip class="itme" effect="dark" placement="left">
                <template #content>
                  <div>
                    <pre class="ui-preBlock">
                    {{ JSON.stringify(JSON.parse(scope.row.excetpionInfo), null, 2) }}
                  </pre
                    >
                  </div>
                </template>
                <el-tag class="ell w100">{{ scope.row.excetpionInfo }}</el-tag>
              </el-tooltip>
            </template>
          </template>
        </el-table-column>
        <!-- 充电站ID -->
        <el-table-column prop="chargeStationId" :label="$t('lang.rms.web.charger.chargerId')" />
        <!-- 起点 -->
        <el-table-column prop="startPoint" :label="$t('lang.rms.web.monitor.robot.startPoint')" />
        <!-- 终点 -->
        <el-table-column prop="endPoint" :label="$t('lang.rms.web.monitor.robot.endPoint')" />
        <!-- 上游任务ID -->
        <el-table-column prop="hostTaskId" :label="$t('lang.rms.fed.parentTaskId')" />
        <!-- 创建时间 -->
        <el-table-column
          prop="createTime"
          :label="$t('lang.rms.web.monitor.robot.taskCreateTime')"
        />
        <!-- 更新时间 -->
        <el-table-column prop="updateTime" :label="$t('lang.rms.fed.updateTime')" />
        <el-table-column
          v-if="!isRoleGuest"
          fixed="right"
          :label="$t('lang.rms.fed.textOperation')"
          width="320"
        >
          <template slot-scope="scope">
            <m-confirm-btn
              v-if="!scope.row.jobStageId && String(scope.row.cancelOper) === '1'"
              :label="$t('lang.rms.fed.cancel')"
              :message="$t('lang.rms.fed.isSure', [$t('lang.rms.fed.cancelTask')])"
              round
              size="mini"
              @sureClick="() => handlerClose(scope)"
            ></m-confirm-btn>
            <!-- <el-button
              v-if="!scope.row.jobStageId && String(scope.row.cancelOper) === '1'"
              type="primary"
              icon="el-icon-close"
              circle
              size="mini"
              @click="() => handlerClose(scope)"
            ></el-button> -->
            <m-confirm-btn
              v-if="!scope.row.jobStageId && String(scope.row.deleteOper) === '1'"
              :label="$t('lang.rms.fed.delete')"
              :message="$t('lang.rms.fed.isSure', [$t('lang.rms.fed.deleteTask')])"
              size="mini"
              round
              @sureClick="() => handlerDelete(scope)"
            ></m-confirm-btn>
            <!-- <el-button
              v-if="!scope.row.jobStageId && String(scope.row.deleteOper) === '1'"
              type="primary"
              icon="el-icon-delete"
              size="mini"
              circle
              @click="() => handlerDelete(scope)"
            ></el-button> -->
            <!-- 子任务显示任务性情按钮 -->
            <el-button
              round
              :loading="scope.row.detailLoad"
              size="mini"
              @click="() => handleTaskDetail(scope.row)"
            >
              {{ $t("lang.rms.fed.taskDetail") }}
            </el-button>
            <el-button
              round
              :loading="scope.row.exportLoading"
              size="mini"
              @click="() => exportLog(scope.row)"
            >
              {{ $t("lang.rms.fed.exportLog") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: right">
        <geek-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total-page="pageCount"
          @currentPageChange="currentPageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
    </div>
    <task-detail
      v-if="dialogVisible"
      :visible.sync="dialogVisible"
      :initRow="dialogInitRow"
      @debugModelSearch="debugModelSearch"
    ></task-detail>
  </geek-main-structure>
</template>

<script>
// lang.rms.fed.isSure 是否确定{0}
// lang.rms.fed.cancelTask 取消任务
// lang.rms.fed.deleteTask 删除任务
let defaultData = new Date(new Date().toLocaleDateString());
import taskDetail from "./components/taskDetailV2.vue";
export default {
  components: { taskDetail },
  name: "TaskManage",
  data() {
    return {
      form: {
        stationId: "",
        taskId: "",
        taskState: "",
        createTime: [defaultData, defaultData],
        updateTime: [defaultData, defaultData],
        jobType: "DELIVER_SHELF",
      },
      page: {
        pageSize: 10,
        currentPage: 1,
      },
      pageCount: 0,
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          // 任务iD
          jobId: {
            label: "lang.rms.fed.taskId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 任务类型
          jobType: {
            label: "lang.rms.fed.taskType",
            default: ["DELIVER_SHELF", "DELIVER_BOX"],
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
            multiple: true,
            collapseTags: true,
            filterable: true,
            rules: [{ required: true, message: this.$t("lang.rms.fed.choose"), trigger: "change" }],
          },
          // 任务状态
          jobState: {
            label: "lang.rms.web.monitor.robot.taskState",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
          },
          // 机器人ID
          robotId: {
            label: "lang.rms.fed.listRobotId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 货架ID
          shelfCode: {
            label: "lang.rms.fed.containerId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 任务指令
          jobInstruction: {
            label: "lang.rms.fed.taskInstruction",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 充电站ID
          chargeStationId: {
            label: "lang.rms.web.charger.chargerId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 工作站ID
          stationId: {
            label: "lang.rms.fed.stationId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.placeHolder",
          },
          // 创建时间
          createTime: {
            label: "lang.rms.web.monitor.robot.taskCreateTime",
            default: [defaultData, defaultData],
            valueFormat: "yyyy-MM-dd",
            type: "daterange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startTime",
            "end-placeholder": "lang.rms.fed.endTime",
          },
          // 更新时间
          updateTime: {
            label: "lang.rms.fed.updateTime",
            default: [defaultData, defaultData],
            valueFormat: "yyyy-MM-dd",
            type: "daterange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startTime",
            "end-placeholder": "lang.rms.fed.endTime",
          },
        },
        rules: [],
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
          // {
          //   label: "lang.rms.fed.buttonExport",
          //   handler: "on-export",
          //   type: "success",
          // },
        ],
      },
      tableData: [],
      taskDialogList: [],
      detailDialogInfo: null,
      dayTime: 24 * 60 * 60 * 1000 - 1000, // 到当前0点
      cacheRowExpanison: [], // 保存当前页面打开的rowExpansion;
      tabList: ["DELIVER_SHELF", "DELIVER_BOX"],
      activeTab: "DELIVER_SHELF", // 激活的tab
      dialogVisible: false, // 任务详情弹框
      dialogInitRow: {}, // 任务详情内容。
      curTimer: 0,
    };
  },
  computed: {
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
  activated() {
    this.getSearchConditionValue();
    this.getTableList();
  },
  methods: {
    handleTabClick() {
      this.page.currentPage = 1;
      this.form = Object.assign(this.form, { jobType: this.activeTab });
      this.getTableList();
    },
    recordExpandChange(row, status) {
      if (status) {
        this.cacheRowExpanison = [...new Set(this.cacheRowExpanison.concat(row.uuid))];
      } else {
        const set = new Set(this.cacheRowExpanison);
        set.delete(row.uuid);
        this.cacheRowExpanison = [...set];
      }
    },
    async handlerDelete({ row }) {
      const { code } = await $req.post("/athena/task/monitor/conditionsNew/del", {
        jobId: row.jobId,
        jobType: row.jobType,
      });
      if (!code) {
        this.getTableList();
        this.$message.success(this.$t("lang.common.success"));
      }
    },
    async handlerClose({ row }) {
      const { code } = await $req.post("/athena/task/monitor/conditionsNew/cancel", {
        jobId: row.jobId,
        jobType: row.jobType,
      });
      if (!code) {
        this.getTableList();
        this.$message.success(this.$t("lang.common.success"));
      }
    },
    loadChildTask(row, treeNode, resolve) {
      const { createTime, updateTime, jobType, ...others } = this.form;

      let temporaryCreateTime = createTime ? createTime.map(item => new Date(item).getTime()) : "";
      let temporaryUpdateTime = updateTime ? updateTime.map(item => new Date(item).getTime()) : "";
      if (temporaryCreateTime && temporaryCreateTime[1]) temporaryCreateTime[1] += this.dayTime;
      if (temporaryUpdateTime && temporaryUpdateTime[1]) temporaryUpdateTime[1] += this.dayTime;
      $req
        .get("/athena/task/monitor/conditionsNew/jobStage", {
          ...(this.form || {}),
          jobId: row.jobId,
          createTime: temporaryCreateTime ? temporaryCreateTime.join("-") : "",
          updateTime: temporaryUpdateTime ? temporaryUpdateTime.join("-") : "",
        })
        .then(res => {
          if (!res.data || !res.data.recordList) return resolve([]);
          return resolve(res.data.recordList.map(i => ({ ...i, uuid: `task-${i.jobStageId}` })));
        });
    },
    getTableList() {
      const { createTime, updateTime, jobType, ...others } = this.form;

      let temporaryCreateTime = createTime ? createTime.map(item => new Date(item).getTime()) : "";
      let temporaryUpdateTime = updateTime ? updateTime.map(item => new Date(item).getTime()) : "";
      if (temporaryCreateTime && temporaryCreateTime[1]) temporaryCreateTime[1] += this.dayTime;
      if (temporaryUpdateTime && temporaryUpdateTime[1]) temporaryUpdateTime[1] += this.dayTime;
      const data = {
        ...others,
        createTime: temporaryCreateTime ? temporaryCreateTime.join("-") : "",
        updateTime: temporaryUpdateTime ? temporaryUpdateTime.join("-") : "",
        pageSize: this.page.pageSize,
        currentPage: this.page.currentPage,
        jobType: jobType || "DELIVER_SHELF",
      };

      // if (jobType === "DELIVER_BOX") {
      //   data.warehouseJob = true
      // }

      $req.get("/athena/task/monitor/conditionsNew", data).then(res => {
        let result = res.data;
        if (result != null) {
          let list = result.recordList;
          list.forEach(item => {
            item.uuid = `parent-${item.jobId}`;
            item.detailLoad = false;
            item.exportLoading = false;
            if (item.jobType !== "DELIVER_BOX") return; // 只有这种类型显示子类型；
            item.children = [];
            item.hasChildren = true;
          });
          this.tableData = list;
          this.page.currentPage = result.currentPage || 1;
          this.pageCount = result.pageCount;
          this.curTimer = +new Date();
          this.$nextTick(() => this.checkDefaultRowExpand());
        }
      });
    },
    checkDefaultRowExpand() {
      const cache = this.cacheRowExpanison;
      if (!cache.length) return;
      for (let i = 0, item; (item = this.tableData[i++]); ) {
        cache.includes(item.uuid) && this.$refs.myTable.toggleRowExpansion(item, false);
      }
    },
    // 分页
    currentPageChange(val) {
      this.page.currentPage = val;
      this.getTableList();
    },
    // 改变每页显示条数
    pageSizeChange(val) {
      this.page.pageSize = val;
      this.getTableList();
    },
    onQuery(val) {
      this.page.currentPage = 1;
      const { jobType } = val;
      this.tabList = jobType;
      if (!jobType.includes(this.activeTab)) {
        this.activeTab = jobType[0];
      }
      this.form = Object.assign(this.form, val, { jobType: this.activeTab });
      this.getTableList();
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    onExport() {
      const { boxCode, stationId, taskId, taskState, createTime } = this.form;
      let temporaryCreateTime = createTime ? createTime.map(item => new Date(item).getTime()) : "";
      if (temporaryCreateTime && temporaryCreateTime[1]) temporaryCreateTime[1] += this.dayTime;
      const data = {
        taskId,
        taskState,
        boxCode,
        stationId,
        createTime: temporaryCreateTime ? temporaryCreateTime.join("-") : "",
      };
      $req.get("/athena/task/monitor/exportTaskExcel", data).then(res => {
        if (res.code === 0) {
          if ($req.isDev) {
            window.open($req.API_URL + res.data);
          } else {
            window.open(window.location.origin + res.data);
          }
        }
      });
    },
    getRowKey(row) {
      let rowID = row.jobStageId || row.jobId;
      return String(rowID);
    },
    async exportLog(row) {
      row.exportLoading = true;
      const params = {
        jobId: row.jobStageId || row.jobId,
      };
      if (row.jobType === "DELIVER_BOX") {
        params.warehouseJob = true;
      }
      const { code, data } = await $req.postParams(
        `athena/engine/tools/job/generateJobLog`,
        params,
      );
      row.exportLoading = false;
      if (code === 0) {
        const exportUrl = `${location.origin}/${data.uri}`;
        // 取url最后一个/后面的字符串作为文件名
        const fileName = exportUrl.substring(exportUrl.lastIndexOf("/") + 1);
        // 下载url
        const a = document.createElement("a");
        a.href = exportUrl;
        a.download = fileName;
        a.target = "_blank";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    },
    getSearchConditionValue() {
      $req.get("/athena/task/monitor/querySearchConditionValue").then(res => {
        const jobTypeList = [];
        const jobStateList = [];
        res.data.jobType.forEach(item => {
          jobTypeList.push({
            label: item,
            value: item,
          });
        });
        res.data.jobState.forEach(item => {
          jobStateList.push({
            label: item,
            value: item,
          });
        });

        this.formConfig.configs.jobType.options = jobTypeList;
        this.formConfig.configs.jobState.options = jobStateList;
      });
    },

    async handleTaskDetail(row, params = {}) {
      row.detailLoad = true;
      const paramsObj = {
        jobId: row.jobStageId || row.jobId,
      };
      if (row.jobType === "DELIVER_BOX") {
        paramsObj.warehouseJob = true;
      }
      const { data } = await $req.get("/athena/engine/tools/job/showJobDetail", {
        ...paramsObj,
        ...params,
      });
      row.detailLoad = false;
      this.dialogVisible = true;
      this.dialogInitRow = data ? { ...row, ...data } : { ...row };
    },

    debugModelSearch() {
      this.handleTaskDetail(this.dialogInitRow, {
        traceLevel: "debug",
      });
    },
  },
};
</script>

<style lang="less" scoped>
.task-state {
  display: block;
}

.ui-preBlock {
  padding: 15px;
  background: #2d2d2d;
  text-align: left;
  color: #fff;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow: auto;
}

.icon-span {
  position: absolute;
  color: #ff0000;
  right: 0;
  top: 12px;
  transform: rotate(30deg);
  font-size: 14px;
  border: 1px dashed #ff0000;
  border-radius: 5px;
  background: rgba(255, 0, 0, 0.05);
}
</style>
