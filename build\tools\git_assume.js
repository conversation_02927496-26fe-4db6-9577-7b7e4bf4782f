const log4js = require("log4js");
const execSync = require("child_process").execSync; // 同步子进程
const buildConfig = require("../../config/common.conf");

const GIT_ENV = process.env.CONF_ENV || "local"; // 环境变量
const assumeFiles = ["./config/app.dev.conf.js", `${buildConfig.linkPath["monitor2D"]}/build/config.js`];
function gitAssume() {
  let logger = log4js.getLogger("git.assume");
  logger.level = "debug";

  logger.info("当前build CONF_ENV:", GIT_ENV);
  try {
    if (GIT_ENV == "jenkins") {
      // jenkins取消忽略提交rms 某些文件
      assumeFiles.forEach(file => {
        execSync(`git update-index --no-skip-worktree ${file}`);
        execSync(`git update-index --no-assume-unchanged ${file}`);
      });
    } else {
      assumeFiles.forEach(file => {
        execSync(`git update-index --skip-worktree ${file}`);
        logger.info("忽略git提交当前文件：", file);
      });
    }
  } catch (e) {
    logger.error(e);
  }
}

gitAssume();
