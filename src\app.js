/* ! <AUTHOR> at 2021/01 */

import "element-ui/lib/theme-chalk/index.css";
import "@less/index.less";
import Vue from "vue";

import "@libs";
import store from "@store"; // 引入store
import router from "@route"; // 引入router
import i18n from "@lang"; // 引入国际化

// 系统加载前需要运行的内容
await $utils.rmsInitConfig();

// 右上角error-log
Vue.config.productionTip = false;
Vue.config.errorHandler = function (err, vm, info) {
  Vue.nextTick(() => {
    const errInfo = {
      err,
      vm,
      info,
    };
    store.dispatch("addErrorLog", errInfo);
    console.error(err);
  });
};

import ElementUI from "element-ui";
import Plugins from "@plugins"; // 项目自定义全局组件ui

Vue.use(ElementUI, {
  i18n: (key, value) => i18n.t(key, value),
  size: "small",
});
Vue.use(Plugins);

// element-geek
import elementGeek from "element-geek";
import "element-geek/style.css";
import "element-geek/theme/index.scss";
Vue.use(elementGeek, { i18n: (key, value) => i18n.t(key, value) });

// 引入事件总线（焦add）
Vue.prototype.$EventBus = new Vue();
import Root from "@views/root";

console.log(store);
window.$app = new Vue({
  store: store,
  router,
  i18n,
  render: h => h(Root),
}).$mount("app");
