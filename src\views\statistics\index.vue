<template>
  <geek-main-structure :space="false" style="padding: 0; background: #f4f5f7">
    <geek-tabs-nav class="tab-class" :nav-list="navList" :default-active="activeNavId" @select="tabsNavChange" />
    <component :is="componentName" :options="curData" :componentId="activeNavId" />
  </geek-main-structure>
</template>

<script>
import previewChart from "./dashboard/preview.vue";
import statisticsMap from "./components/map";
import dashboardOther from './dashboard/chart/dashboard.other';
import dashboardEfficiency from './dashboard/chart/dashboard.efficiency';
import dashboardRobotStaIndicators from './dashboard/chart/dashboard.robotStaIndicators';
import dashboardHome from './dashboard/chart/dashboard.home';
import dashboardTaskVolume from './dashboard/chart/dashboard.taskVolume';
import distanceAndDuration from './dashboard/chart/dashboard.distanceAndDuration';

export default {
  name: "statistics",
  components: { previewChart, statisticsMap },
  data() {
    return {
      activeNavId: "home",
      navList: [
        {
          id: "home",
          component: "previewChart",
          text: "图表(首页)",
          data: dashboardHome,
        },
        {
          id: "dashboard_efficiency",
          component: "previewChart",
          text: "图表（任务耗时分析）",
          data: dashboardEfficiency,
        },
        {
          id: "dashboard_robotStaIndicators",
          component: "previewChart",
          text: "图表（机器人统计指标相关）",
          data: dashboardRobotStaIndicators,
        },
        {
          id: "dashboardTaskVolume",
          component: "previewChart",
          text: "图表（任务量统计）",
          data: dashboardTaskVolume,
        },
        {
          id: "distanceAndDuration",
          component: 'previewChart',
          text: "图表(搬运时长和距离统计)",
          data: distanceAndDuration
        },
        {
          id: "statisticsChart",
          component: "previewChart",
          text: "图表(其他)",
          data: dashboardOther,
        },
        {
          id: "statisticsMap",
          component: "statisticsMap",
          text: "地图热度",
        },
      ],
    };
  },
  activated() {
    this.activeNavId = "home";
  },
  computed: {
    curData() {
      return this.navList.find(item => this.activeNavId === item.id).data;
    },
    componentName() {
      return this.navList.find(item => {
        return item.id === this.activeNavId;
      })?.component;
    },
  },
  methods: {
    tabsNavChange(id) {
      if (this.activeNavId === id) return;
      this.activeNavId = id;
    },
  },
};
</script>

<style lang="less" scoped>
@tab-nav-height: 31px;

.tab-class {
  width: 100%;
  height: @tab-nav-height;
  padding: 0 12px;
  color: #fff;

  :deep(> .el-menu-item) {
    padding-top: 2px;
    height: @tab-nav-height;
    line-height: @tab-nav-height;
    font-size: 13px;
  }
}
</style>
