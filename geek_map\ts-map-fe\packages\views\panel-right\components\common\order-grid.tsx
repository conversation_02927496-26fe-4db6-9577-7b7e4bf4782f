import { CSSProperties } from "react";

type PropsGridData = {
  items: Array<{ label: string; value: any; style?: CSSProperties }>;
  style?: CSSProperties;
};
function OrderGrid(props: PropsGridData) {
  return (
    <table className="component-flex-grid" style={props.style}>
      <tbody>
        {props.items.map((item, index) => {
          let value = item.value;
          const vType = Object.prototype.toString.call(value);
          if (value === "--") return null;
          else if (vType === "[object Object]" || vType === "[object Array]") {
            value = JSON.stringify(item.value);
          }
          return (
            <tr key={index}>
              <td className="item-label" colSpan={1}>
                {item.label}
              </td>
              <td className="item-value" colSpan={1} style={item?.style || null}>
                {value}
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
}
export default OrderGrid;
