/* ! <AUTHOR> at 2022/08/31 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { getMap2D, $eventBus } from "../../singleton";

function DialogHelp() {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [stationIds, setStationIds] = useState<any>([]);

  useEffect(() => {
    $eventBus.on("mapStationPosition", res => {
      const data = res?.data || null;
      if (!data) {
        setVisible(false);
        setStationIds([]);
        return;
      }

      setVisible(true);
      setStationIds(data);
    });
    return () => {
      $eventBus.off("mapStationPosition");
    };
  }, []);

  return (
    visible && (
      <section className="map2d-pop-station">
        {stationIds.map((item: any, index: number) => {
          return (
            <span
              key={index}
              className="map-pop-item"
              style={{
                top: item.bounds.y + item.bounds.height / 2,
                left: item.bounds.x + item.bounds.width / 2,
                transform: `translate(-50%, -50%) scale(${item.scale})`,
              }}
            >
              {item.code}
            </span>
          );
        })}
      </section>
    )
  );
}

export default DialogHelp;
