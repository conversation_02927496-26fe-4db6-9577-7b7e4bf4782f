import { ToolPanelType } from "@packages/type/editUiType";
import {
  AREA_TRAFFIC_LIGHT,
  AREA_STOP,
  AREA_ROBOT,
  AREA_SHELF,
  AREA_TASK_CONTROL,
  AREA_NO_STAY,
  AREA_BLOCK,
  AREA_SINGLE_LANE,
  AREA_TRAFFIC_CONTROL,
  AREA_HIGHWAY_AREA,
  AREA_SORTING_AREA,
  // AREA_RESTRICT_BIG_ARC_AREA,
  AREA_EMPTYING_AREA,
  AREA_CUSTOM_AREA,
  AREA_RESTRICT_BIG_ARC_AREA,
  AREA_ADD_FN,
  GATHERING_AREA,
  STATIC_SPEED_LIMIT_AREA,
  REAL_TIME_SPEED_LIMIT_AREA,
  OBSTACLE_AVOIDANCE_AREA,
  PLC_LIMIT_AREA,
  HIGH_ALTITUDE_OBSTACLE_AREA,
  SLAM_NAVIGATION_AREA,
  CLOSE_OBSTACLE_AVOIDANCE_AREA,
  RESOURCE_ISOLATION_AREA,
} from "@packages/configure/dict/nodeType";

// 添加区域
export const ADD_TRAFFIC_LIGHT_AREA: ToolPanelType = {
  option: {
    icon: "map-font-baojing",
    name: "addTrafficLightArea",
    describe: "lang.rms.fed.area.trafficLights",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_TRAFFIC_LIGHT,
  },
};
// 区域设置-系统急停区域
export const ADD_STOP_AREA: ToolPanelType = {
  option: {
    icon: "map-font-stop",
    name: "addStopArea",
    describe: "lang.rms.fed.area.systemStop",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_STOP,
  },
};

// 区域 - 交通管制区域
export const ADD_TRAFFIC_CONTROL_AREA: ToolPanelType = {
  option: {
    icon: "map-font-jiaotongguanzhi-copy",
    name: "addTrafficControl",
    describe: "lang.rms.fed.search.type.logic.area.trafficControl",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_TRAFFIC_CONTROL,
  },
};

// 区域 - 高速路区域
export const ADD_HIGHWAY_AREA: ToolPanelType = {
  option: {
    icon: "map-font-gaosulukou",
    name: "addHighwayArea",
    describe: "lang.rms.fed.addExpresswayArea",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_HIGHWAY_AREA,
  },
};

// 区域设置 - 机器人调度;
export const ADD_ROBOT_AREA: ToolPanelType = {
  option: {
    icon: "map-font-robot",
    name: "addRobotArea",
    describe: "lang.rms.map.edit.area.guidance.robotArea",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_ROBOT,
  },
};

// 区域设置 - 货架区域;
export const ADD_SHELF_AREA: ToolPanelType = {
  option: {
    icon: "map-font-shinshophuojia",
    name: "addShelfArea",
    describe: "lang.rms.map.edit.shelfArea",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_SHELF,
  },
};

// 区域设置 - 任务控制区域;
export const ADD_TASK_CONTROL_AREA: ToolPanelType = {
  option: {
    icon: "map-font-task-limit",
    name: "addTaskControlArea",
    describe: "lang.rms.fed.area.controlTask",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_TASK_CONTROL,
  },
};

// 区域设置 - 禁止停留区域;
export const ADD_NO_STAY_AREA: ToolPanelType = {
  option: {
    icon: "map-font-no-stay",
    name: "addNoStayArea",
    describe: "lang.rms.fed.search.type.logic.area.noStay",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_NO_STAY,
  },
};

// 区域设置 - 分拣区;
export const ADD_SORTING_AREA: ToolPanelType = {
  option: {
    icon: "map-font-ercifenjian",
    name: "addSortingArea",
    describe: "lang.rms.fed.sortingArea",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_SORTING_AREA,
  },
};

// 区域设置 - 清洁区域;
export const ADD_AREA_EMPTYING: ToolPanelType = {
  option: {
    icon: "map-font-qingxishi",
    name: "addAreaEmptying",
    describe: "lang.rms.fed.area.cleaningArea",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_EMPTYING_AREA,
  },
};

// 区域设置 - 地图区块;
export const ADD_BLOCK_AREA: ToolPanelType = {
  option: {
    icon: "map-font-addCell",
    name: "addBlockArea",
    describe: "lang.rms.map.edit.mapBlock",
    eventName: "map:addArea",
    group: "area",
    isSelect: true,
    areaType: AREA_BLOCK,
  },
};

// 区域设置 - 单行道独木桥
export const ADD_ONEWAY_STREET: ToolPanelType = {
  option: {
    icon: "map-font-chedaozhanyong",
    name: "addOneWayStreet",
    describe: "lang.rms.fed.area.singlaneAndBridge",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: AREA_SINGLE_LANE,
  },
};

// 区域设置 - 自定义蒙层
export const ADD_CUSTOM: ToolPanelType = {
  option: {
    icon: "map-font-18",
    name: "addCustom",
    describe: "lang.rms.fed.area.customArea",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: AREA_CUSTOM_AREA,
  },
};

// 区域设置 - 限制弧线转弯区域
export const ADD_RESTRICT_BIG_ARC_AREA: ToolPanelType = {
  option: {
    icon: "map-font-mengban",
    name: "addRestrictBigArcArea",
    describe: "限制弧线转弯区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: AREA_RESTRICT_BIG_ARC_AREA,
  },
};
//集合区域
export const ADD_GATHERING_AREA: ToolPanelType = {
  option: {
    icon: "map-font-jihe",
    name: "addAssemblyArea",
    describe: "集合区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: GATHERING_AREA,
  },
}
//静态限速区域
export const ADD_STATIC_SPEED_LIMIT_AREA: ToolPanelType = {
  option: {
    icon: "map-font-quyu",
    name: "addLimitSpeedArea",
    describe: "静态限速区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType:STATIC_SPEED_LIMIT_AREA,
  },
}
//动态限速区域
export const ADD_REAL_TIME_SPEED_LIMIT_AREA: ToolPanelType = {
  option: {
    icon: "map-font-ziyuan",
    name: "addRealTimeLimitSpeedArea",
    describe: "动态限速区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: REAL_TIME_SPEED_LIMIT_AREA,
  },
}
//避障区域
export const ADD_OBSTACLE_AVOIDANCE_AREA: ToolPanelType = {
  option: {
    icon: "map-font-luzhang",
    name: "addObstacleLimitArea",
    describe: "避障区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: OBSTACLE_AVOIDANCE_AREA,
  },
}

//安全PLC避障区域
export const ADD_PLC_LIMIT_AREA: ToolPanelType = {
  option: {
    icon: "map-font-anquan",
    name: "addPLCArea",
    describe: "安全PLC避障区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: PLC_LIMIT_AREA,
  },
}
//高空避障区域
export const ADD_HIGH_ALTITUDE_OBSTACLE_AREA: ToolPanelType = {
  option: {
    icon: "map-font-gaokongqiuqian",
    name: "addHighAltitudeObstacleArea",
    describe: "高空避障区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: HIGH_ALTITUDE_OBSTACLE_AREA,
  },
}
//slam导航区域
export const ADD_SLAM_NAVIGATION_AREA: ToolPanelType = {
  option: {
    icon: "map-font-sen027",
    name: "addSlamGuideArea",
    describe: "slam导航区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: SLAM_NAVIGATION_AREA,
  },
}
//关闭避障区域
export const ADD_CLOSE_OBSTACLE_AVOIDANCE_AREA: ToolPanelType = {
  option: {
    icon: "map-font-mengban",
    name: "addCloseObstacleArea",
    describe: "关闭避障区域",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: CLOSE_OBSTACLE_AVOIDANCE_AREA,
  },
}
//资源隔离区域
export const ADD_RESOURCE_ISOLATION_AREA: ToolPanelType = {
  option: {
    icon: "map-font-stacker",
    name: "addResourceIsolationArea",
    describe: "lang.rms.fed.area.resourceIsolation",
    group: "area",
    eventName: "map:addArea",
    isSelect: true,
    areaType: RESOURCE_ISOLATION_AREA,
  },
}
export const AREA_ADD: ToolPanelType = {
  option: {
    icon: "map-font-mengban",
    name: "addArea",
    describe: "新增区域",
    group: "area",
    // eventName: "map:addArea",
    eventName: "map:addAreaTrigger",
    isSelect: true,
    areaType: AREA_ADD_FN,
  },
};
// 区域设置 -
// export const ADD_CUSTOM: ToolPanelType = {
//   option: {
//     icon: "map-font-mengban",
//     name: "addCustom",
//     describe: "lang.rms.fed.area.customMask",
//     group: "area",
//     isSelect: true,
//   },
// };
