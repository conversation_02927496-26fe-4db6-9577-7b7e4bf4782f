<template>
  <div :id="'stage'+activeName" class="canvas-map-image">
    <canvas :id="'canvas'+activeName" />
  </div>
</template>

<script>
import { fabric } from 'fabric'
import floorUtils from '../floorBg/utils'
import {
  Drag,
  RotateFree,
  Ruler,
  WallErase,
  WallFree,
  WallStraightLine,
  GroundErase,
  GroundFree,
  GroundStraightLine,
  GroundRegion
} from '../floorBg/events/index'

export default {
  name: 'BgCanvas',
  props: {
    zoom: {
      type: Number,
      require: true
    },
    maxZoom: {
      type: Number,
      require: true
    },
    minZoom: {
      type: Number,
      require: true
    },
    actionType: {
      type: String,
      require: true
    },
    actionAttr: {
      type: Object,
      require: true
    },
    activeName: {
      type: String,
      require: true
    },
    mapData: {
      type: Object,
      require: true
    }
  },
  data() {
    return {
      $stage: null,
      $fabric: null,
      $img: null,

      canvasParams: {
        screenWidth: 0,
        screenHeight: 0,
        wordWidth: 0,
        wordHeight: 0,
        centerX: 0,
        centerY: 0,
        pointX: 0,
        pointY: 0
      },
      layers: {
        back: null,
        wall: null,
        ground: null
      },

      history: [],
      maxHistoryLength: 50,

      actionEvents: null,
      currentEvent: null,
      mousedownFn: null,
      mousemoveFn: null,
      mouseupFn: null
    }
  },
  watch: {
    actionType(v, oldV) {
      if (!this.actionEvents) return
      console.log(v)
      const beforeEvent = this.actionEvents[oldV]
      beforeEvent && beforeEvent.destroy && beforeEvent.destroy()
      this.currentEvent = this.getActionEvents()
    },
    'actionAttr.angle'(angle) {
      if (angle === -1) return

      const { ground, wall, back } = this.layers
      if (ground) ground.image.set('angle', angle).setCoords()
      if (wall) wall.image.set('angle', angle).setCoords()
      if (back) back.image.set('angle', angle).setCoords()
      if (this.$fabric) this.$fabric.requestRenderAll()
    },

    'actionAttr.wallBoundary'(wallBoundary) {
      const currentEvent = this.currentEvent
      if (!currentEvent || !currentEvent.setBoundary) return
      currentEvent.setBoundary(wallBoundary)
    },

    'actionAttr.groundBoundary'(groundBoundary) {
      const currentEvent = this.currentEvent
      if (!currentEvent || !currentEvent.setBoundary) return
      currentEvent.setBoundary(groundBoundary)
    }
  },
  // mounted() {
  //   console.log('mounted====')
  //     this.initFun()
  // },
  activated() {
    this.initFun()
  },
  deactivated() {
    if (this.$fabric && this.$fabric.clear) this.$fabric.clear()
    Object.assign(this.$data, this.$options.data())
  },
  methods: {
    initFun() {
      const $stage = (this.$stage = document.getElementById('stage' + this.activeName))
      Object.assign(this.canvasParams, {
        screenWidth: $stage.clientWidth,
        screenHeight: $stage.clientHeight
      })
      $stage.style.width = this.canvasParams.screenWidth + 'px'
      $stage.style.height = this.canvasParams.screenHeight + 'px'

      this.actionEvents = {
        movement: new Drag(this),
        ruler: new Ruler(this),
        wallErase: new WallErase(this), // 墙-擦除
        wallFree: new WallFree(this), // 墙-添加/自由
        wallStraightLine: new WallStraightLine(this), // 墙-添加/直线
        groundErase: new GroundErase(this), // 地面擦除
        groundFree: new GroundFree(this), // 地面-添加/自由
        groundStraightLine: new GroundStraightLine(this), // 地面-添加/直线
        groundRegion: new GroundRegion(this), // 地面-添加/直线
        rotateFree: new RotateFree(this) // 旋转-自由
      }
      this.currentEvent = this.getActionEvents()
    },
    async drawImage(mapData) {
      const layersData = await floorUtils.getMapImageData(mapData)
      const { width, height } = layersData
      Object.assign(this.canvasParams, { wordWidth: width, wordHeight: height })
      this.resolution = mapData.resolution || 0.02
      this._initZoom()
      this._drawImage(layersData)
      this.scaleImage('init')

      this.rotateChange(mapData.rotate)
    },
    scaleImage(type) {
      this.$nextTick(() => {
        let x = 0
        let y = 0
        if (type === 'slider') {
          const { centerX, centerY } = this.canvasParams
          x = centerX
          y = centerY
        } else if (type === 'init') {
          const { wordWidth, wordHeight, screenWidth, screenHeight } = this.canvasParams
          const ws = screenWidth - (wordWidth * this.zoom) / 100
          const hs = screenHeight - (wordHeight * this.zoom) / 100
          if (ws > 0) x = ws / 2
          if (hs > 0) y = hs / 2
        } else {
          const { pointX, pointY } = this.canvasParams
          x = pointX
          y = pointY
        }
        this.$fabric.zoomToPoint({ x, y }, this.zoom / 100)
      })
    },

    getDataURL() {
      const { wordWidth, wordHeight } = this.canvasParams
      const canvas = document.createElement('canvas')
      canvas.width = wordWidth
      canvas.height = wordHeight
      const canvas2d = canvas.getContext('2d')

      const { back, ground, wall } = this.layers
      canvas2d.drawImage(back.canvas, 0, 0, wordWidth, wordHeight)
      canvas2d.drawImage(ground.canvas, 0, 0, wordWidth, wordHeight)
      canvas2d.drawImage(wall.canvas, 0, 0, wordWidth, wordHeight)
      return canvas.toDataURL()
    },

    // 点击撤销
    operateRevoke() {
      const len = this.history.length
      if (len <= 0) return null

      const { layerName, canvas } = this.history.pop()
      const layer = this.layers[layerName]
      const layer2d = layer.canvas2d

      layer2d.clearRect(0, 0, layer.width, layer.height)
      layer.canvas2d.drawImage(canvas, 0, 0)

      this.$fabric.requestRenderAll()
      this.$emit('historyChange', this.history.length)
    },

    setHistory(layerName) {
      const { wordWidth, wordHeight } = this.canvasParams

      const canvas = document.createElement('canvas')
      canvas.width = wordWidth
      canvas.height = wordHeight
      const canvas2d = canvas.getContext('2d')
      canvas2d.drawImage(this.layers[layerName].canvas, 0, 0)

      const len = this.history.length
      if (len >= this.maxHistoryLength) {
        this.history.shift()
      }

      this.history.push({ layerName, canvas })
      this.$emit('historyChange', this.history.length)
    },

    // 获取当前操作索要运行的function
    getActionEvents() {
      console.log(this.actionType)
      if (this.actionType === 'rotateCompass') return null

      const currentEvent = this.actionEvents[this.actionType]
      if (!currentEvent) throw new Error('没有定义actionType：未知操作!')
      currentEvent.init && currentEvent.init()

      this.mousedownFn = currentEvent.start.bind(currentEvent)
      this.mousemoveFn = currentEvent.move.bind(currentEvent)
      this.mouseupFn = currentEvent.end.bind(currentEvent)

      return currentEvent
    },

    /* 自由旋转设置角度 */
    rotateChange(rotate) {
      if (rotate < 0) {
        rotate += 360
      } else if (rotate > 359) {
        rotate %= 360
      } else if (!rotate) {
        rotate = 0
      }

      this.$emit('rotateChange', null, rotate)
    },

    _initZoom() {
      if (this.zoom !== -1) return
      const { wordWidth, wordHeight, screenWidth, screenHeight } = this.canvasParams

      const scale =
        Math.floor(Math.min(screenWidth / wordWidth, screenHeight / wordHeight) * 1000) / 1000

      this.$set(this.canvasParams, 'centerX', screenWidth / 2)
      this.$set(this.canvasParams, 'centerY', screenHeight / 2)

      const zoom = scale * 100
      const min = zoom > 10 ? 10 : zoom
      this.$emit('update:zoom', zoom)
      this.$emit('setZoomMin', min)
    },
    _drawImage(layersData) {
      let $fabric = this.$fabric
      const canvasName = 'canvas' + this.activeName
      if (!$fabric) {
        $fabric = new fabric.Canvas(canvasName, {
          preserveObjectStacking: true,
          selection: false,
          controlsAboveOverlay: false,
          skipOffscreen: true,
          hoverCursor: 'default',
          isDrawingMode: false,
          selectable: false
        })
        this.$fabric = $fabric
        this.__bindEvent()
      }
      if ($fabric.clear) $fabric.clear()

      const { screenWidth, screenHeight } = this.canvasParams
      $fabric.setWidth(screenWidth)
      $fabric.setHeight(screenHeight)

      const { back, ground, wall } = floorUtils.resolveImageData(layersData)

      const layerBack = floorUtils.transImageData(back)
      $fabric.add(layerBack.image)
      this.$set(this.layers, 'back', layerBack)

      const layerGround = floorUtils.transImageData(ground)
      $fabric.add(layerGround.image)
      this.$set(this.layers, 'ground', layerGround)

      const layerWall = floorUtils.transImageData(wall)
      $fabric.add(layerWall.image)
      this.$set(this.layers, 'wall', layerWall)
    },
    __bindEvent() {
      const $fabric = this.$fabric
      $fabric.on('mouse:wheel', event => {
        event.e.preventDefault()
        event.e.stopPropagation()
        const delta = event.e.deltaY
        if (delta) {
          let zoom = this.zoom - delta / 15
          if (zoom > this.maxZoom) {
            zoom = this.maxZoom
          } else if (zoom < this.minZoom) {
            zoom = this.minZoom
          }
          this.$emit('update:zoom', zoom)
          this.scaleImage()
        }
      })

      $fabric.on('mouse:down', event => {
        const actionType = this.actionType
        if (actionType === 'rotateCompass') return

        if (['wallErase', 'wallFree', 'wallStraightLine'].includes(actionType)) {
          // 这些事件需要加history记录
          this.setHistory('wall')
        } else if (
          ['groundErase', 'groundFree', 'groundStraightLine', 'groundRegion'].includes(actionType)
        ) {
          // 这些事件需要加history记录
          this.setHistory('ground')
        }
        this.mousedownFn(event)
      })
      $fabric.on('mouse:move', event => {
        const { offsetX, offsetY } = event.e
        this.canvasParams.pointX = offsetX
        this.canvasParams.pointY = offsetY

        if (this.actionType === 'rotateCompass') return
        this.mousemoveFn(event)
      })
      $fabric.on('mouse:up', event => {
        if (this.actionType === 'rotateCompass') return
        this.mouseupFn(event)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.canvas-map-image {
  position: relative;
  width: 100%;
  height: 100%;
  background: #eee;
  overflow: hidden;
}
</style>
