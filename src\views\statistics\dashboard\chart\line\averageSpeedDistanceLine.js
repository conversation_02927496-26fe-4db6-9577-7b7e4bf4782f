import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 路径规划比率
 */
export default class AverageSpeedDistanceLine extends Chart {
  /**
   * 初始化图表 - 路径规划比率
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('line', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "路径规划比率";

    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'elDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        option: {
          type: "datetimerange"
        }
      },
      // 机器人类型, 任务类型, 任务动作, 机器人指令
      // https://confluence.geekplus.cc/pages/viewpage.action?pageId=177542368
    ]
  }

  async request(params) {
    const date = params?.date || [];
    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 7;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);


    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/path/plan/stat/sumByDayList', {
      startTime,
      endTime,
      statType: 'PATH_DISTANCE_TO_MIN_ASTAR_DISTANCE',
      ...params
    })

    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const servers = [];

    // 机器人类型
    const robotTypeKeys = Object.keys(data.type2Data || {});
    robotTypeKeys.forEach(robotType => {
      // 机器人任务类型

      if (robotType !== 'DEFAULT') {
        const robotTypeItem = data.type2Data?.[robotType] || [];
        servers.push({
          name: robotType,
          type: 'line',
          data: robotTypeItem.DEFAULT.DEFAULT.DEFAULT || []
        })
        // Object.keys(robotTypeItem).forEach(robotAction => {
        //   // 机器人任务动作
        //   if (robotAction !== 'DEFAULT') {
        //     const robotAcItem = robotTypeItem[robotAction];
        //     Object.keys(robotAcItem).forEach(instruct => {
        //       // 动作指令

        //       if (instruct !== 'DEFAULT') {

        //       }
        //     })
        //   }
        // })
      }
    });
    return parseEchartOption({
      title: { text: this.title || '' },
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: 50,
        right: 20,
        bottom: 30,
        top: 50
      },
      xAxis: {
        type: 'category', data: (data.xAxis || []).map(item => {
          return $utils.Tools.formatDate(item, "yyyy-MM-dd hh:mm:ss")
        })
      },
      yAxis: { type: 'value' },
      series: servers
    })
  }
}