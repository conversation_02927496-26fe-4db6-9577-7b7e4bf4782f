import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { DownOutlined } from "@ant-design/icons";
import { Button, Dropdown, Menu, message } from "antd";
import { getMap2D } from "../../../singleton";

function BoxTool() {
  const { t } = useTranslation();
  const [selectedKeys, setSelectedKeys] = useState([]);
  const items = [
    {
      key: "ranging",
      label: t("lang.rms.web.monitor.toolbox.ranging"),
    },
  ];
  const menuClick = ({ key }: any) => {
    setSelectedKeys([key]);

    const map2D = getMap2D();
    map2D.mapRender.trigger("ranging", data => {
      if (data.code !== 0) {
        message.error("超出楼层范围");
      }
    });
  };

  /** esc键 退出测距 */
  useEffect(() => {
    const escKeydown = (e: any) => {
      if (e.keyCode !== 27) return; // 按 ESC
      setSelectedKeys([]);
    };
    document.addEventListener("keydown", escKeydown, false);
    return () => {
      document.removeEventListener("keydown", escKeydown, false);
    };
  }, []);

  return (
    <Dropdown
      className="map2d-top-box-tool"
      overlay={
        <Menu
          items={items}
          selectable
          selectedKeys={selectedKeys}
          onSelect={menuClick}
          className="map2d-top-panel-dropdown"
        />
      }
      placement="bottomRight"
      arrow
    >
      <Button size="small" style={{ fontSize: "13px" }}>
        {t("lang.rms.web.monitor.toolbox")}
        <DownOutlined />
      </Button>
    </Dropdown>
  );
}

export default BoxTool;
