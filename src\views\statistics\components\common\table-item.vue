<template>
  <div class="centent">
    <div class="table">
      <div v-if="title" class="title">{{ $t(title) }}</div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column v-for="item in tableCloumns" :key="item.prop" :prop="item.prop" :label="$t(item.label)" :width="item.width">
          <template slot-scope="scope">
            <div v-if="item.type === 'time'">
              {{ toTime(scope.row[item.prop] || 0) }}
            </div>
            <div v-else>
              {{ scope.row[item.prop] }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: "table-item",
  props: {
    option: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },

  data() {
    return {};
  },

  computed: {
    title() {
      return this.option?.title || [];
    },
    tableData() {
      return this.option?.tableData || [];
    },
    tableCloumns() {
      return this.option?.tableCloumns || [];
    }
  },

  mounted() {
  },

  destroyed() {
  },

  methods: {
    toTime(time) {
      return $utils.Tools.formatDate(Number(time), "yyyy-MM-dd");
    }
  },
};
</script>

<style lang="less" scoped>
.centent {
  width: 100%;
  height: 100%;
  position: relative;
}

.title {
  height: 30px;
  line-height: 30px;
  text-align: center;
}

.table {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  background: #FFF;
  border: 0.01rem solid #eee;
  border-radius: 0.03rem;
  box-shadow: 0 0.02rem 0.12rem 0 rgb(0 0 0 / 10%);
  overflow: auto;
}
</style>
