/* ! <AUTHOR> at 2022/09/08 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseOutlined } from "@ant-design/icons";
import { Card, Select, Button } from "antd";
import { getMap2D } from "../../../../singleton";

import OrderPanelGrid from "../common/order-panel-grid";

const { Option } = Select;
type PropsOrderData = {
  visible: boolean;
  shelfCode: code;
  onCancel: () => void;
};
function ShelfUpdateAngle(props: PropsOrderData) {
  const { t } = useTranslation();
  const [shelfAngle, setShelfAngle] = useState("");

  // 初始
  useEffect(() => {
    if (!props.visible) return;
    return () => {
      setShelfAngle("");
    };
  }, [props.visible]);

  const controlHandler = () => {
    const reqMsg = "ShelfInstructionRequestMsg";
    const resMsg = "ShelfInstructionResponseMsg";
    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket(reqMsg, {
        shelfCode: props.shelfCode,
        instruction: "UPDATE_SHELF_ANGLE",
        shelfAngle,
      })
      .then(res => {
        if (res.msgType !== resMsg) return;
        _$utils.wsCmdResponse(res?.body || {});
      });

    props.onCancel();
  };

  return (
    props.visible && (
      <Card
        size="small"
        type="inner"
        title={t("lang.rms.fed.updateShelfAngle")}
        extra={<CloseOutlined onClick={props.onCancel} />}
        actions={[
          <Button
            type="primary"
            onClick={controlHandler}
            size="small"
            style={{ float: "right", marginRight: 6 }}
          >
            {t("lang.rms.fed.confirm")}
          </Button>,
        ]}
        className="component-operate-detail"
      >
        <OrderPanelGrid
          style={{ border: 0, margin: 0 }}
          items={[
            {
              label: t("lang.rms.fed.shelfAngle"),
              node: (
                <Select style={{ width: "100%" }} onChange={value => setShelfAngle(value)}>
                  <Option value="0">0</Option>
                  <Option value="90">90</Option>
                  <Option value="180">180</Option>
                  <Option value="-90">-90</Option>
                </Select>
              ),
            },
          ]}
        />
      </Card>
    )
  );
}

export default ShelfUpdateAngle;
