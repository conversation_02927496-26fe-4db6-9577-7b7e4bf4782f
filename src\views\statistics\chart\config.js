/**
 * 这个文件主要进行一些图表的配置, 以及常量的定义
 */
import * as echarts from "echarts";

/**
 * echarts 主题注册, 如有修改可在下方链接生成新的主题
 * https://echarts.apache.org/zh/theme-builder.html
 */
import echartsShineConfig from "./echarts.config.json";
echarts.registerTheme("shine", echartsShineConfig)

export const DEF_OPTION = {
  title: {
    text: "",
  },
  tooltip: {
    trigger: "axis",
    // position: ["100%", "100%"],
    confine: true,
    extraCssText: "overflow-y:auto;max-height:500px;pointer-events:auto !important;z-index: 2",
  },
  toolbox: {
    feature: {
      saveAsImage: {},
    },
  },
  grid: {
    left: "2%",
    right: "4%",
    bottom: "15%",
    containLabel: true,
  },
  legend: {
    data: [],
    type: "scroll",
  },
  animation: false,
  xAxis: { data: [] },
  yAxis: {},
  dataZoom: [
    {
      type: "slider",
      xAxisIndex: 0,
      filterMode: "none",
    },
    {
      type: "inside",
      xAxisIndex: 0,
      filterMode: "none",
    },
  ],
  series: [],
}

export const CHART_REQUEST_DEF = {
  apiUrl: "",
  dataKey: "",
  title: "",
}

export const LINE_COLORS = {
  a: [
    "#4D000A",
    "#770611",
    "#A1161F",
    "#CB2E34",
    "#F54E4E",
    "#F76965",
    "#F98D86",
    "#FBB0A7",
    "#FDD1CA",
    "#771E05",
    "#A23714",
    "#CC5729",
    "#F77E45",
    "#F9925A",
    "#FAAD7D",
    "#FCC6A1",
    "#FDDEC5",
    "#FFF4EB",
    "#793004",
    "#A64B0A",
    "#D26913",
    "#FF8D1F",
    "#FF9626",
    "#FFB357",
    "#FFCD87",
    "#FFE3B8",
  ],
  b: [
    "#4D2D00",
    "#774B04",
    "#A26F0F",
    "#CC961F",
    "#F7C034",
    "#F9CC44",
    "#FADC6C",
    "#FCE995",
    "#FDF4BE",
    "#FFFCE8",
    "#785E07",
    "#A38614",
    "#CFB325",
    "#FAE13C",
    "#FBE94B",
    "#FCF374",
    "#FDFA9D",
    "#FEFEC6",
  ],
  c: [
    "#447006",
    "#629412",
    "#84B723",
    "#A8DB39",
    "#B8E24B",
    "#CBE970",
    "#DEF198",
    "#EEF8C2",
    "#004D1C",
    "#046625",
    "#0A802D",
    "#129A37",
    "#1DB440",
    "#27C346",
    "#50D266",
    "#7EE18B",
    "#B2F0B7",
    "#EBFFEC",
  ],
  d: [
    "#00424D",
    "#06616C",
    "#11838B",
    "#1FA6AA",
    "#30C9C9",
    "#3FD4CF",
    "#66DFD7",
    "#90E9E1",
    "#BEF4ED",
    "#F0FFFC",
    "#001A4D",
    "#052F78",
    "#134CA3",
    "#2971CF",
    "#469AFA",
    "#5AAAFB",
    "#7DC1FC",
    "#A1D5FD",
    "#C6E8FE",
    "#041B79",
    "#0E32A6",
    "#1D4DD2",
    "#306FFF",
    "#3C7EFF",
    "#689FFF",
    "#93BEFF",
    "#BEDAFF",
  ],
  e: [
    "#16004D",
    "#27066E",
    "#3E138F",
    "#5A25B0",
    "#7B3DD1",
    "#8E51DA",
    "#A974E3",
    "#C59AED",
    "#DFC2F6",
    "#42004D",
    "#650370",
    "#8A0D93",
    "#B01BB6",
    "#D92ED9",
    "#E13DDB",
    "#E866DF",
    "#F092E6",
    "#F7C1F0",
    "#4D0034",
    "#770850",
    "#A1176C",
    "#CB2B88",
    "#F545A6",
    "#F756A9",
    "#F97AB8",
    "#FB9EC8",
    "#FDC3DB",
    "#FFE8F1",
  ],
};
