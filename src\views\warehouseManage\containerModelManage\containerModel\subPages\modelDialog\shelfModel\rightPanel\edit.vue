<template>
  <!-- 编辑任务 -->
  <div class="editTask">
    <el-row>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        <span class="taxt-red">*</span> {{ $t("lang.rms.fed.shelfModelAlias") }}:
      </el-col>
      <el-col :span="14">
        <el-input
          v-model="editTaskData.modelName"
          :disabled="viewDisabled"
          size="mini"
          maxlength="32"
          :placeholder="$t('lang.rms.fed.pleaseEnterContainerModelName')"
        />
      </el-col>
    </el-row>

    <el-row>
      <!-- 货架类别 -->
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        <span class="taxt-red">*</span> {{ $t("lang.rms.fed.classCode") }} :
      </el-col>
      <el-col :span="14">
        <el-select
          v-model="editTaskData.categoryId"
          :disabled="viewDisabled"
          :placeholder="$t('lang.rms.fed.choose')"
        >
          <el-option
            v-for="item in shelfCategoryDict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-col>
    </el-row>
    <!-- <el-row>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.api.result.warehouse.supportedSizeType") }} :
      </el-col>
      <el-col :span="14">
        <el-select
          v-model="editTaskData.sizeTypes"
          :disabled="viewDisabled"
          filterable
          multiple
          allow-create
          default-first-option
          :placeholder="$t('lang.rms.fed.choose')"
        >
          <el-option
            v-for="item in sizeTypeDict"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </el-col>
    </el-row> -->
    <el-row>
      <!-- 是否可移动 -->
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle"> {{ $t("lang.rms.fed.isItMovable") }} :</el-col>
      <el-col :span="14">
        <el-select
          v-model="editTaskData.move"
          :disabled="viewDisabled"
          :placeholder="$t('lang.rms.fed.choose')"
        >
          <el-option :label="$t('lang.rms.web.container.canNotMove')" value="0" />
          <el-option :label="$t('lang.rms.web.container.canMove')" value="1" />
        </el-select>
      </el-col>
    </el-row>
    <el-row>
      <!-- 是否下发给机器人 -->
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.containerManage.needSendRobot.msg") }} :
      </el-col>
      <el-col :span="14">
        <el-select
          v-model="editTaskData.needSendRobot"
          :disabled="viewDisabled"
          :placeholder="$t('lang.rms.fed.choose')"
          @change="val => handleNeedSendRobot(val)"
        >
          <el-option :label="$t('lang.rms.fed.no')" value="0" />
          <el-option :label="$t('lang.rms.fed.yes')" value="1" />
        </el-select>
      </el-col>
    </el-row>
    <el-row>
      <!-- 下发模型ID -->
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        <el-tooltip
          class="item"
          effect="dark"
          :content="$t('lang.rms.containerManage.sendModelId.tips')"
          placement="top"
        >
          <el-button type="text"><i class="el-icon-question" /></el-button>
        </el-tooltip>
        <span>{{ $t("lang.rms.containerManage.sendModelId.msg") }}</span> :
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.sendModelId"
          :disabled="viewDisabled || String(editTaskData.needSendRobot) === '0'"
          :min="0"
          size="mini"
          :step="1"
        />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="modelTitle">{{ $t("lang.rms.fed.shelfSize") }}:</el-col>
      <!-- 货架尺寸 -->
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.length") }}(mm):
      </el-col>
      <!-- 长 -->
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.length"
          :disabled="viewDisabled"
          :min="0"
          :max="99999999"
          size="mini"
          :step="1"
        />
      </el-col>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.width") }}(mm):
      </el-col>
      <!-- 宽 -->
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.width"
          :disabled="viewDisabled"
          :min="0"
          :max="99999999"
          size="mini"
          :step="1"
        />
      </el-col>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">{{ $t("lang.rms.fed.shelfHeight") }}(mm):</el-col>
      <!-- 货架高度 -->
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.height"
          :disabled="viewDisabled"
          :min="0"
          :max="100000"
          size="mini"
          :step="1"
        />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="modelTitle">{{ $t("lang.rms.fed.shelfFoot") }}:</el-col>
      <!-- 货架腿 -->
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.length") }}(mm):
      </el-col>
      <!-- 长 -->
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.legLength"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].shelfMaxHLen"
          size="mini"
          :step="1"
        />
      </el-col>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.width") }}(mm):
      </el-col>
      <!-- 宽 -->
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.legWidth"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].shelfMaxWLen"
          size="mini"
          :step="1"
        />
      </el-col>
    </el-row>

    <!-- 通行 -->
    <el-row>
      <el-col :span="24" class="modelTitle">{{ $t("lang.rms.fed.container.pass") }}:</el-col>
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.length") }}(mm):
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.passLength"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].passMaxLen"
          size="mini"
          :step="1"
        />
      </el-col>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle">
        {{ $t("lang.rms.web.monitor.cell.fieldPrefix.width") }}(mm):
      </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.passWidth"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].passMaxWidth"
          size="mini"
          :step="1"
        />
      </el-col>
      <el-col :span="24" class="hr" />
      <el-col :span="10" class="labelTitle"> {{ $t("lang.rms.fed.textHeight") }}(mm): </el-col>
      <el-col :span="14">
        <el-input-number
          v-model="editTaskData.passHeight"
          :disabled="viewDisabled"
          :min="0"
          :max="dockModelMap[dockModelType].passMaxHeight"
          size="mini"
          :step="1"
        />
      </el-col>
    </el-row>
    <div class="dividerStyle" />
    <div class="buttomBtnSeat" />
  </div>
</template>

<script>
import { mapMutations, mapState } from "vuex";
const defaultEditTaskData = () =>
  JSON.parse(
    JSON.stringify({
      modelCategory: "SHELF",
      modelName: "",
      categoryId: "",
      length: "",
      width: "500",
      height: "500",
      legLength: "",
      legWidth: "",
      move: "1",
      needSendRobot: "0",
      sendModelId: "",
      passLength: 0,
      passWidth: 0,
      passHeight: 0,
    }),
  );

export default {
  props: {
    viewDisabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      modelId: "",
      // 编辑任务数据
      editTaskData: defaultEditTaskData(),
      // loading
      saveLoading: false,
      requsetTaskLoading: false,
      requsetTaskTypeListLoading: false,
      dockModelType: "def",
      dockModelMap: {
        M100: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1000,
          shelfDefHLen: 800,
          shelfMaxWLen: 1100,
          shelfMaxHLen: 900,
          shelfMinWLen: 600,
          shelfMinHLen: 600,
        },
        M1000: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1220,
          shelfDefHLen: 1020,
          shelfMaxWLen: 1500,
          shelfMaxHLen: 1500,
          shelfMinWLen: 1020,
          shelfMinHLen: 1020,
        },
        def: {
          legDefWLen: 40,
          legDefHLen: 40,
          shelfDefWLen: 1000,
          shelfDefHLen: 1000,
          shelfMaxWLen: 50000,
          shelfMaxHLen: 50000,
          shelfMinWLen: 300,
          shelfMinHLen: 300,
          passMaxLen: 50000,
          passMaxWidth: 50000,
          passMaxHeight: 50000,
        },
      },
    };
  },
  computed: {
    ...mapState("containerModal", ["maxModelId", "shelfCategoryDict", "sizeTypeDict"]),
    isEmpty() {
      return this.$store.state.containerModal.emptySwich;
    },
    editData() {
      console.log(this.$store.state.containerModal.editData)
      return this.$store.state.containerModal.editData;
    },
  },
  watch: {
    isEmpty() {
      setTimeout(() => {
        this.claerData();
      }, 500);
    },
    editTaskData: {
      handler(val) {
        this.setHJModalData(val);
      },
      deep: true,
    },
    editData: {
      handler(val) {
        const newValue = Object.assign({}, val);
        if (newValue.id ?? "" !== "")
          this.editTaskData = Object.assign(newValue, {
            move: String(newValue.move ?? ""),
            needSendRobot: String(newValue.needSendRobot ?? "0"),
          });
        else this.editTaskData = JSON.parse(JSON.stringify(defaultEditTaskData()));
      },
      deep: true,
      immediate: true,
    },
  },
  activated() {
    this.dockModelType = "def";
  },
  methods: {
    ...mapMutations("containerModal", ["setHJModalData"]),
    claerData() {
      this.editTaskData = defaultEditTaskData();
    },
    handleNeedSendRobot(val) {
      if (String(val) === "0") this.editTaskData.sendModelId = "";
      else if (!this.editTaskData.sendModelId) this.editTaskData.sendModelId = this.maxModelId;
    },
  },
};
</script>

<style scoped lang="less">
.labelTitle {
  text-align: right;
  padding-right: 8px;
}
.editTask {
  height: 100%;
  display: flex;
  flex-direction: column;

  .modelTitle {
    text-align: left;
    height: 30px;
    font-weight: 600;
  }

  .hr {
    height: 5px;
  }

  .pointsBoxsMain {
    flex: 1;
    padding: 10px;
    overflow: auto;
    border-radius: 10px;
    border: 1px solid #ccc;
  }

  .taskTitle {
    text-align: left;
    padding-top: 20px;
    padding-left: 10px;
  }

  .taskAddTaskTitle {
    height: 32px;
    line-height: 32px;
    text-align: left;
  }

  .pointsBox {
    text-align: left;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 10px;
    position: relative;
    margin-bottom: 10px;

    .clearPoint {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }

  .buttomBtnSeat {
    height: 50px;
  }

  .buttomBtns {
    margin: 20px 0;
    display: block;
    width: 100%;
    text-align: center;
  }
}

.divider-hr {
  margin: 12px 0;
}

.mb5 {
  margin-bottom: 5px;
}

.mb15 {
  margin-bottom: 15px;
}

.dividerStyle {
  display: block;
  height: 1px;
  width: 100%;
  margin: 24px 0;
  background-color: #dcdfe6;
  position: relative;
}

.renwalBtn {
  width: 100%;
  height: 42px;
  margin-top: 20px;
}
.taxt-red {
  color: red;
}
</style>
