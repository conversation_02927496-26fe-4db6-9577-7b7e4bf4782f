import { useRobotAnimal } from "../../../../utils/model-func";
import { addMethods } from "../../../../utils/utils";
import globalConfig from "../../../../config";
import * as THREE from "three";

class P40 {
  constructor(Map3d) {
    this.Map3d = Map3d;
  }
  getRobotMesh(item) {
    const mesh = this.Map3d.modelInstances[item.uuid].model;
    return mesh.children[0].children
      .filter(i => !i.name.includes("robot-p40-box"))
      .map(i => {
        i.userData.uuid = item.uuid;
        return i;
      });
  }
  afterRender(item) {
    const model = this.Map3d.modelInstances[item.uuid].model;
    this.__visibleBox(item, model);
    // hover
    const body = model.getObjectByName("robot-p40");
    const box3 = new THREE.Box3().setFromObject(body.clone());
    const hover = new THREE.Box3Helper(box3, globalConfig.THEME.HOVER_3D);
    hover.name = "hover";
    hover.visible = false;
    body.parent.add(hover);
    // select
    const select = new THREE.Box3Helper(box3, globalConfig.THEME.SELECTED);
    select.name = "select";
    select.visible = false;
    body.parent.add(select);

    model.rotation.y = -item.radAngle;

    addMethods(body);
  }
  update(oldValue, newVal) {
    if (!this.Map3d.modelInstances[oldValue.uuid]) return;
    const mesh = this.Map3d.modelInstances[oldValue.uuid].model;
    const { taskPhase: nt } = newVal;
    const { location: ov, radAngle: r } = oldValue;
    mesh.userData.curLocation = [ov.x, ov.y, r];
    // 更新机器人任务状态；
    this.__renderRobotStatus(newVal, mesh);
    // 更新是否有货箱
    this.__visibleBox(newVal, mesh);
    // 坐标更新动画
    this.__useRobotAnimal(newVal, mesh);
    if (nt !== "BOX_ARRIVED") return;
    this.__useRobotLift(newVal, mesh);
  }
  __visibleBox(item, model) {
    const lattice = (item["onloadRack"]?.lattices || [])[0];
    const mesh = model.getObjectByName("robot-p40-box");
    mesh.visible = lattice && lattice.latticeStatus === "OCCUPIED";
  }
  __renderRobotStatus(nv, mesh) {
    const body = mesh.getObjectByName("robot-p40");
    const workEffect = ["GO_SOMEWHERE_TO_STAY"];
    body.status = nv.taskId && !workEffect.includes(nv.taskType) ? "work" : "normal";
  }
  __useRobotAnimal(nv, mesh) {
    const { location, radAngle } = nv;
    useRobotAnimal({ mesh, location: [location.x, location.y, radAngle] });
  }
  // 播发机器人动画 - 调试阶段实现；
  __useRobotLift(nv, mesh) {
    const { obstacleCount: no } = nv;
    const plant = mesh.getObjectByName("robot-p40-plant");
    const boxMesh = mesh.getObjectByName("robot-p40-box");
    if (no === 700) {
      // todo: 上升 && box location
      plant.scale.y = 1;
      boxMesh.position.y = 0.33 + 0.33 / 2;
    }
    if (no === 282) {
      // todo: 下降 && box location
      plant.scale.y = 0.7;
      boxMesh.position.y = 0.23 + 0.23 / 2;
    }
  }
}
export default P40;
