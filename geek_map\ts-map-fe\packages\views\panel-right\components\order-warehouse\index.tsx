/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { CloseOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { But<PERSON>, Collapse, Modal } from "antd";
import { getMap2D } from "../../../../singleton";

import OrderGrid from "../common/order-grid";
import TaskCreate from "./task-create";
import TaskDetail from "./task-detail";

const { confirm } = Modal;
const { Panel } = Collapse;
type PropsOrderData = {
  isCurrent: boolean;
  mapReleased: boolean;
};
function OrderWarehouse(props: PropsOrderData) {
  const { t } = useTranslation();
  const [isCreate, setIsCreate] = useState(false);
  const [taskList, setTaskList] = useState([]);
  const [taskDetailId, setTaskDetailId] = useState("");
  const intervalRef = useRef(null);

  // 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;
    setIsCreate(false);
    const map2D = getMap2D();
    map2D.mapRender.triggerLayers([]);

    getTaskList();
    return () => {
      setTaskList([]);
      setTaskDetailId("");
      map2D.mapRender.trigger("stop");
      if (intervalRef.current) {
        clearTimeout(intervalRef.current);
      }
    };
  }, [props.isCurrent]);

  const getTaskList = () => {
    if (!props.isCurrent) return;
    _$utils.reqGet("/athena/warehouse/inspection/display").then(res => {
      const data = res.data || [];
      const taskList: any = data.map((item: any) => {
        let _textDuration;
        let _startTime = _$utils.formatDate(item?.startTime, "yyyy-MM-dd hh:mm:ss");
        if (item.hasOwnProperty("durationTime")) {
          let mss: number = item?.durationTime || 0;
          var hours = parseInt((mss / (1000 * 60 * 60)).toString());
          var minutes = parseInt(((mss % (1000 * 60 * 60)) / (1000 * 60)).toString());
          var seconds = parseInt(((mss % (1000 * 60)) / 1000).toString());
          _textDuration = [
            hours ? hours + " : " : "",
            minutes ? minutes + " : " : "",
            seconds ? seconds : 0,
          ].join("");
        } else {
          _textDuration = 0;
        }
        item.startTime = _startTime;
        item.textDuration = _textDuration;
        return item;
      });
      setTaskList(taskList);

      if (intervalRef.current) clearTimeout(intervalRef.current);
      intervalRef.current = setTimeout(() => {
        getTaskList();
      }, 1000);
    });
  };

  const deleteTask = (e: any, inspectionId: any, index: number) => {
    e.stopPropagation();
    confirm({
      icon: <ExclamationCircleOutlined />,
      content: t("lang.rms.fed.confirmDelete"),
      onOk() {
        _$utils
          .reqPost("/athena/warehouse/inspection/delete", {
            inspectionId,
          })
          .then(() => {
            taskList.splice(index, 1);
            setTaskList([...taskList]);
          });
      },
    });
  };

  const finishTask = (inspectionId: any) => {
    confirm({
      icon: <ExclamationCircleOutlined />,
      content: t("lang.rms.fed.confirmTheOperation"),
      onOk() {
        _$utils
          .reqPost("/athena/warehouse/inspection/finish", {
            inspectionId,
          })
          .then(() => {
            let currentItem = taskList.find(item => item.inspectionId === inspectionId);
            if (currentItem) {
              currentItem.inspectionState = "COMPLETED";
            }
            setTaskList([...taskList]);
          });
      },
    });
  };

  const exportTask = (inspectionId: any) => {
    _$utils
      .reqPost("/athena/warehouse/inspection/exportExcel", {
        inspectionId,
        language: _$utils.getLocalLang(),
      })
      .then(res => {
        if (!__rms_env_conf.isPro) {
          window.open(__rms_env_conf.API_URL + res.data);
        } else {
          window.open(window.location.origin + res.data);
        }
      });
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component map2d-control-btn-group"
    >
      <div className="panel-right-title">{t("lang.rms.fed.warehouseMgmt")}</div>
      <Button
        size="middle"
        type="primary"
        block
        style={{ textAlign: "center", marginTop: 0 }}
        onClick={() => setIsCreate(true)}
      >
        {t("lang.rms.fed.buttonNewWork")}
      </Button>

      {isCreate && (
        <TaskCreate
          mapReleased={props.mapReleased}
          refreshList={getTaskList}
          onCancel={() => setIsCreate(false)}
        />
      )}

      <Collapse defaultActiveKey={[0]} accordion className="warehouse-task-list">
        {taskList.map((item, index) => {
          return (
            <Panel
              key={index}
              header={
                <p className="warehouse-task-header">
                  <label>{item.inspectionName}</label>
                  {item.inspectionState !== "COMPLETED" && (
                    <span className="danger">
                      {t("lang.rms.fed.textProgress")}
                      {item.progress}%
                    </span>
                  )}
                  {item.inspectionState === "COMPLETED" && (
                    <span className="danger">{t("lang.rms.fed.textCompleted")}</span>
                  )}
                  <a onClick={e => deleteTask(e, item.inspectionId, index)}>
                    <CloseOutlined />
                  </a>
                </p>
              }
            >
              <div className="warehouse-radio">
                <Button size="small" type="link" onClick={() => finishTask(item.inspectionId)}>
                  {t("lang.rms.fed.buttonEnd")}
                </Button>
                <Button size="small" type="link" onClick={() => exportTask(item.inspectionId)}>
                  {t("lang.rms.fed.buttonExport")}
                </Button>
                <Button size="small" type="link" onClick={() => setTaskDetailId(item.inspectionId)}>
                  {t("lang.rms.fed.buttonView")}
                </Button>
              </div>

              <OrderGrid
                style={{ borderBottom: 0, borderLeft: 0, borderRight: 0, margin: 0 }}
                items={[
                  {
                    label: t("lang.rms.fed.textStartTime"),
                    value: item?.startTime || "--",
                  },
                  {
                    label: t("lang.rms.fed.textDuration"),
                    value: item?.textDuration || "--",
                  },
                  {
                    label: t("lang.rms.fed.inputRobotId"),
                    value: item?.robotId || "--",
                  },
                  {
                    label: t("lang.rms.fed.textCellInspectionCase"),
                    value: t(item.detailedProgress),
                  },
                  {
                    label: t("lang.rms.fed.textAbnormal"),
                    value: t(item.errorNum),
                    style: { color: "#ff2328" },
                  },
                ]}
              />
            </Panel>
          );
        })}
      </Collapse>

      {taskDetailId && <TaskDetail id={taskDetailId} onCancel={() => setTaskDetailId("")} />}
    </div>
  );
}

export default OrderWarehouse;
