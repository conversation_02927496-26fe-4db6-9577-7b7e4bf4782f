<template>
  <el-card v-show="detailData.boxCode" shadow="never" class="component-operate-detail">
    <div slot="header" class="header">
      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>
    </div>
    <order-group-grid>
      <grid-item :label="$t('lang.rms.fed.boxCode')" :value="detailData.boxCode || '--'" />
      <grid-item :label="$t('lang.rms.fed.boxStatus')" :value="detailData.boxStatus || '--'" />
      <grid-item
        :label="$t('lang.rms.fed.boxCurrentLocation')"
        :value="detailData.location || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.placeLatticeCode')"
        :value="detailData.placeLatticeCode || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.currentLatticeCode')"
        :value="detailData.currentLatticeCode || '--'"
      />
      <grid-item :label="$t('lang.rms.fed.robotId')" :value="detailData.robotId || '--'" />
      <grid-item
        :label="$t('lang.rms.fed.locationCode')"
        :value="detailData.locationCode || '--'"
      />
    </order-group-grid>
  </el-card>
</template>

<script>
import OrderGroupGrid from "../common/order-group-grid.vue";
import GridItem from "../common/order-group-grid-item.vue";

export default {
  name: "DetailBox",
  components: { OrderGroupGrid, GridItem },
  props: {
    detailData: {
      type: Object,
      require: true,
    },
    detailTitle: {
      type: String,
      require: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.component-operate-detail {
  background: #fbfbfb;

  ::v-deep tr > td {
    padding-bottom: 0;
  }
}
</style>
