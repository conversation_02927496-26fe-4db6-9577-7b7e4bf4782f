/* ! <AUTHOR> at 2022/09/08 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseOutlined, InfoCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { Card, Tag, Button, Input } from "antd";
import { getMap2D } from "../../../../singleton";

import OrderPanelGrid from "../common/order-panel-grid";

type PropsOrderData = {
  visible: boolean;
  cellData: cellData;
  robotId: code;
  onCancel: () => void;
};
function RobotGoTo(props: PropsOrderData) {
  const { t } = useTranslation();
  const [isEmpty, setIsEmpty] = useState(false);
  const [cellCode, setCellCode] = useState("");

  // 地图点击 地图切换可点击层
  useEffect(() => {
    if (!props.visible) return;
    const map2D = getMap2D();
    map2D.mapRender.enableMultiClick(true);
    map2D.mapRender.triggerLayers(["cell"]);

    return () => {
      setIsEmpty(false);
      setCellCode("");
      map2D.mapRender.enableMultiClick(false);
      map2D.mapRender.clearSelects("cell");
      map2D.mapRender.triggerLayers(["robot"]);
    };
  }, [props.visible]);

  // cellCode 变化
  useEffect(() => {
    const cellData = props.cellData;
    const newCellCode = cellData?.cellCode || "";
    if (newCellCode !== cellCode) {
      const map2D = getMap2D();
      map2D.mapRender.clearSelects("cell", [cellCode]);
      setCellCode(newCellCode);
    }
  }, [props.cellData]);

  const controlHandler = () => {
    if (!cellCode) {
      setIsEmpty(true);
      return;
    }

    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket("RobotInstructionRequestMsg", {
        robotId: props.robotId,
        instruction: "GO_SOMEWHERE",
        destCode: cellCode,
      })
      .then(res => {
        if (res.msgType !== "RobotInstructionResponseMsg") return;
        _$utils.wsCmdResponse(res?.body || {});
      });

    props.onCancel();
  };

  return (
    props.visible && (
      <Card
        size="small"
        type="inner"
        title={t("lang.rms.fed.goTo")}
        extra={<CloseOutlined onClick={props.onCancel} />}
        actions={[
          <Button
            type="primary"
            onClick={controlHandler}
            size="small"
            style={{ float: "right", marginRight: "8px" }}
          >
            {t("lang.rms.fed.confirm")}
          </Button>,
        ]}
        className="component-operate-detail"
      >
        <Tag
          icon={<InfoCircleOutlined />}
          color="processing"
          style={{ marginLeft: 8, marginTop: 8 }}
        >
          {t("lang.rms.fed.selectPointOnTheMap")}
        </Tag>
        <OrderPanelGrid
          style={{ borderLeft: 0, borderRight: 0, marginBottom: 0 }}
          items={[
            {
              label: t("lang.rms.fed.targetCoding"),
              node: (
                <Input
                  size="small"
                  readOnly={true}
                  value={props.cellData?.cellCode || ""}
                  placeholder={t("lang.rms.fed.targetCoding")}
                />
              ),
            },
            {
              label: t("lang.rms.fed.textAbsoluteCoordinate"),
              node: `${props.cellData?.location?.x || "--"},${props.cellData?.location?.y || "--"}`,
            },
            {
              label: t("lang.rms.fed.textIndexCoordinates"),
              node: `${props.cellData?.location?.x || "--"},${props.cellData?.location?.y || "--"}`,
            },
          ]}
        />

        <Tag
          icon={<CloseCircleOutlined />}
          color="error"
          style={{ marginLeft: 8, display: isEmpty ? "inline-block" : "none" }}
        >
          {t("lang.rms.fed.nodeCannotBeEmpty")}
        </Tag>
      </Card>
    )
  );
}

export default RobotGoTo;
