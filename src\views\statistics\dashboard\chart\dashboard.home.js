import TaskStatisticsCred from "./cred/taskStatisticsCred";
import TaskBeenIssuedCred from "./cred/taskBeenIssuedCred";
import TaskCompletionRateBar from "./bar/taskCompletionRateBar";
import RobotCountCred from "./cred/robotCountCred";
import RobotWorkCountCred from "./cred/robotWorkCountCred";
import TaskRobotPrSnapshot from "./bar/taskRobotPrSnapshot";
import CancelJobCountCred from "./cred/cancelJobCountCred";
import StationReceiveByTotalBar from "./bar/StationReceiveByTotalBar";
import RobotUtilizationTrendsLine from "./line/robotUtilizationTrendsLine";
import StationReceiveBySplitBar from "./bar/stationReceiveBySplitBar";
import StartLocationByCredGrop from "./cred/startLocationByCredGrop";
export default [
  // 已下发任务数量
  new TaskStatisticsCred({ width: 6, height: 5 }),
  // 已完成任务数量
  new TaskBeenIssuedCred({ width: 6, height: 5 }),
  // 取消的任务数
  new CancelJobCountCred({ width: 6, height: 5 }),
  // 任务完成率
  new TaskCompletionRateBar({ width: 7, height: 5 }),
  // 机器人总数
  new RobotCountCred({ width: 7, height: 5 }),
  // 工作中的机器人数量
  new RobotWorkCountCred({ width: 7, height: 5 }),
  // 机器人利用率
  new TaskRobotPrSnapshot({ width: 9, height: 5 }),
  // 工作站接收任务总数
  new StationReceiveByTotalBar({ width: "50%", height: "300px" }),
  // 机器人利用趋势
  new RobotUtilizationTrendsLine({ width: "50%", height: "300px" }),
  // 工作站已完成任务量分布
  new StationReceiveBySplitBar({ width: "100%", height: "300px" }),
  // startLocationOnTransfer/startLocationOnLattice/startLocationOnRobot
  new StartLocationByCredGrop({ width: "100%", height: "300px" }),
]
