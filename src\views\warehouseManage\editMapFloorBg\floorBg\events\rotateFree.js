/* ! <AUTHOR> at 2021/07 */
import { fabric } from "fabric";

class EventsRotateFree {
  constructor($vm) {
    this.$vm = $vm;

    this.$fabric = null;

    this.isDrag = false;
    this.rotateLineColor = "#409effd9";
    this.point = { x1: 0, y1: 0, x2: 0, y2: 0 };
    this.freeLine = null;
  }

  init() {
    this.$fabric = this.$vm.$fabric;
  }

  start(e) {
    this.isDrag = true;
    const { x, y } = e.absolutePointer;
    this.point = { x1: x, y1: y, x2: x, y2: y };
    // 创建线
    this.createRotateFreeLine();
  }

  move(e) {
    if (!this.isDrag) return;

    const { x, y } = e.absolutePointer;

    Object.assign(this.point, { x2: x, y2: y });
    this.updateRotateFreeLine();
  }

  end(e) {
    this.isDrag = false;

    const { x, y } = e.absolutePointer;
    Object.assign(this.point, { x2: x, y2: y });

    const { x1, y1, x2, y2 } = this.point;
    // 如果距离太短 则不进行旋转操作
    if (Math.abs(x1 - x2) + Math.abs(y1 - y2) < 30) return;
    // 旋转
    let rotate = parseFloat(180 - Math.atan2(x2 - x1, y2 - y1) * Math.PI * 18.25, 10);

    if (rotate < 90) {
      rotate = 360 + rotate - 90;
    } else {
      rotate -= 90;
    }
    rotate = this.$vm.actionAttr.angle - rotate;
    this.$vm.rotateChange(rotate);
    this.removeRotateFreeLine();
  }

  createRotateFreeLine() {
    let $fabric = this.$fabric;
    const freeLine = new fabric.Line(Object.values(this.point), {
      strokeWidth: 5,
      stroke: this.rotateLineColor,
      selectable: false,
    });
    this.freeLine = freeLine;
    $fabric.add(freeLine);
    $fabric.requestRenderAll();
  }

  updateRotateFreeLine() {
    let $fabric = this.$fabric;
    let freeLine = this.freeLine;
    const { x2, y2 } = this.point;
    freeLine.set({ x2, y2 });

    $fabric.requestRenderAll();
  }

  removeRotateFreeLine() {
    let $fabric = this.$fabric;
    let freeLine = this.freeLine;
    this.point = { x1: 0, y1: 0, x2: 0, y2: 0 };
    $fabric.remove(freeLine);
    $fabric.requestRenderAll();
    this.freeLine = null;
  }
}

export default EventsRotateFree;
