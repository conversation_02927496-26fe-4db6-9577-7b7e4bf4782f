import Base from "../../core/abstractPlugin";
import RenderRack from "./elements/render-rack";
import RenderRobot from "./elements/render-robot";
import RenderShelf from "./elements/render-shelf";
import RenderCRack from "./elements/render-crack";
import RenderStation from "./elements/render-station";
import RenderCharge from "./elements/render-charge";
import BehaviorHover from "./actions/behavior-hover";
import BehaviorSelect from "./actions/behavior-select";
import MapData from "./websocket";
import { throttle, nextTick } from "../../utils/utils";
import { updateTime } from "./constant/monitor";
import compassPluginInstance from "../common/compassPlugin";
import TWEEN from "@tweenjs/tween.js";

const use_plugins = ["compassPlugin"];

class MonitorPlugin extends Base {
  constructor(options) {
    super(options);
    this.PluginName = "monitorPlugin";
    this.actionCategory = "box"; // box -货箱、 lattices - 货位、robot - 机器人、floor - 单元格；
    this.rack = null;
    this.robot = null;
    this.mapData = null;
    this.wsUrl = options.wsUrl;
  }
  // created 才会加载map3d对象；
  created() {
    // 注册插件
    this.Map3d.registerPlugin([compassPluginInstance]);
    // 渲染行为
    this.__instanceHover();
    this.__instanceSelect();
    // 渲染layer
    this.__instanceRack();
    this.__instanceRobot();
    this.__instanceStation();
    this.__instanceShelf();
    this.__instanceCRack();
    this.__instanceCharge();
    // 加载websocket数据；
    this.__loadMapData();
  }
  activated() {
    this.Map3d.enablePlugin(use_plugins);
    this.EventInstance.add("monitor", {
      moveHandle: throttle(event => this.__handleHover(event), 32),
      clickHandle: throttle(event => this.__handleSelect(event), 64),
    });
  }
  deactivated() {
    this.EventInstance.off("monitor");
    this.Map3d.disabledPlugin(use_plugins);
  }
  destroyed() {
    if (this.rack) {
      this.rack.destory();
      this.rack = null;
    }
    if (this.robot) {
      this.robot.destory();
      this.robot = null;
    }
    if (this.crack) {
      this.crack.destory();
      this.crack = null;
    }
    if (this.shelf) {
      this.shelf.destory();
      this.shelf = null;
    }
    if (this.station) {
      this.station.destory();
      this.station = null;
    }
    if (this.behaviorHover) {
      this.behaviorHover.destory();
      this.behaviorHover = null;
    }
    if (this.behaviorSelect) {
      this.behaviorSelect.destory();
      this.behaviorSelect = null;
    }
    if (this.mapData) {
      this.mapData.destory();
      this.mapData = null;
    }

    this.EventInstance.off("monitor");
  }
  // 显示货箱热度
  showBoxHot(flag) {
    this.rack.isHeatMode = flag;
    this.rack.reRenderBox();
    this.shelf.isHeatMode = flag;
    this.shelf.renderShelfHot();
  }
  // 显示机器人路径
  showRobotPath(flag) {
    this.robot.isPath = flag;
  }
  // 放大 - threejs不支持调节
  zoomIn() {}
  // 缩小 - threejs不支持调节
  zoomOut() {}
  // 显示货箱架
  showRacksLattices(flag) {
    const ls = this.rack.getRackLayers();
    ls.forEach(l => (flag ? this.rack.showLayerId(l) : this.rack.hideLayerId(l)));
  }
  // 显示有任务货箱
  showTaskBoxs(flag) {
    this.rack.isTaskMode = flag;
    this.rack.reRenderBox();
  }
  // 显示俯视视角
  showTopView() {
    const { camera, floor } = this.Map3d;
    const { left: l, right: r, top: t, bottom: b } = floor.floorInfo;
    const { x, z } = camera.get().position;
    // const { x, z } = camera.get().position;
    let position = [];
    if (x > l && x < r && z > -t && z < -b) {
      position = [x, 0, z];
    } else {
      position = [(r + l) / 2, 0, -(b + t) / 2];
    }
    this.Map3d.camera.flyTo(position, 90);
  }

  // 显示楼层
  showRackLayers(layers) {
    const ls = [].concat(layers);
    const ols = this.rack.getRackLayers();
    ols.forEach(l => this.rack.hideBoxLayerId(l));
    ls.forEach(l => this.rack.showBoxLayerId(l));
  }

  // 显示货架层
  showRackLatticeLayer(layers) {
    const ls = [].concat(layers);
    const ols = this.rack.getRackLayers();
    ols.forEach(l => this.rack.hideLayerId(l));
    ls.forEach(l => this.rack.showLayerId(l));
  }

  // change
  changeActionType(type) {
    this.behaviorSelect.clearSelect();
    this.behaviorHover.clearHover();
    this.behaviorHover && (this.behaviorHover.actionType = type);
    this.behaviorSelect && (this.behaviorSelect.actionType = type);
  }
  // 选中
  select(data) {
    if (!data.data) return;
    if (data.actionType === "box")
      this.behaviorSelect.selectByCode({ data: [{ latticeCode: data.data }] });

    if (data.actionType === "lattice")
      this.behaviorSelect.selectByCode({ data: [{ latticeCode: data.data }] });

    if (data.actionType === "robot") {
      const robot = this.Store.getModelData("ROBOT").find(i => String(i.id) === String(data.data));
      if (!robot) return;
      const mesh = this.Map3d.modelInstances[robot.uuid];
      if (!mesh) return;
      this.behaviorSelect.selectByCode({ mesh: mesh.model });
    }

    if (data.actionType === "cell")
      this.behaviorSelect.selectByCode({ data: [this.Store.findCellByCellCode(data.data)] });

    if (data.actionType === "station") {
      const station = this.Store.getModelData("STATION").find(
        i => String(i.stationId) === String(data.data),
      );
      if (!station) return;
      const mesh = this.Map3d.modelInstances[station.uuid];
      if (!mesh) return;
      this.behaviorSelect.selectByCode({ mesh: mesh.model });
    }
  }
  clearSelect() {
    this.behaviorSelect.clearSelect();
  }
  render() {}
  __handleHover(event) {
    this.behaviorHover && this.behaviorHover.hover(event);
  }
  __handleSelect(event) {
    this.behaviorSelect && this.behaviorSelect.select(event);
  }
  __instanceRack() {
    const that = this;
    this.rack = new RenderRack({
      monitor: this,
      dispatch: {
        renderRack(mesh) {
          // 创建rack完成；
          that.Map3d.scene.add(mesh);
          that.Emitter.emit("after:renderRack", { layers: that.rack.getRackLayers() });
          that.behaviorHover.addMesh("box", that.rack.getBoxMesh());
          that.behaviorHover.addMesh("lattice", that.rack.getLayersMesh());
          that.behaviorSelect.addMesh("box", that.rack.getBoxMesh());
          that.behaviorSelect.addMesh("lattice", that.rack.getLayersMesh());
        },
      },
    });
  }
  __instanceRobot() {
    const that = this;
    this.robot = new RenderRobot({
      monitor: this,
      dispatch: {
        renderRobot() {
          that.behaviorHover.addMesh("robot", that.robot.getRobotMesh());
          that.behaviorSelect.addMesh("robot", that.robot.getRobotMesh());
        },
      },
    });
  }
  __instanceShelf() {
    const that = this;
    this.shelf = new RenderShelf({
      monitor: this,
      dispatch: {
        renderShelf() {
          // that.behaviorHover.addMesh("robot", that.robot.getRobotMesh());
          // that.behaviorSelect.addMesh("robot", that.robot.getRobotMesh());
        },
      },
    });
  }
  __instanceCRack() {
    const that = this;
    this.crack = new RenderCRack({
      monitor: this,
      dispatch: {
        renderCRack() {
          // that.behaviorHover.addMesh("robot", that.robot.getRobotMesh());
          // that.behaviorSelect.addMesh("robot", that.robot.getRobotMesh());
        },
      },
    });
  }
  __instanceCharge() {
    const that = this;
    this.charge = new RenderCharge({
      monitor: this,
    });
  }
  __instanceStation() {
    const that = this;
    this.station = new RenderStation({
      monitor: this,
      dispatch: {
        renderStation() {
          that.behaviorHover.addMesh("station", that.station.getStationMesh());
          that.behaviorSelect.addMesh("station", that.station.getStationMesh());
        },
      },
    });
  }
  __instanceHover() {
    const that = this;
    this.behaviorHover = new BehaviorHover({ monitor: that, dispatch: {} });
  }
  __instanceSelect() {
    const that = this;
    this.behaviorSelect = new BehaviorSelect({
      monitor: that,
      dispatch: {
        selectEvent(actionType, data) {
          that.Emitter.emit("selected:element", { actionType, data });
          if (actionType === "cell" && data) {
            that.mapData.setCellCodes([data.cellCode]);
          }
        },
        unSelectEvent(actionType, data) {
          if (actionType === "cell" && data && data[0]) {
            that.mapData.removeCellCode(data[0].cellCode);
          }
        },
        clearSelectEvent(actionType) {
          that.Emitter.emit("selected:element", { actionType, data: null });
        },
      },
    });
  }
  __loadMapData() {
    const that = this;
    this.mapData = new MapData(this.wsUrl, {
      FloorIdsReady(floorIds) {
        that.Emitter.emit("floorIds:ready", floorIds);
      },
      FloorsDataReady(floorsInfo) {
        that.Map3d.initMap(floorsInfo.floorsData);
        that.behaviorHover.addMesh("cell", [that.Map3d.floor.floorGeo.children[1]]);
        that.behaviorSelect.addMesh("cell", [that.Map3d.floor.floorGeo.children[1]]);
      },
      async DisplayDataReady(display) {
        if (display.racks) that.rack.create(display.racks);
        await nextTick();
        if (display.robots) that.robot.create(display.robots);
        await nextTick();
        if (display.shelves && display.shelves.length) that.shelf.create(display.shelves);
        if (display.cRacks && display.cRacks.length) that.crack.create(display.cRacks);
        await nextTick();
        if (display.stations && display.stations.length) that.station.create(display.stations);
        if (display.charges && display.charges.length) that.charge.create(display.charges);
        await nextTick();
        if (display.isFinish) {
          that.Emitter.emit("after:renderReady");
          that.Map3d.ticker.add(that.__refreshData.bind(that));
          // that.__refreshData();
        }
      },
      DisplayDataUpdate(display) {
        if (display.racks) that.rack && that.rack.update(display.racks);
        if (display.robots && display.robots.length)
          that.robot && that.robot.update(display.robots);
        if (display.shelves && display.shelves.length) that.shelf.update(display.shelves);
        if (display.cRacks && display.cRacks.length) that.crack.update(display.cRacks);
        if (display.cells && display.cells.length) that.Store.updateCellsData(display.cells);
        if (display.stations && display.stations.length) that.station.update(display.stations);
        if (display.delShelves && display.delShelves.length) {
          that.shelf.delete(display.delShelves);
          that.crack.delete(display.delShelves);
        }
        that.behaviorSelect && that.behaviorSelect.trigger();
      },
    });
    this.mapData.reqFloorMap([1]);
  }
  // 方案1
  __refreshData() {
    const allTweens = TWEEN.getAll();
    if (!allTweens || !allTweens.length) {
      const exec = throttle(() => this.mapData?.reqUpdateMap(), 34);
      exec();
    }
  }
  // 没有合适的环境测试1,2两种方案哪种好。
  // __refreshData() {
  //   const that = this;
  //   setTimeout(() => {
  //     const allTweens = TWEEN.getAll();
  //     if (allTweens && allTweens.length) {
  //       allTweens.map(i => i?.stop() || "");
  //       TWEEN.removeAll();
  //     }
  //     that.mapData && that.mapData.reqUpdateMap();
  //     that.__refreshData();
  //   }, updateTime);
  //   that.mapData && that.mapData.reqUpdateMap();
  // }
}

export default MonitorPlugin;
