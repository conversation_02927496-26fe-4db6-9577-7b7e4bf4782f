import Base from "../core/abstractPlugin";
import * as THREE from "three";
import { v4 as uuidv4 } from "uuid";

class AddElement extends Base {
  constructor(options) {
    super(options);
    this.addCategory = "";
    this.formatter = null;
    this.raycaster = new THREE.Raycaster();
    this.pointer = new THREE.Vector2();
    this.PluginName = "addPlugin";
  }
  activated() {
    this.EventInstance.add("addElement", {
      upHandle: event => this._addElement(event),
      dropHandle: event => this._addElement(event),
    });
  }
  deactivated() {
    this.addCategory = "";
    this.formatter = null;
    this.EventInstance.off("addElement");
  }
  destroyed() {
    this.addCategory = "";
    this.EventInstance.off("addElement");
    this.formatter = null;
  }
  _addElement(event) {
    const result = this.Map3d.search.get([this.Map3d.scene.getObjectByName("floorBox")], event);
    if (!result) return;
    let value = { location: result.point, startBounds: result.point };
    const cell = result.data[0];
    if (cell) value = cell;
    if (this.formatter) value = this.formatter(value);
    value.uuid = uuidv4();
    this.Map3d.command.exec("add", { value }, () => this.Map3d.Emitter.emit("after:add", value));
  }
}

export default AddElement;
