import { usePureNumber, useCodeQualified } from "@packages/hook/useRules";
import { NodeAttrEditConf } from "@packages/type/editUiType";
import DICT from "@packages/configure/dict";

// 充电站新增的配置内容
export const CHARGER_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  return {
    name: "charger",
    tabTitle: "lang.rms.fed.chargeAdd",
    labelWidth: "85px",
    formItem: [
      {
        prop: "cellCode",
        label: "lang.rms.fed.coverPoint",
      },
      // 角度
      {
        prop: "angle",
        label: "lang.rms.fed.angle",
        component: "elInputNumber",
        min: -360,
        max: 360,
        step: 1,
      },
      // 启用状态
      {
        prop: "enable",
        label: "lang.venus.web.common.enableStatus",
        component: "elSelect",
        data: DICT.ENABLE_STATUS_DICT || [],
      },
      // 交互模式
      {
        prop: "interactiveMode",
        label: "lang.rms.fed.interactiveMode",
        component: "elSelect",
        data: DICT.INTERACTIVE_MODE_DICT || [],
      },
      // 充电站ID
      {
        prop: "chargerId",
        label: "lang.rms.web.charger.chargerId",
        component: "elInput",
        maxlength: 13,
        showWordLimit: true,
        rules: [usePureNumber()],
      },
      // 外部编码
      {
        prop: "hostCode",
        label: "lang.rms.fed.hostCode",
        component: "elInput",
        maxlength: 9,
        showWordLimit: true,
        rules: [useCodeQualified()],
      },
      //机器人类型
      {
        prop: "robotTypes",
        label: "auth.rms.robotType.button.robotType",
        // describe: "lang.rms.fed.chargeInfo",
        component: "elSelect",
        multiple: true,
        data: attrStore.robotTypeDict2 || [],
        loading: !attrStore.robotTypeDict2.length,
        event: {
          visibleChange(visible: boolean) {
            if (visible) {
              attrStore.getAllRobotTypesByOnly();
            }
          },
        },
      }
    ],
  };
};
