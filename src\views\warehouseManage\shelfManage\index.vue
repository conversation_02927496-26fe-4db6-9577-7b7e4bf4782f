<template>
  <geek-main-structure style="padding-top: 0">
    <geek-tabs-nav
      ref="tabsNav"
      :block="true"
      :nav-list="permissionNavList"
      @select="tabsNavChange"
      class="shelf-management-nav"
    />
    <keep-alive>
      <component :is="activeName" @goFirstTab="goFirstTab" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import ShelfQuery from "./shelfQuery";
import ShelfEnter from "./shelfEnter";
import ShelfOperations from "./shelfOperations";
import ShelfStatic from "./shelfStatic";
import ShelfStaticHistory from "./shelfStaticHistory";
export default {
  components: { ShelfQuery, ShelfEnter, ShelfOperations, ShelfStatic, ShelfStaticHistory },
  data() {
    return {
      permissionNavList: [],
      navList: [
        { permissionName: "TabShelfQueryManagePage", id: "ShelfQuery", text: "auth.rms.shelfQuery.page" },
        { permissionName: "TabShelfAddManagePage", id: "ShelfEnter", text: "lang.rms.fed.shelfEntering" },
        {
          permissionName: "TabShelfHandleManagePage",
          id: "ShelfOperations",
          text: "lang.rms.fed.shelfOperations",
        },
        {
          permissionName: "TabShelfStaticAdjustControlPage",
          id: "ShelfStatic",
          text: "lang.rms.fed.shelfStaticAdjustmentControl",
        },
        {
          permissionName: "TabAdjustShelfControllerManagePage",
          id: "ShelfStaticHistory",
          text: "auth.rms.adjustShelfControllerManage.page",
        },
      ],
      activeName: "",
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "shelfManage"));

    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  beforeRouteLeave(to, from, next) {
    this.activeName = "";
    next();
  },
  methods: {
    goFirstTab() {
      const activeName = this.permissionNavList.length ? this.permissionNavList[0].id : "";
      this.activeName = activeName;
      this.$refs.tabsNav.trigger(activeName);
    },
    tabsNavChange(id) {
      if (this.activeName === id) return;
      this.activeName = id;
    },
  },
};
</script>
<style lang="less" scoped>
.shelf-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
