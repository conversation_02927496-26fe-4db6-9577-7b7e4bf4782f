<template>
  <div class="funcRackAngle">
    <!-- 限制规则 -->
    <el-select v-model="curSelectType" multiple>
      <el-option
        v-for="item in LIMIT_SHELF_ANGLE_CELLFUNC"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>

    <!-- 通用角度限制 -->
    <template v-if="isAngAngleLimit">
      <p class="title">通用角度限制(ANY)</p>
      <el-select v-model="fromData[LIMIT_SHELF_ANGLE_CELLFUNC_ANY][0].limitAngles" multiple>
        <el-option
          v-for="item in ANGLE_DICT"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </template>

    <template v-if="isAngAngleLimit_DESTINATION">
      <p class="title">通用角度限制(DESTINATION)</p>
      <el-select v-model="fromData[LIMIT_SHELF_ANGLE_CELLFUNC_DESTINATION][0].limitAngles" multiple>
        <el-option
          v-for="item in ANGLE_DICT"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </template>

    <template v-if="fromData[LIMIT_SHELF_ANGLE_CELLFUNC_SHELF]">
      <el-divider />
      <div v-for="(shelfItem, index) in fromData[LIMIT_SHELF_ANGLE_CELLFUNC_SHELF]" :key="index">
        <p class="title">货架类型编码</p>
        <el-select v-model="shelfItem.limitObj">
          <el-option
            v-for="item in holderTypeDict"
            :key="item.id"
            :label="item.name"
            :value="String(item.id)"
          />
        </el-select>
        <p class="title">通行角度</p>

        <div>
          <el-select class="w180" v-model="shelfItem.limitAngles" multiple>
            <el-option
              v-for="item in ANGLE_DICT"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            v-if="index === 0"
            class="circleBtn"
            type="primary"
            circle
            @click="addShelfItem"
          >
            <span class="mapFont map-font-jia"></span>
          </el-button>
          <el-button v-else class="circleBtn" type="primary" circle @click="removeShelfItem(index)">
            <span class="mapFont map-font-jianshao"></span>
          </el-button>
        </div>
      </div>
    </template>
    <template v-if="fromData[LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR]">
      <el-divider />
      <div
        v-for="(robotItem, index) in fromData[LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR]"
        :key="index"
      >
        <p class="title">机器人型号</p>
        <el-select v-model="robotItem.limitObj">
          <el-option
            v-for="item in robotTypeList"
            :key="item.id"
            :label="item.displayName"
            :value="String(item.displayName)"
          />
        </el-select>
        <p class="title">通行角度</p>

        <div>
          <el-select class="w180" v-model="robotItem.limitAngles" multiple>
            <el-option
              v-for="item in ANGLE_DICT"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            v-if="index === 0"
            class="circleBtn"
            type="primary"
            circle
            @click="addRobotItem"
          >
            <span class="mapFont map-font-jia"></span>
          </el-button>
          <el-button v-else class="circleBtn" type="primary" circle @click="removeRobotItem(index)">
            <span class="mapFont map-font-jianshao"></span>
          </el-button>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ComputedRef, toRefs, ref, Ref, watch, defineEmits } from "vue";
import {
  LIMIT_SHELF_ANGLE_CELLFUNC,
  LIMIT_SHELF_ANGLE_CELLFUNC_ANY,
  LIMIT_SHELF_ANGLE_CELLFUNC_SHELF,
  LIMIT_SHELF_ANGLE_CELLFUNC_DESTINATION,
  LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR,
} from "@packages/configure/dict/shelfAngleCellFun";
import { ANGLE_DICT } from "@packages/configure/dict/angle";
import { useAttrStore } from "@packages/store/attr";
import { storeToRefs } from "pinia";
import { keys } from "lodash-unified";
interface FromDataType {
  [k: string]: { limitObj: string; limitAngles: number[] }[];
}

interface LimitItemType {
  type: string;
  ruleMap: {
    [k: string | number]: string | number[];
  };
}

const props = defineProps<{
  fromData: LimitItemType[];
}>();

const curSelectType: Ref<string[]> = ref([]);
const fromData: Ref<FromDataType> = ref(getInitFromData());
const attrStore = useAttrStore();
const { holderTypeDict, robotTypeList } = storeToRefs(attrStore);
const emits = defineEmits<{
  (event: "change", data: any[]): void;
}>();
attrStore.getAllRobotTypesByOnly();

watch(curSelectType, value => {
  // 初始化数据
  value.forEach(key => {
    // 如果没有数据则初始化
    if (!fromData.value[key]) {
      switch (key) {
        case LIMIT_SHELF_ANGLE_CELLFUNC_ANY:
          fromData.value[key] = [{ limitObj: "ANGLES", limitAngles: [] }];
          break;
        case LIMIT_SHELF_ANGLE_CELLFUNC_DESTINATION:
          fromData.value[key] = [{ limitObj: "ANGLES", limitAngles: [] }];
          break;
        case LIMIT_SHELF_ANGLE_CELLFUNC_SHELF:
          fromData.value[key] = [{ limitObj: "", limitAngles: [] }];
          break;
        case LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR:
          fromData.value[key] = [{ limitObj: "", limitAngles: [] }];
          break;
      }
    }
  });

  Object.keys(fromData.value).forEach((key: string) => {
    value.includes(key) || delete fromData.value[key];
  });
});

watch(
  fromData,
  value => {
    const angleList: LimitItemType[] = [];
    Object.keys(value).forEach(type => {
      const ruleMap: { [k: string | number]: number[] } = {};
      // any 和 destination 类型
      if ([LIMIT_SHELF_ANGLE_CELLFUNC_ANY, LIMIT_SHELF_ANGLE_CELLFUNC_DESTINATION].includes(type)) {
        ruleMap["ANGLES"] = value[type][0].limitAngles;
      } else {
        value[type].forEach(item => {
          ruleMap[item.limitObj] = item.limitAngles;
        });
      }
      angleList.push({ type, ruleMap });
    });
    emits("change", angleList);
  },
  {
    deep: true,
  },
);

const isAngAngleLimit: ComputedRef<boolean> = computed(() => {
  return (
    LIMIT_SHELF_ANGLE_CELLFUNC_ANY in fromData.value
  );
});

const isAngAngleLimit_DESTINATION: ComputedRef<boolean> = computed(() => {
  return LIMIT_SHELF_ANGLE_CELLFUNC_DESTINATION in fromData.value;
});

/**
 * 新增货架设置
 */
function addShelfItem() {
  fromData.value[LIMIT_SHELF_ANGLE_CELLFUNC_SHELF].push({
    limitObj: "",
    limitAngles: [],
  });
}

function addRobotItem() {
  fromData.value[LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR].push({
    limitObj: "",
    limitAngles: [],
  });
}

/**
 * 删除货架设置
 */
function removeShelfItem(index: number) {
  fromData.value[LIMIT_SHELF_ANGLE_CELLFUNC_SHELF].splice(index, 1);
}

function removeRobotItem(index: number) {
  fromData.value[LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR].splice(index, 1);
}

function getInitFromData() {
  const data: LimitItemType[] = props.fromData || [];
  const result: { [k: string]: any } = {};
  const selects: string[] = [];
  data.forEach(item => {
    if (
      [LIMIT_SHELF_ANGLE_CELLFUNC_ANY, LIMIT_SHELF_ANGLE_CELLFUNC_DESTINATION].includes(item.type)
    ) {
      const limitObj = "ANGLES";
      const angles = item.ruleMap[limitObj];
      const limitAngles = angles ? angles : [];
      result[item.type] = [
        {
          limitObj,
          limitAngles,
        },
      ];
    } else {
      const list: any[] = [];
      Object.keys(item.ruleMap).forEach((limitObj) => {
        const limitAngles = item.ruleMap[limitObj];
        list.push({ limitObj, limitAngles })
      })
      result[item.type] = list;
    }
  });

  result[LIMIT_SHELF_ANGLE_CELLFUNC_ANY] && selects.push(LIMIT_SHELF_ANGLE_CELLFUNC_ANY);
  result[LIMIT_SHELF_ANGLE_CELLFUNC_SHELF] && selects.push(LIMIT_SHELF_ANGLE_CELLFUNC_SHELF);
  result[LIMIT_SHELF_ANGLE_CELLFUNC_DESTINATION] && selects.push(LIMIT_SHELF_ANGLE_CELLFUNC_DESTINATION);
  result[LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR] &&
    selects.push(LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR);
  curSelectType.value = selects;
  return result;
}
</script>

<style scoped lang="scss">
.funcRackAngle {
  position: relative;

  .title {
    margin: 0;
  }

  .w180 {
    width: 180px;
  }

  .circleBtn {
    width: 32px;
    height: 32px;
    margin-left: 15px;
  }
}
</style>
