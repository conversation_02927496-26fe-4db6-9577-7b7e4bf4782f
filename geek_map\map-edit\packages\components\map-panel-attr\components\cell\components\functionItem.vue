<template>
  <div class="functionstyle">
    <FunItem
      v-for="funItem in functions"
      :fromData="getFunFromData(funItem.funcType)"
      :funcName="funItem.funcType"
      @close="closeItem(funItem.funcType)"
      @update="updateFormData"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, toRefs } from "vue";
import FunItem from "./funItem.vue";

interface FormDataType {
  [k: string]: any;
}

const props = defineProps<{
  fromData: FormDataType;
  updateValue: Function;
}>();

const { fromData } = toRefs(props);

// 这里要设置哪些功能节点依赖于 fromData.functions
const functions = computed(() => {
  return fromData.value.functions || [];
});

/**
 * 删除一个功能节点
 */
function closeItem(name: string) {
  const list = [...functions.value];
  const index = list.findIndex((item: any) => {
    return item.funcType === name;
  });

  if (index !== -1) {
    list.splice(index, 1);
  }

  props.updateValue(list);
}

function updateFormData({ name, data }: { name: string; data: any }) {
  const list = [...functions.value];
  const index = list.findIndex((item: any) => {
    return item.funcType === name;
  });
  if (index !== -1) {
    list.splice(index, 1, data);
  }

  props.updateValue(list);
}

function getFunFromData(name: string) {
  const dataItem = functions.value.find((item: any) => {
      return item.funcType === name;
    }) || {};
  
  const data: {[k:string]: any} = {};
   Object.keys(dataItem).forEach((key: string) => {
    const item = dataItem[key];
    if (item && typeof item === 'object') {
      data[key] = JSON.stringify(item);  
    } else {
      data[key] = item;
    }
  });
  
  return data;
}
</script>

<style scoped lang="scss">
.functionstyle {
  width: 100%;
}

.funcBaseFrom {
  width: 100%;
}
</style>
