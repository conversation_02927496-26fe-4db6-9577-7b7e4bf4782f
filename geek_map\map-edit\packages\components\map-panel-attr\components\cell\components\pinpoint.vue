<template>
  <div class="pinpointSlotStyle">
    <el-divider />
    <div class="title">
      {{ $t('lang.rms.fed.accuratePositioning') }}
      <el-tooltip
        effect="dark"
        :enterable="false"
        :content="$t('lang.rms.fed.precisionCellCodeMaskTip')"
        placement="bottom"
      >
        <span class="mapFont map-font-bangzhu1 describeIcon"></span>
      </el-tooltip>
    </div>
    <!--  -->
    <el-form-item label-width="100px" :label="$t('lang.rms.fed.chooseOnLineRobot')">
      <el-select-v2
        v-model="onlineVehicle"
        :options="options"
        :placeholder="$t('lang.rms.fed.choose')"
        @visible-change="selectVisibleChange"
      />
    </el-form-item>
    <div class="updateLocationCont">
      <el-button class="updateLocationBtn" type="primary" @click="updateLocation"
        >{{$t('lang.rms.fed.updateLocation')}}</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, Ref } from "vue";
import { storeToRefs } from "pinia";
import { queryRobot } from "@packages/api/map";
import { useAppStore } from "@packages/store/app";
import { useAttrStore } from "@packages/store/attr";
import { useEditMap } from "@packages/hook/useEdit";
const onlineVehicle: Ref<string> = ref("");
const options: Ref<{ label: string; value: string; location: { x: number; y: number; } }[]> = ref([]);
const timer: Ref<NodeJS.Timer | null> = ref(null);
const appStore = storeToRefs(useAppStore());
const attrStore = storeToRefs(useAttrStore());
const editMap = useEditMap();

const selectVisibleChange = (visible: boolean) => {
  if (visible) {
    startQueryRobot();
  } else {
    stopQueryRobot();
  }
};

function getQueryRobot() {
  queryRobot({
    floorId: String(appStore.floorId?.value),
  }).then(({ data }) => {
    if (data) {
      options.value = Object.values(data).map((item: any) => {
        return {
          label: item.id,
          value: item.id,
          location: item.location,
        };
      });
    }
  });
}

function startQueryRobot() {
  timer.value = setInterval(() => {
    getQueryRobot();
  }, 1000);
}

function stopQueryRobot() {
  timer.value && clearInterval(timer.value);
}

function updateLocation() {
  const optItem = options.value.find(item => item.value === onlineVehicle.value);
  if (optItem) {
    editMap.value?.updateElements({
      id: <string>attrStore.layerName.value,
      data: [{ ...attrStore.curNodeDataByIndex.value, location: optItem.location }],
    });
  }
}
</script>

<style scoped lang="scss">
.title {
  font-size: 14px;
  font-weight: 900;
}

.updateLocationCont {
  text-align: center;
  padding-top: 20px;
}

.describeIcon {
  margin-right: 3px;
  color: #409eff;
}
</style>
