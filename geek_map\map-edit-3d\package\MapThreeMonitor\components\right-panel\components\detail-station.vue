<template>
  <el-card shadow="never" class="component-operate-detail">
    <div slot="header" class="header">
      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>
    </div>

    <order-group-grid>
      <grid-item
        :label="$t('lang.rms.web.monitor.robot.workStationId')"
        :value="detailData.stationId || '--'"
      />
      <!-- <grid-item :label="$t('lang.rms.fed.type')" :value="detailData.robotType || '--'" /> -->

      <grid-item
        :label="$t('lang.rms.web.monitor.robot.location')"
        :value="detailData.location || '--'"
      />

      <!-- <template v-if="virtualRacks && virtualRacks.length > 0"> -->
      <!-- <grid-item
        v-for="(item, index) in virtualRacks"
        :key="index"
        :label="item.latticeCode"
        :value="item.boxCode"
      /> -->
      <!-- </template> -->

      <grid-item :label="$t('lang.rms.fed.lattice')" :value="virtualRacks" />
    </order-group-grid>
  </el-card>
</template>

<script>
import OrderGroupGrid from "../common/order-group-grid.vue";
import GridItem from "../common/order-group-grid-item.vue";

export default {
  name: "RobotDetail",
  components: { OrderGroupGrid, GridItem },
  props: {
    detailData: {
      type: Object,
      require: true,
    },
    detailTitle: {
      type: String,
      require: true,
    },
  },
  computed: {
    virtualRacks() {
      let value = this.detailData.hasOwnProperty("virtualRacks");
      if (!value) return null;
      else {
        const rack = this.detailData.virtualRacks;
        const result = [];
        if (rack.hasOwnProperty("stationRackLattices")) {
          let layers = rack.stationRackLattices;
          for (const key in layers) {
            if (Object.hasOwnProperty.call(layers, key)) {
              const layerData = layers[key];
              for (let i = 0; i < layerData.length; i++) {
                const lattice = layerData[i];
                const box = {
                  latticeCode: lattice.latticeCode,
                  relativeIndex: lattice.relativeIndex,
                };
                result.push(box);
                if (lattice.hasOwnProperty("relateBox")) {
                  const curBox = lattice.relateBox;
                  box["boxCode"] = curBox.boxCode;
                } else {
                  continue;
                }
              }
            } else {
              continue;
            }
          }
          if (result.length > 0) {
            return JSON.stringify(result);
          } else {
            return null;
          }
        } else {
          return null;
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-operate-detail {
  background: #fbfbfb;

  ::v-deep tr > td {
    padding-bottom: 0;
  }
}
</style>
