/* ! <AUTHOR> at 2022/07/25 */
import SelectLayer from "./select/layer";
import SelectData from "./select/store";
import RectEvent from "./rect/rect";
import RangingEvent from "./ranging/ranging";

class MapViewEvent {
  private mapCore: MRender.MainCore;
  private rectEvent: RectEvent;
  private rangingEvent: RangingEvent;
  private store: SelectData;
  private layer: SelectLayer;
  private mapSelected: (data: MRender.clickParams) => void;
  constructor(mapCore: MRender.MainCore) {
    this.mapCore = mapCore;
  }

  init() {
    this.bindViewEvents();
    this.store = new SelectData();
    this.layer = new SelectLayer(this.mapCore, this.store);
    this.rectEvent = new RectEvent(this.mapCore);
    this.rangingEvent = new RangingEvent(this.mapCore);
  }

  getSelect(layer: MRender.layerName, code: code) {
    return this.store.getSelect(`${layer}__${code}`);
  }

  getSelectsByType(type: "all" | "cell" | "robot" = "all") {
    return this.store.getSelectsByType(type);
  }

  updateSelect(layer: MRender.layerName, options: any): boolean {
    return this.layer.update(layer, options);
  }

  renderDestCircle(cellData: mCellData, destNum: number) {
    if (!cellData) return;
    this.layer.renderDestCircle(cellData, destNum);
  }
  removeDestCircle() {
    this.layer.removeDestCircle();
  }

  clearSelects(layerName: MRender.layerName | "all", codes?: Array<any>) {
    this.store.removeSelects(layerName, codes);
  }

  triggerCenter(params: { layer: MRender.layerName; code: code }) {
    const _this = this;
    const mapCore = _this.mapCore;
    const mapData = mapCore.mapData;
    const { layer, code } = params;

    let itemData, floorId, position;
    switch (layer) {
      case "robot":
      case "charger":
      case "device":
        itemData = mapData[layer].getData(code);
        if (!itemData) return;
        floorId = itemData?.options?.floorId;
        position = itemData?.options?.position;
        _this.triggerEleCenter(floorId, position);
        break;
      case "rack":
      case "cell":
      case "shelf":
        itemData = mapData[layer].getData(code);
        if (!itemData) return;

        floorId = itemData?.floorId;
        position = { x: itemData["hitArea"][0], y: itemData["hitArea"][1] };
        _this.triggerEleCenter(floorId, position);
        break;
    }
  }

  triggerClick(data: MRender.layerElements) {
    const _this = this;
    const mapCore = _this.mapCore;
    const mapData: any = mapCore.mapData;

    let layer: keyof MRender.layerElements;
    let itemData;
    for (layer in data) {
      let codes: Array<any> = data[layer];
      switch (layer) {
        case "robot":
        case "charger":
        case "device":
          codes.forEach(code => {
            itemData = mapData[layer].getData(code);
            if (!itemData) return;
            _this.elementClick(layer, itemData.options);
          });
          break;
        case "rack":
        case "cell":
        case "shelf":
          codes.forEach(code => {
            itemData = mapData[layer].getData(code);
            if (!itemData) return;
            _this.elementClick(layer, itemData);
          });
          break;
      }
    }
  }

  bindViewEvents() {
    const _this = this;
    const mapCore = _this.mapCore;
    const viewport = mapCore.mapView.getViewport();
    if (!viewport) return;

    const meshData = mapCore.meshData;
    const mapData = mapCore.mapData;
    viewport.on("pointerdown", (e: any) => {
      if (this.rectEvent.getTriggerStatus()) return;
      if (this.rangingEvent.getTriggerStatus()) return;
      const target = e?.target;
      if (!target || target?.name === "rms-viewport" || !target?.mapType) return;

      const mapType: MRender.layerName = target.mapType;
      let key, eData, point, itemData;
      switch (mapType) {
        case "robot": // 点击机器人
        case "charger": // 点击充电站
        case "device": // 点击设备
          key = target.name || "";
          itemData = mapData[mapType].getData(key);
          _this.elementClick(mapType, itemData.options);
          break;
        case "cell":
        case "shelf": // 点击货架
        case "rack":
          eData = e?.data;
          if (!eData) throw new Error(mapType + "点击, 没有e.data, e 出错了?");
          key = target.name || "";
          point = eData.getLocalPosition(target);
          itemData = meshData[mapType].getData(key);
          _this.meshClick(mapType, point, itemData);
          break;
        case "realtimeObstacle":
        case "knockArea":
          eData = e?.data;
          if (!eData) throw new Error(mapType + "点击, 没有e.data, e 出错了?");
          key = target.floorId || "";
          _this.areasClick(mapType, key, eData);
          break;
      }
    });
  }

  triggerRect(cb: callback) {
    this.rectEvent.init(cb);
  }

  triggerRanging(cb: callback) {
    this.rangingEvent.init(cb);
  }

  setSelectCallback(cb: (data: MRender.clickParams) => void) {
    this.mapSelected = cb;
  }

  uninstall() {
    this.rectEvent.remove();
    this.rangingEvent.remove();
    this.layer.repaint();
    this.store.uninstall();
  }

  destroy() {
    this.mapCore = null;
    this.mapSelected = null;
    this.rectEvent.destroy();
    this.rectEvent = null;
    this.rangingEvent.destroy();
    this.rangingEvent = null;
    this.store.destroy();
    this.store = null;
    this.layer.destroy();
    this.layer = null;
  }

  private elementClick(
    layerName: MRender.layerName,
    options: mRobotData | mChargerData | mDeviceData,
  ) {
    this.layer.render(layerName, options);

    if (!this.mapSelected) return;
    const isMulti = this.mapCore.mapConfig.getRenderConfig("isMultiSelect");
    this.mapSelected({ layer: layerName, code: options.code, multi: isMulti });
    this.mapCore.mapView.renderAll();
  }

  private meshClick(
    layerName: MRender.layerName,
    point: { x: number; y: number },
    itemMeshData: Array<mCellData | mRackData | mShelfData>,
  ) {
    const { x, y } = point;
    const options = itemMeshData.find(item => {
      let hitArea = item.hitArea; // hitArea: [x, y, x1, y1]
      return x > hitArea[0] && x < hitArea[2] && y > hitArea[1] && y < hitArea[3];
    });
    if (!options) {
      const { error } = console;
      error("找不到点击数据");
      return;
    }

    this.layer.render(layerName, options);

    if (!this.mapSelected) return;
    const isMulti = this.mapCore.mapConfig.getRenderConfig("isMultiSelect");
    this.mapSelected({ layer: layerName, code: options.code, multi: isMulti });
    this.mapCore.mapView.renderAll();
  }

  private areasClick(layerName: MRender.layerName, floorId: floorId, eData: any) {
    const mapCore = this.mapCore;
    const mapFloor = mapCore.mapFloors[floorId];
    if (!mapFloor) return;

    const layerAreas = mapFloor.layerAreas;
    const point = eData.getLocalPosition(layerAreas.getContainer(layerName));
    if (!point?.hasOwnProperty("x") || !point?.hasOwnProperty("y")) return;
    const code = layerAreas.getAreaCode(layerName, point.x, point.y);

    if (!this.mapSelected) return;
    const isMulti = this.mapCore.mapConfig.getRenderConfig("isMultiSelect");
    this.mapSelected({ layer: layerName, code, multi: isMulti });
    this.mapCore.mapView.renderAll();
  }

  private triggerEleCenter(floorId: floorId, position: location) {
    const mapCore = this.mapCore;
    const mapFloor = mapCore.mapFloors[floorId];
    if (!mapFloor) return;
    const floorLayer = mapFloor.getLayerFloor();
    const { x, y } = position;
    let newX, newY;

    const angle = mapCore.mapConfig.getRenderConfig("mapAngle");
    switch (angle) {
      case 90:
        newX = floorLayer.x + Math.abs(y);
        newY = floorLayer.y + x;
        break;
      case 180:
        newX = floorLayer.x - x;
        newY = floorLayer.y - y;
        break;
      case 270:
        newX = floorLayer.x - Math.abs(y);
        newY = floorLayer.y - x;
        break;
      default:
        newX = floorLayer.x + x;
        newY = floorLayer.y + y;
        break;
    }

    const viewport = mapCore.mapView.getViewport();
    viewport.moveCenter(newX, newY);
    this.mapCore.mapView.renderAll();
  }
}

export default MapViewEvent;
