<template>
  <el-dialog
    :title="$t('lang.rms.fed.edit')"
    :visible.sync="dialogVisible"
    :before-close="close"
    :append-to-body="true"
  >
    <el-form label-position="top" label-width="80px" :inline="true" class="dialog-form">
      <el-form-item :label="$t('lang.rms.fed.nameOfParameter')">
        <el-input :value="rowData.code" disabled="disabled" />
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.effectiveImmediately')">
        <el-input :value="formatterImmediate(rowData.immediate)" disabled="disabled" />
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.parameterValues')">
        <el-input v-model="parameterValues" />
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.parameterLabel')">
        <el-input :value="rowData.tags" disabled="disabled" />
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.describe')">
        <el-input :value="$t(rowData.descr)" type="textarea" rows="4" disabled="disabled" />
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">{{ $t("lang.rms.fed.save") }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  name: "DialogParamsEdit",
  props: ["immediateList"],
  data() {
    return {
      dialogVisible: false,
      rowData: {},
      parameterValues: "",
    };
  },
  methods: {
    open(rowData) {
      this.rowData = rowData;
      this.parameterValues = rowData.value;
      this.dialogVisible = true;
    },
    close() {
      Object.assign(this.$data, this.$options.data());
    },
    save() {
      let data = Object.assign({}, this.rowData, { value: this.parameterValues });
      this.$emit("save", data);
      this.close();
    },
    formatterImmediate(value) {
      if (!value) return "";
      const immediateList = this.immediateList;
      let obj = immediateList.find(item => item["key"] === value.toString());
      return obj ? this.$t(obj.value) : value;
    },
  },
};
</script>
<style lang="less" scoped>
.dialog-form {
  :deep(.el-form-item) {
    width: 100%;
    margin-bottom: 10px;
  }

  :deep(.el-form-item__label) {
    padding-bottom: 0;
    font-size: 13px;
    font-weight: 800;
  }
}
</style>
