/**
 * 这里存储了每种不同的节点  需要禁用的功能
 */
import {
  OMNI_DIR_CELL,
  SHELF_CELL,
  STATION_CELL,
  CHARGER_CELL,
  BLOCKED_CELL,
  PALLET_RACK_CELL,
  ELEVATOR_CELL,
  TRANS_CELL,
  QUEUE_CELL,
  LINE_S_LINE,
  LINE_BEZIER,
  LINE_F_ARC,
  LINE_B_ARC,
  DEVICE_STATION,
  DEVICE_CHARGER,
  DEVICE_REFLECTIVE,
  DEVICE_SAFE,
  DEVICE_ELEVATOR,
  AREA_TRAFFIC_LIGHT,
  AREA_STOP,
  AREA_ROBOT,
  AREA_SHELF,
  AREA_TASK_CONTROL,
  AREA_NO_STAY,
  AREA_BLOCK,
  AREA_SINGLE_LANE,
  AREA_TRAFFIC_CONTROL,
  AREA_SORTING_AREA,
  AREA_RESTRICT_BIG_ARC_AREA,
  GATHERING_AREA,
  STATIC_SPEED_LIMIT_AREA,
  REAL_TIME_SPEED_LIMIT_AREA,
  OBSTACLE_AVOIDANCE_AREA,
  PLC_LIMIT_AREA,
  HIGH_ALTITUDE_OBSTACLE_AREA,
  SLAM_NAVIGATION_AREA,
  CLOSE_OBSTACLE_AVOIDANCE_AREA,
  AREA_CUSTOM_AREA,
  AREA_EMPTYING_AREA
} from "@packages/configure/dict/nodeType";

// 隐藏tab  hiddenTabs
// 禁用节点  disabledCodes
// 隐藏节点  hiddenCodes
export interface PanelDcitType {
  [key: string]: {
    hiddenTabs: string[];
    hiddenCodes: string[];
    disabledCodes: string[];
  };
}

/**
 * 单元格需要被禁用或隐藏的内容
 */
export const CELL_PANEL_DICT: PanelDcitType = {
  def: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [ELEVATOR_CELL]: {
    hiddenTabs: ["station", "charger"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [TRANS_CELL]: {
    hiddenTabs: ["station", "charger"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [OMNI_DIR_CELL]: {
    hiddenTabs: ["station", "charger"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [PALLET_RACK_CELL]: {
    hiddenTabs: ["station", "charger"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [BLOCKED_CELL]: {
    hiddenTabs: ["station", "charger", "func"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [SHELF_CELL]: {
    hiddenTabs: ["station", "charger"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  // 二维码单元格
  [QUEUE_CELL]: {
    hiddenTabs: ["station", "charger"],
    hiddenCodes: [],
    disabledCodes: [],
  },
};

/**
 * 线段需要被禁用或隐藏的内容
 */
export const LINE_PANEL_DICT: PanelDcitType = {
  def: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [LINE_S_LINE]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [LINE_BEZIER]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [LINE_F_ARC]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [LINE_B_ARC]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
};

/**
 * 设备需要被禁用或隐藏的内容
 */
export const DEVICE_PANEL_DICT: PanelDcitType = {
  def: {
    hiddenTabs: ["charger", "station", "elevator","safeBase","safeLocal"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [DEVICE_STATION]: {
    hiddenTabs: ["charger", "makerLocal", "makerBase", "elevator","safeBase","safeLocal"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [DEVICE_CHARGER]: {
    hiddenTabs: ["station", "makerLocal", "makerBase", "elevator","safeBase","safeLocal"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [DEVICE_REFLECTIVE]: {
    hiddenTabs: ["station", "charger", "elevator","safeBase","safeLocal"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [DEVICE_SAFE]: {
    hiddenTabs: ["station", "charger", "elevator","makerLocal", "makerBase"],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [DEVICE_ELEVATOR]: {
    hiddenTabs: ["station", "charger", "makerLocal", "makerBase","safeBase","safeLocal"],
    hiddenCodes: [],
    disabledCodes: [],
  },
};

/**
 * 区域需要被禁用或隐藏的内容
 */
export const AREA_PANEL_DICT: PanelDcitType = {
  def: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_TRAFFIC_LIGHT]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_STOP]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_ROBOT]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_SHELF]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_TASK_CONTROL]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_NO_STAY]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_BLOCK]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_SINGLE_LANE]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_TRAFFIC_CONTROL]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_SORTING_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_RESTRICT_BIG_ARC_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [AREA_CUSTOM_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [GATHERING_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [STATIC_SPEED_LIMIT_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [REAL_TIME_SPEED_LIMIT_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [OBSTACLE_AVOIDANCE_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [PLC_LIMIT_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [HIGH_ALTITUDE_OBSTACLE_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [SLAM_NAVIGATION_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
  [CLOSE_OBSTACLE_AVOIDANCE_AREA]: {
    hiddenTabs: [],
    hiddenCodes: [],
    disabledCodes: [],
  },
};
