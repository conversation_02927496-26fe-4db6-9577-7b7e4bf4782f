<template>
  <div>
    <el-dropdown trigger="hover" class="geek-version-info">
      <div class="avatar-wrapper">
        <img src="@imgs/common/icon-version.png" width="21" />
      </div>
      <el-dropdown-menu slot="dropdown">
        <div class="version-info">
          <strong>{{ $t("lang.rms.fed.front.end") }}</strong>
          <div>{{ $t("lang.rms.fed.rms.version") }} : {{ versionInfoStatic.tag || "--" }}</div>
          <div>{{ $t("lang.rms.fed.packing.date") }} : {{ versionInfoStatic.date || "--" }}</div>
          <div>CommitID : {{ versionInfoStatic.commitId || "--" }}</div>
          <strong>{{ $t("lang.rms.fed.rear.end") }}</strong>
          <div>
            {{ $t("lang.rms.fed.rms.version") }} :
            {{ versionInfoApi.tag || versionInfoApi.rmsVersion || "--" }}
          </div>
          <div>{{ $t("lang.rms.fed.packing.date") }} : {{ versionInfoApi.packagingDate || "--" }}</div>
          <div>CommitID: {{ versionInfoApi.commitId || "--" }}</div>
        </div>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: "GeekVersionInfo",
  data() {
    return {
      versionInfoStatic: {},
      versionInfoApi: {},
    };
  },
  created() {
    this.getVersionInfoStatic();
    this.getGitProperties();
  },
  methods: {
    getVersionInfoStatic() {
      $req.reqGitVersionConfig().then(res => {
        this.versionInfoStatic = res;
      });
    },
    getGitProperties() {
      $req.get("/athena/git/properties/get").then(res => {
        this.versionInfoApi = res.data;
      });
    },
  },
};
</script>

<style lang="less" scoped>
.geek-version-info {
  margin: 6px 8px 0 5px;
  cursor: pointer;

  .avatar-wrapper {
    .g-flex();
    line-height: 46px;
    min-width: 20px;
    justify-content: flex-end;
    font-size: 13px;
    color: @g-nav-right-color;
  }
}

.version-info {
  padding: 0 10px;
}
</style>
