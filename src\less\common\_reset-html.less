@charset "utf-8";
//此文件重置html等标签样式
/**-----------------------------样式-----------------------------**/
html,
body,
body div,
object,
iframe,
h1, h2, h3, h4, h5, h6,hgroup,
p, label, span, em, strong, i,
blockquote,
pre,
abbr,
address,
cite,
code,
del,
dfn,
img,
ins,
kbd,
q,
samp,
small,
sub, sup,
var, b,
time, mark,
fieldset, legend, form,
dl, dt, dd, ol,
ul, li,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, figure, details, summary,figcaption,
footer, header, menu, nav, section,
audio, video,
input,select,textarea,button,
a, hr{
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  font-weight: normal;
  vertical-align: baseline;
  background: transparent;
  //user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}


html,
body {
  background-color: @g-root-bg;
  color: @g-root-color;
}

html {
  width: 100%;
  height: 100%;
  font-size: @g-root-size;
  overflow-y:auto;
  overflow-x: hidden;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-touch-action: manipulation;
  -webkit-overflow-scrolling: touch;

  * {
    outline: 0;
    -webkit-text-size-adjust: none;
    -webkit-tap-highlight-color: transparent;
  }
}

body {
  position: relative;
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  &, &:before, &:after {
    min-width: 320px;
    min-height: 100%;
    font-size: @g-font-size;
    margin: 0 auto !important;
    font-family: @g-root-font-family !important;
    overflow-x: hidden;
  }
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block;
  font-family: inherit;
}
