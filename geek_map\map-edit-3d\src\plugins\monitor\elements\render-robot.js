import P40 from "./robots/P40";
import P800 from "./robots/P800";
import RS from "./robots/RS";
import RobotPath from "./robots/Path";
import globalConfig from "../../../config";

/**
 * 机器人渲染相关功能
 * 1. robot - rs/p40
 * 2. robot - animal
 * 3. robot - path
 * 4. robot - box;
 * 5. robot - select/hover
 */
class RenderRobot {
  constructor(options) {
    this.monitor = options.monitor;
    this.dispatch = options.dispatch || {
      updateRobot() {},
      renderRobot() {},
    };
    this.curFloorId = 1;
    this.isPath = false; // 是否渲染Path;
    const Map3d = this.monitor.Map3d;
    this.robotInstance = { P40: new P40(Map3d), RS: new RS(Map3d), P800: new P800(Map3d) };
    this.robotPath = new RobotPath(Map3d);
    this.boxHelper = null;
    this.selectRobotData = null;
  }
  create(robots) {
    this.monitor.Emitter.on("after:renderModel", this.__afterRenderRobots.bind(this));
    robots.map(i => this.__formatRobots(i));
    this.__renderRobot(robots);
    // 创建robot Path对象；
    this.robotPath.create();
  }
  update(robots) {
    this.__updateRobot(robots);
  }
  destory() {
    this.monitor.Emitter.off("after:renderModel", this.__afterRenderRobots.bind(this));
    this.robotPath.destory();
    this.robotInstance = null;
    this.robotPath = null;
    this.boxHelper = null;
    this.selectRobotData = null;
  }
  // 获取所有可点击的mesh;
  getRobotMesh() {
    const { Store, Map3d } = this.monitor;
    const robotsData = Store.getModelData("ROBOT");
    return robotsData.reduce((pre, cur) => {
      const robot = this.robotInstance[cur.robotType];
      return robot ? pre.concat(robot.getRobotMesh(cur)) : pre;
    }, []);
  }
  // 取消robot选择
  cancelHoverRobot(mesh) {
    if (!mesh) return;
    const hover = mesh.getObjectByName("hover");
    hover && (hover.visible = false);
  }
  hoverRobot(mesh) {
    if (!mesh) return;
    const hover = mesh.getObjectByName("hover");
    hover && (hover.visible = true);
  }
  selectRobot(mesh) {
    if (!mesh) return;
    const { Store } = this.monitor;
    const select = mesh.getObjectByName("select");
    select && (select.visible = true);
    const selectRobotData = Store.findModelByUuid("ROBOT", mesh.userData.uuid);
    if (!selectRobotData.id) this.selectRobotData = null;
    if (selectRobotData.id) this.selectRobotData = selectRobotData;
  }
  cancelSelectRobot(mesh) {
    if (!mesh) return;
    const select = mesh.getObjectByName("select");
    select && (select.visible = false);
    this.selectRobotData = null;
  }
  __formatRobots(robot) {
    let robotState = "Normal";
    let radAngle = robot.radAngle;
    if (robot["taskId"]) robotState = "Work";
    if (robot["robotState"] === "DISCONNECTED") robotState = "Offline";
    if (!!robot["errorCode"]) robotState = "Error";

    let robotType = "P800";
    if (robot["robotSeries"].indexOf("RS") !== -1) {
      robotType = "RS";
      if (robot["robotType"] === "P40") robotType = "P40";
    }
    const diff = 2 * (Math.PI / 180);
    if (Math.abs(Math.abs(radAngle) - 0) <= diff) radAngle = 0;
    if (Math.abs(Math.abs(radAngle) - Math.PI / 2) <= diff)
      radAngle = (Math.PI / 2) * (radAngle < 0 ? -1 : 1);
    if (Math.abs(Math.abs(radAngle) - Math.PI) <= diff)
      radAngle = Math.PI * (radAngle < 0 ? -1 : 1);

    return Object.assign(robot, { robotState, robotType, radAngle, floorId: robot.location.z });
  }

  __afterRenderRobots({ category, data: models }) {
    if (category !== "ROBOT" || !models.length) return;
    // P40- 初始化box  RS - 初始化box
    for (let i = 0, v; (v = models[i++]); ) {
      const robotInstance = this.robotInstance[v.robotType];
      robotInstance && robotInstance.afterRender(v);
    }
    this.dispatch.renderRobot && this.dispatch.renderRobot();
  }
  __renderRobot(robots) {
    if (!robots || !robots.length) return;
    const { Store, Map3d } = this.monitor;
    const robotsData = Store.getModelData("ROBOT");
    const isInit = !robotsData || !robotsData.length;
    const color = globalConfig.THEME["ROBOT_PATH_COLOR"];
    robots = robots.map(i =>
      i.pathColor ? i : { ...i, pathColor: color[Math.floor(Math.random() * 10)] },
    );
    const params = [
      robots,
      {
        category: "ROBOT",
        useModelName: r => r.robotType,
      },
    ];
    isInit ? Map3d.initModel(...params) : Map3d.appendModelData(...params);
  }
  __updateRobot(robots) {
    // 增加、删除、修改
    const { Store } = this.monitor;
    const nrobots = robots.map(i => i.id);
    const robotsModel = Store.getModelData("ROBOT");
    // 处理删除和修改-机器人是全量更新的；
    for (let i = 0, ov; (ov = robotsModel[i++]); ) {
      const findIndex = nrobots.findIndex(a => a === ov.id);
      if (!~findIndex) {
        this.__handleDelRobot(ov);
        continue;
      }
      const nv = this.__formatRobots(robots[findIndex]);
      this.robotInstance[ov.robotType] && this.robotInstance[ov.robotType].update(ov, nv);
      Store.updateModelData("ROBOT", { ...ov, ...nv });
      delete robots[findIndex];
    }
    // 处理新增
    this.__renderRobot(robots.filter(i => i));
    // 渲染所有更新机器人path; ==== 执行顺序不可改
    this.__renderPath(Store.getModelData("ROBOT"));
    this.dispatch.updateRobot && this.dispatch.updateRobot();
  }
  // 后期要改，放到map3d对象上，删除合理些；
  __handleDelRobot(ov) {
    const Map3d = this.monitor.Map3d;
    const obj = Map3d.modelInstances[ov.uuid];
    Map3d.scene.remove(obj.model);
    delete Map3d.modelInstances[ov.uuid];
  }
  // 渲染path;
  __renderPath(robots) {
    if (this.selectRobotData && !this.isPath) {
      const { pathColor: color, path: paths } = this.selectRobotData;
      this.robotPath.drawPath([{ color, paths }]);
      return;
    }
    if (!this.isPath) return this.robotPath.clearPath();
    this.robotPath.drawPath(robots.map(i => ({ color: i.pathColor, paths: i.path })));
  }
}
export default RenderRobot;
