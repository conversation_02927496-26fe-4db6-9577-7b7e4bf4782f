<template>
  <div>
    <el-tabs v-if="isTab" v-model="activeName">
      <el-tab-pane :label="$t('lang.rms.fed.shelfModel')" name="goodsShelves">
        <div class="tabContent">
          <shelfModel :view-disabled="viewDisabled" />
        </div>
      </el-tab-pane>
      <el-tab-pane :label="$t('lang.rms.fed.groundSupportModel')" name="groundShelves">
        <div class="tabContent">
          <groundModel :view-disabled="viewDisabled" />
        </div>
      </el-tab-pane>
    </el-tabs>
    <div v-else class="tabContent">
      <component :is="componentId" :view-disabled="viewDisabled"></component>
    </div>
  </div>
</template>

<script>
import shelfModel from "./shelfModel";
import groundModel from "./groundModel";
import { mapActions, mapGetters, mapMutations, mapState } from "vuex";
export default {
  components: {
    shelfModel,
    groundModel,
  },
  props: {
    viewDisabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeName: "goodsShelves",
      tabObj: { SHELF: "goodsShelves", SHELF_HOLDER: "groundShelves" },
      componentDict: { SHELF: "shelfModel", SHELF_HOLDER: "groundModel" },
      componentId: "", // groundModel - 地面支架  ShelfModel -货架模型；
    };
  },
  computed: {
    ...mapState("containerModal", ["editData", "tabModelActive"]),
    isTab() {
      return ((this.editData || {}).id ?? "") === "";
    },
    isEmpty() {
      return this.$store.state.containerModal.emptySwich;
    },
  },
  watch: {
    isEmpty: {
      handler() {
        setTimeout(() => {
          this.activeName = "goodsShelves";
        }, 500);
      },
    },
    tabModelActive: {
      handler(nv) {
        if (this.tabObj[nv]) {
          this.activeName = this.tabObj[nv];
          this.componentId = this.componentDict[nv];
        } else {
          this.activeName = "goodsShelves";
          this.componentId = "ShelfModel";
        }
      },
      deep: true,
      immediate: true,
    },
    activeName(val) {
      if (val === "goodsShelves") this.setTabModelActive("SHELF");
      if (val === "groundShelves") this.setTabModelActive("SHELF_HOLDER");
    },
  },
  methods: {
    ...mapMutations("containerModal", ["setTabModelActive"]),
  },
};
</script>

<style scoped>
.tabContent {
  height: 56vh;
  min-height: 500px;
  position: relative;
}
</style>
