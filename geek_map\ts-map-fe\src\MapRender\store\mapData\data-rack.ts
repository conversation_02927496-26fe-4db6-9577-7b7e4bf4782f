/* ! <AUTHOR> at 2023/04/20 */

class RacksData implements MRender.MapData {
  private mapData: { [propName: code]: mRackData } = {};

  setData(code: code, data: mRackData) {
    this.mapData[code] = data;
  }

  getData(code: code) {
    return this.mapData[code];
  }

  getByFloorId(floorId: floorId): void {}

  getAll() {
    return this.mapData;
  }

  delData(code: code) {
    delete this.mapData[code];
  }

  uninstall() {
    this.mapData = {};
  }

  destroy() {
    this.uninstall();
  }
}

export default RacksData;
