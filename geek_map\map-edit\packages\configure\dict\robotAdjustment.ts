/**
 * 存储了货架行为模式
 */
export const ROBOT_ADJUSTMENT_DEFAULT = 0;
export const ROBOT_ADJUSTMENT_ADJUSTBYVMARK = -1;
export const ROBOT_ADJUSTMENT_GROUNDANDALIGN = 1;
export const ROBOT_ADJUSTMENT_GROUNDANDXY = 2;
export const ROBOT_ADJUSTMENT_SHELFANDALIGN = 3;
export const ROBOT_ADJUSTMENT_SHELFANDXY = 4;

export const ROBOT_ADJUSTMENT = [
  {
    label: "lang.rms.fed.function.adjust.default", //默认行为
    value: ROBOT_ADJUSTMENT_DEFAULT,
  },
  {
    label: "lang.rms.fed.function.adjust.groundAndAlign", //识别地面二维码，前后对齐+方向角微调
    value: ROBOT_ADJUSTMENT_GROUNDANDALIGN,
  },
  {
    label: "lang.rms.fed.function.adjust.groundAndXY", //识别地面二维码，X,Y,角度对齐
    value: ROBOT_ADJUSTMENT_GROUNDANDXY,
  },
  {
    label: "lang.rms.fed.behavior.none", //vmark，不调整
    value: ROBOT_ADJUSTMENT_ADJUSTBYVMARK,
  },
];

export const ROBOT_ADJUSTMENT_CONTSHELF = [
  {
    label: "lang.rms.fed.function.adjust.default", //默认行为
    value: ROBOT_ADJUSTMENT_DEFAULT,
  },
  {
    label: "lang.rms.fed.function.adjust.groundAndAlign", //识别地面二维码，前后对齐+方向角微调
    value: ROBOT_ADJUSTMENT_GROUNDANDALIGN,
  },
  {
    label: "lang.rms.fed.function.adjust.groundAndXY", //识别地面二维码，X,Y,角度对齐
    value: ROBOT_ADJUSTMENT_GROUNDANDXY,
  },
  {
    label: "lang.rms.fed.function.adjust.shelfAndAlign", //识别货架二维码，前后对齐+方向角微调
    value: ROBOT_ADJUSTMENT_SHELFANDALIGN,
  },
  {
    label: "lang.rms.fed.function.adjust.shelfAndXY", //识别货架二维码，X,Y,角度对齐
    value: ROBOT_ADJUSTMENT_SHELFANDXY,
  },
  {
    label: "lang.rms.fed.behavior.none", //vmark，不调整
    value: ROBOT_ADJUSTMENT_ADJUSTBYVMARK,
  },
];
