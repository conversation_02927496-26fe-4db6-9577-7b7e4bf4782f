@charset "utf-8";
@import "./icon/iconfont.css";
#root .map2d-top-panel {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-between;
  top: 0;
  right: 0;
  left: 0;
  height: 38px;
  line-height: 38px;
  margin: auto;
  background: #fff;
  border-bottom: 1px solid #eee;
  overflow: hidden;

  .map2d-fast-search {
    .ant-select-selector {
      height: 28px;
      line-height: 28px;
      width: 180px;
    }
    .ant-select-selection-placeholder {
      line-height: 28px;
      font-size: 13px;
    }
  }
  .map2d-top-floor-change,
  .map2d-top-box-tool {
    margin-right: 8px;
  }

  .map2d-msg-info {
    display: flex;
    height: 28px;
    line-height: 28px;
    border: 1px solid #ddd;

    > li {
      position: relative;
      text-align: center;
      font-size: 13px;
      margin: 0 5px;

      &:after {
        top: 30%;
        right: -6px;
        width: 0;
        height: 40%;
        border-right: 1px solid #ddd;
        position: absolute;
        display: block;
        content: " ";
        font-size: 0;
        line-height: 0;
      }

      &:last-child:after {
        border: 0;
      }
      span {
        cursor: pointer;
      }
      span.toolbar-menu {
        font-size: 16px;
        padding-left: 0;
        color: #444;

        &.is-active {
          color: #409eff;
        }
      }
    }
  }

  .map2d-toolbar-container {
    display: flex;
    align-items: center;
    height: 28px;
    border: 1px solid #d9d9d9;
    margin: 0 5px;

    .toolbar-menu {
      position: relative;
      display: inline-flex;
      justify-content: center;
      line-height: 20px;
      font-size: 18px;
      width: 20px;
      height: 20px;
      cursor: pointer;
      color: #008bd5;
      text-align: center;
      margin: 0 5px;

      &:after {
        top: 10%;
        right: -5px;
        width: 0;
        height: 80%;
        border-right: 1px solid #008bd5;
        position: absolute;
        display: block;
        content: " ";
        font-size: 0;
        line-height: 0;
      }

      &:last-child:after {
        display: none;
      }

      &.is-active {
        background: #d6eafb;
      }
    }

    &.left-bar .toolbar-menu {
      border-right: 0;
      color: #444;

      &:after {
        display: none;
      }
    }
  }

  // .map2d-toolbar-compass {
  //   i {
  //     display: block;
  //     line-height: 20px;
  //     width: 100%;
  //     height: 20px;
  //     background: url(./icon/compass.png) no-repeat 50% 50%;
  //     background-size: 20px;
  //   }

  //   &.compass90 i {
  //     transform: rotate(90deg);
  //   }

  //   &.compass180 i {
  //     transform: rotate(180deg);
  //   }

  //   &.compass270 i {
  //     transform: rotate(270deg);
  //   }
  // }
}

.map2d-top-panel-dropdown li {
  padding: 2px 12px !important;
  font-size: 13px !important;
}

.map2d-fast-search-dropdown {
  margin-right: -10px;
  input {
    display: none !important;
  }
  .ant-tree-switcher-noop {
    border-radius: 50%;
    border: 3px solid #ccc;
    height: 12px;
    width: 12px;
    margin: 6px 6px 0;
  }
  .ant-tree-treenode-selected {
    .ant-tree-switcher-noop {
      border-color: #1890ff;
    }
  }
  .ant-tree-list-holder-inner {
    padding-right: 20px;
    position: relative !important;
  }
}
