/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { StopOutlined, RightCircleOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { Button, Modal, message } from "antd";
import { getMap2D, checkBtn } from "../../../singleton";

const { confirm } = Modal;
type PropsControlGroup = {
  isCollapse: boolean;
  mapConfig: MWorker.mapConfig;
};
function ControlGroup(props: PropsControlGroup) {
  const { t } = useTranslation();
  const [systemRunning, setSystemRunning] = useState(undefined);
  const [taskRunning, setTaskRunning] = useState(undefined);

  const controlHandler = (name: string) => {
    let instruction = "";
    switch (name) {
      case "system":
        instruction = systemRunning ? "SYSTEM_STOP" : "SYSTEM_RECOVER";
        break;
      case "task":
        instruction = taskRunning ? "TASK_STOP" : "TASK_RECOVER";
        break;
      case "fire":
        instruction = systemRunning ? "FIRE_STOP" : "SYSTEM_RECOVER";
        break;
    }

    let warningText;
    switch (instruction) {
      case "SYSTEM_STOP":
        warningText = t("lang.rms.web.monitor.message.openUrgency");
        break;
      case "TASK_STOP":
        warningText = t("lang.rms.web.monitor.message.pauseTask");
        break;
      case "FIRE_STOP":
        warningText = t("lang.rms.web.monitor.message.openFireStop");
        break;
      case "SYSTEM_RECOVER":
        warningText = t("lang.rms.web.monitor.message.cancelUrgency");
        break;
      case "TASK_RECOVER":
        warningText = t("lang.rms.web.monitor.message.cancelPauseTask");
        break;
      default:
        warningText = t("lang.rms.fed.confirmTheOperation");
        break;
    }
    let conf = localStorage.getItem("Geek_RMSConfig") ? JSON.parse(localStorage.getItem("Geek_RMSConfig")) : {};
    if (conf.enableConfirmSystemStop) {
      const map2D = getMap2D();
      const reqMsg = "WarehouseInstructionRequestMsg";
      const resMsg = "WarehouseInstructionResponseMsg";
      map2D.mapWorker.reqSocket(reqMsg, { instruction }).then(res => {
        if (res.msgType !== resMsg) return;
        const msg = t(res?.body?.msg || "");
        if (res?.body?.code === 0) {
          message.success(msg);
          return;
        }

        const code = res?.body?.code || -1;
        if (code === 3080 || code === 3083) {
          // TODO 3080 3083 是系统恢复和任务恢复的失败码
          message.error(msg);
        } else {
          message.error(msg);
        }
      });
      return
    }
    confirm({
      icon: <ExclamationCircleOutlined />,
      content: warningText,
      onOk() {
        const map2D = getMap2D();
        const reqMsg = "WarehouseInstructionRequestMsg";
        const resMsg = "WarehouseInstructionResponseMsg";
        map2D.mapWorker.reqSocket(reqMsg, { instruction }).then(res => {
          if (res.msgType !== resMsg) return;
          const msg = t(res?.body?.msg || "");
          if (res?.body?.code === 0) {
            message.success(msg);
            return;
          }

          const code = res?.body?.code || -1;
          if (code === 3080 || code === 3083) {
            // TODO 3080 3083 是系统恢复和任务恢复的失败码
            message.error(msg);
          } else if (code === 3156 || code === 3157) {
            const errorText = t(res?.body?.msg || "");
            confirm({
              icon: <ExclamationCircleOutlined />,
              content: errorText,
              okText: t('lang.rms.config.system.stop.forceResetConfirm'),
              onOk() {
                const map2D = getMap2D();
                const reqMsg = "WarehouseInstructionRequestMsg";
                const resMsg = "WarehouseInstructionResponseMsg";
                map2D.mapWorker.reqSocket(reqMsg, {
                  instruction: 'SYSTEM_RECOVER',
                  forceRecover: true
                }).then(res => {
                  if (res.msgType !== resMsg) return;
                  const msg = t(res?.body?.msg || "");
                  if (res?.body?.code === 0) {
                    message.success(msg);
                    return;
                  }
        
                  const code = res?.body?.code || -1;
                  if (code === 3080 || code === 3083) {
                    // TODO 3080 3083 是系统恢复和任务恢复的失败码
                    message.error(msg);
                  } else {
                    message.error(msg);
                  }
                });
              },
            });
          } else {
            message.error(msg);
          }
        });
      },
    });
  };

  // mapConfig 推送消息
  useEffect(() => {
    const data = props.mapConfig;
    if (data?.hasOwnProperty("systemRunning")) setSystemRunning(data.systemRunning);
    if (data?.hasOwnProperty("taskRunning")) setTaskRunning(data.taskRunning);
  }, [props.mapConfig]);

  return (
    systemRunning !== undefined && (
      <>
        <div className="map2d-control-btn-group">
          {checkBtn("MonitorSystemStop") && (
            <Button
              size="middle"
              type="primary"
              block
              danger={systemRunning}
              icon={systemRunning ? <StopOutlined /> : <RightCircleOutlined />}
              onClick={() => controlHandler("system")}
            >
              {systemRunning
                ? t("lang.rms.fed.systemEmergencyStop")
                : t("lang.rms.fed.systemOperation")}
            </Button>
          )}
          {checkBtn("MonitorTaskRecovery") && (
            <Button
              size="middle"
              type="primary"
              block
              danger={taskRunning}
              icon={taskRunning ? <StopOutlined /> : <RightCircleOutlined />}
              onClick={() => controlHandler("task")}
            >
              {taskRunning ? t("lang.rms.fed.pauseTask") : t("lang.rms.fed.taskRecovery")}
            </Button>
          )}
          {checkBtn("MonitorFireStop") && (
            <Button
              size="middle"
              type="primary"
              block
              danger={systemRunning}
              icon={systemRunning ? <StopOutlined /> : <RightCircleOutlined />}
              onClick={() => controlHandler("fire")}
            >
              {systemRunning ? t("lang.rms.fed.fireStop") : t("lang.rms.fed.systemOperation")}
            </Button>
          )}
        </div>
        {checkBtn("MonitorSystemStop") && (
          <Button
            type="primary"
            danger={systemRunning}
            icon={systemRunning ? <StopOutlined /> : <RightCircleOutlined />}
            onClick={() => controlHandler("system")}
            className={`system-status-btn ${props.isCollapse ? "is-collapse" : ""}`}
          />
        )}
      </>
    )
  );
}

export default ControlGroup;
