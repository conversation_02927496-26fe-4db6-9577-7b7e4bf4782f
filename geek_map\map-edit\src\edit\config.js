// const types = {
//   'CHARGER':'CHARGER',
//   'CELL':'CELL',
//   'STATION':'STATION',
//   'MARKER':'MARKER',
//   'LINE':'LINE',
//   'AREA':'AREA',
//   'OPERATE':'OPERATE'
// }
//调整单元格间距
const cellRatio = 0.9
let COEFFICIENT = 0.02
const setCoefficient = (resolution) => {
  COEFFICIENT = resolution
}

const OMNI_DIR_CELL = 0xd2e5f5
const elementColor = {
  DEFAULT_CELL: 0xdddddd, // 默认cellColor

  OMNI_DIR_CELL: OMNI_DIR_CELL, // 道路节点
  E2W_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  W2E_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  S2N_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  N2S_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  E2W_S2N_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  E2W_N2S_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  W2E_S2N_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  W2E_N2S_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  E2W_W2E_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  N2S_S2N_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  E2W_W2E_N2S_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  E2W_W2E_S2N_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  N2S_S2N_E2W_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  N2S_S2N_W2E_PATH_CELL: OMNI_DIR_CELL, // 带方向道路节点
  NUll_CELL: 0x3f9cfb, // 空cell
  CHARGER_CELL: 0xd2e5f5, // 让机器人进去的点，充电站点
  STATION_CELL: 0xf29361, // 工作站
  DROP_CELL: 0x90dd9f, // 投递点
  QUEUE_CELL: 0xede2be, // 排队点
  SHELF_CELL: 0x74c3cb, // 货架点
  BOX_RACK_CELL: 0x42b983, // 固定货架
  TURN_CELL: 0xd7c99f, // 转面点
  CHARGER_PI_CELL: 0xdddddd, // 充电桩点
  BLOCKED_CELL: 0xdddddd, // 障碍点
  ELEVATOR_CELL: 0xd2e5f5, // 电梯点
  PALLET_RACK_CELL: 0x7890e9, // 托盘位点

  //箭头颜色
  // UNLOAD_DIR: 0xe55431,
  // LOAD_DIR: 0x4f9ae5,
  // BOTH_DIR: 0x07ce96
  // aa:'#03d79e',
  UNLOAD_DIR: 0xfc6957,
  LOAD_DIR: 0x6d80fd,
  BOTH_DIR: 0x03d79e
}
// '#fd7070'
const colorConfig = {
  INACTIVE_AREA: 0x6680ef,
  ACTIVE_AREA: 0xef973b
}

const lineStyle = {
  // tt:'#e9ef3b',
  width:1.6,
  ACTIVE_LINE:0xef973b,
  INACTIVE_LINE: 0x6680ef,
  UNLOAD: 0xfc6957,
  LOAD: 0x6d80fd,
}
const areaStyle = {
  INACTIVE_AREA: 0x6680ef,
  ACTIVE_AREA: 0xef973b
}
//默认配置
// const {CELL,CHARGER,STATION,MARKER,AREA,LINE} = types
const defaultConfig = {
  CELL: {
    length:1,
    width:1,
    nonstandardNode: false
  },
  SHELF_CELL: {
    length:1,
    width:1,
    yawAngle: 0,
    needAngles: [0],
    nonstandardNode: false
  },
  //设备的默认大小
  DEVICE: {
    length:0.5,
    width:0.5
  },
  CHARGER: {
    length:0.5,
    width:0.5,
    angle:0
  },
  STATION: {
    length:0.5,
    width:0.5,
    queueSize:5,
    waitCellNumber:0,
    enable: true,
    direction:"east"
  },
  ELEVATOR: {
    length:0.5,
    width:0.5,
    queueCells: null,
    entryQueueNum: 0,
    waitCellNumber: 0,
    entryWaitNum: 1
  },
  MARKER: {
    length:0.4,
    width:0.4
  },
  SAFE: {
    length:0.5,
    width:0.5
  }
}
export {
  colorConfig,
  elementColor,
  lineStyle,
  areaStyle,
  defaultConfig,
  cellRatio,
  COEFFICIENT,
  setCoefficient
}
