/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect, useRef } from "react";
import { ExclamationCircleOutlined, EnvironmentOutlined } from "@ant-design/icons";
import { Table, Modal } from "antd";
import { useTranslation } from "react-i18next";
import { getMap2D, $eventBus } from "../../singleton";

let list: Array<any> = [];
let listIds: any = {};
let scrollNum: number = 5;
let robotList: any = [];
function DeviceWarning() {
  const { t } = useTranslation();
  const [count, setCount] = useState(0);
  const [stat, setStat] = useState(null);
  const [taskStat, setTaskStat] = useState(null);
  const [scrollList, setScrollList] = useState([]);
  const [visible, setVisible] = useState(false);
  const [tableList, setTableList] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deviceFaultContent, setDeviceFaultContent] = useState("");

  const intervalRef = useRef(null);

  useEffect(() => {
    $eventBus.on("wsMessageDeviceLeftTop", data => {
      data = data || [];
      let newIds: any = {};
      let deviceFault = false;
      data.forEach((item: any, index: number) => {
        if (!item.eventId) return;
        if (!listIds[item.eventId]) {
          list.push(item);
        }
        if ([91000, 91001, 91002, 91003, 91004, 91005, 91006].includes(item.faultCode)) {
          deviceFault = true;
          setDeviceFaultContent(item.eventContent);
        }
        newIds[item.eventId] = item;
      });

      setIsModalOpen(deviceFault);

      let delIndex: any[] = [];
      list.forEach((item: any, index: number) => {
        let newItem = newIds[item.eventId];
        if (!newItem) {
          delIndex.push(index);
        } else {
          Object.assign(item, newItem);
        }
      });
      delIndex.forEach((delI: number) => {
        list.splice(delI, 1);
      });
      listIds = newIds;
    });
    $eventBus.on("wsMessageRobotLeftTop", stat => setStat(stat));
    return () => {
      $eventBus.off("wsMessageDeviceLeftTop");
      $eventBus.off("wsMessageRobotLeftTop");
    };
  }, []);

  // 任务统计
  useEffect(() => {
    $eventBus.on("wsMessageTaskLeft", stat => setTaskStat(stat));
    return () => $eventBus.off("wsMessageTaskLeft");
  }, []);

  useEffect(() => {
    $eventBus.on("dialogRobotWarning", data => setVisible(data.visible));
    $eventBus.on("wsMessageRobotLeft", result => {
      if (JSON.stringify(result) === JSON.stringify(robotList)) return;
      robotList = result;
      setTableList(result);
    });

    return () => {
      $eventBus.off("wsMessageRobotLeft");
      $eventBus.off("dialogRobotWarning");
    };
  }, [visible]);

  useEffect(() => {
    intervalRef.current = setTimeout(() => {
      setData();
      if (count > 30) setCount(0);
      else setCount(count + 1);
    }, 3000);
    return () => {
      clearTimeout(intervalRef.current);
    };
  }, [count]);

  const setData = () => {
    const len = list.length;

    let arr: any = [];
    if (len <= scrollNum) arr = list.slice(0);
    else {
      arr = list.slice(0, scrollNum);
      list.push(list.shift());
    }

    setScrollList(
      arr.map((item: any, index: number) => {
        let arr: any = [];
        let obj: any = { layer: null, code: null, text: "" };
        if (item.extPropertyValue) arr.push(t(item.extPropertyValue));
        if (item.extPropertCode) arr.push(t(item.extPropertCode) + " ");
        arr.push(t(item.eventType));
        if (item.eventObj && item.eventObj !== "null") {
          arr.push(t(item.eventObj));
          obj.code = item.eventObj;
          switch (item.category) {
            case 1:
              obj.layer = "robot";
              break;
            case 5:
              obj.layer = "charger";
              break;
            case 7:
              obj.layer = "shelf";
              break;
          }
        }
        arr.push(":");
        arr.push(_$utils.transMsgLang(item.eventContent));
        obj.text = arr.join("");

        return obj;
      }),
    );
  };

  const columns: any = [
    {
      title: t("lang.rms.fed.number"),
      dataIndex: "eventObj",
      width: 80,
      render: (text: any, row: any) => {
        return <span className={row.eventLevel == 1 ? "warning" : "error"}>{text}</span>;
      },
    },
    {
      title: t("lang.rms.fed.textAbnormal"),
      dataIndex: "eventContent",
      width: 105,
      render: (text: any, row: any) => {
        return (
          <span
            style={{ margin: -8, padding: 8, display: "block", cursor: "pointer" }}
            onClick={() => {
              Modal.info({
                title: t("lang.rms.fed.textDetails"),
                content: (
                  <>
                    <p>
                      <label>{t("lang.rms.fed.config.robotId")}:</label>
                      <span>{row.eventObj}</span>
                    </p>
                    <p>
                      <label>{t("lang.rms.web.monitor.exception.info")}:</label>
                      <span>{t(row.eventContent)}</span>
                    </p>
                    <p>
                      <label>{t("lang.rms.web.monitor.exception.time")}:</label>
                      <span>{row.time}</span>
                    </p>
                    <p>
                      <label>{t("lang.rms.web.monitor.exception.method")}:</label>
                      <span>{t(row.eventSolution)}</span>
                    </p>
                  </>
                ),
              });
            }}
          >
            {_$utils.transMsgLang(text)}
          </span>
        );
      },
    },
    {
      title: t("lang.rms.web.monitor.toolbox.localization"),
      dataIndex: "actionCommon",
      width: 60,
      render: (text: any, row: any) => {
        return (
          <EnvironmentOutlined
            onClick={() => {
              const robotId = row.eventObj;
              if (!robotId) return;
              const map2D = getMap2D();
              map2D.mapWorker.reqQuery({ layer: "robot", code: robotId });
              map2D.mapRender.trigger("click", { robot: [robotId] });
              map2D.mapRender.setEleCenter({ layer: "robot", code: robotId });
            }}
          />
        );
      },
    },
  ];
  return (
    <>
      <div className="map2d-left-warning-panel">
        <ul className="map2d-robot-info">
          <li>
            <label>{t("lang.rms.fed.textAbnormal")}</label>
            <span>{stat?.exceptionCount || 0}</span>
          </li>
          <li>
            <label>{t("lang.rms.web.monitor.robot.idle")}</label>
            <span>{stat?.idleCount || 0}</span>
          </li>
          <li>
            <label>{t("lang.rms.web.monitor.robot.charging")}</label>
            <span>{stat?.chargingCount || 0}</span>
          </li>
          <li>
            <label>{t("lang.rms.web.monitor.robot.working")}</label>
            <span>{stat?.workingCount || 0}</span>
          </li>
          <li>
            <label>{t("lang.rms.equipment.connection.status.offline")}</label>
            <span>{stat?.disconnectedCount || 0}</span>
          </li>
          <li>
            <label>{t("lang.rms.fed.remove")}</label>
            <span>{stat?.removedCount || 0}</span>
          </li>
        </ul>
        <ul className="map2d-robot-info robot-task">
          <li>
            <label>{t("lang.rms.box.boxstate.new")}</label>
            <span>{taskStat?.newJobCount || 0}</span>
          </li>
          <li>
            <label>{t("lang.rms.box.boxstate.allocated")}</label>
            <span>{taskStat?.assignJobCount || 0}</span>
          </li>
          <li>
            <label>{t("lang.rms.robot.exestatus.executing")}</label>
            <span>{taskStat?.executingJobCount || 0}</span>
          </li>
        </ul>
        {scrollList.length > 0 && (
          <div className="map2d-device-warning-panel">
            {scrollList.map((item, index) => {
              return (
                <p
                  title={item.text}
                  key={index}
                  className="item-error"
                  style={item.layer ? null : { cursor: "default" }}
                  onClick={() => {
                    if (!item.code || !item.layer) return;
                    const layer = item.layer;
                    const code = item.code;
                    const map2D = getMap2D();
                    let clickParams: any = {};
                    clickParams[layer] = [code];
                    map2D.mapWorker.reqQuery({ layer, code });
                    map2D.mapRender.trigger("click", clickParams);
                    map2D.mapRender.setEleCenter({ layer, code });
                  }}
                >
                  <ExclamationCircleOutlined style={{ color: "#ff5e62", marginRight: 3 }} />
                  {item.text}
                </p>
              );
            })}
          </div>
        )}

        {visible && (
          <div className="map2d-robot-warning-panel">
            <Table
              dataSource={tableList}
              rowKey="eventObj"
              columns={columns}
              size="small"
              bordered
              pagination={false}
              scroll={{ y: 280, x: "100%" }}
              className="robot-list"
              rowClassName="robot-list-row"
            />
          </div>
        )}
      </div>

      <div
        style={{
          position: "fixed",
          top: 98,
          left: 0,
          right: 0,
          margin: "0 auto",
          width: 320,
          background: "#eee",
          opacity: 0.8,
          color: "#000",
          padding: 16,
          display: isModalOpen ? "block" : "none",
        }}
      >
        <p style={{ fontWeight: 600, fontSize: 18, textAlign: "center" }}>
          {t("lang.rms.monitor.browser.stop.doing")}
        </p>
        <p style={{ fontWeight: 600, textAlign: "center" }}>
          {t("lang.rms.monitor.browser.stop.triggerInfo")}:{t(deviceFaultContent)}
        </p>
      </div>
    </>
  );
}

export default DeviceWarning;
