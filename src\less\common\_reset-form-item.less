@charset "utf-8";
//此文件重置button、input等标签样式
/**-----------------------------定义-----------------------------**/
@common-reset-element-font-size: 14px;
.reset-border-line() {
  border: 1px solid #ccc;
  border-radius: 5px;
}

/**-----------------------------样式-----------------------------**/
::-webkit-search-cancel-button {
  display: none;
}

button,
input,
select,
textarea {
  margin: 0;
  padding: 0 5px;
  min-height: 28px;
  box-sizing: border-box;
  vertical-align: baseline;
  font-size: @common-reset-element-font-size;
  -webkit-appearance: none;
  font-family: inherit;
  .reset-border-line();
}

button,
input {
  line-height: normal;
  outline: none;
}

/* make buttons play nice in IE */
button,
input[type=button] {
  width: auto;
  overflow: visible;
  .reset-border-line();
}
button{
  border: 0;
}


input {
  &:focus {
    outline: medium;
  }

  &[type="radio"] {
    vertical-align: text-bottom;
  }

  &[type="checkbox"] {
    vertical-align: bottom;
  }
}


