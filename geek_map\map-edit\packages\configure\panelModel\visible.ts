import { ToolPanelType } from "@packages/type/editUiType";

// 框选/单选
export const VISIBLE_BOXSELECTION: ToolPanelType = {
  option: {
    icon: "map-font-kuangxuan1",
    name: "boxSelect",
    eventName: "map:boxSelect",
    describe: "lang.rms.fed.boxSelectionMode",
    isSelect: true,
  },
};


// 负载、空载全部显示
export const VISIBLE_ROAD_BOTHLOAD: ToolPanelType = {
  option: {
    icon: "map-font-roadBothLoad",
    name: "roadBothLoad",
    eventName: "map:roadBothLoad",
    describe: "lang.rms.fed.showLoadUnLoad",
    isSelect: true,
    group: "road",
    active: true,
  },
};

// 显示空载
export const VISIBLE_ROAD_UNLOAD: ToolPanelType = {
  option: {
    icon: "map-font-roadUnLoad",
    name: "roadUnLoad",
    eventName: "map:roadUnLoad",
    describe: "lang.rms.fed.unLoad",
    isSelect: true,
    group: "road",
  },
};

// 显示负载
export const VISIBLE_ROAD_LOAD: ToolPanelType = {
  option: {
    icon: "map-font-roadLoad",
    name: "roadLoad",
    eventName: "map:roadLoad",
    describe: "lang.rms.fed.load",
    isSelect: true,
    group: "road",
  },
};

// 机器人
export const VISIBLE_ROBOT: ToolPanelType = {
  option: {
    icon: "map-font-cheliang",
    name: "visRobot",
    eventName: "map:roadLoad",
    describe: "lang.rms.fed.optionRobot",
    isSelect: true,
    disabled() {
      return true;
    },
  },
};

// 反光柱
export const VISIBLE_REFLECTOR: ToolPanelType = {
  option: {
    icon: "map-font-dingshitufanguangzhu",
    name: "visReflector",
    eventName: "map:visibleMaker",
    describe: "lang.rms.web.map.element.marker",
    isSelect: true,
  },
};

// 辅助线
export const VISIBLE_GUIDE: ToolPanelType = {
  option: {
    icon: "map-font-fuzhuxian",
    name: "visGuide",
    eventName: "map:roadLoad",
    describe: "lang.rms.fed.auxiliaryLine",
    isSelect: true,
    defDisabled: true,
  },
};

// 撤销
export const VISIBLE_REVOKE: ToolPanelType = {
  option: {
    icon: "map-font-undo",
    name: "visRevoke",
    eventName: "map:historyBack",
    describe: "lang.rms.fed.revoke",
    defDisabled: true,
  },
};

// 取消撤销
export const VISIBLE_REVOKE_CANCEL: ToolPanelType = {
  option: {
    icon: "map-font-redo",
    name: "visRevokeCancel",
    eventName: "map:historyForward",
    describe: "lang.common.cancel",
    defDisabled: true,
  },
};

// 保存
export const VISIBLE_SAVE: ToolPanelType = {
  option: {
    icon: "map-font-save",
    name: "save",
    eventName: "map:save",
    describe: "lang.rms.fed.save",
    defDisabled: true,
  },
};
