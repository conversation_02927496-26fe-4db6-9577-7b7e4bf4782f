/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Select, Button } from "antd";
import { getMap2D, $eventBus } from "../../../../singleton";

import UpdateAngle from "./update-angle";
import ToCell from "./to-cell";

const { Option } = Select;

type deadRobot = { code: code; errorCodes: Array<any>; taskId: any };
type PropsOrderData = {
  isCurrent: boolean;
  deadRobots: { robotData: { [propName: code]: deadRobot }; robotCodes: Array<code> };
};
function OrderDeadlock(props: PropsOrderData) {
  const { t } = useTranslation();
  const [currentRobot, setCurrentRobot] = useState<deadRobot>(null);
  const [operation, setOperation] = useState<"forceMove" | "bodyRotation">(undefined);
  const [deadRobots, setDeadRobots] = useState<{ [propName: code]: deadRobot }>({});
  const [robotCodes, setRobotCodes] = useState([]);
  const [cellCode, setCellCode] = useState<code>(null);

  useEffect(() => {
    if (!props.isCurrent) return;
    let robotCodes = props.deadRobots?.robotCodes || [];
    setDeadRobots(props.deadRobots?.robotData || {});
    setRobotCodes(robotCodes);

    if (robotCodes.length) {
      const map2D = getMap2D();
      map2D.mapRender.renderFeature("deadRobotPath", robotCodes);
    }
  }, [props.isCurrent, props.deadRobots]);

  useEffect(() => {
    if (!props.isCurrent) return;
    setOperation(undefined);
    const map2D = getMap2D();
    map2D.mapRender.triggerLayers(["robot"]);

    $eventBus.on("mapClick", (data: MRender.clickParams) => {
      const layer = data?.layer;
      switch (layer) {
        case "robot":
          setCurrentRobot(null);
          map2D.mapWorker.reqQuery({ layer, code: data.code });
          break;
        case "cell":
          setCellCode(data.code || "");
          break;
      }
    });
    $eventBus.on("wsDataQueryRightTab", wsData => {
      if (!props.isCurrent || !wsData) return;
      const { params, data } = wsData;
      switch (params.layer) {
        case "robot":
          if (data) {
            let robot = { code: data.id, errorCodes: data.errorCodes, taskId: data.taskId };
            setCurrentRobot(robot);
          } else setCurrentRobot(null);
          break;
      }
    });
    return () => {
      $eventBus.off("wsDataQueryRightTab");
      $eventBus.off("mapClick");
      map2D.mapRender.renderFeatureColor({ type: "cell", clear: true });
      map2D.mapRender.renderFeature("deadRobotPath", null);
      clearRobot();
    };
  }, [props.isCurrent]);

  const controlHandler = (cmd: string) => {
    let params;
    switch (cmd) {
      case "forceMove": // 强制移动
      case "bodyRotation": // 本体旋转
        setOperation(cmd);
        return;
      case "releaseNode": // 释放节点
        params = {
          instruction: "RELEASE_ROBOT_CELL",
          robotId: currentRobot.code,
        };
        break;
      case "resumeScheduling": // 恢复调度
        params = {
          instruction: "RECOVER_DISPATCHING",
          robotId: currentRobot.code,
        };
        break;
    }

    setOperation(undefined);
    const reqMsg = "WarehouseInstructionRequestMsg";
    const resMsg = "WarehouseInstructionResponseMsg";
    const map2D = getMap2D();
    map2D.mapWorker.reqSocket(reqMsg, params).then(res => {
      if (res.msgType !== resMsg) return;
      _$utils.wsCmdResponse(res?.body || {});
    });
  };

  const robotSearch = (value: string) => {
    if (!value) {
      clearRobot();
      setOperation(undefined);
      return;
    }
    const map2D = getMap2D();
    map2D.mapWorker.reqQuery({ layer: "robot", code: value });
    map2D.mapRender.trigger("click", { robot: [value] });
    map2D.mapRender.setEleCenter({ layer: "robot", code: value });
    setCurrentRobot(deadRobots[value] || null);
  };

  // 机器人清除
  const clearRobot = () => {
    setCurrentRobot(null);
    setCellCode(null);
    const map2D = getMap2D();
    map2D.mapWorker.stopQuery();
    map2D.mapRender.clearSelects();
    map2D.mapRender.enableMultiClick(false);
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component"
    >
      <div className="panel-right-title">{t("lang.rms.fed.deadlockResolution")}</div>
      <Select
        value={currentRobot ? currentRobot.code.toString() : ""}
        showSearch
        allowClear
        style={{ width: "100%" }}
        placeholder={t("lang.rms.fed.choose")}
        onChange={value => robotSearch(value)}
        filterOption={(input, option) =>
          (option!.children as unknown as string).toLowerCase().includes(input.toLowerCase())
        }
      >
        {robotCodes.map(item => (
          <Option key={item} value={item}>
            {item}
          </Option>
        ))}
      </Select>

      <div className="component-btn-group">
        <Button
          type="primary"
          block
          disabled={!currentRobot || !currentRobot?.code}
          onClick={() => controlHandler("forceMove")}
        >
          {t("lang.rms.fed.forceMove")}
        </Button>
        <Button
          type="primary"
          block
          disabled={!currentRobot || !currentRobot?.code}
          onClick={() => controlHandler("bodyRotation")}
        >
          {t("lang.rms.fed.bodyRotation")}
        </Button>
        <Button
          type="primary"
          block
          disabled={!currentRobot}
          onClick={() => controlHandler("releaseNode")}
        >
          {t("lang.rms.fed.releaseNode")}
        </Button>
        <Button
          type="primary"
          block
          disabled={!currentRobot}
          onClick={() => controlHandler("resumeScheduling")}
        >
          {t("lang.rms.fed.resumeScheduling")}
        </Button>
      </div>

      <ToCell
        visible={operation === "forceMove"}
        currentRobot={currentRobot}
        cellCode={cellCode}
        onCancel={() => {
          setCellCode(null);
          setOperation(undefined);
        }}
      />
      <UpdateAngle
        visible={operation === "bodyRotation"}
        currentRobot={currentRobot}
        onCancel={() => {
          setOperation(undefined);
        }}
      />
    </div>
  );
}

export default OrderDeadlock;
