<template>
  <el-dialog
    :title="$t('lang.rms.fed.buttonEdit')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    @closed="close"
    width="380px"
  >
    <geek-customize-form ref="refForm" :form-config="formConfig" />
    <span slot="footer">
      <el-button @click="close">{{ $t("lang.rms.fed.cancel") }}</el-button>
      <el-button type="primary" @click="onSubmit">{{ $t("lang.rms.fed.save") }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      rowData: {},

      formConfig: {
        attrs: {
          labelWidth: "100px",
          labelPosition: "right",
        },
        configs: {
          userName: {
            label: "lang.rms.fed.inputUserName",
            default: "",
            tag: "input",
            required: true,
            disabled: true,
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
          },
          realName: {
            label: "lang.rms.fed.inputFullName",
            default: "",
            tag: "input",
            required: true,
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
          },
          phone: {
            label: "lang.rms.fed.inputTelephone",
            default: "",
            tag: "input",
          },
          roleIds: {
            label: "lang.rms.fed.role",
            tag: "select",
            default: "",
            required: true,
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "change" }],
            options: [],
          },
        },
      },

      saveLoading: false,
      dialogVisible: false,
    };
  },
  computed: {
    isEdit() {
      let currentUsername = $utils.Data.getUserInfo();
      return currentUsername === this.formObj.userName;
    },
  },
  methods: {
    open(row) {
      this.dialogVisible = true;
      this.rowData = row;
      const params = {
        userName: row.userName, // 用户名
        realName: row.realName, // 姓名
        phone: row.phone ? row.phone : "", // 电话
        roleIds: Number(row.roleIds) || "", // 角色
      };
      this.$nextTick(() => {
        this.getRoleList();
        this.$refs.refForm.reset();
        this.$refs.refForm.setData(params);
      });
    },
    close() {
      this.dialogVisible = false;
    },

    onSubmit() {
      this.$refs.refForm.validate().then(data => {
        const params = {
          user: {
            userId: this.rowData.userId,
            userName: data.userName,
            realName: data.realName,
            phone: data.phone,
          },
          roleIds: [Number(data.roleIds)],
        };
        this.saveLoading = true;
        $req
          .post("/athena/api/coreresource/auth/user/updateUser/v1", params)
          .then(res => {
            if (res.code === 0) {
              this.saveLoading = false;
              this.$success();
              this.$emit("updateList");
              this.close();
            }
          })
          .catch(() => {
            this.saveLoading = false;
          });
      });
    },

    getRoleList() {
      $req.get("/athena/api/coreresource/auth/role/pageQuery/v1").then(res => {
        if (res.code !== 0) return;
        const list = res?.data?.recordList || [];
        this.formConfig.configs.roleIds.options = list.map(item => ({ value: item.roleId, label: item.name }));
      });
    },
  },
};
</script>

<style lang="less" scope></style>
