import { UN_RENDER_CELL_TYPE_ARR } from "../../../constant/elements";
class WSMessage {
  constructor(dispatch) {
    this.dispatch = dispatch || {
      FloorIdsReady() {},
      FloorsDataReady() {},
      DisplayDataReady() {},
      DisplayDataUpdate() {},
    };

    this.currentFloorIds = [1];
    this.cellCodesSize = {}; // 记录当前楼层的cellCode Size;
    this.floorChanging = true; // 地图是否正在初始化
    this.floorMapInit = false; // 浏览器当前第一次init，防止操作切换楼层时的一些逻辑问题

    this.initDisplay = { isFinish: false }; // 分批次给初始化数据，组合；
  }

  dispatchFloorChange() {
    this.floorChanging = true;
    this.floorMapInit = false;
    this.initDisplay = { isFinish: false };
  }

  MapInitResponseMsg(res) {
    const { header, body } = res.response;
    if (this.__isResponseError("MapInitResponseMsg", header)) return;
    const InitDataType = header.ext;

    if (!this.floorMapInit) {
      const { map } = body;
      if (InitDataType !== "MapInfo" || !map)
        throw new Error("ws MapInitResponseMsg:: 没有返回map数据呀");
      this.floorMapInit = true;
      this.__installMapFloors(map);
      return;
    }
    const isFinish = !!InitDataType.includes("MapFinished");
    this.__installDisplay(body, isFinish);
    if (isFinish) this.floorChanging = false;
  }

  MapUpdateResponseMsg(res) {
    const { header, body } = res.response;
    if (this.__isResponseError("MapUpdateResponseMsg", header)) return;
    if (this.floorChanging) return;
    this.__UpdateDisplay(body);
  }

  __isResponseError(msg, header) {
    // 响应报错
    if (header.code !== 0) {
      console.error(msg, header);
      return true;
    }
    return false;
  }

  // 处理返回数据
  __installMapFloors(map) {
    if (!map.floorIds) throw new Error("ws MapInitResponseMsg:: map数据结构中没有floorIds数据呢");
    if (!map.floors) throw new Error("ws MapInitResponseMsg:: map数据结构中没有floors数据呢");
    const floorIds = map.floorIds || [];
    const floors = map.floors || {};

    const floorsInfo = this.__formatFloorsData(floors);
    this.currentFloorIds = floorsInfo.curFloorIds;
    this.dispatch.FloorIdsReady && this.dispatch.FloorIdsReady(floorIds);
    this.dispatch.FloorsDataReady && this.dispatch.FloorsDataReady(floorsInfo);
  }

  // 初始化数据是分批次给的
  __installDisplay(body, isFinish) {
    this.initDisplay.isFinish = isFinish;
    if (body.displayRobots) {
      this.initDisplay.robots = (this.initDisplay.robots || []).concat(
        this.__formatRobots(body.displayRobots),
      );
    }
    if (body.displayRacks) {
      this.initDisplay.racks = {
        ...(this.initDisplay.racks || {}),
        ...this.__formatRacks(body.displayRacks),
      };
    }
    if (body.displayShelves) {
      const { shelves, cRacks } = this.__formatShelves(body.displayShelves);
      this.initDisplay.shelves = (this.initDisplay.shelves || []).concat(shelves);
      this.initDisplay.cRacks = (this.initDisplay.cRacks || []).concat(cRacks);
    }

    if (body.displayWorkStations) {
      this.initDisplay.stations = (this.initDisplay.stations || []).concat(
        this.__formatStations(body.displayWorkStations),
      );
    }

    if (isFinish) {
      this.dispatch.DisplayDataReady && this.dispatch.DisplayDataReady(this.initDisplay);
      this.initDisplay = { isFinish: false };
    }
  }

  __UpdateDisplay(body) {
    let display = {};
    if (body.displayRobots) {
      display.robots = this.__formatRobots(body.displayRobots);
    }
    if (body.displayRacks) {
      display.racks = this.__formatRacks(body.displayRacks);
    }
    if (body.displayCells) {
      display.cells = body.displayCells;
    }
    if (body.delShelves) {
      display.delShelves = body.delShelves;
    }
    if (body.displayShelves) {
      const { shelves, cRacks } = this.__formatShelves(body.displayShelves);
      display.shelves = shelves;
      display.cRacks = cRacks;
    }
    if (body.displayWorkStations) {
      display.stations = this.__formatStations(body.displayWorkStations);
    }
    this.dispatch.DisplayDataReady && this.dispatch.DisplayDataUpdate(display);
  }

  // format robots
  __formatRobots(data) {
    let curFloorIds = this.currentFloorIds;
    let robots = [];
    for (let key in data) {
      const item = data[key];
      if (!item.location) {
        console.error("displayRobots 找不到位置:", item);
        continue;
      }
      if (!item.location.z || !curFloorIds.includes(String(item.location.z))) continue;
      robots.push(item);
    }
    return robots;
  }
  // format racks
  __formatRacks(data) {
    let curFloorIds = this.currentFloorIds;
    let racks = {};
    for (let key in data) {
      const item = data[key];
      const { location, locationCode } = item;
      if (!location) {
        console.error("displayRacks 找不到位置:", item);
        continue;
      }
      if (!item.location.z || !curFloorIds.includes(String(item.location.z))) continue;
      item.width = "width" in item ? item.width : this.cellCodesSize[locationCode].width || 1;
      item.length = "length" in item ? item.length : this.cellCodesSize[locationCode].length || 1;
      racks[key] = item;
    }
    return racks;
  }
  __formatShelves(data) {
    let shelves = [],
      cRacks = [];
    let curFloorIds = this.currentFloorIds;
    for (let key in data) {
      let shelf = data[key];
      if (shelf.shelfStatus === "UN_BUILT") continue;
      if (!shelf.location) continue;
      if (!shelf.location.z) continue;
      if (!curFloorIds.includes(String(shelf.location.z))) continue;
      if ("popPickShelf" in shelf && shelf.popPickShelf) {
        cRacks.push(shelf);
        continue;
      }
      shelves.push(shelf);
    }
    return { shelves, cRacks };
  }
  __formatStations(data) {
    let curFloorIds = this.currentFloorIds;
    let stations = [];
    for (let key in data) {
      const item = data[key];
      if (!item.location) {
        console.error("displayWorkStations 找不到位置：", item);
        continue;
      }
      if (!item.location.z) continue;
      if (!curFloorIds.includes(String(item.location.z))) continue;
      stations.push(item);
    }
    return stations;
  }
  // format data
  __formatFloorsData(floors) {
    let floorsData = {},
      curFloorIds = [];
    for (let key in floors) {
      const { floorId, resolution, mapCells, background, mask } = floors[key] || {};
      const floor = {
        resolution,
        floorId,
        splitImage: false, // 监控暂不支持slam
        locationX: background.leftBottomPoint.x,
        locationY: background.leftBottomPoint.y,
      };
      if (!mapCells || !mapCells.length) continue;
      curFloorIds.push(key);
      mapCells.map(({ cellCode, width, length }) => {
        this.cellCodesSize[cellCode] = { width, length };
      });
      floorsData[floorId] = {
        floor,
        cells: mapCells.filter(i => !UN_RENDER_CELL_TYPE_ARR.includes(i.cellType)),
      };
    }
    return { curFloorIds, floorsData };
  }

  destroy() {
    this.currentFloorIds = [1];
    this.cellCodesSize = {}; // 记录当前楼层的cellCode Size;
    this.floorChanging = true; // 地图是否正在初始化
    this.floorMapInit = false; // 浏览器当前第一次init，防止操作切换楼层时的一些逻辑问题
  }
}

export default WSMessage;
