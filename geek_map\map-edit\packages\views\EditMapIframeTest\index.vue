<template>
  <iframe
    class="EditMapIframeTest"
    ref="iframe"
    src="http://localhost:8080/edit"
    frameborder="0"
  ></iframe>
</template>

<script>
export default {
  props: {},
  data() {
    return {};
  },
  computed: {},
  created() {},
  mounted() {
    const { iframe } = this.$refs;
    iframe.onload = () => {
      iframe.contentWindow.postMessage(
        {
          type: "option",
          body: {
            mapId: "1",
            floorId: "2",
            autoI18n: true,
          },
        },
        "*",
      );
    };
  },
  watch: {},
  methods: {},
  components: {},
};
</script>

<style scoped lang="scss">
.EditMapIframeTest {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
</style>
