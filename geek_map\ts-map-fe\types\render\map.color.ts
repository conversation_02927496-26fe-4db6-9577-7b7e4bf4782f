/* ! <AUTHOR> at 2023/04/20 */
namespace MRender {
  /** shader color */
  export type shaderColor = Array<any>;

  /**
   * 热度颜色对应0-10
   */
  export type hotColor = {
    0: color16;
    1: color16;
    2: color16;
    3: color16;
    4: color16;
    5: color16;
    6: color16;
    7: color16;
    8: color16;
    9: color16;
    10: color16;
  };

  /**  map公共配置颜色 */
  export type MapColorConfig = {
    BG?: color16; // 地图背景色
    DEFAULT_CELL?: color16; // 默认cellColor
    OMNI_DIR_CELL?: color16; // 道路节点
    CHARGER_CELL?: color16; // 充电站点
    STATION_CELL?: color16; // 工作站
    DROP_CELL?: color16; // 投递点
    QUEUE_CELL?: color16; // 排队点
    SHELF_CELL?: color16; // 货架点
    BOX_RACK_CELL?: color16; // 固定货架
    TURN_CELL?: color16; // 转面点
    CHARGER_PI_CELL?: color16; // 充电桩点
    BLOCKED_CELL?: color16; // 障碍点
    ELEVATOR_CELL?: color16; // 电梯点
    PALLET_RACK_CELL?: color16; // 托盘位点
    S_LINE?: color16; // 路线直线
    BEZIER?: color16; // 路线曲线
    AREA?: color16; // 区域
    SHELF?: color16; // 货架
    SELECTED?: color16; // Cell、货架、机器人选中颜色
    LOCKED?: color16; // Cell锁定颜色
    STOPPED?: color16; // Cell停止(暂停)颜色
    CHARGER_NORMAL?: color16; // 充电站正常颜色
    CHARGER_WORK?: color16; // 充电站工作状态颜色
    CHARGER_OFFLINE?: color16; // 充电站离线颜色
    CHARGER_ERROR?: color16; // 充电站错误颜色
    DEVICES_NORMAL?: color16; // 设备-正常
    DEVICES_ABNORMAL?: color16; // 设备-异常
    DEVICES_UNUSUAL?: color16; // 设备-配置异常
    LOAD_CELL_DIR?: color16; // cell负载箭头颜色
    UNLOAD_CELL_DIR?: color16; // cell空载箭头颜色
    LOAD_LINE_DIR?: color16; // 线段负载箭头颜色
    UNLOAD_LINE_DIR?: color16; // 线段空载箭头颜色
    SEGMENT?: color16; // map-segments的颜色
    HOT_CONF?: hotColor;
    [propName: string]: any;
  };
}
