module.exports = {
  parser: "babel-eslint",
  parserOptions: {
    sourceType: "module",
  },
  plugins: ["prettier"],
  rules: {
    // prettier
    "prettier/prettier": "error",
    // js
    "eol-last": "error",
    "no-trailing-spaces": "error",
    "comma-style": ["error", "last"],
    "comma-dangle": ["error", "always-multiline"],
    "no-multi-spaces": "error",
    // "no-undef": "error",
    // "no-unused-vars": "error",
    quotes: ["error", "double", { avoidEscape: true, allowTemplateLiterals: true }],
    indent: ["error", 2, { SwitchCase: 1, ignoredNodes: ["ConditionalExpression"] }],
    "object-curly-spacing": ["error", "always"],
    "arrow-parens": ["error", "as-needed"],
    "spaced-comment": ["error", "always"],
  },
};
