<template>
  <div class="info">
    <el-descriptions :title="$t('lang.rms.fed.mapInfo')" :column="1">
      <el-descriptions-item
        v-for="(dataItem, index) in iconConf[0].innerData"
        :label="$t(dataItem.label)"
      >
        {{ templateStore.parseContent(dataItem.value) }}
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions class="descriptions" :column="1">
      <template #title>
        <span class="mapHelp"
          >{{ $t("lang.rms.fed.operationTips") }} <i class="mapFont icon map-font-bangzhu1"></i>
        </span>
      </template>

      <el-descriptions-item
        v-for="(dataItem, index) in iconConf[1].innerData"
        :label="$t(dataItem.label)"
      >
        {{ $t(dataItem.value) }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script lang="ts" setup>
import { iconConf } from "@packages/configure/canvasIcons.conf";
import { useTemplateStore } from "@packages/store/template";
const templateStore = useTemplateStore();
</script>

<style scoped lang="scss">
.info {
  width: 300px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 14px;
  padding: 10px;

  .descriptions {
    margin-top: 20px;
  }

  .mapHelp {
    color: #f56c6c;
  }
}
</style>

<style lang="scss">
.el-descriptions {
  .el-descriptions__body {
    .el-descriptions__table:not(.is-bordered) {
      .el-descriptions__cell {
        padding-bottom: 1px;
        .el-descriptions__label {
          font-weight: 900;
        }
      }
    }
  }
}
</style>
