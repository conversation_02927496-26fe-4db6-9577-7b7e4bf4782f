<template>
  <div class="geek-customize-table">
    <!-- v-bind会自动将配置的属性赋值过来 -->
    <div v-if="tableConfig.actions" class="table-header">
      <template v-for="item in tableConfig.actions">
        <el-button
          v-if="item.hasOwnProperty('permission') ? item.permission : true"
          :key="item.label"
          v-bind="item"
          :size="item.mini ? item.mini : 'mini'"
          @click="actionHandler(item.handler)"
        >
          {{ $t(item.label) }}
        </el-button>
      </template>
    </div>

    <el-table
      ref="tableRef"
      v-bind="tableConfig.attrs"
      :data="data"
      border
      align="center"
      size="mini"
      @selection-change="selectionChange"
      @expand-change="expandChange"
      class="customize-table"
    >
      <!-- table expand -->
      <el-table-column v-if="tableConfig.expand" type="expand">
        <slot slot-scope="props" :name="tableConfig.expand.slotName" :row="props.row" />
      </el-table-column>

      <!-- table selection -->
      <el-table-column
        v-if="tableConfig.attrs && tableConfig.attrs.selection"
        type="selection"
        width="40"
        align="center"
      />

      <el-table-column
        v-if="tableConfig.attrs && tableConfig.attrs.index"
        :label="$t('lang.rms.fed.listSerialNumber')"
        type="index"
        width="50"
        align="center"
      />

      <!-- table column -->
      <template v-for="column in tableConfig.columns">
        <el-table-column v-if="column.slotName" v-bind="column" :key="column.prop" :label="$t(column.label)">
          <template slot-scope="scope">
            <slot :name="column.slotName" :row="scope.row" />
          </template>
        </el-table-column>
        <el-table-column
          v-else-if="column.operations"
          v-bind="column"
          :key="column.prop"
          :label="$t(column.label)"
          class-name="operation-btn"
          fixed="right"
          align="center"
        >
          <template #default="scope">
            <template v-for="item in column.operations">
              <el-button
                v-if="item.hasOwnProperty('permission') ? item.permission : true"
                :key="item.label"
                v-bind="item"
                :disabled="item.isDisabled ? item.isDisabled(scope.row) : false"
                :type="item.type ? item.text : 'text'"
                @click="operationHandler(item.handler, scope.row)"
              >
                {{ item.label ? $t(item.label) : $t(item.labelFormat(scope.row)) }}
              </el-button>
            </template>
          </template>
        </el-table-column>
        <el-table-column v-else v-bind="column" :key="column.prop" :label="$t(column.label)" />
      </template>
    </el-table>

    <div v-if="page" style="text-align: right; padding-top: 8px">
      <geek-pagination
        :current-page="page.currentPage"
        :page-size="page.pageSize"
        :total-page="page.pageCount"
        @currentPageChange="currentPageChange"
        @pageSizeChange="pageSizeChange"
      />
    </div>
  </div>
</template>
<script>
export default {
  name: "GeekCustomizeTable",
  props: {
    data: {
      required: true,
      type: Array,
      default: () => [],
    },
    page: {
      type: Object,
      default: null,
    },
    tableConfig: {
      required: true,
      attrs: {
        type: Object,
        default: () => {
          return {
            selection: false, // 多选框
            index: false, // 序号
          };
        },
      },
      expand: {
        type: Object,
        default: null,
      },
      actions: { type: Object, default: null }, // table header action
      columns: {
        required: true,
        type: Array,
        default: () => [],
      },
    },
  },
  methods: {
    // 分页
    currentPageChange(val) {
      this.$emit("page-change", Object.assign({}, this.page, { currentPage: val }));
    },
    // 改变每页显示条数
    pageSizeChange(val) {
      this.$emit("page-change", Object.assign({}, this.page, { currentPage: 1, pageSize: val }));
    },
    actionHandler(handler) {
      this.$emit(handler);
    },
    operationHandler(handler, row) {
      this.$emit(handler, row);
    },
    selectionChange(selections) {
      this.$emit("selectionChange", selections);
    },
    expandChange(row, rowList) {
      this.$emit("expandChange", { row, rowList });
    },
  },
};
</script>

<style lang="less">
.geek-customize-table {
  .table-header {
    .g-flex();
    justify-content: flex-end;
    padding-bottom: 8px;
  }
  .customize-table {
    width: 1005;
  }
  .el-table th.el-table__cell {
    background: #f5f7fa;
  }
  .el-table {
    th.el-table__cell {
      background: #f5f7fa;
    }
    th.el-table__cell,
    td.el-table__cell {
      .cell {
        line-height: 1.6;
        padding: 5px;
      }
    }
    &.el-table--small,
    &.el-table--mini {
      .el-table__cell {
        padding: 3px 0;
      }
    }
    .el-table__expanded-cell[class*="cell"] {
      padding: 0;
      .el-table tr:last-child td {
        border-right: 1px solid #ebeef5;
      }
    }
    .el-checkbox,
    .el-checkbox__input {
      position: unset;
      line-height: unset;
    }
  }

  .el-table .cell.el-tooltip {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 120px;
    text-align: center;
  }
  .el-table .no-expand .el-table__expand-column {
    .cell {
      display: none;
    }
  }
  .operation-btn {
    .cell {
      padding: 0 3px !important;
    }
    .el-button,
    .el-button + .el-button {
      margin: 0 3px !important;
    }
  }
  .el-table__empty-block {
    border-bottom: 1px solid #eee;
  }
}
.el-table__expanded-cell .geek-customize-table {
  padding-left: 48px;
  overflow: hidden;
  margin-left: -1px;
  margin-right: -1px;
}
</style>
