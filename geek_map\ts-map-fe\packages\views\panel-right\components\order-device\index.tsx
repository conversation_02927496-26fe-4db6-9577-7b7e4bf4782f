/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseCircleFilled } from "@ant-design/icons";
import { Input } from "antd";
import { getMap2D, $eventBus } from "../../../../singleton";

import DeviceDetail from "./device-detail";

const { Search } = Input;
type PropsOrderData = {
  isCurrent: boolean;
};
function OrderDevice(props: PropsOrderData) {
  const { t } = useTranslation();
  const [deviceCode, setDeviceCode] = useState("");
  const [deviceData, setDeviceData] = useState<deviceData>(null);
  const [totalDevice, setTotalDevice] = useState("--");

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.isCurrent) return;
    $eventBus.on("wsDataQueryRightTab", wsData => {
      if (!props.isCurrent || !wsData) return;
      const { params, data } = wsData;
      switch (params?.layer) {
        case "device":
          if (data) setDeviceData(data);
          else setDeviceData(null);
          break;
      }
    });
    return () => {
      $eventBus.off("wsDataQueryRightTab");
    };
  }, [props.isCurrent]);

  // 地图点击
  useEffect(() => {
    if (!props.isCurrent) return;

    $eventBus.on("mapClick", (data: MRender.clickParams) => {
      switch (data?.layer) {
        case "device":
          setDeviceData(null);
          setDeviceCode(data.code.toString());
          const map2D = getMap2D();
          map2D.mapWorker.reqQuery({ layer: "device", code: data.code });
          break;
      }
    });

    return () => {
      $eventBus.off("mapClick");
    };
  }, [props.isCurrent]);

  // 清空数据 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;

    _$utils.reqGet("/athena/equipment/device/findAll").then(res => {
      const devices = res?.data?.data;
      if (!devices) return;
      setTotalDevice(devices?.length || 0);
    });

    const map2D = getMap2D();
    map2D.mapRender.triggerLayers(["device"]);
    return () => {
      clearDevice();
    };
  }, [props.isCurrent]);

  // s设备清除
  const clearDevice = () => {
    setDeviceCode("--");
    setDeviceData(null);
    setTotalDevice("--");
    const map2D = getMap2D();
    map2D.mapWorker.stopQuery();
    map2D.mapRender.clearSelects();
  };

  const deviceSearch = (value: string) => {
    if (!value) return;
    const map2D = getMap2D();
    map2D.mapWorker.reqQuery({ layer: "device", code: value });
    map2D.mapRender.trigger("click", { device: [value] });
    map2D.mapRender.setEleCenter({ layer: "device", code: value });
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component"
    >
      <div className="panel-right-title">{t("lang.venus.web.common.deviceManage")}</div>
      <Search
        value={deviceCode}
        placeholder={t("lang.rms.fed.enterDeviceIDOrName")}
        onSearch={deviceSearch}
        enterButton
        allowClear={{ clearIcon: <CloseCircleFilled onClick={() => clearDevice()} /> }}
        onChange={e => setDeviceCode(e.target.value)}
      />

      <p style={{ color: "#0da0f9", textIndent: 3, fontSize: 14, paddingTop: 8 }}>
        <label>{t("lang.rms.fed.textTotal")}: </label>
        <span>{totalDevice}</span>
      </p>

      <DeviceDetail deviceData={deviceData} />
    </div>
  );
}

export default OrderDevice;
