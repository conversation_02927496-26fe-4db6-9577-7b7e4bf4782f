<template>
  <section class="legend-content">
    <h3 class="legend-title">图例</h3>
    <div class="legend-inner">
      <section v-for="(item, index) in legendRenderArr" :key="index" class="legend-tab">
        <h3 class="legend-tab-title">{{ item.title }}</h3>
        <div v-for="(i, index) in item.arr" :key="index" class="legend-item">
          <div :class="i.class ? i.class : 'legend-box'" :style="{ borderColor: `#${i.c}` }"></div>
          <div class="legend-name">{{ i.t }}</div>
        </div>
      </section>
    </div>
  </section>
</template>

<script>
export default {
  props: {
    theme: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  name: "LegendBox",
  data() {
    const ConfigColor = this.theme.MapTheme;
    return {
      legendRenderArr: [
        {
          title: this.$t("lang.rms.fed.initialization.box") + "/" + this.$t("lang.rms.fed.lattice"),
          arr: [
            {
              t: this.$t("lang.rms.charger.status.occupied"), // 占用
              c: ConfigColor["RACK_BOX"].toString(16),
            },
            {
              t: this.$t("lang.rms.fed.selected"),
              c: ConfigColor["SELECTED"].toString(16),
            },
            {
              t: this.$t("lang.rms.fed.buttonLock"),
              c: ConfigColor["LOCKED"].toString(16),
            },
          ],
        },
        {
          title: this.$t("lang.rms.fed.cell"),
          arr: [
            {
              t: this.$t("lang.rms.fed.selected"),
              c: ConfigColor["SELECTED"].toString(16),
            },
            {
              t: this.$t("lang.rms.fed.buttonLock"),
              c: ConfigColor["LOCKED"].toString(16),
            },
            {
              t: this.$t("lang.rms.fed.pause"),
              c: ConfigColor["STOPPED"].toString(16),
            },
          ],
        },
        {
          title: this.$t("lang.rms.fed.deliverBox"),
          arr: [
            {
              t: this.$t("lang.rms.charger.status.occupied"),
              c: ConfigColor["RACK_BOX"].toString(16),
            },
          ],
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss">
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.legend-content {
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 10;
  padding: 10px;
  background: #edeff1;
  border-radius: 6px;
  color: #95a0ac;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);

  .legend-title {
    line-height: 30px;
    font-size: 16px;
    font-weight: bold;
    color: #676767;
  }

  .legend-inner {
    width: 300px;
    @extend .flex-row;

    .legend-tab {
      flex: 1;
      @extend .flex-column;

      .legend-tab-title {
        font-size: 12px;
        line-height: 20px;
      }

      .legend-item {
        @extend .flex-row;
        justify-content: center;
        align-items: center;

        .legend-box {
          border: 8px solid #fff;
          border-radius: 2px;
        }

        .legend-circle {
          height: 16px;
          width: 16px;
          box-sizing: border-box;
          border: 2px solid #fff;
          border-radius: 50%;
        }

        .legend-name {
          flex: 1;
          font-size: 12px;
          padding-left: 10px;
          line-height: 30px;
        }
      }
    }
  }
}
</style>
