const toolBarList = [
  // {
  //   icon: "iconfont icon-zoom-out",
  //   text: "lang.rms.fed.zoomOut",
  //   event: "zoomIn",
  //   active: false,
  //   desc: "放大",
  //   isShow: true,
  // },
  // {
  //   icon: "iconfont icon-zoom-in",
  //   text: "lang.rms.fed.zoomIn",
  //   event: "zoomOut",
  //   active: false,
  //   desc: "缩小",
  //   isShow: true,
  // },
  {
    icon: "icon-full-screen",
    text: "lang.rms.fed.fullScreen",
    event: "fullScreen",
    active: false,
    desc: "全屏",
    isShow: true,
  },
  {
    icon: "icon-daolu",
    text: "lang.rms.fed.displayAllPath",
    event: "isShowRobotPath",
    active: false,
    desc: "机器人路径",
    isShow: true,
  },
  {
    icon: "icon-kong<PERSON><PERSON><PERSON>",
    text: "显示隐藏货位",
    event: "isShowLattice",
    active: true,
    desc: "显示隐藏货位",
    isShow: true,
  },
  {
    icon: "icon-gis_fushi",
    text: "俯视角",
    event: "isShowTopView",
    active: false,
    desc: "俯视角",
    isShow: true,
  },
  {
    icon: "icon-box",
    text: "lang.rms.fed.showTaskBox",
    event: "isShowTaskBox",
    active: false,
    desc: "显示有任务货箱",
    isShow: true,
  },
  {
    icon: "icon-shelf-heat-display",
    text: "lang.rms.fed.shelfHeatDisplay",
    event: "toggleMapShelfHeat",
    active: false,
    desc: "显示货架/货箱热度",
    isShow: true,
  },
  {
    icon: "icon-show-help-doc",
    text: "lang.rms.fed.showHelpDoc",
    event: "isShowLegend",
    active: false,
    desc: "显示帮助说明",
    isShow: true,
  },
  {
    icon: "icon-to2D",
    text: "2D",
    event: "to2D",
    active: false,
    desc: "2D",
    isShow: true,
  },
];

export default toolBarList;
