/* ! <AUTHOR> at 2021/03 */
import { fabric } from "fabric";

class EventsRuler {
  constructor($vm) {
    this.$vm = $vm;

    this.$fabric = null;
    this.isDrag = false;
    this.freeLine = null;
    this.freeLineText = null;
    this.params = {
      x1: 0,
      y1: 0,
      x2: 0,
      y2: 0,
    };
  }

  start(e) {
    this.isDrag = true;
    this.$fabric = this.$vm.$fabric;

    this._removeRulerLine();
    const { x, y } = e.absolutePointer;
    Object.assign(this.params, { x1: x, y1: y });
    this._createRulerFreeLine();
  }

  move(e) {
    if (!this.isDrag) return;

    const { x, y } = e.absolutePointer;
    Object.assign(this.params, { x2: x, y2: y });
    this._updateRulerFreeLine();
  }

  end(e) {
    this.isDrag = false;
    const { x, y } = e.absolutePointer;
    Object.assign(this.params, { x2: x, y2: y });
    this._calRulerWidth();
  }

  destroy() {
    if (!this.$fabric) return;
    this._removeRulerLine();
  }

  _removeRulerLine() {
    let $fabric = this.$fabric;
    const { freeLine, freeLineText } = this;

    if (freeLine) {
      $fabric.remove(freeLine);
    }

    if (freeLineText) {
      $fabric.remove(freeLineText);
    }

    $fabric.requestRenderAll();
    this.freeLine = null;
    this.freeLineText = null;
  }

  _createRulerFreeLine() {
    let $fabric = this.$fabric;
    const { x1, y1 } = this.params;
    const freeLine = new fabric.Line([x1, y1, x1, y1], {
      strokeWidth: 10,
      stroke: "#409effa8",
      selectable: false,
    });
    this.freeLine = freeLine;
    $fabric.add(freeLine);
    $fabric.requestRenderAll();
  }

  _updateRulerFreeLine() {
    let $fabric = this.$fabric;
    let freeLine = this.freeLine;
    const { x2, y2 } = this.params;
    freeLine.set({ x2, y2 }).setCoords();
    $fabric.requestRenderAll();
  }

  _calRulerWidth() {
    let $fabric = this.$fabric;
    const { x1, y1, x2, y2 } = this.params,
      resolution = this.$vm.resolution;

    const lineWidth = (Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2) * resolution).toFixed(3);

    let top = y1;
    let left = x1;
    let rotate = parseFloat(180 - Math.atan2(x2 - x1, y2 - y1) * Math.PI * 18.25, 10);

    if (x1 > x2) {
      top = y2;
      left = x2;
      rotate -= 180;
    }

    const freeLineText = new fabric.IText(`${lineWidth}m`, {
      top,
      left,
      fill: "#409eff",
      angle: rotate - 90,
      fontSize: 60,
      lineHeight: y2 - y1,
    });

    this.freeLineText = freeLineText;
    $fabric.add(freeLineText);
    $fabric.requestRenderAll();
  }
}

export default EventsRuler;
