<template>
  <i class="svg-icon">
    <svg class="icon" aria-hidden="true">
      <use :xlink:href="xlinkHref"></use>
    </svg>
  </i>
</template>
<script>
export default {
  name: "svgIcon",
  props: {
    symbolId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {};
  },
  computed: {
    xlinkHref() {
      return `#${this.symbolId}`;
    },
  },
};
</script>
<style scoped lang="scss">
.svg-icon {
  .icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
}
</style>
