/* ! <AUTHOR> at 2023/04/20 */

namespace MRender {
  /** layer 类 */
  export interface Layer {
    /**
     * 初始化 layer
     * @param mapCore
     */
    init(mapCore: MRender.MainCore, floorId: floorId): void;
    /** get container */
    getContainer(): any;
    /**
     * render 元素
     * @param elements
     */
    render(elements?: Array<any>): void;
    /** 重新渲染 所有元素 */
    repaint(): void;
    /** container 显示隐藏 */
    toggle(isShow: boolean): void;
    /** 地图销毁 */
    destroy(): void;
  }
}
