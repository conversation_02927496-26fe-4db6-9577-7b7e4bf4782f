<template>
  <section v-if="!isOnlyContent" class="outer-wrap">
    <geek-aside
      :isShowGeekIcon="isShowGeekIcon"
      :sidebarCollapse="sidebarCollapse"
      :sidebarHidden="sidebarHidden"
      @handleSidebarStatus="handleSidebarStatus"
    />

    <section class="right-container">
      <geek-header :isShowGeekIcon="isShowGeekIcon" />
      <geek-main />
    </section>
  </section>
  <geek-main v-else class="only-content" />
</template>

<script>
import { mapMutations } from "vuex";
import GeekHeader from "./components/geek-header";
import GeekAside from "./components/geek-aside";
import GeekMain from "./components/geek-main";

export default {
  name: "Layout",
  components: { GeekHeader, GeekAside, GeekMain },
  data() {
    return {
      isOnlyContent: !!this.$route.query.onlycontent,
      sidebarCollapse: true,
      sidebarHidden: false,
      isShowGeekIcon: $utils?._staticConfig?.showGeekIcon || false,
    };
  },
  computed: {
    sidebarShadow() {
      if (this.isOnlyContent) return false;
      return this.sidebarHidden && !this.sidebarCollapse;
    },
  },
  created() {
    this.isOnlyContent = !!this.$route.query.onlycontent;
    if (this.isOnlyContent) return;
    this.initSidebarStatus();
  },
  watch: {
    sidebarCollapse() {
      if (this.isOnlyContent) return;
      this.setSideBarChange();
    },
    sidebarHidden() {
      if (this.isOnlyContent) return;
      this.setSideBarChange();
    },
  },
  mounted() {
    if (this.isOnlyContent) return;
    window.addEventListener("resize", this.getCollapse(), false);
  },
  methods: {
    ...mapMutations(["setSideBarChange"]),
    getCollapse() {
      return $utils.Tools.debounce(() => {
        const w = this.getClientWidth();
        if (!this.sidebarCollapse && w < 1024) {
          this.sidebarCollapse = true;
          $utils.Data.setSidebarStatus(true);
        }
        this.sidebarHidden = w < 800;
      }, 300);
    },
    handleSidebarStatus() {
      const flag = !this.sidebarCollapse;
      this.sidebarCollapse = flag;
      $utils.Data.setSidebarStatus(flag);
    },
    handleSidebarHidden() {
      this.sidebarCollapse = true;
      $utils.Data.setSidebarStatus(true);
    },

    getClientWidth() {
      return document.documentElement.clientWidth;
    },

    initSidebarStatus() {
      const w = this.getClientWidth();
      const ss = $utils.Data.getSidebarStatus();
      if (!ss && ss !== false) {
        const flag = w < 1024;
        $utils.Data.setSidebarStatus(flag);
        this.sidebarCollapse = flag;
      } else if (ss === false) {
        if (w < 1024) {
          $utils.Data.setSidebarStatus(true);
          this.sidebarCollapse = true;
        } else {
          this.sidebarCollapse = ss;
        }
      } else if (ss === true) {
        this.sidebarCollapse = ss;
      }

      this.sidebarHidden = w < 800;
    },
  },
};
</script>

<style lang="less" scoped>
.outer-wrap {
  min-height: 100%;
  height: calc(100%);
  .g-flex();
  background: url(~@imgs/login/login-bg.png) 50% 50% no-repeat;
  background-size: 100% 100%;

  .right-container {
    height: calc(100%);
    display: flex;
    flex: 1;
    flex-basis: auto;
    flex-direction: column;
    -webkit-box-orient: vertical;
    min-width: 0;
  }
}
.only-content {
  padding: 0 !important;
  background: rgba(0, 0, 0, 0.3);
}
</style>
