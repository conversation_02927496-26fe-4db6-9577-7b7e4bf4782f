/* ! <AUTHOR> at 2021/01 */

export default [
  /** ************** 地图操作2D ****************/
  {
    path: "/monitor/robotControl",
    name: "robotControl",
    meta: {
      title: "lang.rms.page.menu.mapMonitor", // 地图监控
      icon: "location",
      auth: "auth.rms.monitor.page",
      noPermissionGuest: true,
    },
    component: () => import("@views/Map/singleMap2D"),
  },
  {
    path: "/monitor/robotControl3D",
    name: "singleMap3D",
    meta: {
      title: "singleMap3D",
      auth: "auth.rms.monitor.page",
      notMenu: true,
      noPermissionGuest: true,
    },
    component: () => import("@views/Map/singleMap3D"),
  },
  {
    path: "/singleEdit2D",
    name: "singleEdit2D",
    meta: {
      title: "singleEdit2D",
      auth: false,
      notMenu: true,
      noPermissionGuest: true,
    },
    component: () => import("@views/Map/singleEdit2D"),
  },
];
