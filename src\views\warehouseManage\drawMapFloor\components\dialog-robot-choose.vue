<template>
  <el-dialog
    :title="$t('lang.rms.fed.pleaseSelectRobot')"
    :visible.sync="isShow"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="false"
    class="robot-draw-floor-dialog"
    width="400px"
  >
    <div v-if="robots.length <= 0">{{ $t("lang.rms.fed.noAvailableRobotsFound") }}</div>

    <div v-else>
      <el-radio-group v-model="robotId">
        <template v-for="item in robots">
          <el-radio :key="item" :label="item" border>{{ item }}</el-radio>
        </template>
      </el-radio-group>
      <el-switch v-model="extend" :active-text="$t('lang.rms.fed.extendedMapScanning')" />
    </div>

    <div slot="footer">
      <el-button @click="drawCancel">{{ $t("lang.rms.fed.cancel") }}</el-button>
      <el-button :disabled="drawDisabled" type="primary" @click="drawStart">
        {{ $t("lang.rms.fed.startDrawing") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "RobotChoose",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    mapId: {
      type: String,
      require: true,
    },
    floorId: {
      type: String,
      require: true,
    },
  },
  data() {
    return {
      isShow: this.visible,
      robots: [],

      robotId: "",
      extend: false,
      reqStatus: false,
    };
  },
  computed: {
    drawDisabled() {
      return this.robots.length <= 0 || !this.robotId || this.reqStatus;
    },
  },
  watch: {
    visible(flag) {
      if (flag !== this.isShow) this.isShow = flag;
    },
    isShow(flag) {
      if (flag !== this.visible) this.$emit("update:visible", flag);
    },
  },
  activated() {
    this.getDrawRobots();
  },
  deactivated() {
    this.$message.closeAll();
  },
  methods: {
    drawStart() {
      this.reqStatus = true;
      $req
        .postParams(
          "/athena/map/draw/scanMap",
          {
            mapId: this.mapId,
            floorId: this.floorId,
            robotId: this.robotId,
            extend: this.extend,
          },
          { intercept: false },
        )
        .then(res => {
          this.$emit("floorDrawing", this.robotId);
          this.isShow = false;
          this.reqStatus = false;
        })
        .catch(e => {
          if (e && e.code && e.msg) {
            const errorMsg = $utils.Tools.transMsgLang(e.msg);
            this.$error(errorMsg, false);
          }
          this.reqStatus = false;
          console.log(e);
        });
    },
    drawCancel() {
      this.$router.go(-1);
      this.isShow = false;
    },
    getDrawRobots() {
      $req
        .get("/athena/map/draw/getAllOnlineRobot", {}, { intercept: false })
        .then(res => {
          let data = res.data;
          if (data) this.robots = data;
        })
        .catch(e => {
          console.log(e);
        });
    },
  },
};
</script>

<style lang="less" scoped></style>
