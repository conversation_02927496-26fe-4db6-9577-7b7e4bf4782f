/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { AutoComplete, Collapse, Modal, Tabs } from "antd";
import { $eventBus, getMap2D } from "../../../../singleton";

import OrderPanelGrid from "../common/order-panel-grid";
const { Panel } = Collapse;
const { confirm } = Modal;
type PropsOrderData = {
  isCurrent: boolean;
  mapConfig: MWorker.mapConfig;
  logicAreas: Array<any>;
  speedLimitAreas: Array<any>;
};
function OrderZone(props: PropsOrderData) {
  const { t } = useTranslation();
  const [zoneId, setZoneId] = useState("");
  const [zoneResults, setZoneResults] = useState([]);
  const [globalsValue, setGlobalsValue] = useState("");
  const [stopAreaCount, setStopAreaCount] = useState("");
  const [runningAreaCount, setRunningAreaCount] = useState("");
  const [speedLimitCount, setSpeedLimitCount] = useState("");

  const [logicNames, setLogicNames] = useState([]);
  const [areaList, setAreaList] = useState([]);
  const [speedLimitAreaList, setSpeedLimitAreaList] = useState([]);

  // 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;
    const map2D = getMap2D();
    map2D.mapRender.triggerLayers([]);

    return () => {};
  }, [props.isCurrent]);

  // 接受ws mapConfig 推送消息
  useEffect(() => {
    if (!props.mapConfig) return;
    const mapConfig = props.mapConfig || {};
    for (let key in mapConfig) {
      const value = mapConfig[key];
      switch (key) {
        case "systemState":
          if (value === "STOP") setGlobalsValue(t("lang.rms.fed.stopping"));
          else if (value === "RUNNING") setGlobalsValue(t("lang.rms.fed.running"));
          else setGlobalsValue("");
          break;
        case "stopAreaCount":
          setStopAreaCount(value);
          break;
        case "runningAreaCount":
          setRunningAreaCount(value);
          break;
        case "inSpeedLimitCount":
          setSpeedLimitCount(value);
          break;
      }
    }
  }, [props.mapConfig]);

  //准备一下 模糊查询的 list
  useEffect(() => {
    const logicAreas = JSON.parse(JSON.stringify(props.logicAreas));
    setAreaList(logicAreas);
    const searchAreaDatas = [];
    for (let i = 0; i < logicAreas.length; i++) {
      const area = logicAreas[i];
      searchAreaDatas.push({ value: area.logicName.toString() });
      searchAreaDatas.push({ value: area.logicId.toString() });
    }
    setLogicNames(searchAreaDatas);

    const speedLimitAreas = JSON.parse(JSON.stringify(props.speedLimitAreas));
    setSpeedLimitAreaList(speedLimitAreas);
    const searchSpeedLimitAreaDatas = [];
    for (let i = 0; i < speedLimitAreas.length; i++) {
      const area = speedLimitAreas[i];
      searchSpeedLimitAreaDatas.push({ value: area.areaName.toString() });
      searchSpeedLimitAreaDatas.push({ value: area.areaCode.toString() });
    }
    setLogicNames(searchAreaDatas);
  }, [props.logicAreas, props.speedLimitAreas]);

  const controlHandler = (e: any, instruction: string, item: any) => {
    e.stopPropagation();
    const map2D = getMap2D();

    let warningText = t("lang.rms.fed.confirmTheOperation");
    confirm({
      icon: <ExclamationCircleOutlined />,
      content: warningText,
      onOk() {
        const reqMsg = "WarehouseInstructionRequestMsg";
        const resMsg = "WarehouseInstructionResponseMsg";
        map2D.mapWorker.reqSocket(reqMsg, { instruction, areaIds: [item.logicId] }).then(res => {
          if (res.msgType !== resMsg) return;
          _$utils.wsCmdResponse(res?.body || {});
        });
      },
    });
  };

  const speedLimitControlHandler = (e: any, instruction: string, item: any) => {
    e.stopPropagation();
    const map2D = getMap2D();

    let warningText = t("lang.rms.fed.confirmTheOperation");
    confirm({
      icon: <ExclamationCircleOutlined />,
      content: warningText,
      onOk() {
        const reqMsg = "WarehouseInstructionRequestMsg";
        const resMsg = "WarehouseInstructionResponseMsg";
        map2D.mapWorker
          .reqSocket(reqMsg, {
            instruction,
            active: !item.active,
            level: 1,
            areaIds: [item.areaId],
          })
          .then(res => {
            if (res.msgType !== resMsg) return;
            _$utils.wsCmdResponse(res?.body || {});
          });
      },
    });
  };

  const zoneSelect = (v: any) => {
    const list = Object.values(areaList);
    const result: any[] = [];
    list.forEach((item, i) => {
      if (item.logicId.toString() === v || item.logicName.toString() === v) {
        result.push(item);
      }
    });

    if (result.length > 0) {
      setAreaList(result);
    }
  };
  const clearZone = () => {
    setZoneId("");
    setZoneResults([]);
    setAreaList(props.logicAreas);
  };
  //模糊查询
  const searchResult = (query: string) => {
    if (!props.isCurrent) return;
    if (query.length < 1) setZoneResults([]);
    else {
      const list = logicNames || [];
      let results = query
        ? list.filter(item => item.value.toLowerCase().indexOf(query.toLowerCase()) === 0)
        : list;
      setZoneResults(results);
    }
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component map2d-control-btn-group order-zone-area"
    >
      <div className="panel-right-title">{t("lang.rms.fed.tabControlZoneManagement")}</div>
      <AutoComplete
        style={{ width: "100%" }}
        placeholder={t("lang.rms.fed.enterTheZoneIDOrZoneName")}
        allowClear={true}
        value={zoneId}
        options={zoneResults}
        onSearch={searchResult}
        onChange={(value: string) => setZoneId(value)}
        onSelect={zoneSelect}
        onClear={clearZone}
      />

      <ul className="zone-number-box">
        <li>
          <label>{t("lang.rms.fed.globals")}:</label>
          <span>{globalsValue}</span>
        </li>
        <li>
          <label>{t("lang.rms.fed.partitions")}:</label>
          <div>
            <p className="danger">
              <label>{t("lang.rms.fed.stopping")}</label>
              <span>{stopAreaCount}</span>
            </p>
            <p>
              <label>{t("lang.rms.fed.running")}</label>
              <span>{runningAreaCount}</span>
            </p>
            <p className="warning">
              <label>{t("lang.rms.fed.inSpeedLimit")}</label>
              <span>{speedLimitCount}</span>
            </p>
          </div>
        </li>
      </ul>
      <Tabs
        type="card"
        items={[
          {
            label: t("lang.rms.fed.mapArea.emergencysStop"),
            key: "1",
            disabled: !(props.isCurrent && !!areaList.length),
            children: (
              <Collapse defaultActiveKey={[0]} accordion className="zone-list">
                {areaList.map((item, index) => {
                  return (
                    <Panel
                      key={index}
                      header={
                        <p className="zone-area-header">
                          <label title={t("lang.rms.fed.area") + ":" + item.logicName}>
                            {t("lang.rms.fed.area")}:{item.logicName}
                          </label>

                          {item.systemState === "RUNNING" && (
                            <>
                              <span
                                className="danger fire-stop"
                                title={t("lang.rms.fed.fireStop")}
                                onClick={e => controlHandler(e, "FIRE_STOP", item)}
                              >
                                {t("lang.rms.fed.fireStop")}
                              </span>
                              <span
                                className="danger stop"
                                title={t("lang.rms.fed.stop")}
                                onClick={e => controlHandler(e, "SYSTEM_STOP", item)}
                              >
                                {t("lang.rms.fed.stop")}
                              </span>
                            </>
                          )}
                          {item.systemState !== "RUNNING" && (
                            <span onClick={e => controlHandler(e, "SYSTEM_RECOVER", item)}>
                              {t("lang.rms.fed.renewed")}
                            </span>
                          )}
                        </p>
                      }
                    >
                      <OrderPanelGrid
                        style={{ border: 0, margin: 0 }}
                        items={[
                          { label: t("lang.rms.fed.areaName"), node: item.logicName },
                          {
                            label: t("lang.rms.fed.currentState"),
                            node: (
                              <>
                                {item.systemState === "RUNNING" && (
                                  <span onClick={() => {}} style={{ color: "#0da0f9" }}>
                                    {t("lang.rms.fed.running")}
                                  </span>
                                )}
                                {item.systemState === "STOP" && (
                                  <span onClick={() => {}} style={{ color: "#ff2328" }}>
                                    {t("lang.rms.fed.stopping")}
                                  </span>
                                )}
                                {item.systemState === "FIRESTOP" && (
                                  <span onClick={() => {}} style={{ color: "#ff2328" }}>
                                    {t("lang.rms.fed.fireStop")}
                                  </span>
                                )}
                              </>
                            ),
                          },
                          { label: t("lang.rms.fed.stopRobotNumber"), node: item.stopRobotSize },
                        ]}
                      />
                    </Panel>
                  );
                })}
              </Collapse>
            ),
          },
          {
            label: t("lang.rms.fed.search.type.logic.area.realTimeSpeedLimitArea"),
            key: "2",
            disabled: !(props.isCurrent && !!speedLimitAreaList.length),
            children: (
              <Collapse defaultActiveKey={[0]} accordion className="zone-list">
                {speedLimitAreaList.map((item, index) => {
                  return (
                    <Panel
                      key={index}
                      header={
                        <p className="zone-area-header">
                          <label title={t("lang.rms.fed.area") + ":" + item.areaId}>
                            {t("lang.rms.fed.area")}:{item.areaId}
                          </label>
                          <>
                            {item.active !== true && (
                              <span
                                className="warning speed-limit"
                                title={t("lang.rms.fed.triggerSpeedLimit")}
                                onClick={e =>
                                  speedLimitControlHandler(e, "REAL_TIME_LIMIT_SPEED_AREA", item)
                                }
                              >
                                {t("lang.rms.fed.triggerSpeedLimit")}
                              </span>
                            )}
                            {item.active === true && (
                              <span
                                onClick={e =>
                                  speedLimitControlHandler(e, "REAL_TIME_LIMIT_SPEED_AREA", item)
                                }
                              >
                                {t("lang.rms.fed.releaseSpeedLimit")}
                              </span>
                            )}
                          </>
                        </p>
                      }
                    >
                      <OrderPanelGrid
                        style={{ border: 0, margin: 0 }}
                        items={[
                          { label: t("lang.rms.fed.region"), node: item.areaId },
                          {
                            label: t("lang.rms.fed.currentState"),
                            node: (
                              <>
                                {item.active === true && (
                                  <span onClick={() => {}} style={{ color: "#FF8C00" }}>
                                    {t("lang.rms.fed.inSpeedLimit")}
                                  </span>
                                )}
                                {item.active !== true && (
                                  <span onClick={() => {}} style={{ color: "#0da0f9" }}>
                                    {t("lang.rms.fed.running")}
                                  </span>
                                )}
                              </>
                            ),
                          },
                        ]}
                      />
                    </Panel>
                  );
                })}
              </Collapse>
            ),
          },
        ]}
      />
    </div>
  );
}

export default OrderZone;
