<template>
  <geek-main-structure style="padding-top: 0">
    <geek-tabs-nav :block="true" :nav-list="permissionNavList" @select="tabsNavChange" class="notice-management-nav" />

    <keep-alive>
      <component :is="activeName" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import ExceptionConfig from "./exceptionConfig";
import NoticeManagement from "./noticeManagement";
export default {
  components: { ExceptionConfig, NoticeManagement },
  data() {
    return {
      permissionNavList: [],
      navList: [
        {
          permissionName: "TabNoticeConfigFaultPage",
          id: "ExceptionConfig",
          text: "lang.rms.page.menu.exceptionConfig", // 故障配置
        },
        {
          permissionName: "TabNoticeConfigNoticePage",
          id: "NoticeManagement",
          text: "lang.rms.page.menu.noticeManagement", // 通知管理
        },
      ],
      activeName: "",
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "menuNoticeConfig"));

    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  methods: {
    tabsNavChange(id) {
      if (this.activeName === id) return;
      this.activeName = id;
    },
  },
};
</script>
<style lang="scss" scoped>
.notice-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
