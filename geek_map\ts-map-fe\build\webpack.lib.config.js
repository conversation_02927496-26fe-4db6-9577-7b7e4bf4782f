/* ! <AUTHOR> at 2022/08/02 */
const { resolve } = require("path");
const webpack = require("webpack");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const ForkTsCheckerWebpackPlugin = require("fork-ts-checker-webpack-plugin");
const TerserPlugin = require("terser-webpack-plugin");
const { API_URL } = require("./config");

module.exports = {
  mode: "production",
  devtool: false,
  entry: {
    "monitor2d.min": ["./src/index.ts"],
  },
  output: {
    filename: "[name].js",
    path: resolve("./libs"),
    library: 'monitor2d',
    libraryTarget: "umd",
    environment: {
      arrowFunction: false,
      const: false,
    },
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js", ".jsx", ".json"],
  },
  module: {
    rules: [
      {
        test: /\.(tsx?)$/,
        use: [
          "babel-loader",
          {
            loader: "ts-loader",
            options: {
              // 关闭类型检查，即只进行转译
              // 类型检查交给 fork-ts-checker-webpack-plugin 在别的的线程中做
              // transpileOnly: true,
              // 如果设置了 happyPackMode 为 true 会隐式的设置 transpileOnly: true
              happyPackMode: true,
            },
          },
        ],
        exclude: /node_modules/,
      },
      { test: /\.(jsx?)$/, loader: "babel-loader", exclude: /node_modules/ },
    ],
  },
  plugins: [
    new CleanWebpackPlugin(),
    new webpack.DefinePlugin({
      __rms_env_conf: { isPro: true, API_URL: JSON.stringify(API_URL) },
    }),
    // fork 一个进程进行检查
    new ForkTsCheckerWebpackPlugin({ async: false }),
  ],
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        test: /\.js(\?.*)?$/i,
        extractComments: false,
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true, // 干掉那些debugger;
          },
          mangle: true,
          output: {
            comments: false,
          },
        },
      }),
    ],
  },
};
