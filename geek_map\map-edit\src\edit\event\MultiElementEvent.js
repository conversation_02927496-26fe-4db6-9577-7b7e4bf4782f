import LayerManager from "../layerManager/LayerManager";
import EventBus from '../eventBus/EventBus'
import Mode from "../Mode";
import {pixi2cad,createId} from '../utils/utils'
import {defaultConfig,colorConfig} from '../config'
import {getGlobalViewport} from "../global";
import {Graphics} from "pixi.js";
import CtrlPoint from "../element/baseElement/CtrlPoint";
import Line from "../element/baseElement/Line";
export default class MultiElementEvent {
  static addEvents() {
    const vp = getGlobalViewport()
    const operateLayerInstance = LayerManager.get('OPERATE')
    operateLayerInstance.removeChildren()
    const $operateLayer = operateLayerInstance.container
    const $line = new Graphics()
    const $container = new Graphics();
    let paths = []
    $container.addChild($line)
    $operateLayer.addChild($container)
    let segmentId = createId()
    //完成
    const finished = () => {
      //将pixi坐标转化为cad坐标
      const invertPaths = paths.map(path => {
        return pixi2cad(path)
      })
      const addOp = {
        id: 'LINE',
        data:[{
          segmentId:segmentId,
          points:invertPaths,
          segmentType:'S_LINE',
        }],
        isSaveHistory:false
      }
      operateLayerInstance.removeChildren()
      LayerManager.addElements(addOp)
      // EventBus.$emit('MultiElementEvent:addLine',segmentId)
    }
    //取消
    const cancel = () => {
      LayerManager.deleteElements({
        id: 'LINE',
        data:[segmentId],
        isSaveHistory:false
      })
      Mode.resetMode()
      paths = []
    }
    const events = {
      clicked:e => {
        if(paths.length === 2) return
        const {x,y} = e.world
        let $point;
        $point = CtrlPoint.render(x,y)
        $container.addChild($point)
        paths.push({x,y})
        //渲染线
        $line.clear()
        Line.render($line,paths)
        if(paths.length === 2){
          finished()
        }
      },
      mousemove:e => {
        if(paths.length === 2) return
        const {x,y} = e.data.getLocalPosition(vp);
        if(!paths.length) return
        const movePaths = [...paths,{x,y}]
        //渲染线
        $line.clear()
        Line.render($line,movePaths)
      },
      keydown:e => {
        const {key} = e
        if(key === 'Escape'){
          cancel()
          EventBus.$emit('keydown:Escape')
        }
      }
    }
    return events
  }
}
