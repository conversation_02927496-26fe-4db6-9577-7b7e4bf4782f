/* ! <AUTHOR> at 2023/04/22 */
import <PERSON><PERSON><PERSON> from "./mesh-cell";
import <PERSON><PERSON><PERSON>esh from "./mesh-shelf";
import Rack<PERSON>esh from "./mesh-rack";
import PoppickMesh from "./mesh-poppick";

class MeshData implements MRender.MeshDataMain {
  cell: CellMesh = new CellMesh();
  shelf: ShelfMesh = new ShelfMesh();
  rack: RackMesh = new RackMesh();
  poppick: PoppickMesh = new PoppickMesh();

  uninstall() {
    this.cell.uninstall();
    this.shelf.uninstall();
    this.rack.uninstall();
    this.poppick.uninstall();
  }

  destroy() {
    this.cell.destroy();
    this.shelf.destroy();
    this.rack.destroy();
    this.poppick.destroy();
  }
}

export default MeshData;
