/**
 * 区域
 */
export interface MapAreaDto {
  id?: number;
  areaCode: string;
  areaId: number;
  areaName: string;
  areaType: string;
  cellCodes: string[];
  color?: string | null; //颜色
  controlPoints: { x: number; y: number }[];
  functions?: {
    cellCode: string;
    funcType: string;
    [key: string]: string;
  }[];

  // 补充字段
  [key: string]: any;
}

/**
 * 线数据
 */
export interface MapSegmentDto {
  backwardAngles: null;
  descr: null;
  extendType: string;
  floorId: number;
  forwardAngle: null;
  loadDirs: number;
  loadMaxSpeed: number;
  loadObstacleRange: number;
  loadObstacleRangeAround: number;
  mapId: number;
  segmentId: number;
  segmentLength: number;
  segmentType: string;
  unloadDirs: number;
  unloadMaxSpeed: number;
  unloadObstacleRange: number;
  unloadObstacleRangeAround: number;
  width: number;
  points: PointItem[];
}

/**
 *
 */
export interface MapSingleLaneDto {
  cellCodes: string[];
  enableFollow: number;
  floorId: number;
  id: number;
  isLoad: number;
  mapId: number;
  singleLaneType: number;
}

/**
 * 工作站数据
 */
export interface MapStationDto {
  cellCode: string;
  deliverCells: string;
  direction: string;
  floorId: number;
  hostCode: string;
  id: number;
  layout: number;
  location: { x: number; y: number };
  locked: boolean;
  mapId: number;
  queueSize: number;
  stationId: number;
  type: number;
  waitCellNumber: number;
}

/**
 * 充电站数据
 */
export interface MapChargerDto {
  angle: number;
  cellCode: string;
  chargerId: number;
  floorId: number;
  hostCode: string;
  id: number;
  interactiveMode: number;
  location: { x: number; y: number };
  locked: boolean;
  mapId: number;
  robotTypes: string[];
}

/**
 * 单元格数据
 */
export interface MapNodeDto {
  cellCode: string;
  cellType: string;
  enableIndex: boolean;
  floorId: number;
  hostCode: string;
  id: number;
  isQrNode: boolean;
  length: number;
  loadDirs: null;
  qrCode?: string;
  location: { x: number; y: number };
  mapId: number;
  nodeId: number;
  indexX: number;
  indexY: number;
  nonstandardNode: boolean;
  needAngles?: number[];
  sizeType: string;
  startBounds: { x: number; y: number };
  width: number;
  yawAngle: number;
  shelfHolder?: ShelfHolder;
  functions: {
    cellCode: string;
    [key: string]: string;
  }[];
  [x: string]: any;
}

interface PointItem {
  cellCode: string;
  nodeId: number;
  x: number;
  y: number;
}

interface ShelfHolder {
  cellCode: string;
  deliverBehavior: number;
  fetchBehavior: number;
  holderAngle: number;
  holderId: number;
  holderTypeId: number;
  returnBehavior: number;
}
