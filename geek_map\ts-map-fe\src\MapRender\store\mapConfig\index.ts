/* ! <AUTHOR> at 2023/04/21 */
import MapOptions from "./map-option";
import LayerToggle from "./layer-toggle";
import OptionData from "./option-data";

class ConfigData implements MRender.RenderConfigMain {
  data: OptionData = new OptionData();
  private mapOptions: MapOptions = new MapOptions();
  private layerToggle: LayerToggle = new LayerToggle();

  setRenderConfig(key: keyof MRender.renderConfigs, value: number | boolean | MRender.mapPosition) {
    if (key === "isMultiSelect" && value) {
      this.data.setDestCell(null);
    }
    let config: any = {};
    config[key] = value;
    this.mapOptions.setConfig(config);
  }
  getRenderConfig(key: MRender.mapConfigKey = null): any {
    return this.mapOptions.getConfig(key);
  }

  setLayerVisible(key: keyof MRender.layerToggleOptions, visible: boolean) {
    if (key === "shelfHeat" && !visible) {
      this.data.setShelfHeatData(null);
    }

    let config: MRender.layerToggleOptions = {};
    config[key] = visible;
    this.layerToggle.setConfig(config);
  }
  getLayerVisible(key?: keyof MRender.layerToggleOptions): boolean | MRender.layerToggleOptions {
    return this.layerToggle.getConfig(key);
  }

  uninstall() {
    this.mapOptions.uninstall();
    this.layerToggle.uninstall();
    this.data.uninstall();
  }

  destroy() {
    this.mapOptions.destroy();
    this.layerToggle.destroy();
    this.data.destroy();
  }
}
export default ConfigData;
