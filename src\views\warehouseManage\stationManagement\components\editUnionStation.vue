<template>
  <el-dialog
    :title="$t('lang.rms.web.station.queueNumber')"
    :visible.sync="dialogVisible"
    :show-close="false"
    :close-on-click-modal="false"
    width="600px"
  >
    <div class="total-number">
      <span class="total-number-title"> {{ $t("lang.rms.web.station.unionStationQueueNumber") }} :</span>
      <span class="total-number-num">
        {{ maxRobotQueueSize }}
      </span>
    </div>
    <div class="park-queue-title">{{ $t("lang.rms.web.station.parkQueueNumber") }}</div>

    <geek-customize-table :table-config="tableConfig" :data="stationPoints">
      <template #maxQueueNumber="{ row }">
        <el-input-number
          v-model="row.maxQueueSize"
          :min="0"
          :max="99"
          size="small"
          controls-position="right"
          @change="handleChange"
        />
      </template>
      <template #isWorkingShow="{ row }">
        {{ row.isWorking ? $t("lang.rms.fed.yes") : $t("lang.rms.fed.no") }}
      </template>
    </geek-customize-table>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditUnionStation",

  data() {
    return {
      dialogVisible: false,
      rowData: {},
      stationPoints: [],
      maxRobotQueueSize: 0,
      tableConfig: {
        columns: [
          {
            label: "lang.rms.web.station.parkId",
            prop: "parkId",
            width: "100",
          },
          {
            label: "lang.rms.fed.cellCode",
            prop: "cellCode",
          },
          {
            label: "lang.venus.web.common.isEnable",
            slotName: "isWorkingShow",
          },
          {
            label: "lang.rms.fed.maxQueueNumber",
            slotName: "maxQueueNumber",
          },
        ],
      },
    };
  },

  methods: {
    open(data) {
      this.rowData = data;
      this.stationPoints = this.rowData.stationPoints || []; //JSON.parse(JSON.stringify(data.codeList));
      this.maxRobotQueueSize = data.maxRobotQueueSize || 0;
      this.dialogVisible = true;
    },
    close() {
      this.dialogVisible = false;
    },
    // // 保存
    // save() {

    //   const params = Object.assign({}, formData, {
    //     mapId: this.rowData.mapId,
    //     stationId: this.rowData.stationId,
    //   });
    //   $req.post("/athena/station/updateMaxRobotQueueSize", params).then(res => {
    //     this.$success();
    //     this.dialogVisible = false;
    //     this.$emit("updateMainList");
    //   });
    // },

    handleChange(value) {
      console.log(value, this.stationPoints);
      const arr = JSON.parse(JSON.stringify(this.stationPoints));
      let count = 0;
      for (let i = 0; i < arr.length; i++) {
        const element = arr[i];
        count += element.maxQueueSize;
      }
      this.maxRobotQueueSize = count;
    },
    // 保存
    save() {
      const arr = JSON.parse(JSON.stringify(this.stationPoints));
      let reqParkList = [];
      for (let i = 0; i < arr.length; i++) {
        const element = arr[i];
        const reqEle = {
          parkId: element.parkId,
          maxRobotQueueSize: element.maxQueueSize,
        };
        reqParkList.push(reqEle);
      }
      const data = {
        mapId: this.rowData.mapId,
        stationId: this.rowData.stationId,
        maxRobotQueueSize: this.maxRobotQueueSize,
        stationPointRobotQueueSizes: reqParkList,
      };

      console.log(data);

      $req.post("/athena/station/updateMaxRobotQueueSize", data).then(res => {
        const { code } = res || {};
        if (code === 0) {
          this.$success();
          this.dialogVisible = false;
          this.$emit("updateMainList");
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.total-number {
  font-size: 14px;
  padding-left: 10px;
  border-bottom: 1px solid #eee;

  padding-bottom: 15px;
  .total-number-title {
    padding-right: 5px;
    display: inline-block;
  }
  .total-number-num {
    padding-left: 20px;
    display: inline;
  }
}
.park-queue-title {
  padding-left: 10px;
  font-size: 14px;
  border-bottom: 1px solid #eee;
  height: 40px;
  line-height: 40px;
}
</style>
