/**
 *  处理地图hover事件
 */
import globalConfig from "../../../config";
import { lookForModelGroup } from "../../../utils/utils";
class BehaviorHover {
  constructor(options) {
    this.monitor = options.monitor;
    this.dispatch = options.dispatch || {
      hoverEvent() {},
    };
    this.actionType = "box"; // box-货箱 lattice-货位 robot-机器人 cell-单元格 station-工作站
    this.config = {
      hoverColor: globalConfig.THEME.HOVER_3D,
    };
    this.actionTypeMesh = {};
    this.preSelect = null;
    this.curSelect = null;
  }
  hover(event) {
    const { Map3d } = this.monitor;
    if (!this.actionTypeMesh[this.actionType]) return;
    let meshs = this.actionTypeMesh[this.actionType].filter(mesh => mesh.visible);
    if (!meshs || !meshs.length) return;
    const result = Map3d.search.get(meshs, event);
    const hasSelect = this.monitor.behaviorSelect.getCurSelectUniqKey();
    this.preSelect = this.curSelect;
    // 过滤hover重置select颜色；
    if (hasSelect) {
      const isEqualSelect =
        this.getCurHoverUniqKey(this.preSelect) ===
        this.monitor.behaviorSelect.getCurSelectUniqKey();
      isEqualSelect && (this.preSelect = null);
    }
    this.curSelect = result;
    this.__handlerHover();
  }
  triggerHover() {}
  destory() {
    this.actionTypeMesh = {};
    this.preSelect = null;
    this.curSelect = null;
    this.actionType = "box";
  }
  clearHover() {
    this.preSelect = this.curSelect;
    this.curSelect = null;
    this.__handlerHover();
    this.preSelect = null;
  }
  addMesh(actionType, hoverMeshs) {
    this.actionTypeMesh[actionType] = hoverMeshs;
  }
  getCurHoverUniqKey(result = this.curSelect) {
    if (!result || !result.data || !result.data.length || !result.mesh) return false;
    if (this.actionType === "box" || this.actionType === "lattice")
      return result.data[0].latticeCode;
    if (this.actionType === "cell") return !result.data.length ? false : result.data[0].cellCode;
    if (this.actionType === "robot") return lookForModelGroup(result.mesh, "Model-Group-ROBOT");
    if (this.actionType === "station") return lookForModelGroup(result.mesh, "Model-Group-STATION");
  }
  __handlerBoxHover(ov, nv, color) {
    ov &&
      ov.data &&
      ov.data.length &&
      this.monitor.rack.cancelBoxsColor(ov.data[0].latticeCode, color);
    nv && nv.data && nv.data.length && this.monitor.rack.hoverBoxs(nv.data[0].latticeCode, color);
  }
  __handlerLatticeHover(ov, nv, color) {
    ov &&
      ov.data &&
      ov.data.length &&
      this.monitor.rack.cancelLatticesColor(ov.data[0].latticeCode, color);
    nv &&
      nv.data &&
      nv.data.length &&
      this.monitor.rack.hoverLattices(nv.data[0].latticeCode, color);
  }
  __handlerCellHover(ov, nv, color) {
    const floor = this.monitor.Map3d.floor;
    ov && floor.cancelCellColor(ov.data[0], color);
    nv && floor.hoverCell(nv.data[0], color);
  }
  __handlerRobotHover(ov, nv, color) {
    ov &&
      ov.mesh &&
      this.monitor.robot.cancelHoverRobot(lookForModelGroup(ov.mesh, "Model-Group-ROBOT"), color);
    nv &&
      nv.mesh &&
      this.monitor.robot.hoverRobot(lookForModelGroup(nv.mesh, "Model-Group-ROBOT"), color);
  }
  __handlerStationHover(ov, nv, color) {
    ov &&
      ov.mesh &&
      this.monitor.station.cancelHoverStation(
        lookForModelGroup(ov.mesh, "Model-Group-STATION"),
        color,
      );
    nv &&
      nv.mesh &&
      this.monitor.station.hoverStation(lookForModelGroup(nv.mesh, "Model-Group-STATION"), color);
  }
  __handlerHover() {
    const nv = this.curSelect,
      ov = this.preSelect;
    const color = this.config.hoverColor;
    if (this.actionType === "box") {
      this.__handlerBoxHover(ov, nv, color);
    }
    if (this.actionType === "lattice") {
      this.__handlerLatticeHover(ov, nv, color);
    }
    if (this.actionType === "cell") {
      this.__handlerCellHover(ov, nv, color);
    }
    if (this.actionType === "robot") {
      this.__handlerRobotHover(ov, nv, color);
    }
    if (this.actionType === "station") {
      this.__handlerStationHover(ov, nv, color);
    }
  }
}

export default BehaviorHover;
