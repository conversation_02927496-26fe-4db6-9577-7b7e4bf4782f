/* ! <AUTHOR> at 2023/04/19 */

/**
 * 充电站接口数据类型
 * @param chargerId 唯一充电站ID
 * @param location 位置数据
 * @param chargerDir 方向
 * @param chargerStatus 状态
 * @param cellCode 所占的对应的cellCode
 * @param startBoundLocation 左下角位置
 * @param cellWidth 宽
 * @param cellLength 长
 * @param 其他 可选
 */
type chargerData = {
  chargerId: code;
  location: location;
  chargerDir: "EAST" | "WEST" | "SOUTH" | "NORTH";
  chargerStatus: "ERROR" | "OFFLINE" | "WORK" | "NORMAL" | string;
  cellCode: code;
  startBoundLocation?: location;
  cellLength: number;
  cellWidth: number;
  [propName: string]: any;
};

/**
 * 充电站地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param floorId 楼层
 * @param direction placeDir转换的角度
 * @param statusColor 宽
 * @param position 地图位置数据
 * @param width 宽
 * @param height 高
 * @param cellCode 工作站急停按钮状态，true则当前工作站区域变成红色
 * @param 其他 可选
 */
type mChargerData = {
  code: code;
  floorId: floorId;
  direction: number;
  statusColor: color16;
  position: location;
  width: number;
  height: number;
  cellCode: code;
  [propName: string]: any;
};
