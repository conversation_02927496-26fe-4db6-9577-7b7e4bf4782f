import { NodeAttrEditConf } from "@packages/type/editUiType";
import { useI18n } from "@packages/hook/useI18n";

// 反光柱新增的配置内容
export const MAKER_LOCAL_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  const { t } = useI18n();
  return {
    name: "makerLocal",
    tabTitle: "lang.rms.fed.coordinate",
    labelWidth: "102px",
    formItem: [
      // 左下坐标X
      {
        prop: "startBoundsX",
        label: `${t("lang.rms.fed.lbLocation")}X`,
        describe: "单元格左下角的绝对坐标/m",
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        set(value: number, fromData: { [k: string]: any }) {
          fromData.location || (fromData.location = {});
          fromData.startBounds || (fromData.startBounds = {});
          fromData.startBounds.x = value;
          fromData.location.x = value + fromData.length / 2;
        },
        get(fromData: { [k: string]: any }) {
          return fromData.startBounds?.x || 0;
        },
      },
      // 左下坐标Y
      {
        prop: "startBoundsY",
        label: `${t("lang.rms.fed.lbLocation")}Y`,
        describe: "单元格左下角的绝对坐标/m",
        component: "elInputNumber",
        min: -100000,
        max: 100000,
        step: 0.1,
        precision: 3,
        set(value: number, fromData: { [k: string]: any }) {
          fromData.location || (fromData.location = {});
          fromData.startBounds || (fromData.startBounds = {});
          fromData.startBounds.y = value;
          fromData.location.y = value + fromData.width / 2;
        },
        get(fromData: { [k: string]: any }) {
          return fromData.startBounds?.y || 0;
        },
      },
      {
        appendSlotName: "pinpointSlot",
        onlyComponent: true,
      },
    ],
  };
};
