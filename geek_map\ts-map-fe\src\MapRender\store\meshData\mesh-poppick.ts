/* ! <AUTHOR> at 2023/04/22 */

class MeshShelf implements MRender.MeshData {
  meshData: { [propName: code]: Array<mShelfData> } = {};

  setData(code: code, data: any) {
    this.meshData[code] = data;
  }
  getData(code: code): Array<mShelfData> {
    return this.meshData[code] || null;
  }
  getAll(): { [propName: code]: Array<mShelfData> } {
    return this.meshData;
  }

  delData(code: code) {
    delete this.meshData[code];
  }

  delByFloorId(floorId: floorId) {
    let meshData = this.meshData;
    if (!meshData) return;
    let item;
    for (let key in meshData) {
      item = meshData[key];
      if (item[0] && item[0].floorId === floorId) delete meshData[key];
    }
  }

  uninstall() {
    this.meshData = {};
  }

  destroy() {
    this.uninstall();
  }
}

export default MeshShelf;
