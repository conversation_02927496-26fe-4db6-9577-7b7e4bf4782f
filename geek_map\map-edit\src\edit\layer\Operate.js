import { Container } from "pixi.js";
import { getGlobalViewport } from "../global";
class Operate {
  constructor() {
    this.container = null;
    this.viewport = null;
    this.id = null;
  }
  initLayer(op) {
    const { id, zIndex } = op;
    this.id = id;
    this.viewport = getGlobalViewport();
    this.container = new Container();
    this.container.id = id;
    this.container.name = id;
    this.container.zIndex = zIndex;
    this.container.interactive = true;
    this.container.interactiveChildren = true;
    this.container.sortableChildren = true;
    this.viewport.addChild(this.container);
  }
  removeChildren() {
    this.container.removeChildren();
  }

  //是否可以被点击
  triggerLayer(isTrigger) {
    // this.container.interactiveChildren = isTrigger;
  }
}
export default Operate;
