/* ! <AUTHOR> at 2022/09/08 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseOutlined, CloseCircleOutlined } from "@ant-design/icons";
import { Card, Tag, Button, Input } from "antd";
import { getMap2D } from "../../../../singleton";

type PropsOrderData = {
  visible: boolean;
  robotId: code;
  onCancel: () => void;
};
function RobotUpdateFloor(props: PropsOrderData) {
  const { t } = useTranslation();
  const [floorId, setFloorId] = useState("");
  const [isEmpty, setIsEmpty] = useState(false);

  useEffect(() => {
    if (!props.visible) return;
    return () => {
      setFloorId("");
      setIsEmpty(false);
    };
  }, [props.visible]);

  const controlHandler = () => {
    if (!floorId) {
      setIsEmpty(true);
      return;
    }

    const reqMsg = "RobotInstructionRequestMsg";
    const resMsg = "RobotInstructionResponseMsg";
    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket(reqMsg, {
        robotId: props.robotId,
        instruction: "UPDATE_FLOOR",
        floor: floorId,
      })
      .then(res => {
        if (res.msgType !== resMsg) return;
        _$utils.wsCmdResponse(res?.body || {});
      });
    props.onCancel();
  };

  return (
    props.visible && (
      <Card
        size="small"
        type="inner"
        title={t("lang.rms.fed.goTo")}
        extra={<CloseOutlined onClick={() => props.onCancel()} />}
        actions={[
          <Button
            type="primary"
            onClick={controlHandler}
            size="small"
            style={{ float: "right", marginRight: 6 }}
          >
            {t("lang.rms.fed.confirm")}
          </Button>,
        ]}
        className="component-operate-detail"
      >
        <Input
          onChange={e => setFloorId(e.target.value)}
          addonBefore={t("lang.rms.fed.targetFloorID")}
          placeholder={t("lang.rms.fed.InputTargetFloorID")}
          style={{ padding: "8px 6px 0" }}
        />
        <Tag
          icon={<CloseCircleOutlined />}
          color="error"
          style={{ marginLeft: 8, display: isEmpty ? "inline-block" : "none" }}
        >
          {t("lang.rms.fed.InputTargetFloorID")}!
        </Tag>
      </Card>
    )
  );
}

export default RobotUpdateFloor;
