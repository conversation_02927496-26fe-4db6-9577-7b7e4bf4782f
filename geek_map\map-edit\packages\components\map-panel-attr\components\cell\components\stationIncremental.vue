<template>
  <div class="functionItem">
    <el-form-item label="货架类型编码" prop="name">
      <el-select v-model="ruleForm.region" placeholder="Activity zone">
        <el-option label="Zone one" value="shanghai" />
        <el-option label="Zone two" value="beijing" />
      </el-select>
    </el-form-item>
    <el-form-item label="通行角度" prop="name">
      <el-select v-model="ruleForm.region" placeholder="Activity zone">
        <el-option label="Zone one" value="shanghai" />
        <el-option label="Zone two" value="beijing" />
      </el-select>
    </el-form-item>
</template>

<script setup lang="ts">
import { computed, ComputedRef, toRefs, ref, Ref, watch } from "vue";
import { storeToRefs } from "pinia";
import {
  LIMIT_SHELF_ANGLE_CELLFUNC,
  LIMIT_SHELF_ANGLE_CELLFUNC_ANY,
  LIMIT_SHELF_ANGLE_CELLFUNC_SHELF,
  LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR,
} from "@packages/configure/dict/shelfAngleCellFun";
import { ANGLE_DICT } from "@packages/configure/dict/angle";

interface FromDataType {
  [k: string]: { limitObj: string; limitAngles: number[] }[];
}

const curSelectType: Ref<string[]> = ref([]);
const fromData: Ref<FromDataType> = ref({});

watch(curSelectType, value => {
  // 初始化数据
  value.forEach(key => {
    if (fromData.value[key]) {
      switch (key) {
        case LIMIT_SHELF_ANGLE_CELLFUNC_ANY:
          fromData.value[key] = [{ limitObj: "ANGLES", limitAngles: [] }];
          break;
        case LIMIT_SHELF_ANGLE_CELLFUNC_SHELF:
          fromData.value[key] = [{ limitObj: "", limitAngles: [] }];
          break;
        case LIMIT_SHELF_ANGLE_CELLFUNC_ROBOT_BEHAVIOR:
          fromData.value[key] = [{ limitObj: "", limitAngles: [] }];
          break;
      }
    }
  });

  Object.keys(fromData.value).forEach((key: string) => {
    value.includes(key) || delete fromData.value[key];
  });
});
</script>

<style scoped lang="scss">
.functionItem {
  position: relative;
  border: 1px dashed #ccc;
  margin-bottom: 10px;
  padding: 10px;
  .title {
    font-size: 14px;
    font-weight: 900;
  }

  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    height: 16px;
    line-height: 16px;
    cursor: pointer;
  }
}
.funcBaseFrom {
  width: 100%;
}

.describeIcon {
  color: #409eff;
}
</style>
