/* ! <AUTHOR> at 2023/04/20 */

class ShelvesData implements MRender.MapData {
  private mapData: { [propName: code]: mShelfData } = {};
  private floorIdCodes: { [propName: floorId]: code[] } = {};

  setData(code: code, data: mShelfData) {
    const floorId = data.floorId;
    if (!this.floorIdCodes[floorId]) this.floorIdCodes[floorId] = [];
    this.floorIdCodes[floorId].push(data.code);

    this.mapData[code] = data;
  }

  getData(code: code) {
    return this.mapData[code];
  }

  getByFloorId(floorId: floorId): mShelfData[] {
    const mapData = this.mapData;
    const codes = this.floorIdCodes[floorId] || [];

    let data: mShelfData[] = [];
    for (let i = 0, len = codes.length; i < len; i++) {
      data.push(mapData[codes[i]]);
    }
    return data;
  }

  getAll() {
    return this.mapData;
  }

  delData(code: code) {
    delete this.mapData[code];
  }

  uninstall() {
    this.mapData = {};
    this.floorIdCodes = {};
  }

  destroy() {
    this.uninstall();
  }
}

export default ShelvesData;
