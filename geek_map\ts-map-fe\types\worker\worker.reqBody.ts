/* ! <AUTHOR> at 2023/04/20 */

namespace MWorker {
  /**
   * websocket前端请求传参
   * @param floorIds 楼层，全部楼层传 []
   * @param clientFor3D 是否为3D请求
   * @param robotIds 机器人id
   * @param rackCodes
   * @param shelfCodes
   * @param cellCodes
   * @param chargerIds
   * @param robotsNeedCoverage
   */
  export type wsReqBody = {
    floorIds: floorId[];
    clientFor3D?: boolean;
    robotIds?: Array<code>;
    rackCodes?: Array<code>;
    shelfCodes?: Array<code>;
    cellCodes?: Array<code>;
    chargerIds?: Array<code>;
    stationIds?: Array<code>;
    robotsNeedCoverage?: Array<code>;
  };
}
