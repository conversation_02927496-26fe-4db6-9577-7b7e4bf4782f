<template>
  <div ref="mapMouseMenuRef" class="mapMouseMenu" v-show="visable">
    <el-cascader-panel
      ref="cascaderRef"
      :options="options"
      :props="config"
      @change="clickMenuItem"
    />
  </div>
</template>

<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { ref, Ref, computed, ComputedRef } from "vue";
import { mapMouseMenu } from "./configure";
import { triggerEvent } from "@packages/hook/useEvent";
import { useAttrStore } from "@packages/store/attr";

interface OptionType {
  label: string;
  value: string;
  disabled?: boolean;
  children?: OptionType[];
}

const { t } = useI18n();

const visable: Ref<boolean> = ref(false);

const options: ComputedRef<OptionType[]> = computed(() => {
  // 不要删, 这样可以保持状态同步, 如果出现问题, 请使用toRefs
  if (!visable.value) return [];

  return mapMouseMenu.options
    .filter(item => {
      if (item.condition) {
        return item.condition(attrStore.selectNodes, <string>attrStore.layerName);
      }
      return true;
    })
    .map(item => {
      const data: OptionType = { label: t(item.label), value: <string>item.eventName };
      if (item.disabled) {
        data.disabled = item.disabled(attrStore.selectNodes, <string>attrStore.layerName);
      }
      return data;
    });
});

const cascaderRef = ref();
const mapMouseMenuRef = ref();
const attrStore = useAttrStore();

const config = {
  ...mapMouseMenu.configure,
  multiple: false,
  emitPath: false,
};

/**
 * 当取消菜单时
 * */
function cancelMenu(event?: any) {
  if (
    event &&
    event.path &&
    event.path?.length &&
    event.path.find((item: any) => item === mapMouseMenuRef.value)
  ) {
    return;
  }

  visable.value = false;
  cascaderRef.value.clearCheckedNodes();
  window.removeEventListener("click", cancelMenu);
}

/**
 * 展示菜单
 * */
function showMenu(position: { x: number; y: number }) {
  /**
   * 全局禁用的情况下, 不在呼出右键菜单
   */
  if (attrStore.isGlobalDisabled) {
    return;
  }

  visable.value = true;
  mapMouseMenuRef.value.style.top = `${position.y}px`;
  mapMouseMenuRef.value.style.left = `${position.x}px`;
  window.addEventListener("click", cancelMenu);
}
/**
 * 隐藏菜单
 * */
function hideMenu() {
  visable.value = false;
}
/**
 * 点击菜单内容, 触发菜单事件
 * */
function clickMenuItem(eventName: string) {
  triggerEvent({
    option: {
      name: eventName,
      eventName,
    },
  });
  cancelMenu();
}

defineExpose({
  showMenu,
  hideMenu,
});
</script>

<style scoped lang="scss">
.mapMouseMenu {
  position: fixed;
  z-index: 3;
  background: #fff;
}
</style>
<style>
.mapMouseMenu .el-cascader-menu__wrap.el-scrollbar__wrap {
  height: auto !important;
}
</style>
