import Segment from "../element/Segment";
import BezierSegment from "../element/BezierSegment";
import Base from "./Base";
import EventBus from "../eventBus/EventBus";
import Store from "../store/Store";
import CellElement from "../element/Cell";
const renderList = {
  S_LINE: Segment,
  BEZIER: BezierSegment,
};
export default class Line extends Base {
  constructor(props) {
    super(props);
    this.layerName = "LINE";
  }
  //初始化元素
  initElements(data = []) {
    data.forEach(item => {
      const { segmentType, segmentId } = item;
      const renderFn = renderList[segmentType];
      const $el = renderFn.add(item);
      if ($el) {
        $el.layerName = this.layerName;
        this.container.addChild($el);
        this.setProperties(segmentId, $el, item);
      }
    });
  }
  //添加元素
  addElements(data = []) {
    //历史数据
    const historyData = [];
    const addedData = [];
    data.forEach(item => {
      const { segmentType, segmentId } = item;
      const renderFn = renderList[segmentType];
      const $el = renderFn.add(item);
      $el.layerName = this.layerName;
      this.container.addChild($el);
      this.setProperties(segmentId, $el, item);
      const addInfo = this.getProperties(segmentId);
      const { properties } = addInfo;
      historyData.push({ ...properties });
      addedData.push({ ...addInfo });
    });
    //添加历史记录
    const historyDetail = { action: "add", detail: historyData, layerName: this.layerName };
    return { historyDetail, emitData: addedData };
  }
  //更新元素
  updateElements(data = [], isCoverProperties = false) {
    //历史数据
    const historyData = [];
    const updateData = [];
    data.forEach(item => {
      const { segmentId, points, segmentType } = item;
      //旧数据存储
      const { properties: oldProperties } = this.getProperties(segmentId);
      historyData.push({ ...oldProperties });
      const renderFn = renderList[segmentType];
      const $el = this.id2$el.get(segmentId);
      this.setProperties(segmentId, $el, item, isCoverProperties);
      const updateInfo = this.getProperties(segmentId);
      const { properties } = updateInfo;
      updateData.push({ ...updateInfo });
      renderFn.update($el, properties);
    });
    const historyDetail = { action: "update", detail: historyData, layerName: this.layerName };
    return { historyDetail, emitData: updateData };
  }
  //删除元素
  deleteElements(ids = []) {
    //历史数据
    const historyData = [];
    const deleteData = [];
    ids.forEach(id => {
      const { $el, properties } = this.getProperties(id);
      const { points, segmentId } = properties;
      //删除线与节点的关系
      points.forEach(point => {
        const { nodeId } = point;
        if (nodeId) Store.node2Line.delete(nodeId, segmentId);
      });
      historyData.push({ ...properties });
      deleteData.push({ $el: null, properties: { ...properties } });
      this.id2$el.delete(id);
      this.container.removeChild($el);
    });
    const historyDetail = { action: "delete", detail: historyData, layerName: this.layerName };
    return { historyDetail, emitData: deleteData };
  }
  //更改dir显示类型
  showDirByType() {
    const $con = this.container;
    $con.children.forEach(child => {
      const properties = this.$el2properties.get(child);
      const { segmentType } = properties;
      const renderFn = renderList[segmentType];
      renderFn.update(child, properties);
    });
  }

  //是否可以被点击
  triggerLayer(isTrigger) {
    this.container.interactiveChildren = isTrigger;
  }
}
