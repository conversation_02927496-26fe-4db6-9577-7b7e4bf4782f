import * as THREE from "three";
import system from "../../asset/image/system.png";

export const station2 = (model, config) => {
  const ImageLoader = new THREE.ImageLoader();
  const { next } = config;
  let screen = model.getObjectByName("screen");
  model.scale.set(0.001, 0.001, 0.001);
  model.position.y = 0;
  const group = new THREE.Group();
  group.name = "station-load-group";
  group.add(model);
  ImageLoader.load(system, img => {
    const texture = new THREE.Texture(img);
    texture.needsUpdate = true;
    screen.material = new THREE.MeshBasicMaterial({ map: texture });
    next && next(group);
  });
};

export const station6 = (model, config) => {
  station2(model, {
    screenImg: config.screenImg,
    next: group => {
      const ImageLoader = new THREE.ImageLoader();
      const { next } = config;
      ImageLoader.load(
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAC0AgMAAADQnFUrAAAADFBMVEX//wAA/wD/AACAgIDsSniYAAAAHklEQVQoz2NYtWrBsMD//38gG4eGBgwLTEkYDBMAAOY06mE5dYlzAAAAAElFTkSuQmCC",
        img => {
          const texture = new THREE.Texture(img);
          texture.needsUpdate = true;
          const cylinder = new THREE.Mesh(
            new THREE.CylinderGeometry(0.2, 0.2, 3, 13, 1, true),
            new THREE.MeshBasicMaterial({
              map: texture,
              side: THREE.DoubleSide,
            }),
          );
          cylinder.position.y = 7.5;
          cylinder.position.x = -1;
          cylinder.position.z = 1;
          group.add(cylinder);
          next && next(group);
        },
      );
    },
  });
};
