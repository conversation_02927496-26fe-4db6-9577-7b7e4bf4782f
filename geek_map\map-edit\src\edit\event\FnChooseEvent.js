import Selected from "../selected/Selected";
import {getId} from '../utils/utils'
import Event from "../event/Event";
import {getGlobalViewport} from "../global";
import EventBus from "../eventBus/EventBus";
import LayerManager from "../layerManager/LayerManager";
import {Graphics} from "pixi.js";
import DashLine from "../element/baseElement/DashLine";
import {elementColor,defaultConfig,cellRatio,COEFFICIENT} from '../config'
export default class DefaultEvent {
    //功能选中的ids
    static fnSelectedIds = []
    //当前正处于编辑态的id
    static selectedNodeId = null
    //允许选择的ids
    static enableClickedNodeIds = []
    //是否是多选模式
    static multiple = true
    static addEvents(options) {
        //重置选中模式为单选
        Selected.isMultipleSelected = false
        const vp = getGlobalViewport()
        let timestamp = null
        //设置选中态
        const {selectedNodeId,enableClickedNodeIds,multiple} = options
        this.selectedNodeId = selectedNodeId
        this.enableClickedNodeIds = enableClickedNodeIds
        this.multiple = multiple
        const setSelected = () => {
            const {$el} = LayerManager.getProperties({layerName:"CELL",id:selectedNodeId})
            Selected.renderSelected(selectedNodeId,$el)
        }
        //设置允许点击的单元格
        const setEnableClickedCell = () => {
            enableClickedNodeIds.forEach(nodeId => {
                const {$el} = LayerManager.getProperties({layerName:"CELL",id:nodeId})
                const bounds = $el.getLocalBounds()
                const {x,y,width,height} = bounds
                const $border = new Graphics()
                $border.name = 'fnEnableSelected'
                $el.addChild($border)
                const borderPath = [
                    {x,y},
                    {x:x + width,y},
                    {x:x + width,y:y + height},
                    {x,y:y + height},
                    {x,y},
                ]
                const lineW = 0.5
                const dashArr = [1.5,1]
                // '#26e07a'
                DashLine.render($border,borderPath,{color:0x26e07a,width:lineW,dashArr:dashArr})
            })
        }
        //设置点击态
        const setClickState = (id) => {
          this.fnSelectedIds.push(id)
          const {$el} = LayerManager.getProperties({layerName:"CELL",id})
          $el.tint = 0x78efad;
        }
        //重置点击态
        const resetClickState = (id) => {
          const {$el} = LayerManager.getProperties({layerName:"CELL",id})
          const {cellType} = $el
          $el.tint = elementColor[cellType];
        }
        setSelected()
        setEnableClickedCell()
        const events =  {
            clicked:e => {
                if(Event.isRightClick) return Event.isRightClick = false
                if(Date.now() - timestamp > 300) return timestamp = null
                const target = e.event.target
                if(!target || target.name === 'viewport') return
                const clickedId = getId(target)
                //有选择范围或者没有选择范围时，走以下逻辑
                if(this.enableClickedNodeIds.includes(clickedId) || this.enableClickedNodeIds.length === 0){
                    //判断是多选还是单选
                    if(this.multiple){
                      if(!this.fnSelectedIds.includes(clickedId)){
                        setClickState(clickedId)
                      }else{
                        const index = this.fnSelectedIds.findIndex((id) => id === clickedId)
                        this.fnSelectedIds.splice(index,1)
                        resetClickState(clickedId)
                      }
                    }else{
                      const selectedId = this.fnSelectedIds.length ? this.fnSelectedIds[0] : null
                      if(selectedId) {
                        resetClickState(selectedId)
                        this.fnSelectedIds = []
                      }
                      if(selectedId !== clickedId){
                        setClickState(clickedId)
                      }
                    }
                }
                //发送选中回调
                // const emitData = this.fnSelectedIds.map(id => {
                //     const data = LayerManager.getProperties({layerName:"CELL",id})
                //     return data
                // })
                // EventBus.$emit('selected',emitData)
            },
            mousedown: e => {
                timestamp = Date.now()
            },
            keydown:e => {
                const {key} = e
                if(key === 'Escape'){
                    EventBus.$emit('keydown:Escape')
                }
                Event.activeKey = key
            },
            keyup: (e) => {
                Event.activeKey = null
            },
        }
        return events
    }
    static _resetFnSelected() {
        this.fnSelectedIds.forEach(id => {
            const {$el} = LayerManager.getProperties({layerName:"CELL",id})
            const {cellType} = $el
            $el.tint = elementColor[cellType];
        })
    }
    static fnChoose(nodeIds = []){
        //重置选中态
        this._resetFnSelected()
        //元素设置选中态
        nodeIds.forEach(id => {
            const {$el} = LayerManager.getProperties({layerName:"CELL",id})
            $el.tint = 0x78efad;
        })
        this.fnSelectedIds = nodeIds
    }
    static fnChooseFinish() {
      const emitData = this.fnSelectedIds.map(id => {
        const data = LayerManager.getProperties({layerName:"CELL",id})
        return data
      })
      this.destroy()
      return emitData || []
    }
    static destroy() {
        //重置单元格状态
        this._resetFnSelected()
        this.enableClickedNodeIds.forEach(id => {
            const {$el} = LayerManager.getProperties({layerName:"CELL",id})
            let selected = $el.getChildByName('fnEnableSelected')
            if(selected){
                selected.destroy()
                $el.removeChild(selected)
            }
        })
        this.fnSelectedIds = []
        this.selectedNodeId = null
        this.enableClickedNodeIds = []
        this.multiple = true
    }
}
