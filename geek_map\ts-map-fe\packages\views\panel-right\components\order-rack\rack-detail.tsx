/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Tabs } from "antd";

import OrderGrid from "../common/order-grid";

type PropsOrderData = {
  rack: rackData;
  currentSelect: { type: "box" | "lattice"; latticeCodes: Array<code>; boxCode: "" };
};

function RackDetail(props: PropsOrderData) {
  const { t } = useTranslation();
  const [currentTab, setCurrentTab] = useState("rack");
  const [tabItems, setTabItems] = useState<Array<any>>([]);

  // 接收 poppick 数据
  useEffect(() => {
    const data = props.rack;
    if (!data) {
      setTabItems([]);
      setCurrentTab("rack");
      return;
    }

    let items: any = [
      {
        label: t("lang.rms.fed.rack"),
        key: "rack",
        children: getRack(data),
      },
    ];

    const type = props.currentSelect?.type;
    const lattices = data.lattices || [];
    if (!lattices.length || !["lattice", "box"].includes(type)) {
      setTabItems(items);
      setCurrentTab("rack");
      return;
    }

    const codes = props.currentSelect.latticeCodes;
    switch (type) {
      case "lattice":
        const filters = lattices.filter(item => codes.includes(item.latticeCode));
        if (filters.length) {
          items.push({
            label: t("lang.rms.fed.lattice"),
            key: "lattice",
            children: getLattice(filters),
          });
        }
        break;
      case "box":
        const lattice = lattices.find((item: any) => item.latticeCode === codes[0]);
        if (lattice) {
          items.push({
            label: t("lang.rms.fed.lattice"),
            key: "lattice",
            children: getLattice([lattice]),
          });
          items.push({
            label: t("lang.rms.fed.box"),
            key: "box",
            children: getBox(lattice.relateBox || {}),
          });
        }
        break;
    }

    setTabItems(items);
  }, [props.rack, props.currentSelect]);

  useEffect(() => {
    const currentSelect = props.currentSelect;
    if (!currentSelect) return;

    const type = props.currentSelect.type;
    setCurrentTab(type);
  }, [props.currentSelect]);

  const getRack = (data: any) => {
    return (
      <OrderGrid
        items={[
          {
            label: t("lang.rms.fed.rackCode"),
            value: data?.rackCode || "--",
          },
          {
            label: t("lang.rms.fed.rackLocation"),
            value: data?.location || "--",
          },
          {
            label: t("lang.rms.fed.rackLocationCode"),
            value: data?.locationCode || "--",
          },
          {
            label: t("lang.rms.fed.logicId"),
            value: data?.logicId || "--",
          },
          {
            label: t("lang.rms.fed.layerCount"),
            value: data?.layers || "--",
          },
          {
            label: t("lang.rms.fed.boxCount"),
            value: data.hasOwnProperty("boxNum") ? data.boxNum : "--",
          },
        ]}
      />
    );
  };

  const getLattice = (lattices: any) => {
    return lattices.map((lattice: any, index: number) => {
      return (
        <div key={index} style={{ marginBottom: 10 }}>
          <OrderGrid
            items={[
              {
                label: t("lang.rms.fed.latticeCode"),
                value: lattice?.latticeCode || "--",
              },
              {
                label: t("lang.rms.fed.latticeStatus"),
                value: lattice?.latticeStatus || "--",
              },
              {
                label: t("lang.rms.fed.latticeAvailableStatus"),
                value: lattice?.latticeFlag || "--",
              },
              {
                label: t("lang.rms.fed.layer"),
                value: lattice?.layer || "--",
              },
              {
                label: t("lang.rms.fed.height"),
                value: lattice?.height || "--",
              },
              {
                label: t("lang.rms.fed.fetchDirs"),
                value: lattice?.fetchDirs || "--",
              },
              {
                label: t("lang.rms.fed.locationCode"),
                value: lattice?.locationCode || "--",
              },
            ]}
          />
        </div>
      );
    });
  };

  const getBox = (box: any) => {
    return (
      <OrderGrid
        items={[
          {
            label: t("lang.rms.fed.boxCode"),
            value: box?.boxCode || "--",
          },
          {
            label: t("lang.rms.fed.boxStatus"),
            value: box?.boxStatus || "--",
          },
          {
            label: t("lang.rms.fed.boxCurrentLocation"),
            value: box?.location || "--",
          },
          {
            label: t("lang.rms.fed.placeLatticeCode"),
            value: box?.placeLatticeCode || "--",
          },
          {
            label: t("lang.rms.fed.currentLatticeCode"),
            value: box?.currentLatticeCode || "--",
          },
          {
            label: t("lang.rms.fed.robotId"),
            value: box?.robotId || "--",
          },
          {
            label: t("lang.rms.fed.locationCode"),
            value: box?.locationCode || "--",
          },
        ]}
      />
    );
  };

  return (
    !!props.rack && (
      <Tabs
        activeKey={currentTab}
        onTabClick={key => setCurrentTab(key)}
        className="component-operate-detail"
        items={tabItems}
      />
    )
  );
}

export default RackDetail;
