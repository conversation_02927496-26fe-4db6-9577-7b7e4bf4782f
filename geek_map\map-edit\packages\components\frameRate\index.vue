<template>
  <div class="frameRate">{{ $t("lang.rms.fed.frameRate") }}: {{ frameRateVal }}</div>
</template>

<script setup lang="ts">
import { ref, Ref } from "vue";
import { useEditMap } from "@packages/hook/useEdit";
const frameRateVal: Ref<string> = ref("-");
let frameRateAdd: number = 0;
const editMapRef = useEditMap();
const timer = setInterval(() => {
  if (editMapRef.value) {
    clearInterval(timer);
    const app = editMapRef.value?.app;
    let g_Time = 0;
    app.ticker.add(() => {
      var timeNow = new Date().getTime();
      var timeDiff = timeNow - g_Time;
      g_Time = timeNow;
      frameRateAdd = 1000 / timeDiff;
    });
  }
}, 500);

setInterval(() => {
  frameRateVal.value = String(frameRateAdd.toFixed(1));
}, 500);
</script>

<style scoped lang="scss">
.frameRate {
  position: absolute;
  left: 10px;
  top: 10px;
  color: #67c23a;
  font-size: 12px;
  display: inline-block;
  z-index: 2;
  background: #ffffff73;
  padding: 5px;
  border-radius: 5px;
}
</style>
