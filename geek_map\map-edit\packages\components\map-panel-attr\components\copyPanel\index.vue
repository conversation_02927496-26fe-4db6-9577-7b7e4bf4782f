<template>
  <div class="attrPanel">
    <div class="title">批量复制</div>
    <FromBase ref="fromRef" :formData="formData" :formItem="formItem" labelWidth="120px" />
    <el-row :gutter="20" class="btns">
      <el-col class="btnBox" :offset="6" :span="6">
        <el-button class="btn" @click="cancel">取消</el-button>
      </el-col>
      <el-col class="btnBox" :span="6">
        <el-button class="btn" type="primary" @click="sure">确定</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick } from "vue";
import { FromBase } from "@packages/components/map-panel-attr/components/base/index";
import { defOperationFn } from "@packages/hook/useEvent";
import { useEditMap } from "@packages/hook/useEdit";

const editMap = useEditMap();
const formData: { [key: string]: any } = ref({ x: 0, y: 0 });
const fromRef = ref();
const formItem = computed(() => {
  return [
    // x
    {
      prop: "x",
      label: "目标x轴坐标",
      component: "elInputNumber",
      min: -999999,
      max: 999999,
    },
    // y
    {
      prop: "y",
      label: "目标y轴坐标",
      component: "elInputNumber",
      min: -999999,
      max: 999999,
    },
  ];
});

// 取消复制
function cancel() {
  defOperationFn(<any>editMap.value, {
    isDefMode: true,
    isClearSelect: true,
  });
}

function sure() {
  const localData = fromRef.value.getFormData();
  editMap.value?.copy(localData.x || 0, localData.y || 0);
  nextTick(() => {
    cancel();
  });
}
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 0px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  width: 300px;

  .title {
    font-size: 16px;
    margin: 0px 0 20px;
    font-weight: 900;
  }

  .formBase {
    padding-right: 15px;
  }

  .btns {
    margin: 0;
    height: 70px;
    line-height: 50px;

  .btnBox {
    text-align: center;
  }

    .btn {
      text-align: center;
    }
  }
}
</style>
