/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerImages implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  render(background: any): void {
    const _this = this;
    const container = _this.container;

    const data = background?.data || [];
    for (let i = 0, len = data.length; i < len; i++) {
      const sprite = _this.drawSprite(data[i]);
      container.addChild(sprite);
    }

    const leftBottomPoint = background.leftBottomPoint;
    container.position.set(leftBottomPoint.x, -leftBottomPoint.y);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  repaint(): void {
    const elements = this.container.children;
    elements.forEach((ele: any) => {
      ele.destroy(true);
    });
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
  }

  init(): void {
    const utils = this.mapCore.utils;

    let container = new PIXI.Container();
    container.name = "background";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("background");
    this.container = container;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  getContainer() {
    return this.container;
  }

  private drawSprite(bgItem: any) {
    const { resource, resolution, width, height, location } = bgItem;

    const texture = PIXI.Texture.from(resource);
    const sprite = new PIXI.Sprite(texture);
    sprite.width = width * resolution;
    sprite.height = height * resolution;
    sprite.position.set(location.x * resolution, -location.y * resolution - height * resolution);

    return sprite;
  }
}
export default LayerImages;
