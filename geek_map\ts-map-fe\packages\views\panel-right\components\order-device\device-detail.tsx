/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import OrderGrid from "../common/order-grid";

type PropsOrderData = {
  deviceData: deviceData;
};
function ChargerCommon(props: PropsOrderData) {
  const { t } = useTranslation();
  const [data, setData] = useState(null);
  const [state, setState] = useState("");
  const [connectState, setConnectState] = useState("");

  // 显隐
  useEffect(() => {
    const deviceData = props.deviceData;
    if (!deviceData) {
      setData(null);
      setState("");
      setConnectState("");
      return;
    }

    setData(deviceData);
    switch (deviceData.state) {
      case 0:
        setState(t("lang.rms.fed.normal"));
        break;
      case 1:
        setState(t("lang.rms.fed.unusual"));
        break;
      case 2:
        setState(t("lang.rms.fed.configException"));
      default:
        setState("--");
        break;
    }

    switch (deviceData.connectStatus) {
      case 0:
        setConnectState(t("lang.rms.fed.breakOff"));
        break;
      case 1:
        setConnectState(t("lang.rms.fed.connect"));
        break;
      default:
        setConnectState("--");
        break;
    }

    return () => {
      setData(null);
    };
  }, [props.deviceData]);

  return (
    data && (
      <OrderGrid
        items={[
          {
            label: t("lang.rms.fed.deviceID"),
            value: data?.deviceInfoId || "--",
          },
          {
            label: t("lang.rms.fed.deviceName"),
            value: data?.deviceInfoName || "--",
          },
          {
            label: t("lang.rms.fed.workState"),
            value: state,
          },
          {
            label: t("lang.rms.fed.connectState"),
            value: connectState,
          },
          {
            label: t("lang.rms.fed.coordinate"),
            value: data?.location || "--",
          },
        ]}
      />
    )
  );
}

export default ChargerCommon;
