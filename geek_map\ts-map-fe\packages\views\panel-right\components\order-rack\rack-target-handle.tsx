/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseCircleFilled } from "@ant-design/icons";
import { Button, Input } from "antd";
import { getMap2D } from "../../../../singleton";

const { Search } = Input;
type PropsOrderData = {
  rackCode: code;
  operation: "update" | "move";
  currentSelect: any;
  targetSelect: code;
  onClear: () => void;
  onCancel: () => void;
};
function OrderRackTargetHandle(props: PropsOrderData) {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [actionText, setActionText] = useState("");
  const [instruction, setInstruction] = useState<string>("");

  const [rackCode, setRackCode] = useState<string>("");

  // 监听当前 target rackCode
  useEffect(() => {
    if (!visible) return;

    const code = props.rackCode;
    if (code) setRackCode(code.toString());
    else setRackCode("");
  }, [props.rackCode]);

  // 监听当前操作类型
  useEffect(() => {
    const operation = props.operation;
    const isSelectBox = props.currentSelect?.type === "box";

    if (["update", "move"].includes(operation) && isSelectBox) {
      switch (operation) {
        case "update":
          setActionText(t("lang.rms.fed.update"));
          setInstruction("UPDATE_BOX_LOCATION");
          break;
        case "move":
          setActionText(t("lang.rms.fed.move"));
          setInstruction("MOVE_BOX");
          break;
      }
      setVisible(true);
    } else {
      setActionText("");
      setInstruction("");
      setVisible(false);
    }
  }, [props.operation, props.currentSelect]);

  const onSubmit = () => {
    if (!instruction) return;
    const boxCode = props.currentSelect?.boxCode;
    const latticeCode = props.targetSelect;
    if (!boxCode || !latticeCode) return;

    const map2D = getMap2D();
    let msgType: MWorker.reqMsgType = "BoxInstructionRequestMsg";
    map2D.mapWorker
      .reqSocket(msgType, {
        instruction,
        latticeCode,
        boxCode,
      })
      .then(res => {
        if (!["BoxInstructionResponseMsg"].includes(res.msgType)) {
          return;
        }
        _$utils.wsCmdResponse(res?.body || {});
        props.onCancel();
      });
  };

  // rack搜索
  const rackSearch = (value: string) => {
    if (!value) return;

    const map2D = getMap2D();
    map2D.mapRender.trigger("click", { rack: [value] });
    map2D.mapRender.setEleCenter({ layer: "rack", code: value });
  };

  return (
    visible && (
      <div className="map2d-target-rack-box">
        <h6 style={{ padding: "12px 0 5px", fontSize: "14px", fontWeight: 700 }}>
          {t("lang.rms.fed.currentOperation")}: {actionText}
        </h6>

        <div className="search-box">
          <p style={{ fontSize: "14px", paddingBottom: 6 }}>
            {t("lang.rms.fed.clickAndQueryTargetRack")}：
          </p>
          <Search
            value={rackCode}
            placeholder={t("lang.rms.fed.rackCode")}
            enterButton
            onSearch={rackSearch}
            allowClear={{
              clearIcon: <CloseCircleFilled onClick={() => props.onClear()} />,
            }}
            onChange={e => setRackCode(e.target.value)}
          />
        </div>

        <div style={{ paddingTop: 6, display: "flex", justifyContent: "flex-end" }}>
          <Button type="primary" disabled={false} size="small" onClick={onSubmit}>
            {t("lang.rms.fed.confirm")}
          </Button>
          <Button
            disabled={false}
            size="small"
            onClick={() => props.onCancel()}
            style={{ marginLeft: 8 }}
          >
            {t("lang.rms.fed.cancel")}
          </Button>
        </div>
      </div>
    )
  );
}

export default OrderRackTargetHandle;
