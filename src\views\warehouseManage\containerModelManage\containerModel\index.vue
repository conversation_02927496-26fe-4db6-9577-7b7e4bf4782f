<template>
  <div>
    <SearchWrap>
      <SearchForm @onsubmit="onSubmit" />
    </SearchWrap>
    <div style="text-align: right; margin-bottom: 10px">
      <CreateDialog
        :open="modelOpen"
        :close="modelClose"
        :button-text="$t('lang.rms.web.container.containerModel')"
        :title-text="creatDialogTitle"
        width-num="70%"
        @changeOpen="changeOpen"
        @createconfirm="createConfirm"
        @createcancel="createCancel"
      >
        <model-any :view-disabled="viewDisabled" :model-close="modelClose" />
      </CreateDialog>

      <!-- <CreateDialog
        :open="tolayoutOpen"
        :close="tolayoutClose"
        :title-text="tolayoutShowTitle"
        :show-title="false"
        @createconfirm="tolayoutConfirm"
      >
        <div style="padding-top: 10px">
          <span>
            是否继续进行储位布局编辑，储位布局编辑可以编辑货架/托盘上的几面信息以及货位规格，点击确定会继续进行储位布局编辑，取消将会完成容器编辑
          </span>
        </div>
      </CreateDialog> -->

      <CreateDialog
        :open="layoutOpen"
        :close="layoutClose"
        :title-text="$t('lang.rms.fed.layout')"
        :show-title="false"
        width-num="55%"
        @createconfirm="layoutConfirm"
      >
        <div style="padding-top: 10px">
          <model-layout @planeAllArrData="planeAllArrDataFun" />
        </div>
      </CreateDialog>
    </div>
    <el-table :loading="loading" :data="recordList" style="width: 100%">
      <el-table-column :label="$t('lang.rms.web.container.containerModelId')" prop="id" />
      <el-table-column :label="$t('lang.rms.web.container.containerModelAlias')" prop="modelName" />
      <el-table-column :label="$t('lang.rms.web.container.containerType')" prop="modelCategory" />
      <!-- 货架类别 -->
      <el-table-column :label="$t('lang.rms.fed.classCode')" prop="modelType" />
      <el-table-column :label="$t('lang.rms.web.container.containerPhysicalProperties')" prop="lwh" />
      <!-- <el-table-column label="货位详情" prop="protocolModelEnt" /> -->
      <el-table-column :label="$t('lang.rms.web.container.supportMove')" prop="isMove" />
      <el-table-column :label="$t('lang.rms.web.container.fetchDirs')" prop="fetchDirsStr">
        <template slot-scope="scope">
          <span>
            {{ scope.row.fetchDirsStr ? scope.row.fetchDirsStr.toString() : "" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('lang.rms.containerManage.sendModelId.msg')" prop="sendModelId" />
      <el-table-column :label="$t('lang.rms.fed.operation')" width="180" align="center">
        <template slot-scope="scope">
          <el-button
            v-if="ableViewEditType.includes(scope.row.modelCategory)"
            type="text"
            size="small"
            @click="editListItemInfo(scope.row, 'viewDisabled')"
          >
            {{ $t("lang.rms.fed.buttonView") }}
          </el-button>

          <el-button
            v-if="ableViewEditType.includes(scope.row.modelCategory)"
            type="text"
            size="small"
            @click="editListItemInfo(scope.row)"
          >
            {{ $t("lang.rms.fed.edit") }}
          </el-button>
          <el-button type="text" size="small" @click="deleteListItemInfo(scope.row)">
            {{ $t("lang.rms.fed.delete") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="text-align: right; margin-top: 30px">
      <el-pagination
        background
        layout="total,prev, pager, next, sizes, jumper"
        :page-sizes="[10, 20, 30, 40, 50]"
        :total="paginationParams.total"
        :page-size="paginationParams.pageSize"
        :current-page="paginationParams.currentPage"
        @current-change="paginationChange"
        @size-change="paginationPageChange"
      />
    </div>
  </div>
</template>

<script>
/** 真的挺恶心的页面ε=(´ο｀*)))唉！ */
import SearchWrap from "./Components/SearchWrap";
import SearchForm from "./Components/SearchForm";
import CreateDialog from "./Components/CreateDialog";

import modelAny from "./subPages/modelDialog/index.vue";
import modelLayout from "./subPages/modelLayout/index.vue";

import {
  getContainerModelData,
  addModl,
  saveContainerLayout,
  findLayoutByModelId,
  deleteContainerModel,
} from "../api/index";
import { mapGetters, mapMutations, mapActions } from "vuex";

const ableViewEditType = ["SHELF", "SHELF_HOLDER"];
export default {
  name: "",
  components: {
    SearchWrap,
    SearchForm,
    CreateDialog,
    modelAny,
    modelLayout,
  },
  data() {
    return {
      recordList: [],
      loading: false,
      paginationParams: { pageSize: 10, currentPage: 1, total: 0 },
      creatDialogTitle: "lang.rms.web.container.addContainerModel",
      layoutShowTitle: "",
      tolayoutShowTitle: "是否进行储位布局编辑",

      modelOpen: 0, // 新增弹窗开关
      modelClose: 0,

      layoutOpen: 0, // 布局弹窗开关
      layoutClose: 0,

      tolayoutOpen: 0, // 是否布局弹窗开关
      tolayoutClose: 0,

      searchData: {},

      saveModelId: "",
      saveModelIdData: "",

      layoutSubData: {},
      viewDisabled: false,

      ableViewEditType,
    };
  },
  computed: {
    ...mapGetters("containerModal", ["mapId"]),
  },
  watch: {},
  mounted() {
    // this.layoutOpen++
    // console.log(this.$store.state.containerModal.addHJModalData, "vuex");
  },
  methods: {
    ...mapMutations("containerModal", [
      "setEmptySwich",
      "setActiveIdData",
      "setEditData",
      "setHJModalData",
      "setLayoutOpen",
      "setTabModelActive",
    ]),
    ...mapActions("containerModal", ["fetchMaxModelId", "fetchShelfCategory", "findDistinctSizeType"]),
    planeAllArrDataFun(data) {
      // eslint-disable-next-line
      const _this = this;
      const subData = {
        layoutList: data.map(e => {
          return {
            id: e.id,
            modelId: _this.saveModelId,
            orientation: e.orientation,
            occupyEdge: e.occupyEdge / 1,
            depth: e.depth / 1,
            orientationCount: e.orientationCount / 1,
            containerFlag: 0,
            layout: e.row.map((inE, inIndex) => {
              return {
                layer: e.row.length - inIndex,
                layerHeight: inE.height,
                layerColumns: new Array(inE.cel / 1)
                  .fill(0)
                  .map((columnE, colunmIndex) => ({ columnNum: colunmIndex + 1 })),
              };
            }),
          };
        }),
      };

      this.layoutSubData = subData;
    },
    // 查看/编辑
    editListItemInfo(robotData, val) {
      this.fetchMaxModelId();
      this.fetchShelfCategory();
      this.findDistinctSizeType();
      this.viewDisabled = !!val;
      this.creatDialogTitle = val
        ? "lang.rms.web.container.viewContainerModel"
        : "lang.rms.web.container.editContainerModel";
      // console.log(robotData, '编辑数据')
      let newData = JSON.parse(JSON.stringify(robotData));
      newData.move = newData.move === null ? "1" : newData.move;
      newData.sizeTypes = newData.sizeTypes && newData.sizeTypes.length ? newData.sizeTypes.split(",") : [];
      this.setEditData(newData);
      this.setTabModelActive(newData.modelCategory);
      // this.setHJModalData(robotData)
      this.modelOpen++;
    },
    // 删除
    deleteListItemInfo(row) {
      this.$confirm("是否确认删除?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteContainerModel({ id: row.id }).then(res => {
            if (res.code === 0) {
              this.$message({
                message: "删除成功！",
                type: "success",
              });
              this.getTableData();
            }
          });
        })
        .catch(() => null);
    },
    paginationChange(currentPage) {
      this.searchData.currentPage = currentPage;
      this.paginationParams.currentPage = currentPage;
      this.getTableData();
    },
    paginationPageChange(pageSize) {
      this.paginationParams.currentPage = 1;
      this.searchData.pageSize = pageSize;
      this.paginationParams.pageSize = pageSize;
      this.getTableData();
    },
    // 增加/修改 容器模型提交
    createConfirm() {
      if (this.viewDisabled) {
        this.createCancel();
      } else {
        this.viewDisabled = false;
        // 货架模型
        if (this.$store.state.containerModal.tabModelActive === "SHELF") {
          if (this.$store.state.containerModal.hJModalWarn) {
            // 请添加
            this.$message({
              message: this.$store.state.containerModal.hJModalWarn,
              type: "warning",
            });
          } else if (
            this.$store.state.containerModal.addHJModalData.sendModelId &&
            !/^[1-9]\d*$/.test(this.$store.state.containerModal.addHJModalData.sendModelId)
          ) {
            this.$message.error(
              this.$t("lang.rms.api.result.warehouse.businessModelID") + this.$t("lang.rms.fed.pleaseEnterAnNumber"),
            );
          } else {
            // 异步函数
            let postData = JSON.parse(JSON.stringify(this.$store.state.containerModal.addHJModalData));
            if (postData.sizeTypes && postData.sizeTypes.length) {
              const reg = /^(?!\d)[a-zA-Z0-9]*$/;
              let sizeTypeParamTip = null;
              postData.sizeTypes.forEach(val => {
                if (reg.test(val) === false) {
                  sizeTypeParamTip = this.$t("lang.venus.web.check.canNotStartWithNum");
                }
              });

              if (sizeTypeParamTip) {
                this.$message.error(`${this.$t("lang.rms.api.result.warehouse.supportedSizeType")}${sizeTypeParamTip}`);
                return;
              }
              postData.sizeTypes = postData.sizeTypes.join(",");
            } else {
              postData.sizeTypes = ""
            }

            this.postHolderData(postData);
          }
        } else if (this.$store.state.containerModal.tabModelActive === "SHELF_HOLDER") {
          const groundData = this.$store.state.containerModal.groundData;
          // console.log(11111111, groundData);

          if (!groundData.modelName) {
            this.$message.error(`${this.$t("lang.rms.fed.pleaseEnterModelName")}!`);
            return;
          }
          if (!groundData.length || !groundData.width) {
            this.$message.error(this.$t("lang.rms.fed.trueSize"));
            return;
          }
          if (groundData.sizeTypes && groundData.sizeTypes.length) {
            const reg = /^(?!\d)[a-zA-Z0-9]*$/;
            let sizeTypeParamTip = null;
            groundData.sizeTypes.forEach(item => {
              if (!reg.test(item)) {
                sizeTypeParamTip = this.$t("lang.venus.web.check.canNotStartWithNum");
              }
            });

            if (sizeTypeParamTip) {
              this.$message.error(`${this.$t("lang.rms.api.result.warehouse.supportedSizeType")}${sizeTypeParamTip}`);
              return;
            }
            groundData.sizeTypes = groundData.sizeTypes.join(",");
          } else {
            groundData.sizeTypes = ""
          }
          this.postHolderData(groundData);
        }
      }
    },

    // 新增支架模型
    postHolderData(data) {
      $req.post("/athena/shelfModel/save", data).then(res => {
        if (res.code === 0) {
          this.$message({
            message: data.id ? "编辑成功" : "添加成功！",
            type: "success",
          });
          this.saveModelId = "";

          if (!data.id) {
            this.searchData.currentPage = 1;
            this.paginationParams.currentPage = 1;
          }
          this.getTableData();

          this.modelClose++;
          this.tolayoutOpen++;
        }
      });
    },
    // 新增容器模型
    postSaveData(data) {
      addModl(data).then(res => {
        if (res.code === 0) {
          this.$message({
            message: data.id ? "编辑成功" : "添加成功！",
            type: "success",
          });
          this.saveModelId = res.data;

          if (!data.id) {
            this.searchData.currentPage = 1;
            this.paginationParams.currentPage = 1;
          }
          this.getTableData();

          this.modelClose++;
          this.tolayoutOpen++;
        }
        console.log(res, "保存成功");

        // 开关弹窗
      });
    },

    async tolayoutConfirm() {
      const result = await findLayoutByModelId({ modelId: this.saveModelId });

      // this.setActiveIdData = result.data
      this.setActiveIdData(result.data);

      this.tolayoutClose++;
      this.layoutOpen++;
      this.setLayoutOpen();
    },
    async layoutConfirm() {
      await saveContainerLayout(this.layoutSubData);

      this.$message({
        message: "更改布局成功",
        type: "success",
      });
      this.setEmptySwich();
      this.layoutClose++;
    },
    createCancel() {
      this.viewDisabled = false;
      this.modelClose++;
      this.setEmptySwich();
    },
    changeOpen() {
      this.fetchMaxModelId();
      this.fetchShelfCategory();
      this.findDistinctSizeType();
      this.setEditData({});
      this.setTabModelActive("SHELF");
      this.creatDialogTitle = "lang.rms.web.container.addContainerModel";
    },
    onSubmit(data) {
      this.searchData = Object.assign({}, data);
      this.searchData.currentPage = 1;
      this.paginationParams.currentPage = 1;
      this.searchData.pageSize = this.paginationParams.pageSize;

      this.getTableData();
    },
    async getTableData() {
      const result = await getContainerModelData(this.searchData);

      this.paginationParams.total = result.data.recordCount;
      this.recordList = result.data.recordList.map(e => {
        e.lwh = `${e.length ?? 0}、${e.width ?? 0}、${e.height ?? 0}`;
        e.isMove = e.move === 0 ? "不可移动" : e.move === 1 ? "可移动" : "";
        return e;
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
