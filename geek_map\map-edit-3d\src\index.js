/**
 *  options:{
 *     dom: [canvas插入的dom],
 *     plugin: [注册plugin],
 *     config:{
 *       modelMap: {[key:模型类型 ||  `${模型类别}-${细分type}`]:[value:加载地址]}, // 预加载模型并缓存；
 *       constants:{theme:{}} // 覆盖全局常量
 *       extendsObject3D(getModelObject){}  //getModelObject 获取模型方法； // 需要处理加载得模型，如果不需要处理，则不传；
 *     }
 *  }
 */

/**
 *  initMap数据结构
 *  initMap:{
 *     [floorId]:{
 *          floor:{} // 来自于/athena/map/draw/getMap（二维码地图可能缺少，缺少自动生成）
 *          cells: [] // /athena/map/version/getFloorDetail 的 data.mapFloorDto
 *     }
 *  }
 */
/**
 *  initModel数据结构：
 *   /**
 * 注册模型
 * @param {*} config
 * [modelName唯一key]:{
 *   path: 请求的资源连接，
 *   insetShape: 内置图形 // station2 / station6 不支持setAttributes方法；
 *   isAdsorb: 是否吸附单元格；
 *   setAttributes(model){}, 加工模型
 *   suceess(){},
 *   fail(){}，
 * }
 * 组合模型后期可以修改为middleWare组织形式；
 * 暂时不支持组合模型，需要组合模型，增加insetShape得方式
 *
 */

/**
 *  plugin开发注意：
 *  1. 需要继承PluginBase这个基础类；
 *  2. 必须定义唯一的pluginname,建议命名 `${行为、功能}Plugin`
 *  3. 提供接口：registerPlugin/removePlugins/disabledPlugin/enablePlugin
 */

/**
 * 事件监听使用的是mitt : this.Emitter.xxxx;
 * github: https://github.com/developit/mitt
 */

// three
import * as THREE from "three";
import { OrbitControls, MapControls } from "three/examples/jsm/controls/OrbitControls";
import { TransformControls } from "three/examples/jsm/controls/TransformControls.js";

// store
import mapDataInstance from "./store/data";
// element
import Floors from "./elements/floor";
import Model from "./elements/model";
// plugin
import Plugin from "./plugin";
// util
import { v4 as uuidv4 } from "uuid";
import TWEEN from "@tweenjs/tween.js";

// config
import globalConfig from "./config";

// command
import Command from "./core/command";
import Ticker from "./core/ticker";
import Search from "./core/search";
import Camera from "./core/camera";
import LoadObj from "./core/loadObj";

// plugin
import behaviorBase from "./core/abstractPlugin";
import pointerLock from "./plugins/common/pointerLock";
import monitorPlugin from "./plugins/monitor/monitorPlugin";
import editPlugin from "./plugins/edit/editPlugin";

// load package
import install from "../package/install";

import { throttle } from "./utils/utils";

class Map3D extends Plugin {
  constructor(options) {
    super(options);
    this.config = options.config;
    this.$dom = options.dom;
    this.baseUrl = options.baseUrl;
    // this.floorGeo = [];
    // threejs object
    this.scene = null;
    this.renderer = null;
    this.OrbitControls = null;
    // model object
    this.modelInstances = {};

    // init threejs
    this._initConstants(options.config.constants);
    this._initThreeScene();

    // 功能
    this.command = new Command({ Map3d: this });
    this.ticker = new Ticker(this);
    this.camera = new Camera(this);
    this.search = new Search(this);
    this.loadObj = new LoadObj(this);

    // 记录当前floor对象
    this.floor = null;

    // this._createHelper();
    this._initeControls();
    this._render();
  }

  // 渲染地图
  initMap(data) {
    // 更新楼层数据；
    mapDataInstance.mapData = data;
    // 默认楼层ID
    const curFloorId = Object.keys(data)[0];
    mapDataInstance.curFloorId = curFloorId;
    // 渲染地图
    this._doRenderMap(data[curFloorId]);
    this.Emitter.emit("after:initMap");
  }
  // 渲染模型/根据模型分类
  // 现在模型对应的数据，不包含尺寸，所以适配单元格的尺寸，后期有数据可以再改；
  // category表明一个大类，比如charger/station ,其中station又分为单点工位、联合工位，后台通过type区分，
  initModel(modelArr, config) {
    if (!modelArr || !Array.isArray(modelArr) || !modelArr.length) return;
    const { category, useModelName } = config;
    let newModelArr = modelArr.map(i => ({
      ...i,
      uuid: uuidv4(),
      category,
      useModelName: typeof useModelName === "function" ? useModelName(i) : useModelName,
    }));
    mapDataInstance.modelData = { [category]: newModelArr };
    this._renderModel(newModelArr);
  }

  getModelData(category) {
    return mapDataInstance.getModelData(category);
  }

  // 切换楼层
  showFloorId(id) {
    this.Emitter.emit("before:changeFloorId");
    const is = mapDataInstance.checkVerifyFloorId(id);
    if (!is) return false;
    // 隐藏当前楼层；
    this.hiddenFloorId(mapDataInstance.curFloorId);
    mapDataInstance.curFloorId = id;
    // 更新对应的地图信息；
    const mapData = mapDataInstance.mapData[id];
    this._doRenderMap(mapData);
    // 更新对应的模型数据；
    const modelData = Object.values(mapDataInstance.modelData)
      .flat()
      .filter(i => String(i.floorId) === String(id));
    this._renderModel(modelData);
    this.Emitter.emit("after:changeFloorId", true);
    return true;
  }

  // 隐藏楼层
  hiddenFloorId() {
    const floorGeo = this.scene.getObjectByName("floorGeo");
    // 删除当前地图
    this.scene.remove(floorGeo);
    // 删除当前模型
    for (let key in this.modelInstances) {
      this.scene.remove(this.modelInstances[key].model);
      delete this.modelInstances[key];
    }
    this.modelInstances = {};
  }

  // 增加楼层数据
  appendFloorMapData(data) {
    mapDataInstance.mapData = data;
  }

  // 增加模型数据
  appendModelData(modelArr, config) {
    if (!modelArr || !Array.isArray(modelArr) || !modelArr.length) return;
    const { category, useModelName } = config;
    let newModelArr = modelArr.map(i => ({
      ...i,
      uuid: uuidv4(),
      category,
      useModelName: typeof useModelName === "function" ? useModelName(i) : useModelName,
    }));
    mapDataInstance.addModelData(category, newModelArr);
    this._renderModel(newModelArr);
  }

  addModelAction(modelCategory, formatter) {
    const addPluginInstance = this.getPlugin("addPlugin");
    const selectPluginInstance = this.getPlugin("selectPlugin");
    addPluginInstance.addCategory = modelCategory;
    addPluginInstance.formatter = formatter;
    this.enablePlugin(addPluginInstance.PluginName);
    this.Emitter.on("after:add", cell => {
      this.disabledPlugin(addPluginInstance.PluginName);
      selectPluginInstance.triggerSelect(cell);
    });
  }

  updateModel(nv) {
    const { uuid } = nv;
    if (!this.modelInstances[uuid]) throw new Error("当前数据不存在，请使用addModel");
    const oldValue = this.modelInstances[uuid]._data;
    this.command.exec("update", { oldValue: oldValue, newValue: nv });
    this.Emitter.emit("after:updateModel");
  }

  deleteModel(nv) {
    const { uuid } = nv;
    if (!this.modelInstances[uuid]) throw new Error("当前数据不存在！");
    this.command.exec("del", { value: nv });
    const selectPlugin = this.getPlugin("selectPlugin");
    selectPlugin.triggerCancelSelect();
    this.Emitter.emit("after:deleteModel");
  }

  deleteBatchModels(values) {
    if (!values || !values.length) throw new Error("[deleteModels]>>> 缺少传参！");
    this.command.exec("del", { value: values });
    const selectPlugin = this.getPlugin("selectPlugin");
    selectPlugin.triggerCancelSelect();
    this.Emitter.emit("after:deleteModel");
  }

  desotry() {
    const plugins = this.getPlugin();
    this.removePlugins(plugins.map(i => i.PluginName));
    this.Emitter.all.clear();
    this.Emitter = null;
    this.command.destory();
    this.command = null;
    this.ticker && this.ticker.destroy();
    this.ticker = null;
    this.loadObj && this.loadObj.destory();
    this.loadObj = null;
    this.search && this.search.destory();
    this.search = null;

    mapDataInstance && mapDataInstance.destory();
  }

  resetCamera() {
    this.floor.floorInfo && this.__resetView();
  }

  _doRenderMap(data) {
    this.floor = new Floors(data);
    this.scene.add(this.floor.floorGeo);
    // 新建地图快速查找索引；
    this.search.add(this.floor.floorGeo.children[1], { data: data.cells });
    // 重置摄像头
    this.resetCamera();
  }

  _renderModel(modelArr, config = {}) {
    let length = modelArr.length;
    const { done } = config;
    let count = 0;
    for (let i = 0; i < length; i++) {
      const data = modelArr[i];
      const { category, useModelName } = data;
      const isAdsorb = (this.config.modelMap[useModelName] || {}).isAdsorb || false;
      this.loadObj.getLazy(useModelName, group => {
        const model = new Model({
          object3d: group.clone(),
          data,
          category,
          isAdsorb,
        });
        this.scene.add(model.model);
        this.modelInstances[data.uuid] = model;
        count++;
        if (count === length) {
          count = 0;
          this.Emitter.emit(`after:renderModel`, { category, data: modelArr });
          done && done();
        }
      });
    }
  }

  __resetView() {
    const { right: r, left: l, bottom: b, top: t } = this.floor.floorInfo;
    const xc = (r + l) / 2;
    const yc = -(b + t) / 2;
    this.camera.flyTo([xc, 0, yc], 90);
    // 重置太阳光；
    const directionLight1 = this.scene.getObjectByName("directionLight1");
    directionLight1.position.set(xc, 100, yc);
  }

  _initThreeScene() {
    // create scene
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(globalConfig.THEME.SCENE_COLOR);
    //  create light
    // 环境光
    const ambient = new THREE.AmbientLight(0x404040);
    this.scene.add(ambient);
    // 设置平行光
    const directionLight1 = new THREE.DirectionalLight(0x606060, 0.65);
    directionLight1.name = "directionLight1";
    directionLight1.position.set(50, 50, -50);
    this.scene.add(directionLight1);
    // // 半球光
    let hemiLight = new THREE.HemisphereLight(0xffffbb, 0x606060, 0.8);
    hemiLight.position.set(50, 50, -50);
    this.scene.add(hemiLight);
    // create Renderer
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      logarithmicDepthBuffer: true,
      powerPreference: "high-performance",
      premultipliedAlpha: "false",
    });
    this.renderer.setSize(this.$dom.offsetWidth, this.$dom.offsetHeight);
    this.$dom.appendChild(this.renderer.domElement);
    const that = this;
    window.addEventListener(
      "resize",
      throttle(() => {
        that.camera.changeCameraAspt();
        that.renderer.setSize(that.$dom.offsetWidth, that.$dom.offsetHeight);
      }, 500),
    );
  }
  _initeControls() {
    // this.transformControl = { addEventListener() {}, detach() {} };
    const ControlClass = this.config.controlMode === "Orbit" ? OrbitControls : MapControls;
    const control = new ControlClass(this.camera.get(), this.renderer.domElement);
    control.maxPolarAngle = Math.PI * 0.5;
    control.maxDistance = 500;
    control.minDistance = 1;

    this.OrbitControls = control;
    this.transformControl = new TransformControls(this.camera.get(), this.renderer.domElement);
    this.transformControl.showY = false;
    this.transformControl.addEventListener(
      "dragging-changed",
      event => (control.enabled = !event.value),
    );
    this.scene.add(this.transformControl);
  }
  _initConstants(constants) {
    if (!constants) return;
    const { theme: configTheme = null } = constants || {};
    if (configTheme) globalConfig.THEME = Object.assign(globalConfig.THEME, configTheme);
  }
  _createHelper() {
    // this.scene.add(new THREE.AxesHelper(200));
    // const help = new THREE.AxesHelper(100);
    // help.position.set(41.091, 48.44, 0);
    // this.scene.add(help);
    const grid = new THREE.GridHelper(600, 300, 0x808080, 0x808080);
    grid.position.y = -0.5;
    this.scene.add(grid);
    // this.scene.add(new THREE.CameraHelper(this.camera));
  }
  _render() {
    this.ticker.add(() => {
      TWEEN.update();
      this.renderer.render(this.scene, this.camera.get());
    });
  }
}

Map3D.install = install;

export default Map3D;
export const PluginBase = behaviorBase;
export const PointerLockPlugin = pointerLock;
export const MointorPlugin = monitorPlugin;
export const EditPlugin = editPlugin;

// modules.default = Map3D;
