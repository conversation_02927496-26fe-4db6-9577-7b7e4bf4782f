import intersects from "intersects";
import { UN_RENDER_CELL_TYPE_ARR } from "../constant/elements";
class mapData {
  constructor() {
    this._mapData = {};
    this._mapModelData = [];
    this._floorIds = []; // 记录当前支持的楼层；
    this.curFloorId = null;
  }
  //{[floorId]:{ [floor]: { //地图基础信息 }, [cells]:{ // 单元格渲染 } }}
  set mapData(value) {
    for (let key in value) {
      // 记录当前楼层信息
      const is = this._floorIds.includes(key);
      !is && (this._floorIds = this._floorIds.concat(key));
      // 初始化当前楼层ID
      if (this.curFloorId === null) this.curFloorId = key;
      this._mapData[key] = value[key];
    }
  }
  get mapData() {
    return this._mapData;
  }

  set modelData(value) {
    for (let key in value) {
      // 记录数据
      this._mapModelData[key] = value[key];
    }
  }

  get modelData() {
    return this._mapModelData;
  }

  destory() {
    this._mapData = {};
    this._mapModelData = {};
    this.curFloorId = null;
    this._floorIds = [];
  }

  addModelData(category, value) {
    const arr = this._mapModelData[category] || [];
    this._mapModelData[category] = arr.concat(value);
  }

  getModelData(category) {
    if (this._mapModelData[category]) return [].concat(this._mapModelData[category]);
    return null;
  }

  delModelData(category, value) {
    const arr = this._mapModelData[category] || [];
    const index = arr.find(i => i.uuid === value.uuid);
    arr.splice(index, 1);
  }

  updateModelData(category, value) {
    const arr = this._mapModelData[category];
    const index = arr.findIndex(i => i.uuid === value.uuid);
    this._mapModelData[category].splice(index, 1, value);
  }

  updateCellsData(value) {
    const cells = this.mapData[this.curFloorId].cells;
    const nv = [].concat(value);
    const cellCodes = nv.map(i => i.cellCode);
    for (let i = 0; i < cells.length; i++) {
      if (typeof cells[i] !== "object" || !cells[i].cellCode) continue;
      const index = cellCodes.findIndex(a => a === cells[i].cellCode);
      if (!~index) continue;
      Object.assign(cells[i], nv[index]);
    }
  }

  findCellByCellCode(cellCode) {
    const cells = this.mapData["1"].cells;
    let selectCells = null;
    for (let i = 0; i < cells.length; i++) {
      if (cells[i].cellCode === cellCode) {
        selectCells = cells[i];
        break;
      }
    }
    return selectCells;
  }
  findModelByCellCode(category, cellCode) {
    const data = this._mapModelData[category] || [];
    return [].concat(data.filter(i => i.cellCode === cellCode));
  }
  findModelByUuid(category, uuid) {
    const data = this._mapModelData[category] || [];
    return Object.assign({}, data.find(i => i.uuid === uuid) || {});
  }
  findSelectCell(point) {
    const { x, y } = point;
    const cells = this._mapData[this.curFloorId].cells;
    let selectCells = null;
    for (let i = 0; i < cells.length; i++) {
      const { startBounds, length, width, cellType } = cells[i];
      if (UN_RENDER_CELL_TYPE_ARR.includes(cellType)) continue;
      if (intersects.pointBox(x, -y, startBounds.x, startBounds.y, length, width)) {
        selectCells = cells[i];
        break;
      }
    }
    return selectCells;
  }
  checkVerifyFloorId(id) {
    return this._floorIds.includes(String(id));
  }
}

export default new mapData();
