<template>
  <div class="rightModel">
    <!-- 预览任务 -->
    <TaskView v-show="shelfModelVisible" ref="view" @createTask="createTask" @editTask="editTask" />
    <!-- 编辑任务 -->
    <TaskEdit
      v-show="editShelfModelVisible"
      ref="edit"
      :view-disabled="viewDisabled"
      @close="closeEditTaskView"
    />
  </div>
</template>

<script>
import { mapMutations } from "vuex";
import TaskView from "./view";
import TaskEdit from "./edit";

export default {
  props: {
    viewDisabled: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      shelfModelVisible: false,
      editShelfModelVisible: true,
    };
  },
  methods: {
    /**
     * 创建任务
     *
     * @param {} name 任务名称
     */
    createTask(name) {
      this.$refs.edit.createTask(name);
      this.showEditTask();
    },

    /**
     * 显示编辑任务界面
     */
    showEditTask() {
      this.shelfModelVisible = false;
      this.editShelfModelVisible = true;
    },

    /**
     * 关闭创建/新增任务界面
     * 自动刷新task列表
     */
    closeEditTaskView() {
      this.shelfModelVisible = true;
      this.editShelfModelVisible = false;
      this.$refs.view.getAllShelfModels();
      // this.$refs.edit.claerTask();
      // this.$refs.view.getAllTasks();
    },

    /**
     * 编辑任务
     */
    editTask(id) {
      this.$refs.edit.editTask(id);
      this.showEditTask();
    },
  },
  components: {
    TaskView,
    TaskEdit,
  },
};
</script>

<style scoped lang="less">
.rightModel {
  position: relative;
  width: 300px;
  height: 100%;
  border-left: 1px solid #eee;
  padding: 0 10px;
  overflow-y: auto;
}
</style>
