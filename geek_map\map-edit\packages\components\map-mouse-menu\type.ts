/**
 * 右键菜单可配置内容
 */
export interface MenuConfigureListItemType {
  /**
   * 菜单名称
   */
  label: string;
  /**
   * 菜单触发的事件名称
   */
  eventName?: string;
  /**
   * 次级菜单的展开方式
   * 如果没有设置则会使用全局设置
   */
  expandTrigger?: "click" | "hover";
  /**
   * 是否禁用, 可选
   * @param nodes 当前选中的元素
   * @param layerName 当前选中的元素所处的图层
   */
  disabled?(nodes: any[], layerName: string): boolean;
  /**
   * 是否显示, 可选
   * @param nodes 当前选中的元素
   * @param layerName 当前选中的元素所处的图层
   */
  condition?(nodes: any[], layerName: string): boolean;
  /**
   * 菜单被点击时的操作
   * 这里的内容被视为一个匿名事件, 可以配合eventName使其他地方也可调用
   */
  handle?(nodes: any[], layerName: string): void;
}

export interface MenuConfigureType {
  /**
   * 次级菜单的展开方式
   */
  expandTrigger?: "click" | "hover";
}
