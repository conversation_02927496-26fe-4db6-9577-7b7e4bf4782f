/** 监控页三级页签总汇 */
const monitorTabPermission: { [propName: string]: string } = {
  OrderGroupRobot: "auth.rms.monitor.page.robot", // 机器人
  OrderGroupShelf: "auth.rms.monitor.page.shelf", // 货架
  OrderGroupCell: "auth.rms.monitor.page.nodeManage", // 单元格
  OrderGroupCharger: "auth.rms.monitor.page.chargeManage", // 充电站
  OrderGroupInstruction: "auth.rms.monitor.page.businessInstructions", // 业务指令
  OrderGroupZone: "auth.rms.monitor.page.controlZoneManagement", // 控制分区
  OrderGroupWarehouse: "auth.rms.monitor.page.warehouseMaintainManagement", // 仓库管理
  OrderGroupDevice: "auth.rms.monitor.page.deviceManage", // 设备管理
  OrderGroupRack: "auth.rms.monitor.button.boxManage", // 货箱管理
  OrderGroupDeadlock: "auth.rms.monitor.page.deadlockResolution", // 消除死锁
};

/** 无权限模式下的btn对应code权限  */
const ConfigButtonCodeTranslations: { [propName: string]: string } = {
  // 右侧面板上面三个指令按钮权限如下
  MonitorSystemStop: "showSystemStop", // 显示系统急停按钮
  MonitorTaskRecovery: "showTaskRecovery", // 显示任务恢复按钮
  MonitorFireStop: "showFireStop", // 显示消防急停按钮
  // 右侧面板业务指令Tab按钮权限如下
  MonitorDormancyOneKey: "showRobotSleepButton", // 显示一键休眠按钮
  MonitorAwakenOneKey: "showRobotAwakenButton", // 显示一键唤醒按钮
  MonitorTogetherOneKey: "showCollection", // 显示一键集合按钮
  MonitorShutDownOneKey: "allRobotsShutdownButton", // 显示一键关机按钮
  MonitorScanOneKey: "showRobotScan", // 显示一键扫描
  MonitorReturnShelfOneKey: "allShelfReturnButton", // 显示一键归还货架按钮
  MonitorTurningSurfaceOneKey: "showShelfTurningRunningButton", // 显示一键转面按钮
  // 右侧面板机器人tab中的指令
  disableGoSomewhere: "disableGoSomewhere", // 隐藏机器人去某处按钮
  disableGoCharging: "disableGoCharging", // 隐藏机器人去充电按钮
  disableUpdateFloor: "disableUpdateFloor", // 隐藏机器人更新楼层按钮
  disableRecoverRobot: "disableRecoverRobot", // 隐藏机器人加入按钮
  disableGoRemoveRobot: "disableGoRemoveRobot", // 隐藏机器人移除按钮
  disableRobotRestart: "disableRobotRestart", // 隐藏机器人重启按钮
  disableRobotUnlock: "disableRobotRelieve", // 隐藏机器人解除按钮
  // 右侧面板货架tab中的指令
  disableChangeShelf: "disableChangeShelf", // 隐藏更新位置按钮
  disableMoveShelf: "disableMoveShelf", // 隐藏货架移动按钮
  disableChangeShelfAngle: "disableChangeShelfAngle", // 隐藏货架更新角度按钮
  disableReturnPlacementShelf: "disableReturnPlacementShelf", // 隐藏货架回库按钮
  // 右侧面板充电站tab中的指令
  disableChargeDisable: "disableChargeDisable", // 隐藏充电站禁用/启用按钮
};

/** 有权限模式下的btn对应code权限 */
const ButtonCodeTranslations: { [propName: string]: string } = {
  // 右侧面板上面三个指令按钮权限如下
  MonitorSystemStop: "auth.rms.monitor.button.systemStop", // 系统急停
  MonitorTaskRecovery: "auth.rms.monitor.button.taskRecovery", // 任务恢复
  MonitorFireStop: "auth.rms.monitor.button.fireStop", // 消防急停
  // 右侧面板业务指令Tab按钮权限如下
  MonitorShutDownOneKey: "auth.rms.monitor.button.shutDownOneKey", // 一键关机
  MonitorDormancyOneKey: "auth.rms.monitor.button.dormancyOneKey", // 一键休眠
  MonitorAwakenOneKey: "auth.rms.monitor.button.awakenOneKey", // 一键唤醒
  MonitorTogetherOneKey: "auth.rms.monitor.button.togetherOneKey", // 一键集合
  MonitorScanOneKey: "auth.rms.monitor.button.scanOneKey", // 一键扫描
  MonitorReturnShelfOneKey: "auth.rms.monitor.button.returnShelfOneKey", // 一键归还货架
  MonitorTurningSurfaceOneKey: "auth.rms.monitor.button.turningSurfaceOneKey", // 一键转面
  // 右侧面板机器人tab中的指令
  MonitorRobotGoSomeWhere: "auth.rms.monitor.button.robotGoSomeWhere", // 机器人去某处
  MonitorRobotGoCharging: "auth.rms.monitor.button.robotGoCharging", // 机器人去充电
  MonitorRobotUpdateFloor: "auth.rms.monitor.button.robotUpdateFloor", // 机器人更新楼层
  MonitorRobotJoin: "auth.rms.monitor.button.robotJoin", // 机器人加入
  MonitorRobotRemove: "auth.rms.monitor.button.robotRemove", // 机器人移除
  MonitorRobotRestart: "auth.rms.monitor.button.robotRestart", // 机器人重启
  MonitorRobotUnlock: "auth.rms.monitor.button.robotRelieve", // 机器人解除
  // 右侧面板货架tab中的指令
  MonitorShelfUpdate: "auth.rms.monitor.button.shelfUpdate", // 货架更新位置
  MonitorShelfMove: "auth.rms.monitor.button.shelfMove", // 货架移动
  MonitorShelfLocking: "auth.rms.monitor.button.shelfLock", // 货架锁定
  MonitorShelfRelieve: "auth.rms.monitor.button.shelfUnlock", // 货架解除
  MonitorShelfUpdateAngel: "auth.rms.monitor.button.shelfUpdateAngel", // 货架更新角度
  MonitorShelfReturnPlacement: "auth.rms.monitor.button.shelfReturnPlacement", // 货架回库
  // 右侧面板单元格tab中的指令
  MonitorCellLock: "auth.rms.monitor.button.mapLocking", // 单元格锁定
  MonitorCellStop: "auth.rms.monitor.button.mapSuspend", // 单元格暂停
  MonitorCellUnlock: "auth.rms.monitor.button.mapRelieve", // 单元格解除
  // 右侧面板充电站tab中的指令
  MonitorChargeDisable: "auth.rms.monitor.button.chargeDisable", // 充电站禁用/启用
  MonitorChargeRestart: "auth.rms.monitor.button.chargeRestart", // 充电站重启
};

function checkBtn(code: string, needCheck = false) {
  const permission = _$utils.getRMSPermission();
  if (permission == "test") return true; // iframe 测试

  if (permission) {
    const value = ButtonCodeTranslations[code];
    if (!value) return true;
    const btnAuth = _$utils.getRMSAuthBtnList();
    return btnAuth.indexOf(value) !== -1;
  } else {
    const RMSConfig = _$utils.getRMSConfig();
    if (RMSConfig === "test") return true;
    let key = "";
    switch (code) {
      case "MonitorRobotGoSomeWhere":
        key = ConfigButtonCodeTranslations["disableGoSomewhere"];
        return !RMSConfig[key];
      case "MonitorRobotGoCharging":
        key = ConfigButtonCodeTranslations["disableGoCharging"];
        return !RMSConfig[key];
      case "MonitorRobotUpdateFloor":
        key = ConfigButtonCodeTranslations["disableUpdateFloor"];
        return !RMSConfig[key];
      case "MonitorRobotJoin":
        key = ConfigButtonCodeTranslations["disableRecoverRobot"];
        return !RMSConfig[key];
      case "MonitorRobotRemove":
        key = ConfigButtonCodeTranslations["disableGoRemoveRobot"];
        return !RMSConfig[key];
      case "MonitorRobotRestart":
        key = ConfigButtonCodeTranslations["disableRobotRestart"];
        return !RMSConfig[key];
      case "MonitorRobotUnlock":
        key = ConfigButtonCodeTranslations["disableRobotUnlock"];
        return !RMSConfig[key];
      case "MonitorShelfUpdate":
        key = ConfigButtonCodeTranslations["disableChangeShelf"];
        return !RMSConfig[key];
      case "MonitorShelfMove":
        key = ConfigButtonCodeTranslations["disableMoveShelf"];
        return !RMSConfig[key];
      case "MonitorShelfUpdateAngel":
        key = ConfigButtonCodeTranslations["disableChangeShelfAngle"];
        return !RMSConfig[key];
      case "MonitorChargeDisable":
        key = ConfigButtonCodeTranslations["disableChargeDisable"];
        return !RMSConfig[key];
      case "MonitorShelfReturnPlacement":
        key = ConfigButtonCodeTranslations["disableReturnPlacementShelf"];
        return !RMSConfig[key];
      default:
        key = ConfigButtonCodeTranslations[code];
        if (key === undefined) return true;
        return RMSConfig[key];
    }
  }
}

function getPermissionTab() {
  const permission = _$utils.getRMSPermission();
  if (permission == "test") return Object.keys(monitorTabPermission); // iframe 测试
  if (permission) {
    // 有权限模式
    const menuAuth = _$utils.getRMSAuthTabList();
    let tabs = [];
    for (let key in monitorTabPermission) {
      const value = monitorTabPermission[key];
      if (menuAuth.indexOf(value) !== -1) {
        tabs.push(key);
      }
    }
    return tabs;
  } else {
    // 无权限模式
    return Object.keys(monitorTabPermission);
  }
}

export { checkBtn, getPermissionTab };
