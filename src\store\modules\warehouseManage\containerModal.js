export default {
  namespaced: true,
  state: {
    tabModelActive: "",
    layoutOpen: 0, // 布局弹窗开关

    addHJModalData: {}, // 新增加的模型
    groundData: {},
    hJModalWarn: "", // 警示语
    emptySwich: 0, // 清空  每次+1  执行一次清除
    activeIDData: "", // 当前选中id数据
    editData: {
      // 编辑模型数据
      modelCategory: "SHELF",
      modelName: "",
      categoryId: "",
      length: "",
      width: "500",
      height: "500",
      passLength: 0,
      passWidth: 0,
      passHeight: 0,
      move: "1",
      legLength: 0,
      legWidth: 0,
    }, // tab要修改的数据
    maxModelId: 0, // 下发模型
    shelfCategoryDict: [], // 容器类别
    sizeTypeDict: "", // sizetype字典
  },
  getters: {
    getEmptySwich(state) {
      return state.emptySwich;
    },
  },
  actions: {
    async fetchMaxModelId({ commit }) {
      const res = await $req.get("/athena/shelfModel/getMaxId");
      commit("setMaxModelId", res.data || 0);
    },
    async fetchShelfCategory({ commit }) {
      const res = await $req.post("/athena/shelfCategory/findAll");
      commit("setShelfCategory", res.data.map(i => ({ label: i.categoryName, value: i.id })) || 0);
    },
    async findDistinctSizeType({ commit }) {
      const res = await $req.get("/athena/robot/manage/findDistinctSizeType");
      commit("setSizeType", res.data || 0);
    },
  },
  mutations: {
    setTabModelActive(state, data) {
      state.tabModelActive = String(data);
    },
    setLayoutOpen(state) {
      console.log("打开");
      state.layoutOpen++;
    },
    setEditData(state, data) {
      state.editData = Object.assign({}, data);
    },
    setEmptySwich(state) {
      state.emptySwich++;
    },
    setHJModalData(state, shelfData) {
      state.addHJModalData = Object.assign({}, shelfData);
      if (!shelfData.modelName) {
        state.hJModalWarn = "请填写容器模型别称";
        return;
      }

      if (!shelfData.categoryId) {
        state.hJModalWarn = "请填写货架类别";
        return;
      }

      state.hJModalWarn = "";
    },
    setGroundData(state, groundData) {
      state.groundData = Object.assign({}, groundData);
    },
    setActiveIdData(state, data) {
      state.activeIDData = data;
    },
    setMaxModelId(state, modelId) {
      state.maxModelId = modelId;
    },
    setShelfCategory(state, shelfCategory) {
      state.shelfCategoryDict = shelfCategory;
    },
    setSizeType(state, sizeTypeList) {
      state.sizeTypeDict = sizeTypeList;
    },
  },
};
