/* ! <AUTHOR> at 2021/03 */
import { fabric } from "fabric";

export default {
  /**
   * 解析将图形解析成一个三层的图
   * 此函数会分别生成 地面, 墙, 纯背景
   * 此时地面/墙生成图的大小是一致的
   * @param mapData
   */
  getMapImageData(mapData) {
    const url = mapData.url;
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.src = url;
      img.onload = () => {
        const w = img.width;
        const h = img.height;
        const canvas = document.createElement("canvas");
        canvas.width = w;
        canvas.height = h;
        const canvas2d = canvas.getContext("2d");
        canvas2d.drawImage(img, 0, 0, w, h);

        resolve({
          width: w,
          height: h,
          back: canvas2d.getImageData(0, 0, w, h),
          ground: canvas2d.getImageData(0, 0, w, h),
          wall: canvas2d.getImageData(0, 0, w, h),
        });
      };
    });
  },

  resolveImageData(layersData) {
    // const exclusionNumber = 0.1 * 255;
    const backColorNumber = 128; // 地图背景色128
    // const wallColorNumber = 0;
    const groundColorNumber = 255;

    const backImageOption = layersData.back;
    const backImageData = backImageOption.data;

    const groundImageOption = layersData.ground;
    const groundImageData = groundImageOption.data;

    const wallImageOption = layersData.wall;
    const wallImageData = wallImageOption.data;

    // 开始解析层级
    for (let i = 0, backLayerLen = backImageData.length; i < backLayerLen; i += 4) {
      const i1 = i + 1;
      const i2 = i + 2;
      const i3 = i + 3;
      const [r, g, b, a] = [
        backImageData[i],
        backImageData[i1],
        backImageData[i2],
        backImageData[i3],
      ];

      // 0-100障碍物墙;100-210未探测-修正205;210-255地面。保留所有颜色。擦除墙变成255。
      // 解析地面和墙
      // 默认背景层为纯灰色，透明解析为背景层
      backImageData[i] = backImageData[i1] = backImageData[i2] = backColorNumber;
      backImageData[i3] = 255;
      switch (true) {
        case a === 0:
          // 透明解析为背景层
          wallImageData[i3] = 0;
          groundImageData[i3] = 0;
          break;
        case r >= 0 && r <= 50:
          // 0-50解析为黑色像素
          // 黑色像素墙是 黑色, 地面是透明，背景层为纯灰色
          // 黑色下面需要是白色
          wallImageData[i] = r;
          wallImageData[i1] = g;
          wallImageData[i2] = b;
          wallImageData[i3] = a;
          groundImageData[i] = groundImageData[i1] = groundImageData[i2] = groundColorNumber;
          groundImageData[i3] = 255;
          break;
        case r >= 210 && r <= 255:
          // 210-255解析为白色数据地面
          // 白色数据墙是透明, 地面是白色, 背景层为纯灰色
          groundImageData[i] = r;
          groundImageData[i1] = g;
          groundImageData[i2] = b;
          groundImageData[i3] = a;
          wallImageData[i3] = 0;
          break;
        case r > 50 && r < 210:
          // 50-210解析为灰底，但是要保留色值 墙/地面均是透明
          backImageData[i] = r;
          backImageData[i1] = g;
          backImageData[i2] = b;
          backImageData[i3] = a;
          groundImageData[i3] = wallImageData[i3] = 0;
          break;
        default:
          break;
      }
    }

    return {
      back: backImageOption,
      ground: groundImageOption,
      wall: wallImageOption,
    };
  },
  transBackLayerImageData(backData) {
    const { canvas, canvas2d, ...itData } = backData;
    const backImage = canvas2d.getImageData(0, 0, itData.width, itData.height);
    const backImageData = backImage.data;
    const backLayerLen = backImageData.length;

    // 开始解析层级
    for (let i = 0; i < backLayerLen; i += 4) {
      backImageData[i] = 128;
      backImageData[i + 1] = 128;
      backImageData[i + 2] = 128;
      backImageData[i + 3] = 255;
    }
    canvas2d.putImageData(backImage, 0, 0);

    const image = new fabric.Image(canvas, {
      left: itData.width / 2,
      top: itData.height / 2,
      width: itData.width,
      height: itData.height,
      originX: "center",
      originY: "center",
    });

    return { ...itData, canvas, canvas2d, image };
  },

  transImageData(imgData) {
    const { width, height } = imgData;
    const canvas = document.createElement("canvas");
    canvas.width = width;
    canvas.height = height;
    const canvas2d = canvas.getContext("2d");
    canvas2d.putImageData(imgData, 0, 0);

    const image = new fabric.Image(canvas, {
      left: width / 2,
      top: height / 2,
      width,
      height,
      originX: "center",
      originY: "center",
    });
    image.selectable = false;
    return {
      width,
      height,
      image,
      canvas,
      canvas2d,
    };
  },
};
