import Chart, { requestCache } from "../common";

/**
 * P40机器人利用率
 */
export default class UtilizationRateCredByP40 extends Chart {
  /**
   * P40机器人利用率
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('cred', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "P40机器人利用率";
  }

  async request(params) {
    const { data } = await requestCache('/athena/stats/query/robot/snapshot', {
      date: $utils.Tools.formatDate(new Date, "yyyy-MM-dd"),
      cycle : "5",
      ...params
    })

    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const totalDataItem = (data?.robotSnapshotList || []);
    const list = totalDataItem.filter(item => item.haveData).map(item => {
      return {
        ...item,
        snapshotTime: +new Date(item.snapshotTime)
      }
    });

    list.sort((itemA, itemB) => {
      return itemB.snapshotTime - itemA.snapshotTime;
    });
    
    const newDateItem = list[0];
    const P40_COUNT = newDateItem['DEFAULT_P40_COUNT'] || newDateItem['P40_COUNT'] || 0;
    const P40_WORK = newDateItem['DEFAULT_P40_WORK'] || newDateItem['P40_WORK'] || 0;
    let number = 0;

    if (P40_WORK) {
      number = ((P40_WORK / P40_COUNT) * 100).toFixed(3);
    }

    return {
      id: 'utilizationRateCred',
      title: this.title || '',
      number: number,
      append: '%',
      color: "#d9001b",
    }
  }
} 