<template>
  <div>
    <div class="flex_c">
      <div v-for="(item, index) in timeSlotsList" :key="index">
        <el-time-picker
          v-model="item.value"
          is-range
          range-separator="—"
          value-format="HH:mm:ss"
          @change="change(item)"
        />
        <i
          v-if="timeSlotsList.length > 1"
          class="el-icon-delete hover"
          @click="onDelete(index, item.value)"
        ></i>
      </div>
    </div>
    <el-button type="primary" size="mini" @click="onAdd">add</el-button>
    <!-- <i class="el-icon-circle-plus-outline hover" @click="onAdd"></i> -->
  </div>
</template>

<script>
export default {
  name: "TimePickerSlots",
  props: {
    value: {
      type: String,
      default() {
        return "";
      },
    },
  },
  data() {
    return {
      timeSlotsList: [],
    };
  },

  watch: {
    value: {
      handler(val) {
        // console.log(val, "timeSlotsList");
        if (val && val.indexOf(";") !== -1) {
          this.timeSlotsList = [];
          let arr = val.split(";");
          arr.forEach(item => {
            this.timeSlotsList.push({
              value: JSON.parse(item),
            });
          });
        }
      },
      immediate: true,
    },
  },

  mounted() {},

  methods: {
    onAdd() {
      this.timeSlotsList.push({
        value: "",
      });
    },
    onDelete(ind, value) {
      this.timeSlotsList.splice(ind, 1);
      if (value) {
        let val = "";
        if (this.timeSlotsList.length) {
          this.timeSlotsList.forEach(item => {
            if (item.value) {
              val += `${JSON.stringify(item.value)};`;
            }
          });
        }
        val = val.slice(0, -1);
        this.$emit("change", val);
      }
    },
    change() {
      // console.log(this.timeSlotsList);
      let val = "";
      if (this.timeSlotsList.length) {
        this.timeSlotsList.forEach(item => {
          if (item.value) {
            val += `${JSON.stringify(item.value)};`;
          }
        });
      }
      // console.log(val);
      val = val.slice(0, -1);
      this.$emit("change", val);
    },
  },
};
</script>

<style lang="less" scoped>
.flex_c {
  margin-right: 3px;
  display: flex;
  flex-direction: column;
}
.el-date-editor {
  margin-top: 6px;
}
.hover {
  // margin-right: 3px;
  &:hover {
    cursor: pointer;
    color: #409eff;
  }
}
</style>
