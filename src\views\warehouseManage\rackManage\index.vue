<template>
  <geek-main-structure style="padding-top: 0">
    <geek-tabs-nav :block="true" :nav-list="permissionNavList" @select="tabsNavChange" class="rack-management-nav" />
    <keep-alive>
      <component :is="activeName" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import PackQuery from "./packQuery";
import BoxHistorySearch from "./boxHistorySearch";
export default {
  components: { PackQuery, BoxHistorySearch },
  data() {
    return {
      permissionNavList: [],
      navList: [
        { permissionName: "TabBoxSearchPage", id: "PackQuery", text: "auth.rms.packQuery.page" },
        { permissionName: "TabBoxTrackPage", id: "BoxHistorySearch", text: "auth.rms.boxHistorySearch.page" },
      ],
      activeName: "",
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "boxManage"));
    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  methods: {
    tabsNavChange(id) {
      if (this.activeName === id) return;
      this.activeName = id;
    },
  },
};
</script>
<style lang="less" scoped>
.rack-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
