const fs = require("fs");
const stat = fs.stat;
/*
 * 复制目录中的所有文件包括子目录
 * @param{ String } 需要复制的目录
 * @param{ String } 复制到指定的目录
 */
const copy = function (src, dst) {
  // 读取目录中的所有文件/目录
  fs.readdir(src, function (err, paths) {
    if (err) {
      throw err;
    }
    paths.forEach(function (path) {
      var _src = src + "/" + path,
        _dst = dst + "/" + path,
        readable,
        writable;
      stat(_src, function (err, st) {
        if (err) {
          throw err;
        }
        // 判断是否为文件
        if (st.isFile()) {
          // 创建读取流
          readable = fs.createReadStream(_src);
          // 创建写入流
          writable = fs.createWriteStream(_dst);
          // 通过管道来传输流
          readable.pipe(writable);
        }
        // 如果是目录则递归调用自身
        else if (st.isDirectory()) {
          exists(_src, _dst, copy);
        }
      });
    });
  });
};
// 在复制目录前需要判断该目录是否存在，不存在需要先创建目录
const exists = function (src, dst, callback) {
  stat(dst, (err, stats) => {
    if (stats && stats.isDirectory()) {
      // 存在先删除
      fs.rmdirSync(dst, { recursive: true });
    }

    fs.mkdir(dst, function () {
      callback(src, dst);
    });
  });
};

// const copyList = [
//   {
//     form: path.join(__dirname, "../geek_map/ts-map-fe/monitor2D"),
//     to: path.join(__dirname, "../public/monitor2D"),
//   },
// ];
module.exports = copyList => {
  // 复制目录
  copyList.forEach(item => {
    console.info("复制编译文件:", item.form);
    console.info("复制到:", item.to);
    exists(item.form, item.to, copy);
  });

  console.info("复制完成");
};
