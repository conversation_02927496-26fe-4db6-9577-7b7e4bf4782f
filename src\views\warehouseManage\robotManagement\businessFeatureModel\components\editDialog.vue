<template>
  <el-dialog
    :title="$t(title)"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="520px"
  >
    <geek-customize-form ref="refForm" :form-config="formConfig">
      <template #sizeTypes>
        <el-select
          v-model="sizeTypes"
          multiple
          filterable
          allow-create
          default-first-option
          :placeholder="$t('lang.rms.api.result.warehouse.pleaseSelectOrEnterInput')"
          @change="sizeTypesChange(sizeTypes)"
        >
          <el-option v-for="item in sizeTypeOptions" :key="item" :label="item" :value="item" />
        </el-select>
        <div v-if="sizeTypeParamTip" class="error-tip">{{ sizeTypeParamTip }}</div>
      </template>
    </geek-customize-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditBusinessFeatureModelDialog",
  props: ["sizeTypeOptions"],
  data() {
    return {
      dialogVisible: false,
      operation: "",
      rowData: {},
      container: [],
      chargerType: [],
      taskType: [],
      bizTypes: [],
      sizeTypes: [],
      sizeTypeParamTip: null,
    };
  },
  computed: {
    title() {
      switch (this.operation) {
        case "add":
          return "lang.rms.api.result.warehouse.createRobotBusinessModel";
        default:
          return "lang.rms.api.result.warehouse.robotBusinessModel";
      }
    },

    formConfig() {
      let disabled = false;
      if (this.operation == "view") disabled = true;
      return {
        attrs: {
          labelWidth: "180px",
          labelPosition: "right",
          disabled,
        },
        configs: {
          name: {
            label: "lang.rms.api.result.warehouse.businessFeatureModelName",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterBusinessFeatureModelName",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.businessNotEmpty"),
                trigger: "blur",
              },
            ],
          },
          taskTypes: {
            label: "lang.rms.api.result.warehouse.supportedTaskTypes",
            tag: "select",
            default: "",
            multiple: true,
            required: true,
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.taskType,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.web.robot.pleaseSelectSupportedTaskType"),
                trigger: "blur",
              },
            ],
          },
          chargerTypes: {
            label: "lang.rms.api.result.warehouse.adaptableChargingStationType",
            tag: "select",
            default: "",
            multiple: true,
            required: true,
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.chargerType,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseSelectAdaptableChargingStationType"),
                trigger: "blur",
              },
            ],
          },
          sizeTypes: {
            label: "lang.rms.api.result.warehouse.supportedSizeType",
            slotName: "sizeTypes",
            tag: "select",
            default: "",
            multiple: true,
            filterable: true,
            "allow-create": true,
            placeholder: "lang.rms.api.result.warehouse.pleaseSelectOrEnterInput",
            options: this.sizeTypeOptions,
          },
          containerTypes: {
            label: "lang.rms.api.result.warehouse.supportedContainerTypes",
            tag: "select",
            default: "",
            multiple: true,
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.container,
            rules: [{ required: true, message: this.$t("lang.rms.fed.canNotBeEmpty"), trigger: "blur" }],
          },
        },
      };
    },
  },
  methods: {
    open(type, data) {
      this.getContainerList();
      this.getChargerTypeList();
      this.getDictionary();
      this.getBizTypes();
      this.operation = type;
      this.rowData = data || {};
      this.sizeTypes = data?.sizeTypes || [];
      this.dialogVisible = true;

      const params = {
        name: data?.name || "",
        taskTypes: data?.taskTypes || [],
        chargerTypes: data?.chargerTypes || [],
        sizeTypes: data?.sizeTypes || [],
        containerTypes: data?.containerTypes || [],
      };
      this.$nextTick(() => {
        this.$refs.refForm.reset();
        this.$refs.refForm.setData(params);
      });
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      if (this.operation == "view") {
        this.close();
        return;
      }

      this.$refs.refForm.validate().then(data => {
        let formData = Object.assign({}, data);
        if (this.operation == "edit") formData.id = this.rowData.id;
        if (this.sizeTypeParamTip) {
          return
        }
        formData.sizeTypes = this.sizeTypes;
        $req.post("/athena/robot/manage/businessSave", formData).then(res => {
          this.$success();
          this.close();
          this.$emit("updateList");
        });
      });
    },

    getContainerList() {
      $req.post("/athena/containerModel/findContainerModelByPage", { currentPage: 1, pageSize: 1000 }).then(res => {
        if (res?.code !== 0) return;
        const list = res?.data?.recordList || [];

        let container = {};
        list.forEach(item => {
          const modelType = item.modelType;
          if (container[modelType]) return;
          container[modelType] = { label: modelType, value: modelType };
        });
        this.container = Object.values(container);
      });
    },
    getChargerTypeList() {
      $req.get("/athena/charger/chargerType").then(res => {
        if (res?.code !== 0) return;
        const list = res?.data || [];

        this.chargerType = list.map(item => ({ label: item, value: item }));
      });
    },
    getDictionary() {
      $req.post("/athena/dict/query", { types: ["ROBOT_TASK_TYPE"] }).then(res => {
        if (res.code !== 0) return;
        const list = res?.data["ROBOT_TASK_TYPE"] || [];
        this.taskType = list.map(item => ({ label: item.fieldCode, value: item.fieldValue }));
      });
    },

    getBizTypes() {
      $req.post("/athena/dict/queryList", { dictCode: "ROBOT_BIZ" }).then(res => {
        const data = res?.data || [];
        this.bizTypes = data.map(item => ({ label: item.dictValue, value: item.dictKey }));
      });
    },

    sizeTypesChange(data) {
      if (data.length === 0) {
        this.sizeTypeParamTip = null
        return
      }
      const reg = /^(?!\d)[a-zA-Z0-9]*$/
      if (data) {
        data.forEach((item) => {
          if (reg.test(item)) {
            this.sizeTypeParamTip = null
          } else {
            this.sizeTypeParamTip = this.$t('lang.venus.web.check.canNotStartWithNum')
          }
        })
      }
    }
  },
};
</script>

<style lang="less" scoped>
.error-tip {
  position: absolute;
  top: 100%;
  color: red;
  font-size: 12px;
  line-height: 1;
  margin-top: 2px;
}
</style>
