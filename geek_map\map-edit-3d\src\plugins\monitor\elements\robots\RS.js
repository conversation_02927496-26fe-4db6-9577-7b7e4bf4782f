import * as THREE from "three";
import {
  useRobotAnimal,
  useRSRobotLiftAnimal,
  userRSRobotBoxActionAnimal,
} from "../../../../utils/model-func";
import { addMethods } from "../../../../utils/utils";
import globalConfig from "../../../../config";

class RS {
  constructor(Map3d) {
    this.Map3d = Map3d;
    this.config = {
      rackLayerHeight: 0.5,
      rsbaseHeight: 0.5,
    };
  }
  getRobotMesh(item) {
    const mesh = this.Map3d.modelInstances[item.uuid].model;
    return mesh.children[0].children
      .filter(i => !i.name.includes("robot-rs-boxMesh"))
      .map(i => {
        i.userData.uuid = item.uuid;
        return i;
      });
  }
  afterRender(item) {
    const model = this.Map3d.modelInstances[item.uuid].model;
    const body = model.getObjectByName("robot-rs-body");
    // hover
    const box3 = new THREE.Box3().setFromObject(body.clone());
    const hover = new THREE.Box3Helper(box3, globalConfig.THEME.HOVER_3D);
    hover.name = "hover";
    hover.visible = false;
    hover.updateMatrixWorld(true);
    body.parent.add(hover);
    // select
    const select = new THREE.Box3Helper(box3, globalConfig.THEME.SELECTED);
    select.name = "select";
    select.visible = false;
    select.updateMatrixWorld(true);
    body.parent.add(select);
    model.rotation.y = -item.radAngle;
    // 增加功能监听；
    addMethods(body);
  }
  update(ov, nv) {
    if (!this.Map3d.modelInstances[ov.uuid]) return;
    const mesh = this.Map3d.modelInstances[ov.uuid].model;
    // 更新机器人任务状态；
    this.__renderRobotStatus(nv, mesh);
    // 更新机器人取箱/还箱操作；
    this.__renderRobotFetchReturnAction(nv, mesh);
    // 更新lattices + boxs
    if (!mesh.userData.isRenderLattice) {
      this.__renderLattice(nv, mesh);
    } else {
      this.__renderBox(nv, mesh);
    }
    const { location: ol, radAngle: r } = ov;
    mesh.userData.curLocation = [ol.x, ol.y, r];
    // 坐标更新动画
    this.__useRobotAnimal(nv, mesh);
  }
  __useRobotAnimal(nv, mesh) {
    const { location, radAngle } = nv;
    useRobotAnimal({ mesh, location: [location.x, location.y, radAngle] });
  }
  __renderLattice(nv, mesh) {
    const { onloadRack } = nv;
    const latticeModel = mesh.getObjectByName("robot-rs-lattice");
    const trayModel = mesh.getObjectByName("robot-rs-tray");
    const boxMesh = mesh.getObjectByName("robot-rs-boxMesh");
    const liftGroup = mesh.getObjectByName("robot-rs-liftGroup");
    const latticeBox = new THREE.Box3().setFromObject(latticeModel.clone());
    const tryBox = new THREE.Box3().setFromObject(trayModel.clone());
    const baseH = this.config.rackLayerHeight;
    onloadRack.lattices.forEach(lattice => {
      const isCacheLayer = !lattice.layer;
      const y = this.config.rsbaseHeight + baseH * (lattice.layer - 1);
      if (!isCacheLayer) {
        const model = latticeModel.clone();
        model.position.y = y;
        model.visible = true;
        model.name = `robot-rs-lattice-${lattice.layer}`;
        mesh.children[0].add(model);
      }
      let boxModel = boxMesh.clone();
      boxModel.name = `robot-rs-boxMesh-${lattice.layer}`;
      boxModel.visible = !!lattice.relateBox;
      // 0.3是固定箱子大小，0.15做居中偏移
      if (isCacheLayer) {
        boxModel.position.y = tryBox.max.y;
        boxModel.position.z = tryBox.min.z / 2;
        liftGroup.add(boxModel);
      } else {
        boxModel.position.y = y + latticeBox.max.y + 0.15;
        boxModel.position.z = latticeBox.max.z / 2 + 0.15;
        mesh.children[0].add(boxModel);
      }
    });
    mesh.userData.isRenderLattice = true;
  }
  __renderRobotStatus(nv, mesh) {
    const body = mesh.getObjectByName("robot-rs-body");
    const workEffect = ["GO_SOMEWHERE_TO_STAY"];
    body.status = nv.taskId && !workEffect.includes(nv.taskType) ? "work" : "normal";
  }
  __renderRobotFetchReturnAction(nv, mesh) {
    const liftGroup = mesh.getObjectByName("robot-rs-liftGroup");
    const body = mesh.getObjectByName("robot-rs-body");
    const bodyBox3 = new THREE.Box3().setFromObject(body.clone());
    const halfHeight = bodyBox3.max.y / 2;
    const returnBox = "GO_RETURN_BOX",
      fetchBox = "GO_FETCH_BOX";
    const curPathMode = liftGroup.userData.robotPathMode;
    const nextPathMode = nv.robotPathMode;
    const data = { mesh: liftGroup, target: halfHeight };
    if (nextPathMode === returnBox) {
      if (curPathMode === returnBox)
        userRSRobotBoxActionAnimal({ mesh: liftGroup, action: "return" });
      else useRSRobotLiftAnimal(data);
    } else if (nextPathMode === fetchBox) {
      if (curPathMode === fetchBox)
        userRSRobotBoxActionAnimal({ mesh: liftGroup, action: "fetch" });
      else useRSRobotLiftAnimal(data);
    } else if (
      [returnBox, fetchBox].includes(curPathMode) &&
      ![returnBox, fetchBox].includes(nextPathMode)
    ) {
      useRSRobotLiftAnimal(Object.assign(data, { target: 0 }));
    }
    liftGroup.userData.robotPathMode = nextPathMode;
  }
  __renderBox(nv, mesh) {
    const { onloadRack } = nv;
    onloadRack.lattices.forEach(lattice => {
      const box = mesh.getObjectByName(`robot-rs-boxMesh-${lattice.layer}`);
      box.visible = !!lattice.relateBox;
    });
  }
}
export default RS;
