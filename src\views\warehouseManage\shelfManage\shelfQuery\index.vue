<template>
  <section>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" style="padding: 5px 0" />
    <geek-customize-table :table-config="tableConfig" :data="tableData" :page="tablePage" @page-change="pageChange" />
  </section>
</template>

<script>
export default {
  data() {
    return {
      // 搜索条件
      form: {
        shelfCode: "",
        logicId: "",
        state: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          shelfCode: {
            label: "lang.rms.fed.shelfNumber",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          logicId: {
            label: "lang.rms.fed.region",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterAnNumber",
          },
          state: {
            label: "lang.rms.fed.lock",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "",
                label: "lang.rms.fed.wholeStatus",
              },
              {
                value: 0,
                label: "lang.rms.fed.no",
              },
              {
                value: 1,
                label: "lang.rms.fed.yes",
              },
            ],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {},
        columns: [
          { label: "lang.rms.fed.shelfNumber", prop: "shelfCode", width: "100" },
          { label: "lang.rms.fed.region", prop: "logicId" },
          {
            label: "lang.rms.fed.lock",
            prop: "state",
            formatter: (row, column, cellValue, index) => {
              if (cellValue === "NORMAL") {
                return this.$t("lang.rms.fed.no");
              } else {
                return this.$t("lang.rms.fed.yes");
              }
            },
          },
          { label: "lang.rms.fed.textLength", prop: "length", width: "60" },
          { label: "lang.rms.fed.textWidth", prop: "width", width: "60" },
          { label: "lang.rms.fed.textHeight", prop: "height", width: "60" },
          { label: "lang.rms.fed.shelfPosition", prop: "location", width: "180" },
          { label: "lang.rms.fed.placementCode", prop: "placement", width: "180" },
          { label: "lang.rms.fed.angle", prop: "angle", width: "80" },
          { label: "lang.rms.fed.shelfHeatDisplay", prop: "shelfHeat", width: "70" },
          { label: "lang.rms.fed.cell", prop: "locationCellCode" },
          { label: "lang.rms.fed.placementCell", prop: "placementCellCode" },
        ],
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { pageSize, currentPage } = this.tablePage;
      let params = Object.assign({}, this.form, { pageSize, currentPage });
      $req.get("/athena/shelf/findShelf", params).then(res => {
        let result = res?.data || {};
        this.tableData = result.recordList || [];

        this.tablePage = Object.assign({}, this.tablePage, {
          pageCount: result.pageCount || 0,
          currentPage: result.currentPage || 1,
        });
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
