import Chart, { requestCache } from "../common";

/**
 * 3.2 机器人充电汇总
 */
export default class StatAbnormalitiesTable extends Chart {
  /**
   * 初始化图表 - 3.2 机器人充电无电量统计
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('table', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "每日异常汇总统计";
    
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'elDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        option: {
          type: "datetimerange"
        }
      },
      // 机器人类型
      // https://confluence.geekplus.cc/pages/viewpage.action?pageId=177542368
      // athena/map/version/findRobotType
    ]
  }

  async request(params) {
    const date = params?.date || [];
    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 7;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);

    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/fault/stat/faultsByDay', {
      startTime,
      endTime,
      ...params
    })
    
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    let tableCloumns = [
      { label: '日期', prop: 'TIME' },
      { label: '重规划次数', prop: 'PATH_REPLAN' },
      { label: '死锁发生次数', prop: 'DEAD_LOCK' },
      { label: '解死锁发生次数', prop: 'CHECK_DEAD_LOCK' },
      { label: '解死锁成功率', prop: 'DEAD_LOCK_SOLUTION_SUCCESS' },
      { label: '机器人掉线次数', prop: 'ROBOT_DISCONNECTED' },
      { label: '碰撞发生次数', prop: 'ROBOT_CRASHED' },
      { label: 'SLAM丢定位次数', prop: 'LOCATION_LOSE' },
      { label: '对接失败次数', prop: 'RECOGNITION_FAIL' },
      { label: 'RF对接失败次数', prop: 'RF_RECOGNITION_FAIL' },
      { label: '地面二维码丟码次数', prop: 'GROUND_MISSING_COUNT' },
    ];
    const dataItem = data['DEFAULT'].map(item => {
      let option = {};
      tableCloumns.forEach((cloumnItem, index) => {
        if (!index) {
          option[cloumnItem.prop] = $utils.Tools.formatDate(item[index], "yyyy-MM-dd");
        } else {
          option[cloumnItem.prop] = item[index];
        }
      })
      return option;
    });
   

    return {
      title: this.title,
      tableCloumns,
      tableData: dataItem,
    }
  }
}