<template>
  <div class="shelfModel">
    <!-- 地图 -->
    <leftView />
    <!-- 右侧面板数据 -->
    <rightPanel :view-disabled="viewDisabled" />
  </div>
</template>

<script>
import leftView from "./view";
import rightPanel from "./rightPanel";

export default {
  name: "ShelfModel",
  components: {
    leftView,
    rightPanel,
  },
  props: {
    viewDisabled: {
      type: Boolean,
      default: true,
    },
  },
};
</script>
<style lang="less" scoped>
.shelfModel {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: row;
}
</style>
