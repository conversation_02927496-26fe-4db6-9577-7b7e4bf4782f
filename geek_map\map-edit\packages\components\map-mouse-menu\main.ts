import { MenuConfigureListItemType, MenuConfigureType } from "./type";
import { customAlphabet } from "nanoid/non-secure";
import { GENERATE_ID_ALPHABET } from "@packages/configure/dict/common";
import { addEventListener, triggerLayers } from "@packages/hook/useEvent";
import editVNode from "@packages/hook/useMapVNode";
import { useMapMouseMenuStore } from "./store";

export class MapMouseMenu {
  options: MenuConfigureListItemType[] = [];
  configure: MenuConfigureType = {};

  constructor(configure: MenuConfigureType) {
    this.configure = configure;
    this.interactionEvent();
  }

  interactionEvent() {
    // 右键呼出菜单
    addEventListener("map:rightclick", (editMapRef: any, data: any) => {
      const originalEvent = data.originalEvent;
      const mapMouseMenuRef = editVNode.useMapMouseMenu();
      mapMouseMenuRef.showMenu({
        x: originalEvent.pageX,
        y: originalEvent.pageY,
      });
    });

    // 触发重置时取消状态
    addEventListener("map:resetOperation", (editMapRef: any, data: any) => {
      const mapMouseMenuStore = useMapMouseMenuStore();
      mapMouseMenuStore.reset();
      triggerLayers(["ALL"]);
    });

    // 按下ESC的处理
    addEventListener("map:Escape", (editMapRef: any, data: any) => {
      //隐藏菜单
      const mapMouseMenuRef = editVNode.useMapMouseMenu();
      mapMouseMenuRef.hideMenu();
      const mapMouseMenuStore = useMapMouseMenuStore();
      mapMouseMenuStore.reset();
      triggerLayers(["ALL"]);
    });
  }

  /**
   * 新增一个菜单数据
   * @param menuData 菜单数据
   */
  addMenuItem(menuData: MenuConfigureListItemType) {
    const { eventName, handle, ...otherData } = menuData;
    let curEventName = eventName;

    if (!curEventName) {
      curEventName = `menu:event{${customAlphabet(GENERATE_ID_ALPHABET, 6)}}`;
    }

    if (handle) {
      addEventListener(curEventName, handle);
    }

    this.options.push({ ...otherData, eventName: curEventName });
  }
}
