import * as THREE from "three";
class baseModel {
  constructor(options) {
    this.uuid = options.data.uuid;
  }
  fit(object3d, { width, length }) {
    const sceneExtent = new THREE.BoxGeometry(length, 170, width);
    const cube = new THREE.Mesh(sceneExtent, new THREE.MeshBasicMaterial({ color: 0xffff00 }));
    const cubeSize = new THREE.Box3().setFromObject(cube);
    const gltfSize = new THREE.Box3().setFromObject(object3d);
    let lengthCubBounds = [
      Math.abs(cubeSize.max.x - cubeSize.min.x),
      Math.abs(cubeSize.max.y - cubeSize.min.y),
      Math.abs(cubeSize.max.z - cubeSize.min.z),
    ];
    // .sort((a, b) => a - b)
    let lengthGltfBounds = [
      Math.abs(gltfSize.max.x - gltfSize.min.x),
      Math.abs(gltfSize.max.y - gltfSize.min.y),
      Math.abs(gltfSize.max.z - gltfSize.min.z),
    ];
    // .sort((a, b) => a - b)
    let lengthRations = [
      lengthCubBounds[0] / lengthGltfBounds[0],
      lengthCubBounds[1] / lengthGltfBounds[1],
      lengthCubBounds[2] / lengthGltfBounds[2],
    ];
    let minRatio = Math.min(...lengthRations);
    let padding = 0;
    minRatio -= padding;
    return minRatio * object3d.scale.x;
  }
}

export default baseModel;
