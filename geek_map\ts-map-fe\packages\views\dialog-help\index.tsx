/* ! <AUTHOR> at 2022/08/31 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Modal } from "antd";
import { getMap2D, $eventBus } from "../../singleton";
import HelpIcon from "./help-icon";

function DialogHelp() {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [iconList, setIconList] = useState([]);

  useEffect(() => {
    $eventBus.on("dialogHelpShow", () => setVisible(true));

    const map2d = getMap2D();
    const colors = map2d.mapRender.getMapColors();
    const list = [
      { text: "lang.rms.fed.systemStop", url: HelpIcon["systemStop"] },
      { text: "lang.rms.fed.accessOpen", url: HelpIcon["accessOpen"] },
      { text: "lang.rms.fed.safetyDeviceReset", url: HelpIcon["safetyDeviceReset"] },
      { text: "lang.rms.fed.deviceNormal", url: HelpIcon["deviceNormal"] },
      { text: "lang.rms.fed.configException", url: HelpIcon["deviceConfigError"] },
      { text: "lang.rms.fed.deviceError", url: HelpIcon["deviceError"] },
      { text: "lang.rms.fed.ququePoint", color: colors["QUEUE_CELL"].toString(16) },
      { text: "lang.rms.fed.station", color: colors["STATION_CELL"].toString(16) },
      {
        text: "lang.rms.fed.stationDisable",
        url: HelpIcon["park"],
        bg: colors["STATION_CELL"].toString(16),
      },
      { text: "lang.rms.fed.turnPoint", color: colors["TURN_CELL"].toString(16) },
      { text: "lang.rms.fed.road", color: colors["OMNI_DIR_CELL"].toString(16) },
      { text: "lang.rms.fed.cell", color: colors["DEFAULT_CELL"].toString(16) },
      { text: "lang.rms.fed.lockedCell", color: colors["LOCKED"].toString(16) },
      {
        text: "lang.rms.fed.pause",
        url: HelpIcon["cellPause"],
        bg: colors["STOPPED"].toString(16),
      },
      { text: "lang.rms.fed.selectedCell", color: colors["SELECTED"].toString(16) },
      { text: "lang.rms.fed.moveTypeShelf", url: HelpIcon["shelf"] },
      { text: "lang.rms.fed.lockedShelf", url: HelpIcon["shelfLocked"] },
      { text: "lang.rms.fed.fixedTypeShelf", url: HelpIcon["rack"] },
      { text: "lang.rms.fed.elevator", url: HelpIcon["elevator"] },
      { text: "lang.rms.fed.robotIdle", url: HelpIcon["robotNormal"] },
      { text: "lang.rms.fed.robotTask", url: HelpIcon["robotWork"] },
      { text: "lang.rms.fed.robotOffline", url: HelpIcon["robotOffline"] },
      { text: "lang.rms.fed.robotError", url: HelpIcon["robotError"] },
      { text: "lang.rms.fed.robotIdle", url: HelpIcon["forkNormal"] },
      { text: "lang.rms.fed.robotTask", url: HelpIcon["forkWork"] },
      { text: "lang.rms.fed.robotOffline", url: HelpIcon["forkOffline"] },
      { text: "lang.rms.fed.robotError", url: HelpIcon["forkError"] },
      {
        text: "lang.rms.fed.chargeIdle",
        url: HelpIcon["charger"],
        bg: colors["CHARGER_NORMAL"].toString(16),
      },
      {
        text: "lang.rms.fed.chargeTask",
        url: HelpIcon["charger"],
        bg: colors["CHARGER_WORK"].toString(16),
      },
      {
        text: "lang.rms.fed.chargeOffline",
        url: HelpIcon["charger"],
        bg: colors["CHARGER_OFFLINE"].toString(16),
      },
      {
        text: "lang.rms.fed.chargeError",
        url: HelpIcon["charger"],
        bg: colors["CHARGER_ERROR"].toString(16),
      },
    ];
    setIconList(list);

    return () => {
      $eventBus.off("dialogHelpShow");
    };
  }, []);

  return (
    <Modal
      title="ICON"
      centered
      open={visible}
      footer={null}
      width={800}
      maskClosable={false}
      onCancel={() => setVisible(false)}
      wrapClassName="map2d-dialog-help"
    >
      <ul className="map2d-help-content">
        {iconList.map((item, index) => (
          <li key={index}>
            {item.url ? (
              <div className="img">
                <img
                  src={item.url}
                  style={{ background: item.bg ? `#${item.bg}` : "transparent" }}
                />
              </div>
            ) : (
              <div className="img" style={{ background: `#${item.color}` }} />
            )}
            <div>{t(item.text)}</div>
          </li>
        ))}
      </ul>
    </Modal>
  );
}

export default DialogHelp;
