<template>
  <div v-if="logs.length > 0" class="geek-error-log">
    <el-badge is-dot class="item" @click.native="logVisible = true">
      <button class="el-button el-button--danger" />
    </el-badge>

    <el-dialog :visible.sync="logVisible" title="Error Log" width="80%" :append-to-body="true">
      <el-table :data="logs" border class="geek-header-error-table">
        <el-table-column label="Message">
          <template slot-scope="scope">
            <p>
              <strong>Msg:</strong>
              <el-tag type="danger">{{ scope.row.err }}</el-tag>
            </p>
            <p>
              <strong>Info:</strong>
              <el-tag type="warning"> {{ scope.row.tag }} error in {{ scope.row.info }} </el-tag>
            </p>
            <p>
              <strong>Url:</strong>
              <el-tag type="success">{{ scope.row.url }}</el-tag>
            </p>
          </template>
        </el-table-column>
        <el-table-column label="Stack">
          <template slot-scope="scope">
            <div>{{ scope.row.stack }}</div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  name: "GeekErrorLog",
  data() {
    return {
      logVisible: false,
    };
  },
  computed: {
    ...mapState(["logs"]),
  },
};
</script>

<style lang="less" scoped>
.geek-error-log {
  button {
    padding: 15px;
    background-image: url(~@imgs/layout/icon-debugger.png);
    background-size: 14px;
    background-repeat: no-repeat;
    background-position: 50% 50%;
  }
}

.geek-header-error-table {
  line-height: 23px;

  th,
  td {
    font-size: 12px;
    color: #606266;
  }

  th {
    .cell {
      font-weight: 600;
      color: #909399;
    }
  }

  td {
    p {
      margin: 16px 0;
      .g-flex();
      justify-content: flex-start;

      > strong {
        flex: 0 0 40px;
        font-size: 16px;
        color: #333;
        font-weight: bold;
      }

      .el-tag {
        height: auto;
        margin-left: 8px;
        white-space: normal;
      }
    }

    div {
      line-height: 22px;
      white-space: normal;
    }
  }
}
</style>
