// 功能待完善
// 锁30帧数
import * as THREE from "three";
import Stats from "stats.js";

let timeS = 0;
class Ticker {
  constructor() {
    this.deltaTime = 0;
    this.maxFPS = 20;
    this._prevTime = 0;
    this._animalStash = [];
    this.clock = new THREE.Clock();
    this.renderT = 1 / this.maxFPS;
    this.stats = null;
    // this.__lodStats();
    this._render();
  }
  add(fn) {
    this._animalStash.push(fn);
  }
  addOnce() {}
  remove(fn) {
    const index = this._animalStash.findIndex(i => i === fn);
    !!~index && this._animalStash.splice(index, 1);
  }
  start() {}
  stop() {}
  destroy() {
    this._animalStash = null;
  }
  __lodStats() {
    this.stats = new Stats();
    this.stats.showPanel(0);
    document.body.appendChild(this.stats.dom);
  }
  _render() {
    this.stats && this.stats.begin();
    const T = this.clock.getDelta();
    timeS = timeS + T;
    if (timeS > this.renderT) {
      this._animalStash &&
        this._animalStash.forEach(function (i) {
          i && i();
        });
      timeS = 0;
    }
    this.stats && this.stats.end();
    window.requestAnimationFrame(() => this._render());
  }
}

export default Ticker;
