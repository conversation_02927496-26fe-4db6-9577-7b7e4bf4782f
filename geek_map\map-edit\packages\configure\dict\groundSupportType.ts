/**
 * 存储了地面支架类型的字典
 */
/**
 * 默认行为
 */
export const GROUND_S_ADJUSTMENT_DEFAULT = 0;
/**
 * 不调整
 */
export const GROUND_S_ADJUSTMENT_ADJUSTBYVMARK = 1;
/**
 * 识别地面二维码，前后对齐+方向角微调
 */
export const GROUND_S_ADJUSTMENT_GROUNDANDALIGN = 2;
/**
 * 识别地面二维码，XY角度对齐
 */
export const GROUND_S_ADJUSTMENT_GROUNDANDXY = 3;
/**
 * 识别货架二维码，前后对齐+方向角微调
 */
export const GROUND_S_ADJUSTMENT_SHELFANDALIGN = 4;
/**
 * 识别货架二维码，XY角度对齐
 */
export const GROUND_S_ADJUSTMENT_SHELFANDXY = 5;

export const GROUND_S_ADJUSTMENT = [
  {
    label: "lang.rms.fed.function.adjust.default", //默认行为
    value: GROUND_S_ADJUSTMENT_DEFAULT,
  },
  {
    label: "lang.rms.fed.function.adjust.groundAndAlign", //识别地面二维码，前后对齐+方向角微调
    value: GROUND_S_ADJUSTMENT_GROUNDANDALIGN,
  },
  {
    label: "lang.rms.fed.function.adjust.groundAndXY", //识别地面二维码，X,Y,角度对齐
    value: GROUND_S_ADJUSTMENT_GROUNDANDXY,
  },
  {
    label: "lang.rms.fed.behavior.none", //vmark，不调整
    value: GROUND_S_ADJUSTMENT_ADJUSTBYVMARK,
  },
];

export const GROUND_S_ADJUSTMENT_CONTSHELF = [
  {
    label: "lang.rms.fed.function.adjust.default", //默认行为
    value: GROUND_S_ADJUSTMENT_DEFAULT,
  },
  {
    label: "lang.rms.fed.function.adjust.groundAndAlign", //识别地面二维码，前后对齐+方向角微调
    value: GROUND_S_ADJUSTMENT_GROUNDANDALIGN,
  },
  {
    label: "lang.rms.fed.function.adjust.groundAndXY", //识别地面二维码，X,Y,角度对齐
    value: GROUND_S_ADJUSTMENT_GROUNDANDXY,
  },
  {
    label: "lang.rms.fed.function.adjust.shelfAndAlign", //识别货架二维码，前后对齐+方向角微调
    value: GROUND_S_ADJUSTMENT_SHELFANDALIGN,
  },
  {
    label: "lang.rms.fed.function.adjust.shelfAndXY", //识别货架二维码，X,Y,角度对齐
    value: GROUND_S_ADJUSTMENT_SHELFANDXY,
  },
  {
    label: "lang.rms.fed.behavior.none", //vmark，不调整
    value: GROUND_S_ADJUSTMENT_ADJUSTBYVMARK,
  },
];
