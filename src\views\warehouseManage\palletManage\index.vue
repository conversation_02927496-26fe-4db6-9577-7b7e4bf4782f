<template>
  <geek-main-structure style="padding-top: 0">
    <geek-tabs-nav :block="true" :nav-list="permissionNavList" @select="tabsNavChange" class="pallet-management-nav" />
    <keep-alive>
      <component :is="activeName" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import PalletRackManage from "./palletRackManage";
import PalletPositionManage from "./palletPositionManage";
export default {
  components: { PalletRackManage, PalletPositionManage },
  data() {
    return {
      permissionNavList: [],
      navList: [
        {
          permissionName: "TabPalletRackPage",
          id: "PalletRackManage",
          text: "auth.rms.palletRackManage.palletRackManage",
        },
        {
          permissionName: "TabPalletLatticePage",
          id: "PalletPositionManage",
          text: "auth.rms.palletPositionManage.palletLatticeManage",
        },
      ],
      activeName: "",
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "palletManage"));
    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  methods: {
    tabsNavChange(id) {
      if (this.activeName === id) return;
      this.activeName = id;
    },
  },
};
</script>
<style lang="less" scoped>
.pallet-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
