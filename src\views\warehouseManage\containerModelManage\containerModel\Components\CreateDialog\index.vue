<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)
 * @Date: 2021-12-27 13:59:47
 * @Description:
-->
<template>
  <div>
    <el-button v-if="showTitle" type="primary" @click="dialogShow">
      {{ $t("lang.rms.fed.newlyAdded") }}{{ buttonText }}
    </el-button>
    <div class="creat-dialog">
      <el-dialog
        :title="$t(titleText)"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        :width="widthNum"
        @close="dialogClose"
      >
        <slot />
        <template #footer>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogClose">{{ $t("lang.rms.fed.cancel") }}</el-button>
            <el-button type="primary" @click="dialogConfirm">{{
              $t("lang.rms.fed.confirm")
            }}</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    titleText: {
      type: String,
      default: "提示",
    },
    buttonText: {
      type: String,
      default: "",
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    open: {
      type: Number,
      default: 0,
    },
    close: {
      type: Number,
      default: 0,
    },
    widthNum: {
      type: String,
      default: "50%",
    },
  },
  data() {
    // 这里存放数据
    return {
      dialogVisible: false,
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {
    open() {
      this.dialogVisible = true;
    },
    close() {
      this.dialogVisible = false;
    },
  },
  // 方法集合
  methods: {
    dialogShow() {
      this.dialogVisible = true;
      this.$emit("changeOpen");
    },
    dialogClose() {
      this.$emit("createcancel");
      this.dialogVisible = false;
    },
    dialogConfirm() {
      this.$emit("createconfirm");
    },
  },
};
</script>
<style scoped>
.creat-dialog {
  text-align: left;
}

.creat-dialog :deep(.el-dialog__body) {
  padding-top: 0;
}
</style>
