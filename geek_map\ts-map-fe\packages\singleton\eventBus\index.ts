import EventBus from "js-event-bus";
import events from "./event.config";

const mapBus: EventBus = new EventBus();
const MapEvents: MEvents.Main = {
  _events: [],
  on(eventName: string, cb: (...args: any[]) => void): void {
    if (!events.includes(eventName)) {
      throw new Error(`没有定义此事件::${eventName}! 请在 event.config.ts 中先定义`);
    }
    if (this._events.includes(eventName)) {
      console.warn(`您已绑定过此事件::${eventName}, 不要重复绑定,我给你return了。`);
      return;
    }
    this._events.push(eventName);
    mapBus.on(eventName, cb);
    console.log("[event bus] ----- event on::", eventName); //,this._events);
  },

  emit(eventName: string, params?: { [propName: string]: any }) {
    if (params) {
      mapBus.emit(eventName, null, params);
    } else {
      mapBus.emit(eventName);
    }
    // console.log("[event bus] ----- event emit::", eventName, this._events);
  },

  off(eventName: string) {
    const index = this._events.indexOf(eventName);
    if (index !== -1) {
      this._events.splice(index, 1);
      mapBus.die(eventName);
    }
    console.log("[event bus] ----- event off::", eventName); //,this._events);
  },
  destroy() {
    events.forEach(eventName => {
      mapBus.die(eventName);
    });
    this._events = [];
    console.log("[event bus] ----- event destroy::", this._events);
  },
};

export default MapEvents;
