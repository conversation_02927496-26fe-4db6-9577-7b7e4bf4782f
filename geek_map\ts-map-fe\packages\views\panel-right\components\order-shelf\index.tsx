/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { AutoComplete, Button, message } from "antd";
import { getMap2D, $eventBus, checkBtn } from "../../../../singleton";

import ShelfMove from "./shelf-move";
import ShelfUpdate from "./shelf-update";
import ShelfUpdateAngle from "./shelf-update-angle";
import ShelfDetail from "./shelf-detail";

type PropsOrderData = {
  isCurrent: boolean;
};
function OrderShelf(props: PropsOrderData) {
  const { t } = useTranslation();
  const [shelfCode, setShelfCode] = useState("");
  const [shelfResults, setShelfResults] = useState([]);
  const [shelfData, setShelfData] = useState<shelfData>(null);
  const [cellData, setCellData] = useState<cellData>(null);
  const [operation, setOperation] = useState<"move" | "update" | "updateAngel">(undefined);

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.isCurrent) return;
    $eventBus.on("wsDataQueryRightTab", wsData => {
      if (!props.isCurrent || !wsData) return;
      const { params, data } = wsData;
      switch (params?.layer) {
        case "shelf":
          if (data) setShelfData(data);
          else setShelfData(null);
          break;
        case "cell":
          if (data) setCellData(data);
          else setCellData(null);
          break;
      }
    });
    return () => {
      $eventBus.off("wsDataQueryRightTab");
    };
  }, [props.isCurrent]);

  // 地图点击
  useEffect(() => {
    if (!props.isCurrent) return;

    const map2D = getMap2D();
    $eventBus.on("mapClick", (data: MRender.clickParams) => {
      const layer = data?.layer;
      switch (layer) {
        case "shelf":
          setShelfData(null);
          setShelfCode(data.code.toString());
          map2D.mapWorker.reqQuery({ layer, code: data.code });
          break;
        case "cell":
          const cellData = map2D.mapWorker.getQueryData({ layer, code: data.code });
          setCellData(cellData);
          break;
      }
    });

    return () => {
      $eventBus.off("mapClick");
    };
  }, [props.isCurrent]);

  // 清空数据 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;
    setOperation(undefined);
    const map2D = getMap2D();
    map2D.mapRender.triggerLayers(["shelf"]);
    return () => {
      clearShelf();
    };
  }, [props.isCurrent]);

  const controlHandler = (cmd: string) => {
    if (!shelfData?.shelfCode) {
      const msg = t("lang.rms.fed.pleaseEnterTheShelfID"); // 货架编号不能为空
      message.error(msg);
      return;
    }
    let instruction = "";
    switch (cmd) {
      case "move": // 移动
      case "update": // 更新
      case "updateAngel": // 更新角度
        setOperation(cmd);
        return;
      case "lock": // 锁定
        instruction = "LOCK_SHELF";
        break;
      case "unlock": // 解锁
        instruction = "UNLOCK_SHELF";
        break;
      case "placement": // 回老家
        instruction = "RETURN_SHELF_PLACEMENT";
        break;
    }
    setOperation(undefined);
    const map2D = getMap2D();
    const msgType = "ShelfInstructionRequestMsg";
    map2D.mapWorker
      .reqSocket(msgType, { instruction, shelfCode: shelfData?.shelfCode })
      .then(res => {
        if (res.msgType !== "ShelfInstructionResponseMsg") return;
        _$utils.wsCmdResponse(res?.body || {});
      });
  };

  // 货架清除
  const clearShelf = () => {
    setShelfCode("");
    setShelfResults([]);
    setShelfData(null);
    setCellData(null);
    const map2D = getMap2D();
    map2D.mapWorker.stopQuery();
    map2D.mapRender.clearSelects();
  };

  // 货架选中
  const shelfSelect = (value: string) => {
    if (!value) return;
    setShelfCode(value);
    const map2D = getMap2D();
    map2D.mapWorker.reqQuery({ layer: "shelf", code: value });
    map2D.mapRender.trigger("click", { shelf: [value] });
    map2D.mapRender.setEleCenter({ layer: "shelf", code: value });
  };

  // 货架模糊查询
  const searchResult = (query: string) => {
    if (!props.isCurrent) return;
    if (query.length < 1) setShelfResults([]);
    else {
      _$utils
        .reqGet("/athena/shelf/queryShelfCodes", { info: query })
        .then(res => {
          const queryList = res.data || [];
          let result = queryList.map((item: any) => {
            return { value: item.toString() };
          });
          setShelfResults(result.length ? result : []);
        })
        .catch(e => {
          setShelfResults([]);
        });
    }
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component"
    >
      <AutoComplete
        style={{ width: "100%" }}
        placeholder={t("lang.rms.fed.pleaseEnterTheShelfID")}
        allowClear={true}
        value={shelfCode}
        options={shelfResults}
        onSearch={searchResult}
        onChange={(value: string) => setShelfCode(value)}
        onSelect={shelfSelect}
        onClear={() => {
          setOperation(undefined);
          clearShelf();
        }}
      />

      <div className="component-btn-group">
        {checkBtn("MonitorShelfUpdate") && (
          <Button type="primary" block onClick={() => controlHandler("update")}>
            {t("lang.rms.fed.update")}
          </Button>
        )}
        {checkBtn("MonitorShelfMove") && (
          <Button type="primary" block onClick={() => controlHandler("move")}>
            {t("lang.rms.fed.move")}
          </Button>
        )}
        {checkBtn("MonitorShelfLocking") && (
          <Button type="primary" block onClick={() => controlHandler("lock")}>
            {t("lang.rms.fed.lockShelf")}
          </Button>
        )}
        {checkBtn("MonitorShelfRelieve") && (
          <Button type="primary" block onClick={() => controlHandler("unlock")}>
            {t("lang.rms.fed.unlockShelf")}
          </Button>
        )}
        {checkBtn("MonitorShelfUpdateAngel") && (
          <Button type="primary" block onClick={() => controlHandler("updateAngel")}>
            {t("lang.rms.fed.updateShelfAngle")}
          </Button>
        )}
        {checkBtn("MonitorShelfReturnPlacement") && (
          <Button type="primary" block onClick={() => controlHandler("placement")}>
            {t("lang.rms.fed.shelfReturnPlacement")}
          </Button>
        )}
      </div>

      <ShelfUpdate
        visible={operation === "update"}
        radAngle={shelfData?.radAngle || 0}
        shelfCode={shelfData?.shelfCode || null}
        cellData={cellData}
        onCancel={() => setOperation(undefined)}
      />

      <ShelfMove
        visible={operation === "move"}
        shelfCode={shelfData?.shelfCode || null}
        cellData={cellData}
        onCancel={() => setOperation(undefined)}
      />

      <ShelfUpdateAngle
        visible={operation === "updateAngel"}
        shelfCode={shelfData?.shelfCode || null}
        onCancel={() => setOperation(undefined)}
      />

      <ShelfDetail shelf={shelfData} />
    </div>
  );
}

export default OrderShelf;
