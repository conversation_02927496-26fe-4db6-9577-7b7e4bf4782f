import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 机器人平均充电成功率趋势
 */
export default class RobotChargeLine extends Chart {
  /**
   * 初始化图表 - 机器人平均充电成功率趋势
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('line', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "机器人平均充电成功率趋势";
    
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'elDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        option: {
          type: "datetimerange"
        }
      },
      // 机器人类型
      // https://confluence.geekplus.cc/pages/viewpage.action?pageId=177542368
      // athena/map/version/findRobotType
    ]
  }

  async request(params) {
    const date = params?.date || [];
    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 7;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);

    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stat/charger/sumByDayList', {
      startTime,
      endTime,
      statType: 'ROBOT_CHARGE_SUCCESS_PROPORTION',
      ...params
    })
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const xAxisData = data.xAxis || [];
    const seriesData = data.day2Data?.['-1'] || [];
      return parseEchartOption({
        title: { text: this.title || '' },
        xAxis: { type: 'category', data: xAxisData.map(item => {
          return $utils.Tools.formatDate(item, "yyyy-MM-dd")
        }) },
        grid: {
          left: 50,
          right: 20,
          bottom: 30,
          top: 50
        },
        yAxis: { type: 'value' },
        tooltip: { show: true, trigger: 'axis' },
        series: [{ data: seriesData, type: 'line' }]
      })
    }
}