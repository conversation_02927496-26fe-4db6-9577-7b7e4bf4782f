/* ! <AUTHOR> at 2023/05/19 */

type typeDataMap = { [propName: string]: { rootType: string; type: string; data: any[] } };

class FilterData {
  private static StopAreas: any[] = []; // 急停区域数据
  private static TypeDataMap: typeDataMap = {};
  private static TreeData: any[] = [];
  private static defaultKey:string = "";
  private static SpeedLimitAreas :any[]=[]//限速区

  getTreeData() {
    return FilterData.TreeData;
  }

  getDefaultKey() {
    return FilterData.defaultKey;
  }

  getDataMap() {
    return FilterData.TypeDataMap;
  }

  getStopAreas() {
    return FilterData.StopAreas;
  }
  
  getSpeedLimitAreas() {
    return FilterData.SpeedLimitAreas;
  }

  setData(data: any[]) {
    let fastData: any[] = [];
    let defaultKey = "";
    data.forEach((item: any) => {
      const rootType = item.type;
      if (!defaultKey) defaultKey = rootType;

      const root: any = {
        title: item.i18,
        value: rootType,
        key: rootType,
        selectable: false,
        children: this.getRootChildren(item?.data || [], rootType),
      };

      fastData.push(root);
    });

    FilterData.TreeData = fastData;
    FilterData.defaultKey = defaultKey;
  }

  private setStopArea(stopAreaData: any[]) {
    let stopAreas: any[] = [];
    stopAreaData.forEach((item: any) => {
      stopAreas.push({
        floorId: item.extraProperties?.floorId,
        areaId: item.extraProperties?.areaId,
        areaType: item.extraProperties?.areaType,
        cellCodes: item.functionalArea,
      });
    });
    FilterData.StopAreas = stopAreas;
  }
  private setSpeedLimitArea (speedLimitAreaData: any[]) {
    let speedLimitAreas: any[] = [];
    speedLimitAreaData.forEach((item: any) => {
      speedLimitAreas.push({
        floorId: item.extraProperties?.floorId,
        areaId: item.extraProperties?.areaId,
        areaType: item.extraProperties?.areaType,
        cellCodes: item.functionalArea,
      });
    });
    FilterData.SpeedLimitAreas = speedLimitAreas;
  }

  private getRootChildren(rootChildren: any[], rootType: string) {
    let children: any[] = [];

    rootChildren.forEach((level2: any) => {
      const data = level2.data || [];
      const type = level2.type;

      FilterData.TypeDataMap[type] = { rootType, type, data };

      let childrenItem: any = {
        title: level2.i18,
        value: type,
        key: type,
        isLeaf: true,
      };

      if (rootType == "WAREHOUSE_AREA" && data.length) {
        childrenItem.isLeaf = false;
        childrenItem.extraText = `(${data.length})`;
        childrenItem.children = this.getAreaChildren(data, type, rootType);

        if (type === "STOP") this.setStopArea(data);
        if (type === "REAL_TIME_SPEED_LIMIT_AREA") this.setSpeedLimitArea(data);
      }

      children.push(childrenItem);
    });

    return children;
  }

  private getAreaChildren(areaData: any[], areaType: string, rootType: string) {
    let children: any[] = [];
    areaData.forEach((level3: any) => {
      const areaId = level3?.extraProperties?.areaId;
      if (!areaId) {
        const { warn } = console;
        warn("这个快速查找区域数据没有给areaId，不渲染，不处理", level3);
        return;
      }

      const key = `${areaType}_${areaId}`;
      FilterData.TypeDataMap[key] = { rootType, type: areaType, data: level3 };

      let childrenItem: any = {
        title: areaType,
        extraText: areaId,
        value: key,
        key,
        isLeaf: true,
      };

      children.push(childrenItem);
    });

    return children;
  }
}

export default new FilterData();
