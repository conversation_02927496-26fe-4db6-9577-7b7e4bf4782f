/* ! <AUTHOR> at 2022/08/23 */
import "antd/dist/antd.min.css";
import "../less/common/index.less";
import "../less/root.less";

import "../lang/i18n";
import "../utils/utils";
import { $eventBus } from "../singleton";
import ReactDom from "react-dom/client";
import Root from "../views/root";

let timer: any = null;
function iframeInit() {
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
  if (_$utils.__isMapDomMounted) {
    $eventBus.emit("iframeCreateMap");
  } else {
    timer = setTimeout(iframeInit, 17);
  }
}
window.addEventListener("message", function (event) {
  const msg = event.data || {};
  switch (msg.type) {
    case "onloadMonitor2d":
      CommonGeekMapColor = msg.mapColor || {};
      CommonGeekMapSetting = msg.mapSetting || {};
      iframeInit();
      break;
    case "langChange":
      $eventBus.emit("iframeLangChange", { lang: msg.localLang });
      break;
  }
});
const hideRightPanel = _$utils.getUrlParameter("rightPanel");
const isIframe = _$utils.getUrlParameter("iframe") || "";
if (isIframe === "rms" || (__rms_env_conf.isPro && isIframe == "test") || !__rms_env_conf.isPro) {
  const $map: HTMLElement = document.getElementById("root") as HTMLElement;
  ReactDom.createRoot($map).render(<Root hideRightPanel={!!hideRightPanel} isIframe={isIframe} />);
}
