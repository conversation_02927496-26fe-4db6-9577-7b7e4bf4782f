import __WebSocket from "./websockt";
import md5 from "js-md5";
import { v4 as uuidv4 } from "uuid";

class MapWorker {
  constructor(url, dispatch) {
    this.floorIds = [];
    this.cellCodes = [];
    this.WebWorker = new __WebSocket(url, dispatch);
  }

  /** 请求初始化地图 */
  reqFloorMap(floorIds = []) {
    this.floorIds = floorIds;
    const msg = "MapInitRequestMsg";
    const params = this.__formatWsData(msg, {
      floorIds,
      clientFor3D: true,
    });
    this.WebWorker.reqFloorMap(params);
  }

  /**
   * 请求Update地图信息
   */
  reqUpdateMap() {
    const floorIds = this.floorIds;
    const msg = "MapUpdateRequestMsg";
    const params = this.__formatWsData(msg, {
      floorIds,
      robotIds: [],
      shelfCodes: [],
      rackCodes: [],
      cellCodes: this.cellCodes,
      clientFor3D: true,
    });
    this.WebWorker.reqUpdateMap(params);
  }

  /**
   * 请求获取 切换楼层 数据
   * @param floorId
   */
  reqChangeFloor(floorId) {
    if (!floorId) throw new Error(`没有传floorId:${floorId}`);
    this.cellCodes = [];
    this.floorIds = floorId;
    const msg = "InitRequestMsg";
    const params = this.__formatWsData(msg, {
      floorIds: floorId,
      clientFor3D: true,
    });
    this.WebWorker.reqChangeFloor(params);
  }

  setCellCodes(codes) {
    this.cellCodes = codes || [];
  }

  removeCellCode(code) {
    const index = this.cellCodes.findIndex(i => String(i) === String(code));
    !!~index && this.cellCodes.splice(index, 1);
  }

  destory() {
    this.WebWorker && this.WebWorker.destroy();
    this.shelfCodes = [];
    this.floorIds = [];
    this.WebWorker = null;
  }

  __formatWsData(wsMsg, body) {
    let data = {
      id: "GEEK",
      msgType: wsMsg,
      request: {
        header: {
          requestId: uuidv4(),
          clientCode: "GEEK",
          warehouseCode: "GEEK",
        },
        body,
      },
    };

    let config = null,
      md5Switch = false,
      result = JSON.stringify(data);
    if ($utils && $utils.Data) {
      config = $utils.Data.getRMSConfig() || {};
    }
    if (config) md5Switch = config.md5Switch || false;
    if (md5Switch) return `${result}@@@${md5(result + "signature#gk")}`;
    else return result;
  }
}

export default MapWorker;
