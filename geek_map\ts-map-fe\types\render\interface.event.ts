/* ! <AUTHOR> at 2023/04/19 */

namespace MEvents {
  export interface Main {
    _events: Array<string>; // 已经被绑定过的event，可重复绑定

    /**
     * 事件绑定
     * @param eventName
     * @param cb
     */
    on(eventName: string, cb: callback): void;

    /**
     * eventName事件触发
     * @param eventName
     * @param params
     */
    emit(eventName: string, params?: { [propName: string]: any }): void;

    /**
     * eventName事件移除
     * @param eventName
     */
    off(eventName: string): void;

    /**
     * 移除全部事件
     */
    destroy(): void;
  }
}
