import Base from "../../core/abstractPlugin";
import { PointerLockControls } from "three/examples/jsm/controls/PointerLockControls";
import * as THREE from "three";

class PointerLock extends Base {
  constructor(options) {
    super(options);
    this.pointerLockControl = null;
    this._moveForward = false;
    this._moveLeft = false;
    this._moveBackward = false;
    this._moveRight = false;
    // 跟随得初始点
    this.origin = null;
    this._effect = ["selectPlugin", "hoverPlugin", "selectMapCellPlugin"];
    this._isAuto = false; // boolean 自动跟随
    this._velocity = new THREE.Vector3();
    this._direction = new THREE.Vector3();
    this.PluginName = "pointerLockPlugin";
  }
  set effectPlugins(value) {
    this._effect = this._effect.concat(value);
  }
  set autoFollow(value = false) {
    this._isAuto = value;
  }
  activated() {
    const that = this;
    this._handlePlugin();
    this._control(false);
    this._resetCamera();
    if (!this.pointerLockControl) {
      // todo:视角转化动画
      const { camera, $dom, scene } = this.Map3d;
      this.pointerLockControl = new PointerLockControls(camera.get(), $dom);
      const obj = this.pointerLockControl.getObject();
      scene.add(obj);
    }
    this.pointerLockControl.lock();
    this.EventInstance.add("PointerLock", {
      keydownHandle(event) {
        switch (event.code) {
          case "ArrowUp":
          case "KeyW":
            that._moveForward = true;
            break;

          case "ArrowLeft":
          case "KeyA":
            that._moveLeft = true;
            break;

          case "ArrowDown":
          case "KeyS":
            that._moveBackward = true;
            break;

          case "ArrowRight":
          case "KeyD":
            that._moveRight = true;
            break;

          default:
            break;
        }
      },
      keyupHandle(event) {
        switch (event.code) {
          case "ArrowUp":
          case "KeyW":
            that._moveForward = false;
            break;

          case "ArrowLeft":
          case "KeyA":
            that._moveLeft = false;
            break;

          case "ArrowDown":
          case "KeyS":
            that._moveBackward = false;
            break;

          case "ArrowRight":
          case "KeyD":
            that._moveRight = false;
            break;

          default:
            break;
        }
      },
    });
    this.Map3d.ticker.add(this._handleMove.bind(this));
    this.pointerLockControl.addEventListener("unlock", () => this._cleanPointLock());
  }
  deactivated() {
    this._cleanPointLock();
  }
  destroyed() {
    this._cleanPointLock();
  }
  _handlePlugin(status) {
    const effect = this._effect;
    !status ? this.Map3d.disabledPlugin(effect) : this.Map3d.enablePlugin(effect);
  }
  _control(flag) {
    this.Map3d.OrbitControls && (this.Map3d.OrbitControls.enabled = flag);
  }
  _resetCamera() {
    const floorInfo = this.Map3d.scene.getObjectByName("floorGeo");
    const { left, bottom, right, top } = floorInfo.userData.floorInfo || { left: 0, bottom: 0 };
    // this.Map3d.camera.position.set(left, 2, bottom);
    // this.Map3d.camera.lookAt(right, 0, -top);
    this.Map3d.camera.flyTo([left, 2, bottom]);
  }
  _cleanPointLock() {
    if (this.pointerLockControl) {
      this.pointerLockControl.dispose();
      this.pointerLockControl = null;
      this.Map3d.ticker.remove(this._handleMove.bind(this));
    }
    this.EventInstance.off("PointerLock");
    this._control(true);
    this._handlePlugin(true);
    this.Map3d.resetCamera();
  }
  _handleMove() {
    if (!this.pointerLockControl.isLocked) return;
    const deltaTime = this.Map3d.ticker.deltaTime;
    // console.log("test-velocity", this._velocity);
    // console.log("test-velocity-0", deltaTime);
    this._velocity.x -= this._velocity.x * 10 * deltaTime;
    this._velocity.z -= this._velocity.z * 10 * deltaTime;
    // console.log("test-velocity-1", this._velocity);
    this._direction.z = Number(this._moveForward) - Number(this._moveBackward);
    this._direction.x = Number(this._moveRight) - Number(this._moveLeft);
    if (this._moveForward || this._moveBackward)
      this._velocity.z -= this._direction.z * 200 * deltaTime;
    if (this._moveLeft || this._moveRight) this._velocity.x -= this._direction.x * 200 * deltaTime;
    // console.log("test-velocity-2", this._velocity);
    this.pointerLockControl.moveRight(-this._velocity.x * deltaTime);
    this.pointerLockControl.moveForward(-this._velocity.z * deltaTime);
  }
}

export default PointerLock;
