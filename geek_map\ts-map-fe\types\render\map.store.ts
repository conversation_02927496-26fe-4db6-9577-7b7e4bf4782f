/* ! <AUTHOR> at 2023/04/20 */

namespace MRender {
  /**
   * 地图位置
   * @param transX x位置
   * @param transY y位置
   * @param scale 缩放比例
   */
  export type mapPosition = {
    transX: number;
    transY: number;
    scale?: number;
  };

  /**
   * render map configs
   * @param fragmentLength 切分计算数量
   * @param initMapPosition 地图初始位置
   * @param isSingleFloor 是否为独立楼层
   * @param showStationPop 是否显示 station pop
   * @param isMultiSelect 是否开启多选
   * @param multiSelectLayers 开启多选的layer
   * @param showTopologicalGraph 初始化时是否显示拓扑线段
   * @param mapAngle 地图角度
   */
  export type renderConfigs = {
    fragmentLength?: number;
    initMapPosition?: mapPosition;
    isSingleFloor?: boolean;
    showStationPop?: boolean;
    isMultiSelect?: boolean;
  };
  /**
   * 地图业务层展示隐藏
   * @param robotTrail 机器人路径 是否展示
   * @param robotOccupy 机器人预占区域 是否展示
   * @param cellHeat 单元格热度 是否展示
   * @param cellLocation 单元格中心点 是否展示
   * @param mapLoad 负载 是否展示
   * @param mapUnload 空载 是否展示
   * @param shelfHeat 货架热度 是否展示
   * @param realtimeObstacle 外部障碍物 是否展示
   */
  export type layerToggleOptions = {
    robotTrail?: boolean;
    robotOccupy?: boolean;
    cellHeat?: boolean;
    cellLocation?: boolean;
    mapLoad?: boolean;
    mapUnload?: boolean;
    shelfHeat?: boolean;
    realtimeObstacle?: boolean;
  };
}
