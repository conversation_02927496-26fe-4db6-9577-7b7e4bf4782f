import { useRobotAnimal } from "../../../utils/model-func";
import globalConfig from "../../../config";
import { addMethods } from "../../../utils/utils";
import * as THREE from "three";
// 货架
class RenderShelf {
  constructor(options) {
    this.isHeatMode = false;
    this.monitor = options.monitor;
    this.dispatch = options.dispatch || {
      renderShelf() {},
    };
    this.robotUUid = {};
  }
  create(shelves) {
    this.monitor.Emitter.on("after:renderModel", this.__afterRenderShelf.bind(this));
    this.__renderShelves(shelves);
  }
  destory() {
    this.monitor.Emitter.off("after:renderModel", this.__afterRenderShelf.bind(this));
    this.robotUUid = {};
  }
  delete(shelves) {
    const { Store, Map3d } = this.monitor;
    const shelfData = Store.getModelData("SHELF");
    let dels = [];
    for (let i = 0, v; (v = shelfData[i++]); ) {
      if (!shelves.includes(v.shelfCode)) continue;
      Map3d.scene.remove(Map3d.modelInstances[v.uuid].model);
      delete Map3d.modelInstances[v.uuid];
      dels.push(v);
    }
    Store.delModelData("SHELF", dels);
  }
  update(shelves) {
    this.__updateShelves(shelves);
  }
  getShelfMesh() {
    const { Store, Map3d } = this.monitor;
    const shelfData = Store.getModelData("SHELF");
    return shelfData.reduce((pre, cur) => {
      const model = Map3d.modelInstances[cur.uuid].model;
      const mesh = model.getObjectByName("shelf_a_lod0");
      if (mesh) {
        mesh.userData.uuid = cur.uuid;
        pre.push(mesh);
      }
      return pre;
    }, []);
  }
  renderShelfHot() {
    const { Store, Map3d } = this.monitor;
    const shelfData = Store.getModelData("SHELF");
    const modelInstances = Map3d.modelInstances;
    const shelfColor = globalConfig.THEME.SHELF_HOT_CONF;
    if (!shelfData) return;
    for (let i = 0, v; (v = shelfData[i++]); ) {
      const modelInstance = modelInstances[v.uuid];
      const score = Math.floor((v.score || 0) / 10);
      modelInstance.model.color = this.isHeatMode ? shelfColor[score] : null;
    }
  }
  __afterRenderShelf({ category, data: models }) {
    if (category !== "SHELF" || !models.length) return;
    // P40- 初始化box  RS - 初始化box
    for (let i = 0, v; (v = models[i++]); ) {
      this.__afterRender(v);
    }
    this.dispatch.renderShelf && this.dispatch.renderShelf();
  }
  __renderShelves(shelves) {
    if (!shelves || !shelves.length) return;
    const { Store, Map3d } = this.monitor;
    const shelfData = Store.getModelData("SHELF");
    const isInit = !shelfData || !shelfData.length;
    const params = [
      shelves,
      {
        category: "SHELF",
        useModelName: "SHELF",
      },
    ];
    isInit ? Map3d.initModel(...params) : Map3d.appendModelData(...params);
  }
  __updateShelves(shelves) {
    const { Store } = this.monitor;
    const shelfData = Store.getModelData("SHELF");
    const robotData = Store.getModelData("ROBOT");
    this.robotUUid = robotData.reduce((pre, cur) => Object.assign(pre, { [cur.id]: cur.uuid }), {});
    let newShelf = shelves;
    for (let i = 0, ov; (ov = shelfData[i++]); ) {
      const index = newShelf.findIndex(a => a.shelfCode === ov.shelfCode);
      if (!~index) continue;
      const nv = newShelf[index];
      this.__shelfMove(ov, nv);
      newShelf.splice(index, 1);
      Store.updateModelData("SHELF", { ...ov, ...nv });
    }
    newShelf.length && this.__renderShelves(newShelf);
  }
  __shelfMove(ov, nv) {
    const { Map3d } = this.monitor;
    const mesh = Map3d.modelInstances[ov.uuid].model;
    const robot = Map3d.modelInstances[this.robotUUid[nv.robotId]]?.model || null;
    // 接触绑定；
    if (!nv.robotId || !~nv.robotId) {
      mesh.removeFromParent();
      mesh.rotation.y = -nv.radAngle * (Math.PI / 180);
      mesh.position.set(nv.location.x, 0, -nv.location.y);
      Map3d.scene.add(mesh);
      return;
    }
    if (!robot) return;
    mesh.rotation.y = -(robot.rotation.y + nv.radAngle * (Math.PI / 180));
    // mesh.userData.curLocation = [0, 0, robot.rotation.y + ov.radAngle * (Math.PI / 180)];
    // useRobotAnimal({ mesh, location: [0, 0, robot.rotation.y + nv.radAngle * (Math.PI / 180)] });
    if (mesh.parent === robot) return;
    mesh.position.set(0, 0, 0);
    robot.add(mesh);
  }
  __afterRender(item) {
    const { Map3d } = this.monitor;
    const model = Map3d.modelInstances[item.uuid].model;
    // hover
    const body = model.getObjectByName("shelf_a_lod0");
    const box3 = new THREE.Box3().setFromObject(body);
    const hover = new THREE.Box3Helper(box3, globalConfig.THEME.HOVER_3D);
    hover.name = "hover";
    hover.visible = false;
    body.parent.add(hover);
    // select
    const select = new THREE.Box3Helper(box3, globalConfig.THEME.SELECTED);
    select.name = "select";
    select.visible = false;
    body.parent.add(select);
    addMethods(model);

    model.rotation.y = -item.radAngle * (Math.PI / 180);
  }
}

export default RenderShelf;
