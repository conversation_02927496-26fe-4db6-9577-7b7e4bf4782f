import {LINE_CAP, LINE_JOIN} from "pixi.js";

export default class Rect {
  static render($el,ops = {x:0,y:0,w:1,h:1}) {
    const {x,y,w,h} = ops
    $el.beginFill(0xff8080)
    $el.alpha = 0.3
    const styleOp = {
      width:0.08,
      color:0xff3333,
      alpha:1,
      alignment:1,
      cap: LINE_CAP.ROUND,
      join: LINE_JOIN.ROUND
    }
    $el.lineStyle(styleOp)
    $el.drawRect(x, y, w, h)
    $el.endFill()
  }
}
