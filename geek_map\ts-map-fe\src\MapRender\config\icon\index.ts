/* ! <AUTHOR> at 2022/07/26 */

import robotIcon from "./robots/robot";
import robotFatIcon from "./robots/robot-fat";
import robotThinIcon from "./robots/robot-thin";
import forkIcon from "./robots/fork";
import shelfIcon from "./shelf/shelf";
import shelfStatusIcon from "./shelf/status";
import deviceIcon from "./device/device";
import elementIcon from "./element/element";
import statusIcon from "./element/status";

const MapIcons: { [key in MRender.iconsNames]: string } = Object.assign(
  robotIcon,
  robotFatIcon,
  robotThinIcon,
  forkIcon,
  shelfIcon,
  shelfStatusIcon,
  deviceIcon,
  elementIcon,
  statusIcon,
);

export default MapIcons;
