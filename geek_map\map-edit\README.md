## 如何使用 map-edit

参考 /packages/views/EditMapIframeTest/index.vue 这个文件是一个 demo

## 参数传递

map-edit 支持 url, localStorage, iframe.postMessage(推荐) 三种方式传递, 支持混用, 但不推荐
混用时优先级: Url < localStorage < iframe.postMessage
并且 url 与 localStorage 的传递仅在初始化时生效, 不支持动态变更

---

url 的参数传递, 适合无任何定制需求, 简单嵌入, 如果使用参数比较多的话不建议使用 url 传递

```html
<iframe src="http://localhost:8080/edit?mapId=1&floorId=1&autoI18n=true" />
```

localStorage 的参数传递, 需要在 url 中定义 localStorage 的位置, 适合定制内容较少的项目

```html
<iframe src="http://localhost:8080/edit?localStorageId=editOption" />
<script>
  localStorage.setItem("editOption", { mapId: 1, floorId: 1, autoI18n: true });
</script>
```

iframe.postMessage 的参数传递, 可以对 map-edit 进行深度定制的展示

```html
<iframe src="http://localhost:8080/edit?localStorageId=editOption" />
<script>
  iframe.onload = () => {
    iframe.contentWindow.postMessage(
      {
        type: "option",
        body: {
          mapId: "1",
          floorId: "5",
          autoI18n: true,
        },
      },
      "*",
    );
  };
</script>
```

## 国际化

map-edit 默认不请求国际化接口, 需要使用 map-edit 的项目主动传入国际化数据 languageData
如果需要开启默认的国际化请求, 可以传入 autoI18n=true, 默认请求 rms 的国际化内容

# postMessage 事件回调

ready 地图加载完毕
loadFail 地图加载失败
event 用户传递的自定义事件

# 支持的参数

mapId 必填
floorId 必填
language 语言, 默认中文
autoI18n 是否自动获取国际化数据, 默认 false
sessionId 如果有权限, 需要传入接口需要的 sessionId
headerType 请求时 sessionId 的位置, 默认是 Cookie

# 文件结构

.
├── packages # UI 相关
│ ├── api # 接口相关, 所有的接口以及接口声明定义在这里
│ ├── assets # 资源相关, 所有的图标, 字体等资源, 存放在这里
│ ├── components # UI 组件相关, 所有的 UI 组件存放在这里
│ ├── configure # 项目配置, 通常来说修改这里的配置文件即可完成一些小改动
│ ├── hook # 一些常用的公共方法存放在这里
│ ├── router # 路由相关
│ ├── store # 数据相关, 全局数据存放在这里
│ ├── type # 类型声明, 大部分的类型声明在这里可以找到
│ ├── views # 页面相关
│ └── logics # 初始化相关, 初始化文件存放在这里
├── src # MapEdit 组件
|-- ...


全局点位编辑  -
限制终点朝向/货架角度限制  11/28
地图编辑上传超时时间 - 11/28
弹出框优化 - 11/29(需求有问题)
分拣场景迁移 - 11/30-12/2
机器人朝向 - 暂不开发
满充策略优化/途经点(后端待定)
机器人/辅助线(需求有问题)
国际化
-------
4.3.4需求  11/24