import { checkPermission, getMonitorTabPermission, getTabPermission } from "./_permission";

export default (Vue, options) => {
  Vue.prototype.checkPermission = checkPermission;
  Vue.prototype.getMonitorTabPermission = getMonitorTabPermission;
  Vue.prototype.getTabPermission = getTabPermission;

  Vue.prototype.$tips = msg => {
    if (typeof msg === "undefined") {
      msg = "undefined";
    } else {
      msg = msg.toString();
    }

    return $app.$message({
      type: "info",
      message: msg,
      showClose: true,
      duration: 3000,
    });
  };
  Vue.prototype.$warning = (msg, autoDis = true) => {
    if (typeof msg === "undefined") {
      msg = "undefined";
    } else {
      msg = msg.toString();
    }

    return $app.$message({
      type: "warning",
      message: msg,
      showClose: true,
      duration: autoDis ? 3000 : 0,
    });
  };
  Vue.prototype.$success = (msg, autoDis = true) => {
    if (typeof msg === "undefined") {
      msg = $app.$t("lang.rms.api.result.ok");
    } else {
      msg = msg.toString();
    }

    return $app.$message({
      type: "success",
      message: msg,
      showClose: true,
      duration: autoDis ? 3000 : 0,
    });
  };
  Vue.prototype.$error = (msg, autoDis = true) => {
    if (typeof msg === "undefined") {
      msg = "undefined";
    } else {
      msg = msg.toString();
    }

    return $app.$message({
      type: "error",
      message: msg,
      showClose: true,
      duration: autoDis ? 3000 : 0,
    });
  };
  Vue.prototype.$geekConfirm = (msg, options = {}) => {
    let opt = Object.assign(
      {
        confirmText: $app.$t("lang.rms.fed.confirm"),
        cancelText: $app.$t("lang.rms.fed.cancel"),
      },
      options,
    );
    return new Promise((resolve, reject) => {
      $app
        .$confirm(msg, $app.$t("lang.rms.fed.prompt"), {
          confirmButtonText: opt.confirmText,
          cancelButtonText: opt.cancelText,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          type: "warning",
        })
        .then(() => {
          resolve();
        })
        .catch(() => {
          reject();
        });
    });
  };
};
