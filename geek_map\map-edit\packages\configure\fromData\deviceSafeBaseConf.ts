import { useRequired } from "@packages/hook/useRules";
import { NodeAttrEditConf } from "@packages/type/editUiType";

// 反光柱新增的配置内容
export const SAFE_BASE_CONF_FN = (baseTabsRef: any, attrStore: any): NodeAttrEditConf => {
  return {
    name: "safeBase",
    tabTitle: "lang.rms.fed.basis",
    labelWidth: "102px",
    formItem: [
      // 反光柱别名
      {
        prop: "deviceInfoCode",
        label: "lang.rms.map.dmp.deviceCode",
        component: "elInput",
        rules: [useRequired()],
        maxlength: 13,
        showWordLimit: true,
      },
    ],
  };
};
