<template>
  <section class="geek-main">
    <keep-alive :exclude="excludeAlive" max="3">
      <router-view />
    </keep-alive>
  </section>
</template>

<script>
export default {
  name: "GeekMain",
  computed: {},
  data() {
    return {
      excludeAlive: "MapMonitor2D,MapMonitor3D,MapEdit2D,statistics",
    };
  },
};
</script>

<style lang="less" scoped>
.geek-main {
  height: calc(100%);
  width: 100%;
  background: @g-main-bg;
  padding: @g-main-padding;
  overflow: auto;
}
</style>
