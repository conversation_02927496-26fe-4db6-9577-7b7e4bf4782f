/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { StopOutlined, RightCircleOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { Button, Modal } from "antd";
import { getMap2D, checkBtn } from "../../../../singleton";

const { confirm } = Modal;
type PropsOrderData = {
  isCurrent: boolean;
  mapConfig: MWorker.mapConfig;
};
function OrderInstruction(props: PropsOrderData) {
  const { t } = useTranslation();
  const [systemRunning, setSystemRunning] = useState(undefined);
  const [sleepRobotsRunning, setSleepRobotsRunning] = useState(undefined); // 一键休眠按钮状态
  const [awakenRobotsRunning, setAwakenRobotsRunning] = useState(undefined); // 一键唤醒按钮状态
  const [shelfTurningRunning, setShelfTurningRunning] = useState(undefined); // 一键转面按钮状态
  const [gatherRunning, setGatherRunning] = useState(undefined); // 一键集合按钮状态
  const [shutdownRunning, setShutdownRunning] = useState(undefined); // 一键关机按钮状态
  const [scanRunning, setScanRunning] = useState(undefined); // 一键扫描状态
  const [shelfReturnRunning, setShelfReturnRunning] = useState(undefined); // 一键归还货架按钮状态

  const controlHandler = (name: string) => {
    let instruction = "";
    switch (name) {
      case "sleepRobotsRunning": // 一键休眠
        instruction = sleepRobotsRunning ? "STOP_SLEEP_ROBOTS" : "SLEEP_ROBOTS";
        break;
      case "awakenRobotsRunning": // 一键唤醒
        instruction = awakenRobotsRunning ? "STOP_AWAKEN_ROBOTS" : "AWAKEN_ROBOTS";
        break;
      case "shelfTurningRunning": // 一键转面
        instruction = shelfTurningRunning ? "STOP_TURN_SHELF" : "TURN_SHELF";
        break;
      case "gatherRunning": // 一键集合
        instruction = gatherRunning ? "STOP_CALL_ROBOTS_TO_LOGOUT" : "CALL_ROBOTS_TO_LOGOUT";
        break;
      case "shutdownRunning": // 一键关机
        instruction = shutdownRunning ? "STOP_SHUTDOWN_ROBOTS" : "SHUTDOWN_ROBOTS";
        break;
      case "scanRunning": // 一键扫描
        instruction = scanRunning ? "STOP_SCAN_CELL" : "SCAN_CELL";
        break;
      case "shelfReturnRunning": // 一键归还货架
        instruction = shelfReturnRunning ? "STOP_SHELF_RETURN" : "SHELF_RETURN";
        break;
    }

    let warningText = t("lang.rms.fed.confirmTheOperation");
    switch (instruction) {
      case "TURN_SHELF":
        warningText = t("lang.rms.fed.clickTurningMsg");
        break;
      case "SHELF_RETURN":
        warningText = t("lang.rms.api.result.warehouse.returnShelf");
        break;
    }

    confirm({
      icon: <ExclamationCircleOutlined />,
      content: warningText,
      onOk() {
        const map2D = getMap2D();
        const reqMsg = "WarehouseInstructionRequestMsg";
        const resMsg = "WarehouseInstructionResponseMsg";
        map2D.mapWorker.reqSocket(reqMsg, { instruction }).then(res => {
          if (res.msgType !== resMsg) return;
          _$utils.wsCmdResponse(res?.body || {});
        });
      },
    });
  };

  // 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;
    const map2D = getMap2D();
    map2D.mapRender.triggerLayers([]);
  }, [props.isCurrent]);

  // 接受ws mapConfig 推送消息
  useEffect(() => {
    if (!props.mapConfig) return;
    const mapConfig = props.mapConfig || {};
    for (let key in mapConfig) {
      let value = mapConfig[key];
      switch (key) {
        case "systemRunning":
          setSystemRunning(value);
          break;
        case "sleepRobotsRunning":
          setSleepRobotsRunning(value);
          break;
        case "awakenRobotsRunning":
          setAwakenRobotsRunning(value);
          break;
        case "shelfTurningRunning":
          setShelfTurningRunning(value);
          break;
        case "gatherRunning":
          setGatherRunning(value);
          break;
        case "shutdownRunning":
          setShutdownRunning(value);
          break;
        case "scanRunning":
          setScanRunning(value);
          break;
        case "shelfReturnRunning":
          setShelfReturnRunning(value);
          break;
      }
    }
  }, [props.mapConfig]);

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component map2d-control-btn-group"
    >
      <div className="panel-right-title">{t("lang.rms.config.group.businessInstruction")}</div>
      {checkBtn("MonitorDormancyOneKey") && (
        <Button // 一键休眠按钮状态
          size="middle"
          type="primary"
          block
          disabled={!systemRunning}
          danger={sleepRobotsRunning}
          icon={sleepRobotsRunning ? <StopOutlined /> : <RightCircleOutlined />}
          onClick={() => controlHandler("sleepRobotsRunning")}
        >
          {sleepRobotsRunning
            ? t("lang.rms.fed.buttonEndOneKeyDormancy")
            : t("lang.rms.fed.buttonOneKeyDormancy")}
        </Button>
      )}
      {checkBtn("MonitorAwakenOneKey") && (
        <Button // 一键唤醒按钮状态
          size="middle"
          type="primary"
          block
          disabled={!systemRunning}
          danger={awakenRobotsRunning}
          icon={awakenRobotsRunning ? <StopOutlined /> : <RightCircleOutlined />}
          onClick={() => controlHandler("awakenRobotsRunning")}
        >
          {awakenRobotsRunning
            ? t("lang.rms.fed.buttonEndOneKeyWakened")
            : t("lang.rms.fed.buttonOneKeyWakened")}
        </Button>
      )}
      {checkBtn("MonitorTurningSurfaceOneKey") && (
        <Button // 一键转面按钮状态
          size="middle"
          type="primary"
          block
          disabled={!systemRunning}
          danger={shelfTurningRunning}
          icon={shelfTurningRunning ? <StopOutlined /> : <RightCircleOutlined />}
          onClick={() => controlHandler("shelfTurningRunning")}
        >
          {shelfTurningRunning
            ? t("lang.rms.fed.stopShelfTurningRunning")
            : t("lang.rms.fed.shelfTurningRunning")}
        </Button>
      )}
      {checkBtn("MonitorTogetherOneKey") && (
        <Button // 一键集合按钮状态
          size="middle"
          type="primary"
          block
          disabled={!systemRunning}
          danger={gatherRunning}
          icon={gatherRunning ? <StopOutlined /> : <RightCircleOutlined />}
          onClick={() => controlHandler("gatherRunning")}
        >
          {gatherRunning
            ? t("lang.rms.fed.stopCallForRobotMuster")
            : t("lang.rms.fed.oneButtonCallForRobotMuster")}
        </Button>
      )}
      {checkBtn("MonitorShutDownOneKey") && (
        <Button // 一键关机按钮状态
          size="middle"
          type="primary"
          block
          disabled={!systemRunning}
          danger={shutdownRunning}
          icon={shutdownRunning ? <StopOutlined /> : <RightCircleOutlined />}
          onClick={() => controlHandler("shutdownRunning")}
        >
          {shutdownRunning
            ? t("lang.rms.fed.stopCallShutdown")
            : t("lang.rms.fed.oneButtonShutdown")}
        </Button>
      )}
      {checkBtn("MonitorScanOneKey") && (
        <Button // 一键扫描状态
          size="middle"
          type="primary"
          block
          disabled={!systemRunning}
          danger={scanRunning}
          icon={scanRunning ? <StopOutlined /> : <RightCircleOutlined />}
          onClick={() => controlHandler("scanRunning")}
        >
          {scanRunning ? t("lang.rms.fed.stopScanning") : t("lang.rms.fed.oneClickScanning")}
        </Button>
      )}
      {checkBtn("MonitorReturnShelfOneKey") && (
        <Button // 一键归还货架按钮状态
          size="middle"
          type="primary"
          block
          disabled={!systemRunning}
          danger={shelfReturnRunning}
          icon={shelfReturnRunning ? <StopOutlined /> : <RightCircleOutlined />}
          onClick={() => controlHandler("shelfReturnRunning")}
        >
          {shelfReturnRunning
            ? t("lang.rms.fed.stopReturnTheShelf")
            : t("lang.rms.fed.oneButtonReturnShelves")}
        </Button>
      )}
    </div>
  );
}

export default OrderInstruction;
