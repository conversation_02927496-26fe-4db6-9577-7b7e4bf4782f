import {toFixed} from "../../utils/utils";
import Control from '../../control/Control'
import { lineStyle } from "../../config";
const {LOAD,UNLOAD} = lineStyle
export default class SegmentArrow {
  static render($el,{color}){
    const {loadDirs,unloadDirs} = $el
    if(!$el.currentPath) return;
    const points = $el.currentPath.points
    const paths = []
    for(let i = 0,len = points.length; i < len; i+=2) {
      paths.push({x:points[i],y:points[i+1]})
    }
    const totalIndex = paths.length - 1
    //中间点坐标Index
    const midIndex = Math.floor(totalIndex / 2)
    const unloadIndex = Math.floor(midIndex / 2) - 1
    const loadIndex = Math.floor(midIndex / 2) + 1
    //展示的空负载类型
    const dirType = Control.dirType
    //负载
    if(loadDirs && dirType !== 'unload'){
      //正方向
      if([1,2].includes(loadDirs)){
        const index =  totalIndex - loadIndex
        const ops = {
          centerIndex: index,
          paths,
          color:LOAD
        }
        this._renderLoadArrowByIndex($el,ops)
      }
      //负方向
      if([3,2].includes(loadDirs)){
        const ops = {
          centerIndex: loadIndex,
          paths,
          color:LOAD,
          forward: false,
        }
        this._renderLoadArrowByIndex($el,ops)
      }
    }
    //空载
    if(unloadDirs && dirType !== 'load'){
      //正方向
      if([1,2].includes(unloadDirs)){
        const index =  totalIndex - unloadIndex
        const ops = {
          centerIndex: index,
          paths,
          color:UNLOAD
        }
        this._renderUnloadArrowByIndex($el,ops)
      }
      //负方向
      if([3,2].includes(unloadDirs)){
        const ops = {
          centerIndex: unloadIndex,
          paths,
          color:UNLOAD,
          forward: false,
        }
        this._renderUnloadArrowByIndex($el,ops)
      }
    }

  }
  //渲染负载箭头
  static _renderLoadArrowByIndex($el,ops = {}){
    const {centerIndex,paths,color,forward = true} = ops
    const center = paths[centerIndex]
    const {x:x1,y:y1} = paths[centerIndex - 1]
    const {x:x2,y:y2} = paths[centerIndex + 1]
    // const {x:x1,y:y1} = paths[centerIndex]
    // const {x:x2,y:y2} = paths[centerIndex + 1]
    let radian = Math.atan2(y2 - y1, x2 - x1);
    radian = forward ? radian : radian + Math.PI
    // const w = 3.6,h = 6.5;
    // const {x,y} = center
    // const tx = toFixed(w * Math.sin(radian))
    // const ty = toFixed(w * Math.cos(radian))
    // const endPoint = [x + h * Math.cos(radian),y + h * Math.sin(radian)]
    // $el.beginFill(color)
    // $el.lineStyle(0);
    // $el.moveTo(x,y)
    // $el.lineTo(toFixed(x - tx), toFixed(y + ty)); // 这里箭头的大小依需求而定
    // $el.lineTo(endPoint[0],endPoint[1]);
    // $el.lineTo(toFixed(x + tx), toFixed(y - ty));
    // $el.lineTo(x,y);
    // $el.endFill()
    const w = 2.4,h = 3.3;
    const {x,y} = center
    const tx = toFixed(w * Math.sin(radian))
    const ty = toFixed(w * Math.cos(radian))
    const [endX,endY] = [toFixed(x + h * Math.cos(radian)),toFixed(y + h * Math.sin(radian))]
    const startX = x + (endX - x) / 2,startY = y + (endY - y) / 2
    $el.beginFill(color)
    $el.lineStyle(0);
    $el.moveTo(startX,startY)
    $el.lineTo(toFixed(x - tx), toFixed(y + ty)); // 这里箭头的大小依需求而定
    $el.lineTo(endX,endY);
    $el.lineTo(toFixed(x + tx), toFixed(y - ty));
    $el.lineTo(startX,startY);
    $el.endFill()
  }
  //渲染空载箭头
  static _renderUnloadArrowByIndex($el,ops = {}){
    const {centerIndex,paths,color,forward = true} = ops
    const center = paths[centerIndex]
    const {x:x1,y:y1} = paths[centerIndex - 1]
    const {x:x2,y:y2} = paths[centerIndex + 1]
    // const {x:x1,y:y1} = paths[centerIndex]
    // const {x:x2,y:y2} = paths[centerIndex + 1]
    let radian = Math.atan2(y2 - y1, x2 - x1);
    radian = forward ? radian : radian + Math.PI
    const w = 2.4,h = 3.3;
    const {x,y} = center
    const tx = toFixed(w * Math.sin(radian))
    const ty = toFixed(w * Math.cos(radian))
    const [endX,endY] = [toFixed(x + h * Math.cos(radian)),toFixed(y + h * Math.sin(radian))]
    const startX = x + (endX - x) / 2,startY = y + (endY - y) / 2
    $el.beginFill(color)
    $el.lineStyle(0);
    $el.moveTo(startX,startY)
    $el.lineTo(toFixed(x - tx), toFixed(y + ty)); // 这里箭头的大小依需求而定
    $el.lineTo(endX,endY);
    $el.lineTo(toFixed(x + tx), toFixed(y - ty));
    $el.lineTo(startX,startY);
    $el.endFill()
  }
}
