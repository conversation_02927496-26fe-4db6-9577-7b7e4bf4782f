<template>
  <geek-main-structure>
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @selectionChange="handleSelection"
      @page-change="pageChange"
      @row-add="rowAdd"
      @rows-del="handleDeleteRobotControl"
      @row-edit="rowEdit"
      @row-application="rowApplication"
      style="margin-top: 10px"
    />

    <el-dialog
      :title="$t('lang.rms.fed.addSoftware')"
      :visible.sync="dialogFormVisible"
      width="640px"
      :before-close="handleClose"
    >
      <el-form ref="addForm" :model="addForm" label-width="80px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.edition')" prop="version">
              <!-- :placeholder="x1" -->
              <el-input v-model="addForm.version" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.type')" prop="type">
              <el-select v-model="addForm.type">
                <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('lang.rms.fed.describe')" prop="descr">
              <!-- :placeholder="x2" -->
              <el-input v-model="addForm.descr" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-upload
        ref="upload"
        class="upload"
        action="/athena/robot/software/upload"
        :limit="1"
        :file-list="fileList"
        :on-success="handleUploadSuccess"
        :auto-upload="false"
      >
        <el-button slot="trigger" size="small" type="primary">
          {{ $t("lang.rms.fed.chooseAFile") }}
        </el-button>
        <el-button style="margin-left: 10px" size="small" type="success" @click="submitUpload">
          {{ $t("lang.rms.fed.upload") }}
        </el-button>
        <div slot="tip" class="el-upload__tip">{{ $t("lang.rms.fed.uploadOneFile") }}</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="clearAddForm">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button
          type="primary"
          :disabled="addForm.version == '' || !uploadInfo.newName"
          @click="handleSubmitRobotControl"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="$t('lang.rms.fed.chargerId') + ',' + $t('lang.rms.fed.multipleNumbersAreSeparatedByComma')"
      :visible.sync="dialogRobotVisible"
    >
      <el-form :model="formRobot">
        <el-form-item>
          <el-input v-model="formRobot.ids" autocomplete="off" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogRobotVisible = false">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button type="primary" @click="handleSubmitActive">
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </geek-main-structure>
</template>

<script>
export default {
  name: "SoftwareControl",
  data() {
    return {
      formRobot: {
        softwareId: "",
        ids: undefined,
      },
      addForm: {
        version: "",
        type: 0,
        descr: "",
      },
      isEdit: false,
      fileList: [],
      dialogFormVisible: false,
      dialogRobotVisible: false,

      uploadInfo: {},
      statusList: [
        {
          value: 0,
          label: this.$t("lang.rms.fed.masterControl"),
        },
      ],
      robotHeaderList: [
        {
          prop: "version",
          label: this.$t("lang.rms.fed.edition"),
        },
        {
          prop: "type",
          label: this.$t("lang.rms.fed.type"),
        },
        {
          prop: "oldName",
          label: this.$t("lang.rms.fed.fileName"),
        },
        {
          prop: "path",
          label: this.$t("lang.rms.fed.storagePath"),
        },
        {
          prop: "createTime",
          label: this.$t("lang.rms.fed.creationTime"),
        },
        {
          prop: "updateTime",
          label: this.$t("lang.rms.fed.updateTime"),
        },
      ],

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {
          selection: true,
        },
        actions: [
          {
            label: "lang.rms.fed.newlyAdded",
            type: "primary",
            handler: "row-add",
          },
          {
            label: "lang.rms.fed.delete",
            type: "danger",
            handler: "rows-del",
          },
        ],
        columns: [
          { label: "lang.rms.fed.edition", prop: "version" },
          {
            label: "lang.rms.fed.type",
            prop: "type",
            formatter: this.statusformatter,
          },
          { label: "lang.rms.fed.fileName", prop: "oldName" },
          { label: "lang.rms.fed.storagePath", prop: "path" },

          { label: "lang.rms.fed.creationTime", prop: "createTime", formatter: this.timeformatter },
          { label: "lang.rms.fed.updateTime", prop: "updateTime", formatter: this.timeformatter },
          {
            label: "lang.rms.fed.listOperation",
            width: "170",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.edit",
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.application",
                handler: "row-application",
              },
            ],
          },
        ],
      },
      tableSelection: [],
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.dialogFormVisible = true;
    },
    rowEdit(data) {
      this.dialogFormVisible = true;
      this.isEdit = true;
      this.addForm = {
        version: data.version || "",
        type: data.type || 0,
        descr: data.descr || "",
      };
      this.uploadInfo = data;
      this.fileList = [
        {
          name: data.newName || "",
          url: data.path,
        },
      ];
    },
    rowApplication(data) {
      this.dialogRobotVisible = true;
      this.formRobot.softwareId = data.id;
    },
    handleSelection(selections) {
      this.tableSelection = selections.map(item => item.id);
    },
    handleSubmitActive() {
      if (!this.formRobot.ids) {
        this.$message.error(this.$t("lang.rms.fed.pleaseEnterChargerId"));
        return false;
      }
      this.dialogRobotVisible = false;
      $req.post("/athena/robot/software/active", {
        softwareId: this.formRobot.softwareId,
        chargeIds: this.formRobot.ids.split(","),
      });
    },

    handleDeleteRobotControl() {
      if (this.tableSelection.length > 0) {
        $req.post("/athena/robot/software/delete", this.tableSelection).then(res => {
          this.getTableList();
        });
      } else {
        this.$tips(this.$t("lang.rms.fed.pleaseSelectOperateVersion"));
      }
    },
    handleSubmitRobotControl() {
      const self = this;
      this.uploadInfo.category = 1;
      const data = Object.assign({}, this.uploadInfo, this.addForm);
      if (this.isEdit) {
        $req.post("/athena/robot/software/update", data).then(res => {
          self.getTableList();
          self.clearAddForm();
          self.isEdit = false;
        });
      } else {
        $req.post("/athena/robot/software/add", data).then(res => {
          self.getTableList();
          self.clearAddForm();
        });
      }
    },
    clearAddForm() {
      this.dialogFormVisible = false;
      this.$refs.upload.clearFiles();
      this.addForm = {
        version: "",
        type: 0,
        descr: "",
      };

      this.uploadInfo = {};
      this.$refs["addForm"].resetFields();
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleUploadSuccess(res) {
      this.uploadInfo = res.data;
    },
    statusformatter(row, column) {
      return this.statusList[row[column["property"]]].label;
    },
    timeformatter(row, column) {
      return new Date(row[column["property"]])?.toLocaleString();
    },

    handleClose() {
      this.clearAddForm();
    },

    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    getTableList() {
      $req
        .get("/athena/robot/software/findAll", {
          pageSize: this.tablePage.pageSize,
          currentPage: this.tablePage.currentPage,
          category: 1,
        })
        .then(res => {
          let result = res.data || {};
          this.tableData = result.recordList;
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: result.currentPage || 1,
            pageCount: result.pageCount || 0,
          });
        });
    },
  },
};
</script>

<style scoped>
.el-select {
  width: 100%;
}
.btnwarp {
  padding: 0 0 30px;
}
.upload {
  width: 300px;
}
</style>
