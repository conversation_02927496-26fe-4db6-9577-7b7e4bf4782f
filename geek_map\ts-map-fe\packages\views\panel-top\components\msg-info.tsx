import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Tooltip } from "antd";
import { InfoCircleTwoTone, ClockCircleTwoTone, AlertOutlined } from "@ant-design/icons";
import { $audio, $eventBus } from "../../../singleton";

type PropsType = {
  messageCount: { backLogCount: number; notificationCount: number };
};
function MsgInfo(props: PropsType) {
  const { t } = useTranslation();
  const [playVoice, setPlayVoice] = useState(false);
  const [robotWarning, setRobotWarning] = useState(false);
  const [msgCount, setMsgCount] = useState(0);
  const [waitCount, setWaitCount] = useState(0);

  // toggle 语音开关
  const handleVoice = () => {
    const flag = !playVoice;
    setPlayVoice(flag);
    if (flag) $audio.play();
    else $audio.pause();
  };

  // 接受ws推送信息
  useEffect(() => {
    const { notificationCount, backLogCount } = props.messageCount || {};
    if (notificationCount) setMsgCount(notificationCount);
    if (backLogCount) setWaitCount(backLogCount);
  }, [props.messageCount]);

  return (
    <ul className="map2d-msg-info">
      <li>
        <span
          className="icon-msg"
          onClick={() => $eventBus.emit("dialogMessageCountShow", { type: "msg" })}
        >
          <InfoCircleTwoTone
            style={{ marginRight: "3px", fontSize: "14px" }}
            twoToneColor="#FF0000"
          />
          {t("lang.rms.fed.buttonMessage")}: {msgCount}
        </span>
      </li>
      <li>
        <span
          className="icon-wait"
          onClick={() => $eventBus.emit("dialogMessageCountShow", { type: "wait" })}
        >
          <ClockCircleTwoTone
            style={{ marginRight: "3px", fontSize: "14px" }}
            twoToneColor="#FF0000"
          />
          {t("lang.rms.fed.buttonToDo")}: {waitCount}
        </span>
      </li>
      <li>
        <Tooltip placement="bottom" title={t("lang.rms.fed.tabMessage")}>
          <AlertOutlined
            style={{ fontSize: 16, color: robotWarning ? "#409eff" : "#444" }}
            onClick={() => {
              $eventBus.emit("dialogRobotWarning", { visible: !robotWarning });
              setRobotWarning(!robotWarning);
            }}
          />
        </Tooltip>
      </li>
      <li>
        <Tooltip placement="bottom" title={t("lang.rms.fed.tabVoicePrompt")}>
          <span
            className="toolbar-menu monitorMap monitor-font-voice-tips"
            style={playVoice ? { color: "#409eff" } : {}}
            onClick={handleVoice}
          />
        </Tooltip>
      </li>
    </ul>
  );
}

export default MsgInfo;
