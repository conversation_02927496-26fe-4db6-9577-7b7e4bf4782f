import { ToolPanelType } from "@packages/type/editUiType";

// 曲线
export const ADD_CURVE: ToolPanelType = {
  option: {
    icon: "map-font-bezier",
    name: "addCurve",
    describe: "lang.rms.fed.addBezier",
    eventName: "map:addBezier",
    group: "topology",
    isSelect: true,
  },
};

// 直线
export const ADD_LINE: ToolPanelType = {
  option: {
    icon: "map-font-direction",
    name: "addLine",
    describe: "lang.rms.web.map.segment.type.line",
    eventName: "map:addLine",
    group: "topology",
    isSelect: true,
  },
};

// 批量直线
export const ADD_LINE_BATCH: ToolPanelType = {
  option: {
    icon: "map-font-a-c",
    name: "addLineBatch",
    describe: "lang.rms.fed.batchSLine",
    group: "topology",
    isSelect: true,
  },
};

// 顺时针弧线
export const ADD_ARC_CLOCKWISE: ToolPanelType = {
  option: {
    icon: "map-font-yuanhu",
    name: "addClockwiseArc",
    describe: "lang.rms.fed.FARC",
    eventName: "map:addBezierByCW",
    group: "topology",
    isSelect: true,
  },
};

// 逆时针弧线
export const ADD_ARC_ANTI_CLOCKWISE: ToolPanelType = {
  option: {
    icon: "map-font-yuanhu-copy",
    name: "addAntiClockwiseArc",
    describe: "lang.rms.fed.BARC",
    eventName: "map:addBezierByCCW",
    group: "topology",
    isSelect: true,
  },
};
