/* ! <AUTHOR> at 2022/09/02 */
import LayerRealtimeObstacle from "./realtime-obstacle";
import LayerKnockArea from "./knock-area";
import LayerStopArea from "./stop-area";
import LayerFilterArea from "./filter-area";
import LayerSpeedLimitArea from "./speed-limit-area";
class LayerArea {
  floorId: floorId;
  private layerRealtimeObstacle: LayerRealtimeObstacle;
  private layerStopArea: LayerStopArea;
  private layerFilterArea: LayerFilterArea;
  private layerKnockArea: LayerKnockArea;
  private layerSpeedLimitArea: LayerSpeedLimitArea;
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.floorId = floor.floorId;
    this.layerRealtimeObstacle = new LayerRealtimeObstacle(mapCore, floor);
    this.layerStopArea = new LayerStopArea(mapCore, floor);
    this.layerFilterArea = new LayerFilterArea(mapCore, floor);
    this.layerKnockArea = new LayerKnockArea(mapCore, floor);
    this.layerSpeedLimitArea = new LayerSpeedLimitArea(mapCore, floor);
  }

  renderAreas(
    type: "realtimeObstacles" | "knockAreas" | "stopAreas" | "filterAreas" | "speedLimitAreas",
    arr: Array<realtimeObstacleData> | Array<knockAreaData> | Array<any>,
  ) {
    switch (type) {
      case "realtimeObstacles":
        this.layerRealtimeObstacle.render(arr as Array<realtimeObstacleData>);
        break;
      case "knockAreas":
        this.layerKnockArea.render(arr as Array<knockAreaData>);
        break;
      case "stopAreas":
        this.layerStopArea.render(arr as Array<any>);
        break;
      case "filterAreas":
        this.layerFilterArea.repaint();
        this.layerFilterArea.render(arr as Array<any>);
        break;
      case "speedLimitAreas":
        this.layerSpeedLimitArea.render(arr as Array<any>);
        break;
    }
  }
  updateAreas(
    type: "realtimeObstacles" | "knockAreas",
    arr: Array<realtimeObstacleData> | Array<knockAreaData>,
  ) {
    switch (type) {
      case "realtimeObstacles":
        this.layerRealtimeObstacle.update(arr as Array<realtimeObstacleData>);
        break;
      case "knockAreas":
        this.layerKnockArea.update(arr as Array<knockAreaData>);
        break;
    }
  }

  toggleRealtimeObstacle(isShow: boolean) {
    this.layerRealtimeObstacle.toggle(isShow);
  }

  getAreaCode(type: string, x: number, y: number) {
    switch (type) {
      case "realtimeObstacle":
        return this.layerRealtimeObstacle.getAreaCode(x, y);
      case "knockArea":
        return this.layerKnockArea.getAreaCode(x, y);
    }
  }

  getContainer(type: string) {
    switch (type) {
      case "realtimeObstacle":
        return this.layerRealtimeObstacle.getContainer();
      case "knockArea":
        return this.layerKnockArea.getContainer();
    }
  }

  repaint(type = "all") {
    switch (type) {
      case "filterAreas":
        this.layerFilterArea.repaint();
      default:
        this.layerRealtimeObstacle.repaint();
        this.layerFilterArea.repaint();
        this.layerKnockArea.repaint();
    }
  }
  destroy(): void {
    this.layerRealtimeObstacle.destroy();
    this.layerRealtimeObstacle = null;
    this.layerStopArea.destroy();
    this.layerStopArea = null;
    this.layerFilterArea.destroy();
    this.layerFilterArea = null;
    this.layerKnockArea.destroy();
    this.layerKnockArea = null;
  }
}
export default LayerArea;
