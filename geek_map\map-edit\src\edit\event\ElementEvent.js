import LayerManager from "../layerManager/LayerManager";
import Mode from "../Mode";
import Selected from "../selected/Selected";
import {pixi2cad,toFixed,createId} from '../utils/utils'
import Control from "../control/Control";
import Event from '../event/Event'
import {defaultConfig} from '../config'
import EventBus from "@/edit/eventBus/EventBus";
export default class ElementEvent {
  static batchAddType = ['OMNI_DIR_CELL']
  static addEvents() {
    const events = {
      clicked:e => {
        Selected.resetAllSelected()
        const layerName = 'CELL'
        const mode = Mode.mode
        const {action,options} = mode
        const {name,isQrNode = false} = options
        const {x,y} = e.world
        const location = pixi2cad({x:toFixed(x),y:toFixed(y)})
        //添加单元格的默认属性
        let addProperties = defaultConfig[name] || defaultConfig['CELL']
        //默认宽高
        // const width = CELL.width,length = CELL.length;
        const {width,length} = addProperties
        //计算startBounds
        const startBounds = {x:toFixed(location.x - length / 2),y:toFixed(location.y - width / 2)}
        const nodeId = createId()
        addProperties = Object.assign({},addProperties,{
          location,
          nodeId,
          mapEditItemId:nodeId,
          id:nodeId,
          cellType:name,
          isQrNode,
          startBounds,
          loadDirs:null,
          unloadDirs:null
        })
        const addOp = {
          id:layerName,
          data: [addProperties],
        }
        if(!this.batchAddType.includes(name)) Mode.resetMode()
        // Mode.resetMode()
        LayerManager.addElements(addOp)
      },
      keydown:e => {
        const {key} = e
        if(key === 'Escape'){
          Mode.resetMode()
        }
      }
    }
    return events
  }
  static onDragStart($el,e) {
    if(!$el.selected || Selected.isMultipleSelected) return;
    Event.isClick = true
    Event.timestamp = Date.now()
    Control.enableDrag(false)
    if(!$el.selected) return;
    if(Event.activeKey !== 'Alt') return $el.dragging = false;
    $el.data = e.data;
    $el.dragging = true;
  }
  static onDragEnd($el,e) {
    Event.isClick = (Date.now() - Event.timestamp) < 300
    $el.dragging = false;
    $el.data = null;
    Control.enableDrag(true)
  }
  static onDragMove($el,e) {
    const {data} = $el
    const newPosition = data.getLocalPosition($el.parent);
    $el.x = newPosition.x;
    $el.y = newPosition.y;
  }
}
