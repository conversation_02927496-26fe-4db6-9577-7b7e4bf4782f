/* ! <AUTHOR> at 2023/04/20 */

type charger = { element: any; options: mChargerData };

class ChargersData implements MRender.MapData {
  private mapData: { [propName: code]: charger } = {};

  setData(code: code, data: charger) {
    this.mapData[code] = data;
  }

  getData(code: code) {
    return this.mapData[code];
  }

  getByFloorId(floorId: floorId): void {}

  delData(code: code) {
    const element = this.mapData[code]?.element;
    element && element.destroy();
    delete this.mapData[code];
  }

  getAll() {
    return this.mapData;
  }

  uninstall() {
    const data = this.mapData;

    let element;
    for (let key in data) {
      element = data[key]?.element;
      element && element.destroy();
    }
    this.mapData = {};
  }

  destroy() {
    this.uninstall();
  }
}

export default ChargersData;
