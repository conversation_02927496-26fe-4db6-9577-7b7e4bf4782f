/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import OrderGrid from "../common/order-grid";

type PropsOrderData = {
  shelf: shelfData;
};
function ShelfDetail(props: PropsOrderData) {
  const { t } = useTranslation();
  const [data, setData] = useState(null);

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.shelf) {
      setData(null);
      return;
    }
    setData(props.shelf);
  }, [props.shelf]);

  return (
    data && (
      <OrderGrid
        items={[
          {
            label: t("lang.rms.fed.shelfCoding"),
            value: data?.shelfCode || "--",
          },
          {
            label: t("lang.rms.fed.type"),
            value: data?.shelfType || "--",
          },
          {
            label: t("lang.rms.fed.state"),
            value: data?.shelfStatus || "--",
          },
          {
            label: t("lang.rms.fed.textNodeCode"),
            value: data?.locationCell || "--",
          },
          {
            label: t("lang.rms.fed.textAbsoluteCoordinate"),
            value: data?.location || "--",
          },
          {
            label: t("lang.rms.fed.textIndexCoordinates"),
            value: data?.locationIndex || "--",
          },
          {
            label: t("lang.rms.fed.angle"),
            value: data.hasOwnProperty("radAngle") ? data.radAngle : "--",
          },
          {
            label: t("lang.rms.fed.robotId"),
            value: data.robotId !== -1 ? data.robotId : "--",
          },
          {
            label: t("lang.rms.fed.placementCode"),
            value: data?.placementCell || "--",
          },
        ]}
      />
    )
  );
}

export default ShelfDetail;
