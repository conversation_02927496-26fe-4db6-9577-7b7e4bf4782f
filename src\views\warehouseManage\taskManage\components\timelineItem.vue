<template>
  <div>
    <el-collapse v-model="collapseActive">
      <el-timeline class="taskDetailTimelineSty">
        <template v-for="(treeItem, index) in treeData">
          <el-timeline-item v-if="treeItem.isGroup" :key="index" icon="el-icon-minus" type="primary">
            <el-collapse-item :name="`collapse_${index}`">
              <template slot="title">
                <div class="title">{{ $t(treeItem.traceTopic) }}</div>
              </template>
              <template v-if="treeItem.childTopics && treeItem.childTopics.length">
                <TimelineItem :treeData="treeItem.childTopics" />
              </template>
            </el-collapse-item>
          </el-timeline-item>
          <template v-else>
            <div class="content">
              <el-divider v-if="treeItem.split"></el-divider>

              <el-collapse-item v-if="treeItem.resultDetail">
                <template slot="title">
                  <div class="titleSlotBox">
                    <!-- 时间 -->
                    <span class="timer">{{ dayjs(treeItem.traceTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    <!-- 状态 -->
                    <span class="status" :class="treeItem.resultType">{{ $t(treeItem.traceTopic) }}</span>
                    <!-- 内容 -->
                    <span>{{ $t(treeItem.resultDesc, treeItem.resultDescValues) }}</span>
                  </div>
                </template>

                <div class="resultDetail">
                  {{ treeItem.resultDetail }}
                </div>
              </el-collapse-item>

              <template v-else>
                <div class="titleSlotBoxNoCollapse">
                  <div class="titleSlotBoxNoCollapseContent">
                    <!-- 时间 -->
                    <span class="timer">{{ dayjs(treeItem.traceTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                    <!-- 状态 -->
                    <span class="status" :class="treeItem.resultType">{{ $t(treeItem.traceTopic) }}</span>
                    <!-- 内容 -->
                    <span>{{ $t(treeItem.resultDesc, treeItem.resultDescValues) }}</span>
                  </div>
                </div>
              </template>

              <!-- 生成日志/下载日志 -->
              <!-- <el-button class="download-btn" size="mini" round @click="createLog(treeItem)">{{ $t('生成日志') }}</el-button>
            <el-button class="download-btn" type="primary" size="mini" round v-if="treeItem.logDownloadUrl">{{ $t('下载日志') }}</el-button> -->
            </div>
          </template>
        </template>
      </el-timeline>
    </el-collapse>
  </div>
</template>
<script>

import dayjs from "dayjs";

export default {
  name: "TimelineItem",
  props: {
    treeData: Array,
  },
  data() {
    return {
      collapseActive: this.treeData.map((item, index) => `collapse_${index}`),
      dayjs
    };
  },
  computed: {

  },
  methods: {
    // createLog({ }) {
    //   const { code } = await $req.post(`athena/engine/tools/job/generateJobLog?jobId=${}`);
    // }
  },

};
</script>
<style lang="less" scoped>
.title {
  font-size: 18px;
  font-weight: 900;
}

.timer {
  font-size: 12px;
  color: #409EFF;
  width: 130px;
}

.status {
  color: #F56C6C;
  padding: 0 10px;
  word-break: keep-all;
  font-size: 12px;

  &.SUCCESS {
    color: #67C23A;
  }
}

.content {
  font-size: 14px;
  color: #909399;
  // word-break: 
}

.titleSlotBox {
  font-size: 12px;
  line-height: 14px;
  color: #999;
}

.titleSlotBoxNoCollapse {
  height: 48px;
  position: relative;
  .titleSlotBoxNoCollapseContent {
    color: #999;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    left: 0;
    font-size: 12px;
  }
}
</style>

<style lang="less">
.taskDetailTimelineSty {
  .el-collapse-item__header, .el-collapse-item__wrap {
    border-bottom: none;
  }
  .el-collapse {
    border-bottom: none;
    border-top: none;
  }
  .el-timeline-item__wrapper {
    top: -18px;
  }
}
</style>