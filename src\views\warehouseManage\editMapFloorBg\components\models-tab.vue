<template>
  <el-tabs
    v-model="activeName"
    :before-leave="leave"
    @tab-click="handleClick"
  >
    <el-tab-pane
      v-for="item in robotTypeTabs"
      :key="item"
      :name="item"
      :label="$t(item)"
    >
      <slot :item="item" />
    </el-tab-pane>
    <el-tab-pane key="add" name="add">
      <span slot="label" style="padding: 8px; font-size: 20px; font-weight: bold"> + </span>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
export default {
  name: 'ModelsTab',
  props: {
    robotTypeTabs: {
      type: Array,
      require: true
    },
    bgHistory: {
      type: Number,
      require: true
    },
    newactiveName: {
      type: String,
      require: true
    }
  },
  data() {
    return {
      activeName: this.robotTypeTabs[0]
    }
  },
  watch: {
    newactiveName() {
      this.activeName = this.newactiveName
    }
  },
  methods: {
    handleClick() {
      this.$emit('activeName', this.activeName)
    },
    leave(currentName) {
      if (currentName === 'add') {
        this.addTab()
        return false
      } else if (this.bgHistory > 0) {
        return false
      } else {
        return true
      }
    },
    addTab() {
      this.$emit('addTab')
    }
  }
}
</script>

<style lang="less" scoped></style>
