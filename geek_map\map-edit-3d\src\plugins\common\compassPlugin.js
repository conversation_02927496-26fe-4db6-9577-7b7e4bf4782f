import * as THREE from "three";
import Base from "../../core/abstractPlugin";
import CompassUrl from "../../asset/image/compass.png";
import { throttle } from "../../utils/utils";

let count = 0;

class CompassPlugin extends Base {
  constructor(options) {
    super(options);
    this.compassDomBox = document.createElement("div");
    this.compassDom = document.createElement("embed");
    this._formatStyleToDom(this.compassDom);
    this.compassDomBox.appendChild(this.compassDom);
    this._formatStyleToDom(this.compassDomBox, {
      position: "absolute",
      left: "10px",
      bottom: "10px",
      width: "40px",
      height: "40px",
      dispaly: "none",
      background: "#fff",
      "border-radius": "4px",
      "box-shadow": "0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)",
      "z-index": 1,
      "pointer-events": "none",
      "user-select": "none",
    });
    this.PluginName = "compassPlugin";
    this.handleChange = null;
  }
  activated() {
    this._formatStyleToDom(this.compassDomBox, { display: "block" });
    if (!count) {
      let src = CompassUrl.includes("data:image")
        ? CompassUrl
        : `${this.Map3d.baseUrl}${CompassUrl.replace("../", "")}`;
      this.compassDom.src = src;
      this.$dom.appendChild(this.compassDomBox);
    }
    this.handleChange = throttle(() => this._listen(), 500);
    this.Map3d.OrbitControls.addEventListener("change", this.handleChange);
    count++;
  }
  deactivated() {
    this._formatStyleToDom(this.compassDomBox, { display: "none" });
    const control = this.Map3d.OrbitControls;
    control && control.removeEventListener("change", this.handleChange);
    this.handleChange = null;
  }
  destroyed() {
    count = 0;
    this.compassDomBox && this.compassDomBox.parentElement.removeChild(this.compassDomBox);
    const control = this.Map3d.OrbitControls;
    control && control.removeEventListener("change", this.handleChange);
    this.handleChange = null;
  }
  _listen() {
    const dir = new THREE.Vector3();
    const sph = new THREE.Spherical();
    // const renderer = this.Map3d.renderer;
    this.Map3d.camera.get().getWorldDirection(dir);
    sph.setFromVector3(dir);
    // this.compassDom.style.transform = `rotate(${THREE.Math.radToDeg(sph.theta) - 180}deg)`;
    this.compassDom.style.transform = `rotate(${THREE.Math.radToDeg(sph.theta) - 180}deg)`;
  }
  _formatStyleToDom(dom, data) {
    if (Object.prototype.toString.call(data) !== "[object Object]") {
      return;
    }
    const preStyle = dom.getAttribute("style")
      ? Object.fromEntries(
          dom
            .getAttribute("style")
            .split(";")
            .map(i => i.split(":")),
        )
      : {};
    const styleStr = Object.entries({ ...preStyle, ...data })
      .map(i => i.join(":"))
      .join(";");
    dom.setAttribute("style", styleStr);
  }
}

export default new CompassPlugin();
