import { COEFFICIENT } from "../config";
import { getGlobalViewport } from "../global";
import { Container, Sprite, Texture } from "pixi.js";
import { cad2pixi, pixi2cad } from "../utils/utils";
export default class BackGround {
  constructor() {
    this.container = null;
    this.viewport = null;
    this.id = null;
    //背景图的初始偏移量
    this.locationX = 0;
    this.locationY = 0;
  }
  initLayer(op) {
    const { id, zIndex } = op;
    this.id = id;
    this.viewport = getGlobalViewport();
    this.container = new Container();
    this.container.id = id;
    this.container.name = id;
    this.container.zIndex = zIndex;
    this.viewport.addChild(this.container);
  }
  initElements(data = {}) {
    const { locationX, locationY, splitImage } = data;
    if (!splitImage) return;
    splitImage.imageItems.forEach(item => {
      const { imageText, height, width, x, y } = item;
      const texture = Texture.from(imageText);
      const sprite = new Sprite(texture);
      sprite.x = x;
      sprite.y = -y - height;
      sprite.height = height;
      if (width) sprite.width = width;
      this.container.addChild(sprite);
    });
    // this.container.x = locationX
    // this.container.y = locationY
    const pixiLocation = cad2pixi({ x: locationX, y: locationY });
    this.container.x = pixiLocation.x;
    this.container.y = pixiLocation.y;
    this.locationX = pixiLocation.x;
    this.locationY = pixiLocation.y;
  }
  //移动背景图
  move(offsetX, offsetY, angle = 0) {
    const { locationX, locationY } = this;
    const nx = locationX - offsetX / COEFFICIENT;
    const ny = locationY + offsetY / COEFFICIENT;
    this.container.x = nx;
    this.container.y = ny;
    this.container.angle = angle;
    //返回cad
    return pixi2cad({ x: nx, y: ny });
  }

  //是否可以被点击
  triggerLayer(isTrigger) {
    // this.container.interactiveChildren = isTrigger;
  }
}
