import LayerManager from '../layerManager/LayerManager'
import EventBus from '../eventBus/EventBus'
export default class History {
  static length = 10
  //回退列表
  static backList = []
  //前进列表
  static forwardList = []
  //被删除的元素存储
  static deleteData = {}
  //是否发生变化
  static isChanged = false

  static add(historyInfo) {
    this.isChanged = true
    //historyContinue是联动的意思，有的历史回退和前进需要进行联动，当historyContinue === true时，连带下一条一起执行
    const {historyContinue,action} = historyInfo
    const len = this.backList.length
    if(len >= 10 && !historyContinue) this.backList.shift()
    this.backList.push(historyInfo)
    if(action === 'delete') this.handlerDeleteData().add(historyInfo)
    this.forwardList = []
    this.historyChange()
  }
  //存储已经删除的数据，用于告知后端那些被删除，用于增量更新
  static handlerDeleteData() {
    const add = (historyInfo) => {
      const {detail,layerName} = historyInfo
      if(!this.deleteData[layerName]) this.deleteData[layerName] = []
      detail.forEach(info => {
        const {nodeId,segmentId,id} = info
        const elementId = nodeId || segmentId || id
        this.deleteData[layerName].push(elementId)
      })
      console.log('被删除的数据',this.deleteData)
    }
    const del = (historyInfo) => {
      const {detail,layerName} = historyInfo
      detail.forEach(info => {
        const {nodeId,segmentId,id} = info
        const elementId = nodeId || segmentId || id
        const index = this.deleteData[layerName].findIndex(id => id === elementId)
        if(index !== -1){
          this.deleteData[layerName].splice(index,1)
        }
      })
      console.log('被删除的数据',this.deleteData)
    }
    const clear = () => {
      this.deleteData = {}
    }
    return {
      add,
      del,
      clear
    }
  }
  //后退
  static back() {
    const len = this.backList.length
    if(!len) return
    const backDetail = this.backList.pop()
    const forwardDetail = this._getNowDetail({...backDetail})
    this.forwardList.unshift({...forwardDetail})
    const {action,detail,layerName,historyContinue} = {...backDetail}
    if(action === 'add'){
      const ids = detail.map(item => {
        return item.nodeId || item.segmentId || item.id
      })
      LayerManager.deleteElements({
        id:layerName,
        data: ids,
        isSaveHistory:false,
        // isEmitData:false
      })
      this.handlerDeleteData().add({...backDetail})
    }
    if(action === 'update'){
      LayerManager.updateElements({
        id:layerName,
        data: detail,
        isSaveHistory:false,
        isCoverProperties:true
      })
    }
    if(action === 'delete'){
      LayerManager.addElements({
        id:layerName,
        data: detail,
        isSaveHistory:false,
        isEmitData:false
      })
      this.handlerDeleteData().del({...backDetail})
    }
    this.historyChange()
    if(historyContinue){
      this.back()
    }
  }
  //前进
  static forward() {
    const len = this.forwardList.length
    if(!len) return
    const forwardDetail = this.forwardList.shift()
    const backDetail = this._getNowDetail({...forwardDetail})
    //加入前进队列
    this.backList.push({...backDetail})
    const {action,detail,layerName,historyContinue} = {...forwardDetail}
    if(action === 'add'){
      LayerManager.addElements({
        id:layerName,
        data: detail,
        isSaveHistory:false,
        isEmitData:false
      })
      this.handlerDeleteData().del({...forwardDetail})
    }
    if(action === 'update'){
      LayerManager.updateElements({
        id:layerName,
        data: detail,
        isSaveHistory:false,
        isCoverProperties:true
      })
    }
    if(action === 'delete'){
      const ids = detail.map(item => {
        return item.nodeId || item.segmentId || item.id
      })
      LayerManager.deleteElements({
        id:layerName,
        data: ids,
        isSaveHistory:false,
        isEmitData:false
      })
      this.handlerDeleteData().add({...forwardDetail})
    }
    this.historyChange()
    //是否触发连续回退
    const nextForwardHistory = this.forwardList[0]
    if(nextForwardHistory && nextForwardHistory.historyContinue){
      this.forward()
    }
    // if(historyContinue !== undefined){
    //   this.forward()
    // }
  }
  //获取当前的编辑信息
  static _getNowDetail(historyDetail){
    const {action,detail,layerName} = historyDetail
    if(['add','delete'].includes(action)) return {...historyDetail}
    const newDetail = detail.map(item => {
      const elementId = item.nodeId || item.segmentId || item.id
      const {properties} = LayerManager.getProperties({layerName,id:elementId})
      return {...properties}
    })
    const newHistoryDetail = {action,detail:newDetail,layerName}
    return newHistoryDetail
  }
  //触发历史事件变动
  static historyChange() {
    const {backList,forwardList,isChanged} = this
    EventBus.$emit('historyChange',{backList,forwardList,isChanged})
  }
  static destroy() {
    this.backList = []
    this.forwardList = []
    this.isChanged = false
    const {backList,forwardList,isChanged} = this
    EventBus.$emit('historyChange',{backList,forwardList,isChanged})
    this.handlerDeleteData().clear()
  }
}
