import { createRouter, RouteRecordRaw, createWebHashHistory } from "vue-router";
import EditMap from "@packages/views/EditMap/EditMap.vue";
import EditMapIframeTest from "@packages/views/EditMapIframeTest/index.vue";

const routes: Array<RouteRecordRaw> = [
  // {
  //   path: "/",
  //   name: "test",
  //   component: EditMapIframeTest,
  // },
  {
    path: "/singleEdit2D",
    name: "singleEdit2D",
    component: EditMap,
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

export default router;
