<template>
  <el-card v-show="detailData.latticeCode" shadow="never" class="component-operate-detail">
    <div slot="header" class="header">
      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>
    </div>

    <order-group-grid>
      <grid-item :label="$t('lang.rms.fed.latticeCode')" :value="detailData.latticeCode || '--'" />
      <grid-item
        :label="$t('lang.rms.fed.latticeStatus')"
        :value="detailData.latticeStatus || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.latticeAvailableStatus')"
        :value="detailData.latticeFlag || '--'"
      />
      <grid-item :label="$t('lang.rms.fed.layer')" :value="detailData.layer || '--'" />
      <grid-item :label="$t('lang.rms.fed.height')" :value="detailData.height || '--'" />
      <grid-item :label="$t('lang.rms.fed.fetchDirs')" :value="detailData.fetchDirs || '--'" />
    </order-group-grid>
  </el-card>
</template>

<script>
import OrderGroupGrid from "../common/order-group-grid.vue";
import GridItem from "../common/order-group-grid-item.vue";

export default {
  name: "DetailLattice",
  components: { OrderGroupGrid, GridItem },
  props: {
    detailData: {
      type: Object,
      require: true,
    },
    detailTitle: {
      type: String,
      require: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.component-operate-detail {
  background: #fbfbfb;

  ::v-deep tr > td {
    padding-bottom: 0;
  }

  .item-label {
    width: 50px;
  }
}
</style>
