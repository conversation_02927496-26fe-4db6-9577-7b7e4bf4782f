<!--维修调度-->
<template>
  <geek-main-structure>
    <geek-tabs-nav :block="true" :nav-list="permissionNavList" @select="tabsNavChange" />

    <keep-alive>
      <component
        :is="activeName"
        ref="tabCon"
        :maintainAreas="maintainAreas"
        @reqServiceWorker="reqServiceWorker"
        @reqReturnWorker="reqReturnWorker"
      />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import Service from "./components/service"; // 维修调度
import Return from "./components/return"; // 机器人返场

import { MapWorker } from "geek-monitor2d";
let mapWorker = null;

export default {
  name: "RobotService",
  components: { Service, Return },
  data() {
    return {
      maintainAreas: [],
      areaTimmer: null,

      permissionNavList: [],
      navList: [
        {
          permissionName: "TabMaintenanceScheduleOnePage",
          id: "Service",
          text: "lang.rms.fed.CallRobotForService", //
        },
        {
          permissionName: "TabMaintenanceScheduleRobotPage",
          id: "Return",
          text: "lang.rms.fed.returnRobot", // 通知管理
        },
      ],
      activeName: "",
    };
  },

  activated() {
    this.initMapWorker();
    this.getMaintainAreas();

    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "maintenanceSchedule"));
    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },

  deactivated() {
    if (this.areaTimmer) {
      clearTimeout(this.areaTimmer);
      this.areaTimmer = null;
    }
    if (mapWorker) mapWorker.destroy();
    mapWorker = null;
  },
  methods: {
    getMaintainAreas() {
      $req.get("/athena/maintain/getMaintainArea").then(res => {
        const list = res?.data || [];

        if (JSON.stringify(list) != JSON.stringify(this.maintainAreas)) {
          this.maintainAreas = list;
        }

        this.areaTimmer = setTimeout(() => {
          this.getMaintainAreas();
          if (this.activeName == "Service") {
            this.$refs.tabCon.getMaintainPercent();
          }
        }, 1000);
      });
    },
    tabsNavChange(id) {
      if (this.activeName === id) return;
      this.activeName = id;
    },

    reqReturnWorker(cmd) {
      if (this.activeName != "Return") return;
      if (!mapWorker) {
        this.$error("no websocket connection");
        return;
      }

      const msgType = "RobotInstructionRequestMsg";
      mapWorker.reqSocket(msgType, cmd).then(res => {
        if (res.msgType !== "RobotInstructionResponseMsg") return;
        const body = res?.body || {};
        const code = body && body.hasOwnProperty("code") ? body.code : -1;
        if (code === 0) {
          this.$success(this.$t("lang.rms.fed.robotReturnSuccessed"));
          this.$refs.tabCon.clearInputRobotId();
        } else {
          const msg = $utils.Tools.transMsgLang(body?.msg || "");
          this.$error(msg);
          if (data.code === 10103) {
            this.$refs.tabCon.clearInputRobotId();
          }
        }
      });
    },

    reqServiceWorker(cmd) {
      if (this.activeName != "Service") return;

      if (!mapWorker) {
        this.$error("no websocket connection");
        return;
      }
      const msgType = "RobotInstructionRequestMsg";
      mapWorker.reqSocket(msgType, cmd).then(res => {
        if (res.msgType !== "RobotInstructionResponseMsg") return;
        const body = res?.body || {};
        const code = body && body.hasOwnProperty("code") ? body.code : -1;
        if (code === 0) {
          this.$success(this.$t("lang.rms.fed.callRobotSucceeded"));
          this.$refs.tabCon.clearInputRobotId();
        } else {
          const msg = $utils.Tools.transMsgLang(body?.msg || "");
          this.$error(msg);
        }
      });
    },
    initMapWorker() {
      mapWorker = new MapWorker(this.getWsUrl());
      mapWorker.init();
    },
    getWsUrl() {
      let protocol = window.location.protocol === "http:" ? "ws" : "wss";
      let hostname = window.location.host;
      if ($req.isDev) {
        hostname = new URL($req.API_URL).hostname;
      }

      const RMSPermission = $utils.Data.getRMSPermission();
      const token = RMSPermission ? `?token=${$utils.Data.getToken()}` : "";

      return `${protocol}://${hostname}/athena-monitor${token}`;
    },
  },
};
</script>

<style lang="less" scoped>
.big-btn {
  margin: 40px 0 0;
  padding: 40px 0;
  border-top: 1px solid #eeeeee;
  text-align: center;
  .el-button {
    height: 200px;
    width: 200px;
    border-radius: 100px;
    font-size: 22px;
  }
  .el-button:active {
    background-color: #c00 !important;
  }
}
</style>
