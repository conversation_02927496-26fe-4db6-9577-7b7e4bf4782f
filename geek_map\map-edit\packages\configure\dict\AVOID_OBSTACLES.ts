/**
 * 无
 */
export const AVOID_OBSTACLES_NONE = -1;
/**
 * 空载
 */
export const AVOID_OBSTACLES_NOLOAD = 1;
/**
 * 负载
 */
export const AVOID_OBSTACLES_LOAD = 2;
/**
 * 空负载
 */
export const AVOID_OBSTACLES_NOLOAD_LOAD = 3;


export const AVOID_OBSTACLES = [
  {
      label: 'lang.rms.robot.tasktype.null',
      value: AVOID_OBSTACLES_NONE
  },
  {
      label: 'lang.rms.fed.noLoad',
      value: AVOID_OBSTACLES_NOLOAD
  },
  {
      label: 'lang.rms.fed.load',
      value: AVOID_OBSTACLES_LOAD
  },
  {
      label: 'lang.rms.fed.noLoadAndLoad',
      value: AVOID_OBSTACLES_NOLOAD_LOAD
  }
]