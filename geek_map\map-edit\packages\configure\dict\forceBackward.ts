/**
 * 表示完全到达并停下才发到达通知
 */
export const FORCE_BACKWARD_DICT_STOP = "0";

/**
 * 表示分配到单元格就通知
 */
export const FORCE_BACKWARD_DICT_DISTRIBUTION = "1";

export const FORCE_BACKWARD_DICT = [
  // 完全到达并停下才发到达通知
  {
    label: "lang.rms.api.result.edit.map.stopNotice",
    value: FORCE_BACKWARD_DICT_STOP,
  },
  // 分配到单元格就通知
  {
    label: "lang.rms.api.result.edit.map.distributionNotice",
    value: FORCE_BACKWARD_DICT_DISTRIBUTION,
  },
];
