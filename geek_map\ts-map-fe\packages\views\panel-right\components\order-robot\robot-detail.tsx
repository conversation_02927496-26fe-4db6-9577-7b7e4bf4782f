/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

import OrderGrid from "../common/order-grid";

type PropsOrderData = {
  robot: robotData;
};
function RobotDetail(props: PropsOrderData) {
  const { t } = useTranslation();
  const [data, setData] = useState(null);

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.robot) {
      setData(null);
      return;
    }
    setData(props.robot);
  }, [props.robot]);

  return (
    data && (
      <OrderGrid
        items={[
          {
            label: t("lang.rms.fed.robotId"),
            value: data?.id || "--",
          },
          {
            label: t("lang.rms.fed.type"),
            value: data?.robotType || "--",
          },
          {
            label: t("lang.rms.fed.robot") + "IP",
            value: data?.ip || "--",
          },
          {
            label: t("lang.rms.fed.textNodeCode"),
            value: data?.locationCell || "--",
          },
          {
            label: t("lang.rms.web.monitor.robot.location"),
            value: data?.location || "--",
          },
          {
            label: t("lang.rms.web.monitor.robot.robotPathMode"),
            value: data?.robotPathMode || "--",
          },
          {
            label: t("lang.rms.fed.confirmState"),
            value: data.hasOwnProperty("posConfirmed")
              ? data.posConfirmed
                ? t("lang.rms.fed.confirmed")
                : t("lang.rms.fed.unconfirmed")
              : "--",
          },
          {
            label: t("lang.rms.fed.errorState"),
            value: data.hasOwnProperty("errorCode") ? data.errorCode : "--",
          },
          {
            label: t("lang.rms.fed.taskId"),
            value: data.hasOwnProperty("taskId") ? data.taskId : "--",
          },
          {
            label: t("lang.rms.fed.taskType"),
            value: data.hasOwnProperty("taskType") ? data.taskType : "--",
          },
          {
            label: t("lang.rms.fed.shelfCoding"),
            value: data?.onloadShelfCode || "--",
          },
          {
            label: t("lang.rms.fed.power"),
            value: data.hasOwnProperty("powerPercent") ? `${data.powerPercent}%` : "--",
          },
          {
            label: t("lang.rms.fed.obstacleCount"),
            value: data.hasOwnProperty("obstacleCount") ? data.obstacleCount : "--",
          },
          {
            label: t("lang.rms.fed.boxHoldingTaskId"),
            value: data.hasOwnProperty("jobIds") ? data.jobIds : "--",
          },
          {
            label: t("lang.rms.fed.robotAreaId"),
            value: data.hasOwnProperty("robotAreaId") ? data.robotAreaId : "--",
          },
          {
            label: t("tag"),
            value: data.hasOwnProperty("tag") ? data.tag : "--",
          },
          {
            label: t("lang.rms.fed.mechanismAngle"),
            value: data.hasOwnProperty("mechanismAngle") ? data.mechanismAngle : "--",
          },
        ]}
      />
    )
  );
}

export default RobotDetail;
