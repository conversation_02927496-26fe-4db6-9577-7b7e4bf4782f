/* ! <AUTHOR> at 2023/04/19 */

/**
 * 设备接口数据类型
 * @param deviceInfoId 唯一设备ID
 * @param location 位置数据
 * @param state 0-正常，1-异常，2-设备异常
 * @param 其他 可选
 */
type deviceData = {
  deviceInfoId: code;
  location: location;
  state: 0 | 1 | 2 | number;
  [propName: string]: any;
};

/**
 * 设备地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param floorId 楼层
 * @param iconTexture device resource icon
 * @param width 宽 0.8
 * @param height 高 0.8
 * @param position 地图位置数据
 * @param 其他 可选
 */
type mDeviceData = {
  code: code;
  floorId: floorId;
  iconTexture: any;
  width: number;
  height: number;
  position: location;
  [propName: string]: any;
};
