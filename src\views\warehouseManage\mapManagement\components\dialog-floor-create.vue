<template>
  <div>
    <el-dialog
      :title="dialogFloorCreate.title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      class="geek-dialog-form"
      width="540px"
      @close="close"
    >
      <el-form
        ref="floorCreate"
        :model="floorCreate"
        label-position="right"
        label-width="70px"
      >
        <el-form-item :label="$t('lang.rms.fed.mapName')" prop="mapName">
          <el-input :value="rowData.name" disabled />
        </el-form-item>
        <el-form-item
          :label="$t('lang.rms.fed.FloorID')"
          prop="floorId"
          :rules="[
            { max:32, message: $t('楼层ID长度不能超过32'),trigger: ['change','blur'] },
            { required: true, message: $t('lang.rms.fed.floorIdCanNotNull')},
            { pattern: /^[1-9]\d?$/, message: this.$t('lang.rms.containerManage.sendModelId.check') }
          ]"
        >
          <el-input v-model="floorCreate.floorId" :placeholder="$t('lang.rms.fed.pleaseEnter')" />
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button @click="visible = false">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button
          type="primary"
          :disabled="disable"
          @click="submit"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'DialogFloorCreate',
  data() {
    return {
      mapName: '',
      floorCreate: {
        floorId: null
      },
      disable: false
    }
  },
  computed: {
    ...mapState('mapManagement', ['dialogFloorCreate']),
    visible: {
      get() {
        return this.dialogFloorCreate.visible
      },
      set(val) {
        const { visible } = this.dialogFloorCreate
        if (!val && val !== visible) {
          this.hideDialog()
        }
      }
    },
    rowData() {
      return this.dialogFloorCreate.rowData
    }
  },
  methods: {
    ...mapMutations('mapManagement', ['hideDialog']),
    close() {
      this.floorCreate.floorId = null
    },
    submit() {
      this.$refs['floorCreate'].validate((valid) => {
        if (valid) {
          this.disable = true
          $req
            .postParams('/athena/map/draw/createFloor', {
              mapId: this.rowData.id,
              floorId: this.floorCreate.floorId
            })
            .then(res => {
              this.reqSuccess(res.msg)
            })
            .catch(e => {
              this.disable = false
            })
        }
      })
    },
    reqSuccess(msg) {
      this.disable = false
      this.visible = false
      this.$emit('refreshList')
      msg = $utils.Tools.transMsgLang(msg)
      this.$success(msg)
    }
  }
}
</script>

<style lang="less" scoped></style>
