// axios配置  可自行根据项目进行更改，只需更改该文件即可，其他文件可以不动
// The axios configuration can be changed according to the project, just change the file, other files can be left unchanged

import type { AxiosResponse } from "axios";
import type { RequestOptions, Result } from "./types";
import type { AxiosTransform, CreateAxiosOptions } from "./axiosTransform";
import { VAxios } from "./Axios";
import { checkStatus } from "./checkStatus";
import { createNow, formatRequestDate, setObjToUrlParams, deepMerge } from "./helper";
import { useI18n } from "@packages/hook/useI18n";
import { ElMessage } from "element-plus";
import { useAppStore } from "@packages/store/app";

/**
 * @description: 数据处理，方便区分多种处理方式
 */
const transform: AxiosTransform = {
  /**
   * @description: 处理请求数据。如果数据不是预期格式，可直接抛出错误
   */
  transformRequestHook: (res: AxiosResponse<Result>, options: RequestOptions) => {
    const { t } = useI18n();
    const { isTransformRequestResult, isReturnNativeResponse } = options;
    // 是否返回原生响应头 比如：需要获取响应头时使用该属性
    if (isReturnNativeResponse) {
      return res;
    }
    // 不进行任何处理，直接返回
    // 用于页面代码可能需要直接获取code，data，message这些信息时开启
    if (!isTransformRequestResult) {
      return res.data;
    }

    // 错误的时候返回
    if (!res.data) {
      throw new Error(t("数据异常！"));
    }
    //  这里 code，result，message为 后台统一的字段，需要在 types.ts内修改为项目自己的接口返回格式
    const { code, message, msg } = res.data;
    console.log(res.data)
    // 成功的逻辑: 现在只要code为ResultEnum.SUCCESS则表示成功
    const hasSuccess = code === 0;
    let messageText = message || msg;
    if (!hasSuccess) {
      if (code === 401) {
        messageText = messageText || "网络超时！";
      } else {
        messageText = messageText || "服务器异常！";
      }
      console.log('这里1')
      const [messageVal, ...msgParams] = messageText.split(",");
      ElMessage.error(t(messageVal, msgParams));
    } else {
      return res.data;
    }
  },

  // 请求之前处理config
  beforeRequestHook: (config, options) => {
    const { apiUrl, joinParamsToUrl, formatDate, joinTime = true } = options;

    if (apiUrl && typeof apiUrl === "string") {
      config.url = `${apiUrl}${config.url}`;
    }

    if (config.url) {
      config.url = config.url.replace("//", "/");
    }

    const params = config.params || {};
    if (config.method?.toUpperCase() === "GET") {
      if (typeof params !== "string") {
        // 给 get 请求加上时间戳参数，避免从缓存中拿数据。
        config.params = Object.assign(params || {}, createNow(joinTime, false));
      } else {
        // 兼容restful风格
        config.url = config.url + params + `${createNow(joinTime, true)}`;
        config.params = undefined;
      }
    } else {
      if (typeof params !== "string") {
        formatDate && formatRequestDate(params);
        config.data = params;
        config.params = undefined;
        if (joinParamsToUrl) {
          config.url = setObjToUrlParams(config.url as string, config.data);
        }
      } else {
        // 兼容restful风格
        config.url = config.url + params;
        config.params = undefined;
      }
    }
    return config;
  },

  /**
   * @description: 请求拦截器处理
   */
  requestInterceptors: config => {
    const appStore = useAppStore();
    if (appStore.sessionId) {
      const headerType = appStore.headerType || "Cookie";
      (config.headers as any)[headerType] = appStore.sessionId;
    }
    return config;
  },

  /**
   * @description: 响应错误处理
   */
  responseInterceptorsCatch: (error: any) => {
    const { t } = useI18n();
    const { response, code, message } = error || {};
    const msg: string = response?.data?.error?.message ?? response?.data?.error?.msg ?? "";
    const err: string = error?.toString?.() ?? "";
    try {
      //cancel不触发报错
      if(error.name !== 'CanceledError'){
        ElMessage.error(msg || "服务器异常");
      }
    } catch (error) {
      throw error;
    }
    checkStatus(error?.response?.status, msg);
    return Promise.reject(error);
  },
};

function createAxios(opt?: Partial<CreateAxiosOptions>) {
  return new VAxios(
    deepMerge(
      {
        timeout: 240 * 1000,
        headers: { "Content-Type": "application/json;charset=UTF-8" },
        // 数据处理方式
        transform,
        // 配置项，下面的选项都可以在独立的接口请求中覆盖
        requestOptions: {
          // 是否返回原生响应头 比如：需要获取响应头时使用该属性
          isReturnNativeResponse: false,
          // 需要对返回数据进行处理
          isTransformRequestResult: true,
          // post请求的时候添加参数到url
          joinParamsToUrl: false,
          // 格式化提交参数时间
          formatDate: true,
          // 消息提示类型
          errorMessageMode: "message",
          // 接口地址
          apiUrl: "",
          //  是否加入时间戳
          joinTime: true,
        },
      },
      opt || {},
    ),
  );
}

export const axios = createAxios();

export { Result };
