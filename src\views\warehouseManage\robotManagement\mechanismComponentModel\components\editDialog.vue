<template>
  <el-dialog
    :title="$t(title)"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="520px"
  >
    <geek-customize-form ref="refForm" :form-config="formConfig" />

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditMechanismComponentModelDialog",
  data() {
    return {
      dialogVisible: false,
      operation: "",
      rowData: {},
      mechanism: [],
    };
  },
  computed: {
    title() {
      switch (this.operation) {
        case "add":
          return "lang.rms.api.result.warehouse.createRobotCompentModel";
        case "edit":
          return "lang.rms.api.result.warehouse.editRobotCompoentModel";
        case "view":
          return "lang.rms.api.result.warehouse.viewRobotCompoentModel";
        default:
          return "lang.rms.api.result.warehouse.robotMechanismCompentModel";
      }
    },

    formConfig() {
      let disabled = false;
      if (this.operation == "view") disabled = true;
      return {
        attrs: {
          labelWidth: "220px",
          labelPosition: "right",
          disabled,
        },
        configs: {
          name: {
            label: "lang.rms.api.result.warehouse.mechanism.component.spuName",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMechanismComponentSPU",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMechanismComponentSPU"),
                trigger: "blur",
              },
            ],
          },
          mechanismModelId: {
            label: "lang.rms.api.result.warehouse.mechanismModel",
            tag: "select",
            default: "",
            required: true,
            placeholder: "lang.rms.fed.pleaseChoose",
            options: this.mechanism,
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseSelectMechanismModelID"),
                trigger: "blur",
              },
            ],
          },
          length: {
            label: "lang.rms.api.result.warehouse.mechanismComponentModelLength",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMechainsmComponentLength",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMechainsmComponentLength"),
                trigger: "blur",
              },
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          width: {
            label: "lang.rms.api.result.warehouse.mechanismComponentModelWidth",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMechanismCompentModelWidth",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMechanismCompentModelWidth"),
                trigger: "blur",
              },
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          beginHeight: {
            label: "lang.rms.api.result.warehouse.mechanismComponentModelHeight",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMechainsmComponentHeight",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseEnterMechainsmComponentHeight"),
                trigger: "blur",
              },
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) {
                    callback(new Error(this.$t("lang.rms.api.result.warehouse.pleaseEnterMechainsmComponentHeight")));
                  } else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          sn: {
            label: "lang.rms.api.result.warehouse.mechainsmComponentSn",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterSnOfMechanismComponents",
            rules: [
            {
                required: true,
                message: this.$t("lang.rms.api.result.warehouse.pleaseEnterSnOfMechanismComponents"),
                trigger: "blur",
              },
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) {
                    callback(new Error(this.$t("lang.rms.api.result.warehouse.pleaseEnterSnOfMechanismComponents")));
                  } else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
        },
      };
    },
  },
  methods: {
    open(type, data) {
      this.getMechanismList();
      this.operation = type;
      this.rowData = data || {};
      this.dialogVisible = true;

      const params = {
        name: data?.name || "",
        mechanismModelId: data?.mechanismModelId || "",
        length: data.hasOwnProperty("length") ? data.length : "",
        width: data.hasOwnProperty("width") ? data.width : "",
        beginHeight: data.hasOwnProperty("beginHeight") ? data.beginHeight : "",
        sn: data.hasOwnProperty("sn") ? data.sn : "",
      };
      this.$nextTick(() => {
        this.$refs.refForm.reset();
        this.$refs.refForm.setData(params);
      });
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      if (this.operation == "view") {
        this.close();
        return;
      }

      this.$refs.refForm.validate().then(data => {
        let formData = Object.assign({}, data);
        if (this.operation == "edit") formData.id = this.rowData.id;

        $req.post("/athena/robot/manage/mechanismComponentSave", formData).then(res => {
          this.$success();
          this.close();
          this.$emit("updateList");
        });
      });
    },

    getMechanismList() {
      $req
        .post("/athena/robot/manage/mechanismPageList?currentPage=1&pageSize=1000", {
          name: "",
          mechanismCode: "",
          controlAbilities: [],
          actionAbilities: [],
        })
        .then(res => {
          if (res?.code !== 0) return;
          const list = res?.data?.recordList || [];

          this.mechanism = list.map(item => ({
            label: `${item.mechanismCode || ""}(${item.name})`,
            value: item.mechanismId,
          }));
        });
    },
  },
};
</script>

<style lang="less" scoped></style>
