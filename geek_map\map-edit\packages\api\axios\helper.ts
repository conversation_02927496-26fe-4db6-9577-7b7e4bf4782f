const DATE_TIME_FORMAT = "YYYY-MM-DD HH:mm";

export function createNow<T extends boolean>(
  join: boolean,
  restful: T,
): T extends true ? string : object;

export function createNow(join: boolean, restful = false): string | object {
  if (!join) {
    return restful ? "" : {};
  }
  const now = new Date().getTime();
  if (restful) {
    return `?_t=${now}`;
  }
  return { _t: now };
}

/**
 * @description: Format request parameter time
 */
export function formatRequestDate(params: any) {
  if (Object.prototype.toString.call(params) !== "[object Object]") {
    return;
  }

  for (const key in params) {
    if (params[key] && params[key]._isAMomentObject) {
      params[key] = params[key].format(DATE_TIME_FORMAT);
    }
    if (typeof key === "string") {
      const value = params[key];
      if (value) {
        try {
          params[key] = typeof value === "string" ? value.trim() : value;
        } catch (error) {
          throw new Error(error as any);
        }
      }
    }
    if (typeof params[key] === "object") {
      formatRequestDate(params[key]);
    }
  }
}
export function setObjToUrlParams(baseUrl: string, obj: any): string {
  let parameters = "";
  for (const key in obj) {
    parameters += key + "=" + encodeURIComponent(obj[key]) + "&";
  }
  parameters = parameters.replace(/&$/, "");
  return /\?$/.test(baseUrl) ? baseUrl + parameters : baseUrl.replace(/\/?$/, "?") + parameters;
}

export function deepMerge<T = any>(src: any = {}, target: any = {}): T {
  let key: string;
  for (key in target) {
    src[key] =
      typeof src[key] === "object" ? deepMerge(src[key], target[key]) : (src[key] = target[key]);
  }
  return src;
}
