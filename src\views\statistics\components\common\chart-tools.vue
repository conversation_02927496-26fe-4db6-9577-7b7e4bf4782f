<template>
  <div class="chart-tools">
    <div>
      <el-button size="mini" type="danger" circle icon="el-icon-back" @click="$emit('cancle')" />
      <el-date-picker
        v-model="date"
        type="date"
        size="mini"
        :editable="false"
        :clearable="false"
        placeholder="选择日期"
        @change="updateChange('date')"
        class="chart-date"
      />

      <el-select
        v-model="cycle"
        size="mini"
        placeholder="请选择时间间隔"
        @change="updateChange('cycle')"
        class="chart-cycle"
      >
        <el-option v-for="item in cycles" :key="item" :label="item" :value="item" />
      </el-select>
    </div>

    <el-select
      v-model="selectedTypes"
      filterable
      multiple
      collapse-tags
      size="mini"
      placeholder="请选择图表项"
      class="chart-types"
      @change="updateChange('keys')"
    >
      <el-option v-for="item in chartTypes" :key="item" :label="item" :value="item" />
    </el-select>
  </div>
</template>

<script>
export default {
  name: "statisticsChartTools",
  props: {
    defaultTypes: {
      type: Array,
      default: () => [],
    },
    chartTypes: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    const today = new Date();
    return {
      cycles: ["1", "5", "10", "60"],

      date: today,
      cycle: "5",
      selectedTypes: this.defaultTypes,
    };
  },
  watch: {
    defaultTypes(types) {
      this.selectedTypes = types;
    },
  },
  methods: {
    updateChange(type) {
      this.$emit("update", {
        actionType: type,
        selectedTypes: this.selectedTypes,
        date: this.date,
        cycle: this.cycle,
      });
    },
  },
};
</script>

<style lang="less" scoped>
@chart-tool-bg: #100c2a;
.chart-tools {
  .g-flex();
  padding: 5px;
  background: @chart-tool-bg;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;

  .chart-date,
  .chart-cycle {
    width: 120px;
    :deep(input) {
      background: @chart-tool-bg;
      color: #fff;
      line-height: 26px;
      border-radius: 3px;
      border-color: #ccc;
      padding-right: 20px;
    }
    :deep(.el-select__caret) {
      width: 20px !important;
    }
  }
  .chart-cycle {
    width: 50px;
  }
  .chart-types {
    width: 380px;
    border: 1px solid #ccc;
    border-radius: 3px;
    overflow: hidden;
    :deep(.el-select__tags),
    :deep(.el-input__inner),
    :deep(.el-select__caret) {
      color: #fff;
      background: @chart-tool-bg !important;
      line-height: 26px;
      border-radius: 3px;
      border: 0;
      border-radius: 0;
    }
    :deep(.el-select__input) {
      margin-left: 10px;
      border: 0;
      border-radius: 0;
    }
    :deep(.el-select__caret) {
      width: 20px !important;
    }
  }
}
</style>
