<template>
  <v-tour name="myTour" :steps="steps" :option="myOptions"></v-tour>
</template>

<script>
export default {
  name: "my-tour",
  data() {
    return {
      myOptions: {
        useKeyboardNavigation: false,
        labels: {
          buttonSkip: "lang.rms.fed.function.skip",
          buttonPrevious: "lang.rms.fed.previousStep",
          buttonNext: "lang.rms.fed.nextStep",
          buttonStop: "lang.rms.fed.textCompleted",
        },
      },
      steps: [
        {
          target: "#v-step-0", // We're using document.querySelector() under the hood
          content: `Discover <strong>Vue Tour</strong>!`,
        },
        {
          target: ".v-step-1",
          content: "An awesome plugin made with Vue.js!",
        },
        {
          target: '[data-v-step="2"]',
          content:
            "Try it, you'll love it!<br>You can put HTML in the steps and completely customize the DOM to suit your needs.",
          params: {
            placement: "top",
          },
        },
      ],
    };
  },
  mounted: function () {
    // this.$tours["myTour"].start();
  },
};
</script>
