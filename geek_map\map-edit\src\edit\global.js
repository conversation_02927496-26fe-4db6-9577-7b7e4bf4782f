let editMap = null;
let app = null;
let viewport = null;

const setGlobalEditMap = a => {
  editMap = a;
};
const getGlobalEditMap = () => {
  return editMap;
};
const setGlobalApp = a => {
  app = a;
};
const getGlobalApp = () => {
  return app;
};
const setGlobalViewport = vp => {
  viewport = vp;
};
const getGlobalViewport = () => {
  return viewport;
};
export {
  setGlobalEditMap,
  getGlobalEditMap,
  setGlobalApp,
  getGlobalApp,
  setGlobalViewport,
  getGlobalViewport,
};
