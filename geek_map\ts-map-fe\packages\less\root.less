@charset "utf-8";
@import url(./pages/panel-top.less);
@import url(./pages/panel-right.less);
@import url(./pages/panel-device-warning.less);
@import url(./pages/dialog-help.less);
@import url(./pages/dialog-msg-wait.less);
@import url(./pages/pop-station.less);
@import url(./pages/warn-audio.less);

.map-monitor-root {
  // top-panel
  .map2d-top-panel {
    z-index: 99 !important;
  }
  // right-panel
  .map2d-right-panel {
    z-index: 99 !important;
  }
  // 加载中dom
  .map2d-spin {
    z-index: 100 !important;
  }
  // system warning border框
  .map2d-warning-box {
    z-index: 10 !important;
  }
  // 左侧 warning panel
  .map2d-left-warning-panel {
    z-index: 99 !important;
  }
  // station id
  .map2d-pop-station {
    z-index: 98 !important;
  }
}

.map-monitor-root {
  .map2d-box {
    position: absolute;
    top: 38px;
    bottom: 0;
    right: 0;
    left: 0;
    margin: auto;
    background: #eee;
  }
  .map2d-spin {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    margin: auto;
    background: #eee;
    padding: 30%;
    opacity: 0.5;
  }
  .map2d-warning-box {
    position: absolute;
    top: 88px;
    bottom: 50px;
    right: 50px;
    left: 50px;
    margin: auto;
    pointer-events: none;
    border-width: 10px;
    border-radius: 3px;
    border-style: solid;
    border-color: transparent;
  }
}

.map2d-dialog-msg-wait,
.map2d-warning-dialog,
.map2d-dialog-help,
.map2d-dialog-task-detail {
  .ant-modal-title {
    font-weight: 600;
  }
  .ant-modal-body {
    max-height: calc(95vh - 200px);
    overflow: auto;
  }
}
.map2d-warning-dialog {
  .ant-modal-body {
    padding: 12px;
    .ant-collapse-content > .ant-collapse-content-box {
      padding: 6px 10px;
    }
    .ant-collapse > .ant-collapse-item > .ant-collapse-header {
      padding: 8px 10px;
    }
  }
  .ant-modal-footer .ant-btn-default {
    display: none;
  }
}
.map2d-dialog-task-detail {
  .ant-modal-body {
    padding: 12px;
    .map2d-task-detail-tree {
      width: 100%;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 99;
      opacity: 0;
      cursor: pointer;

      .ant-select-selector {
        height: 28px;
        overflow: hidden;
        cursor: pointer;
        .ant-select-selection-item {
          cursor: pointer;
        }
        .ant-select-selection-item-remove {
          display: none !important;
        }
      }
    }
    .ant-table-thead .ant-table-cell {
      white-space: nowrap;
      font-weight: 600;
    }
  }
}
.map2d-task-detail-tree-dropdown {
  input {
    display: none !important;
  }
  .ant-select-tree-switcher-noop {
    width: 12px;
  }
}
.map2d-dead-robot-notification {
  opacity: 0.8;
  background: rgb(242 42 42 / 90%);
  color: #fff !important;
  .ant-notification-notice-close-x,
  .anticon.ant-notification-notice-icon-error,
  .ant-notification-notice-message {
    color: #fff;
  }
}
