<template>
  <div class="pointSelect">
    <el-tag
      v-for="itemVal in values"
      :key="itemVal"
      closable
      class="pintItem"
      type="success"
      @close="clearItem(itemVal.cellCode)"
    >
      {{ itemVal.cellCode }}
    </el-tag>
    <el-button class="button" type="primary" size="mini" @click="handle">
      {{ isSelectModel ? $t("完成") : $t("选择") }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { ref, Ref, watch, toRefs, nextTick } from "vue";
import { useAttrStore } from "@packages/store/attr";
import { ElMessage } from "element-plus";
import { useEditMap } from "@packages/hook/useEdit";
/**
 * 全局选择点位, 如果当前已经选择了点
 */

const props = defineProps<{
  modelValue: { cellCode: string; nodeId: string }[];
}>();

const editMap = useEditMap();
const propsRef = toRefs(props);
const isSelectModel = ref(false);
const values: Ref<{ cellCode: string; nodeId: string }[]> = ref(props.modelValue || []);
const attrStore = useAttrStore();

watch(propsRef.modelValue, value => {
  values.value = value;
});

const emits = defineEmits<{
  (event: "update:modelValue", value: { cellCode: string; nodeId: string }[]): void;
  (event: "selectHandle", value: { cellCode: string; nodeId: string }[]): void;
  (event: "change", value: { cellCode: string; nodeId: string }[]): void;
}>();

function handle() {
  const isSel = !isSelectModel.value;

  if (isSel) {
    ElMessage.success("请点击地图上的点位, 选择完成后点击完成");
    attrStore.setMultipleSelectMode(values.value?.map(item => item.nodeId));
  } else {
    const dataList = attrStore.clearMultipleSelectMode();
    const ids = dataList
      .filter(item => {
        return item.layerName === "CELL";
      })
      .map(item => {
        const { nodeId, cellCode } = item.properties;
        return { nodeId, cellCode };
      });
    values.value = ids;
    emits("update:modelValue", ids);
    emits("change", ids);
    nextTick(() => {
      emits("selectHandle", ids);
    });
  }
  isSelectModel.value = isSel;
}

function clearItem(code: string) {
  values.value = values.value.filter(item => item.cellCode !== code);
  emits("update:modelValue", values.value);
  emits("change", values.value);
}
</script>

<style scoped lang="scss">
.pointSelect {
  width: 100%;
  border: 1px Dashed #333;
  padding: 5px;
  border-radius: 1px;

  .pintItem {
    width: 100%;
    justify-content: space-between;
  }

  .button {
    height: 24px;
    width: 100%;
  }
}
</style>
