<template>
  <div class="leftView">
    <Shelf :auxiliary="true" :shelf-data="addHJModalData" />
  </div>
</template>

<script>
import { mapState } from "vuex";
import Shelf from "./shelf";

export default {
  props: {},
  computed: {
    ...mapState("containerModal", ["addHJModalData"]),
  },
  components: {
    Shelf,
  },
};
</script>

<style scoped lang="less">
.leftView {
  flex: 1;
  height: 100%;
  position: relative;
}
</style>
