<template>
  <section>
    <geek-customize-form ref="refFrom" :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      style="margin-top: 10px"
    >
      <template #operation="{ row }">
        <div v-if="row.boxStatus === 'POSITION_CONFIRMING'">
          <el-button type="primary" size="mini" class="btn-opt" @click="itemSave(row)">
            {{ $t("lang.rms.fed.confirm") }}
          </el-button>
          <el-button type="primary" size="mini" class="btn-opt" @click="itemChange(row)">
            {{ $t("lang.rms.box.changeBtn") }}
          </el-button>
        </div>
      </template>
    </geek-customize-table>
  </section>
</template>
<script>
export default {
  data() {
    let start = new Date();
    let end = new Date();
    start.setTime(start.getTime() - 3 * 60 * 60 * 1000);
    return {
      form: {
        boxCode: "",
        dataRange: [],
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          boxCode: {
            label: "lang.rms.fed.boxCode",
            default: "",
            tag: "input",
          },
          dataRange: {
            label: "lang.rms.fed.updateTime",
            default: [start, end],
            valueFormat: "yyyy-MM-dd HH:ss:mm",
            type: "datetimerange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": "lang.rms.fed.startTime",
            "end-placeholder": "lang.rms.fed.endTime",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      dicts: [],
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        columns: [
          {
            label: "lang.rms.fed.updateTime",
            prop: "updateTime",
          },
          {
            label: "lang.rms.fed.destShelfSide",
            prop: "destShelfSide",
            formatter: (row, column, cellValue, index) => {
              return this.$t(this.dicts.find(i => i.value === cellValue)?.label || row[column]);
            },
          },
          { label: "lang.rms.fed.shelfCoding", prop: "destShelfCode" },
          {
            label: "lang.rms.fed.stationNo",
            prop: "stationId",
            formatter(row, column, cellValue, index) {
              return row["destShelfCode"] ? "" : cellValue;
            },
          },
        ],
      },
    };
  },
  activated() {
    $req.reqRMSDict(["BOX_SEARCH"]).then(res => {
      if (res?.code !== 0) return;
      const data = res.data || {};
      const dicts = data["BOX_SEARCH"] || [];

      this.dicts = dicts.map(i => {
        return { label: i.descr, value: i.fieldCode };
      });
    });
  },
  methods: {
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      let start = new Date();
      let end = new Date();
      start.setTime(start.getTime() - 3 * 60 * 60 * 1000);
      this.tablePage.currentPage = 1;
      this.$refs.refFrom.setData({ dataRange: [start, end] });
      this.form = { boxCode: "", dataRange: [start, end] };
    },
    getTableList() {
      const [startTime, endTime] = this.form.dataRange;
      const params = {
        boxCode: this.form.boxCode,
        startTime: new Date(startTime).valueOf(),
        endTime: new Date(endTime).valueOf(),
        page: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      if (!params.boxCode) {
        this.$warning(this.$t("lang.mb.login.required", [this.$t("lang.rms.fed.boxCode")]));
        return;
      }
      $req.get("/athena/box/movePath", params).then(res => {
        let result = res.data;
        if (!result) return;
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          pageCount: result.pageCount || 0,
        });
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
