/* ! <AUTHOR> at 2023/04/20 */
namespace MRender {
  /**
   * 机器人icon
   */
  export type robotIcon =
    | "robotNormal"
    | "robotWork"
    | "robotOffline"
    | "robotError"
    | "robot_box"
    | "robot_belt";
  export type robotFatIcon =
    | "robotFatNormal"
    | "robotFatWork"
    | "robotFatOffline"
    | "robotFatError";
  export type robotThinIcon =
    | "robotThinNormal"
    | "robotThinWork"
    | "robotThinOffline"
    | "robotThinError";
  export type forkIcon = "forkNormal" | "forkWork" | "forkOffline" | "forkError";

  /**
   * 货架icon
   */
  export type shelfIcon = "shelf" | "rack" | "poppick" | "S_HOLDER" | "X_PALLET";
  /**
   * 货架状态icon
   */
  export type shelfStatusIcon = "shelfLocked" | "shelfFault";
  /**
   * 设备icon
   */
  export type deviceIcon = "deviceNormal" | "deviceConfigError" | "deviceError" | "dmpDeviceError";
  /**
   * 其他元素icon
   */
  export type elementIcon = "charger" | "elevator" | "station" | "dest";
  /**
   * 元素状态icon
   */
  export type statusIcon = "cellPause" | "status_selected" | "park";

  /** 所有icon名称 */
  export type iconsNames =
    | robotIcon
    | robotFatIcon
    | robotThinIcon
    | forkIcon
    | shelfIcon
    | shelfStatusIcon
    | elementIcon
    | statusIcon;
}
