<template>
  <geek-main-structure>
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      @row-add="addRobotEventType"
      @row-edit="handleEdit"
      @row-del="handleDel"
      style="margin-top: 10px"
    >
      <template #enableSaveDatabase="{ row }">
        <el-switch
          v-model="row.enableSaveDatabase"
          :active-value="true"
          :inactive-value="false"
          :active-color="switchOps.activeColor"
          :inactive-color="switchOps.inactiveColor"
          @change="changeSwitch(row)"
        ></el-switch>
      </template>
      <template #enablePublish="{ row }">
        <el-switch
          v-model="row.enablePublish"
          :active-value="true"
          :inactive-value="false"
          :active-color="switchOps.activeColor"
          :inactive-color="switchOps.inactiveColor"
          @change="changeSwitch(row)"
        ></el-switch>
      </template>
      <template #enableCallback="{ row }">
        <el-switch
          v-model="row.enableCallback"
          :active-value="true"
          :inactive-value="false"
          :active-color="switchOps.activeColor"
          :inactive-color="switchOps.inactiveColor"
          @change="changeSwitch(row)"
        ></el-switch>
      </template>
    </geek-customize-table>

    <edit-dialog ref="editDialog" @updateTableList="getTableList" />
  </geek-main-structure>
</template>

<script>
import EditDialog from "./components/editDialog";

export default {
  components: { EditDialog },
  data() {
    const isGuest = this.isRoleGuest();
    return {
      isLoading:false,
      switchOps:{
        activeColor:'#13ce66',
        inactiveColor:'#B9B9B9',
      },
      tableData: [],
      tableConfig: {
        actions: [
          {
            label: "lang.rms.fed.newlyAdded",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.fed.eventGroupName", prop: "eventGroupNameI18n" },
          { label: "lang.rms.fed.eventGroupValue", prop: "eventGroup" },
          { label: "lang.rms.fed.eventTypeName", prop: "eventTypeNameI18n" },
          { label: "lang.rms.fed.eventTypeValue", prop: "eventType" },
          { label: "lang.rms.fed.eventDes", prop: "eventDesc" },
          {
            label: "lang.rms.fed.enable.saveDatabase",
            prop: "enableSaveDatabase",
            slotName: 'enableSaveDatabase',
          },
          {
            label: "lang.rms.fed.enable.publish",
            prop: "enablePublish",
            slotName: 'enablePublish',
          },

          {
            label: "lang.rms.fed.robot.event.type.enableCallback",
            prop: "enableCallback",
            slotName: 'enableCallback',
          },
        ].concat(
          isGuest
            ? []
            : {
                label: "lang.rms.fed.listOperation",
                width: "120",
                fixed: "right",
                align: "center",
                operations: [
                  {
                    label: "lang.rms.fed.buttonEdit",
                    handler: "row-edit",
                  },
                  {
                    label: "lang.rms.fed.delete",
                    handler: "row-del",
                  },
                ],
              },
        ),
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    //更改switch
    changeSwitch(row) {
      this.isLoading = true
      const saveKey = [
        'id',
        'enableCallback',
        'enablePublish',
        'enableSaveDatabase',
        'eventDesc',
        'eventGroup',
        'eventGroupNameI18n',
        'eventType',
        'eventTypeNameI18n'
      ]
      const saveObj = {}
      saveKey.forEach(key => {
        saveObj[key] = row[key]
      })
      $req.post("/athena/robot/robotEventType/saveOrUpDate", saveObj).then(res => {
        if (res.code === 0){
          this.getTableList()
        }
      }).catch(err => {
        this.isLoading = false
      });
      this.isLoading = false
    },
    addRobotEventType() {
      this.$refs.editDialog.open();
    },
    handleEdit(row) {
      this.$refs.editDialog.open(row);
    },
    handleDel(row) {
      if (!row) return;
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete")).then(() => {
        $req.get("/athena/robot/robotEventType/delete", { id: row.id }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t(res.msg));
        });
      });
    },
    getTableList() {
      this.isLoading = true
      $req.get("/athena/robot/robotEventType/list").then(res => {
        this.isLoading = false
        let result = res.data;
        if (result && $utils.Type.isArray(result)) {
          result.forEach(element => {
            element.eventGroupNameI18n = this.$t(element.eventGroupNameI18n);
            element.eventTypeNameI18n = this.$t(element.eventTypeNameI18n);
          });
          this.tableData = result;
        }
      }).catch(err => {
        this.isLoading = false
      });
    },
    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>

<style lang="less" scoped>
.form-content {
  //color: #15db65;
  border-bottom: 5px solid #eee;
  padding-bottom: 10px;
  text-align: right;
}

.table-content {
  padding-top: 15px;

  .btn-opt {
    padding: 3px 5px;
    min-height: 10px;
  }
}
</style>
