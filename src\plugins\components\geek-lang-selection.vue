<template>
  <el-dropdown trigger="click" class="geek-lang-selection" @command="setLocalLang">
    <span class="el-dropdown-link">
      <img src="@imgs/common/icon-lang.png" width="20" />
    </span>
    <el-dropdown-menu slot="dropdown" class="lang-menu">
      <el-dropdown-item
        v-for="item in langList"
        :key="item.name"
        :command="item"
        :disabled="localLang === item.name"
      >
        {{ item.text }}
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
import langType from "@lang/_lang-type-data";

export default {
  name: "GeekLangSelection",
  data() {
    return {
      localLang: $utils.Data.getLocalLang(),
      langList: [],
    };
  },
  created() {
    $req
      .get("/athena/api/coreresource/i18n/getLanguages/v1")
      .then(res => {
        if (res.code !== 0 || !res.data || !$utils.Type.isArray(res.data)) return;
        const langData = res.data;

        let defaultLang = "";
        let langList = [];
        langData.forEach(item => {
          const code = item.code;
          if (item.isDefault === 1) defaultLang = code;
          if (langType[code]) {
            langType[code].name = code;
            langType[code].text = item.name;
            langType[code].apiUrl = code;
          } else {
            langType[code] = {
              name: code,
              text: item.name,
              apiUrl: code,
              getElementLang: () => {
                return null;
              },
              getGeekElementLang() {
                return null;
              },
            };
          }
          langList.push(langType[code]);
        });
        this.langList = langList;

        if (!$utils.Data.getLocalLang()) {
          if (defaultLang) $utils.Data.setLocalLang(defaultLang);
          else $utils.Data.setLocalLang();
        }

        const locale = $utils.Data.getLocalLang();
        $utils.Data.setI18nMessage(this.$i18n, langType[locale].apiUrl);
      })
      .catch(e => {
        this.langList = Object.values(langType);
        console.error(e);
      });
  },
  methods: {
    setLocalLang(lang) {
      const { Data } = $utils;
      const { name, apiUrl } = lang;
      this.localLang = name;
      Data.setLocalLang(name);
      Data.setI18nMessage(this.$i18n, apiUrl);

      this.postMessage();
    },

    postMessage() {
      $utils.postMessage("monitor2D", { type: "langChange", localLang: this.localLang });
      $utils.postMessage("singleEdit2D", { type: "langChange", localLang: this.localLang });
    },
  },
};
</script>

<style lang="less" scoped>
.geek-lang-selection {
  .el-dropdown-link {
    cursor: pointer;
  }
}
.lang-menu {
  max-height: 291px;
  overflow-y: scroll;
}
</style>
