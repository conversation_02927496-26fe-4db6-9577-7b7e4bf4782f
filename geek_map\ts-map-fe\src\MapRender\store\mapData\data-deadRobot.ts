/* ! <AUTHOR> at 2023/04/21 */

class DeadRobotData implements MRender.MapData {
  private usedColors: Array<color16> = [];
  private colors: Array<color16> = [
    0xe6f7ff, 0xbae7ff, 0x91d5ff, 0x69c0ff, 0x40a9ff, 0x1890ff, 0x96dd9, 0x50b3, 0x3a8c, 0x2766,
    0xe6fffb, 0xb5f5ec, 0x87e8de, 0x5cdbd3, 0x36cfc9, 0x13c2c2, 0x8979c, 0x6d75, 0x474f, 0x2329,
    0xf0f5ff, 0xd6e4ff, 0xadc6ff, 0x85a5ff, 0x597ef7, 0x2f54eb, 0x1d39c4, 0x10239e, 0x61178,
    0x30852, 0xfffbe6, 0xfff1b8, 0xffe58f, 0xffd666, 0xffc53d, 0xfaad14, 0xd48806, 0xad6800,
    0x874d00, 0x613400, 0xf6ffed, 0xd9f7be, 0xb7eb8f, 0x95de64, 0x73d13d, 0x52c41a, 0x389e0d,
    0x237804, 0x135200, 0x92b00, 0xa6a6a6, 0x999999, 0x8c8c8c, 0x808080, 0x737373, 0x666666,
    0x404040, 0x1a1a1a, 0x0, 0xfcffe6, 0xf4ffb8, 0xeaff8f, 0xd3f261, 0xbae637, 0xa0d911, 0x7cb305,
    0x5b8c00, 0x3f6600, 0x254000, 0xfff0f6, 0xffd6e7, 0xffadd2, 0xff85c0, 0xf759ab, 0xeb2f96,
    0xc41d7f, 0x9e1068, 0x780650, 0x520339, 0xfff7e6, 0xffe7ba, 0xffd591, 0xffc069, 0xffa940,
    0xfa8c16, 0xd46b08, 0xad4e00, 0x873800, 0x612500, 0xf9f0ff, 0xefdbff, 0xd3adf7, 0xb37feb,
    0x9254de, 0x722ed1, 0x531dab, 0x391085, 0x22075e, 0x120338, 0xfff1f0, 0xffccc7, 0xffa39e,
    0xff7875, 0xff4d4f, 0xf5222d, 0xcf1322, 0xa8071a, 0x820014, 0x5c0011, 0xfff2e8, 0xffd8bf,
    0xffbb96, 0xff9c6e, 0xff7a45, 0xfa541c, 0xd4380d, 0xad2102, 0x871400, 0x610b00, 0xfeffe6,
    0xffffb8, 0xfffb8f, 0xfff566, 0xffec3d, 0xfadb14, 0xd4b106, 0xad8b00, 0x876800, 0x614700,
  ];

  private mapData: { [propName: code]: color16 } = null;

  setData(code: code, data: number) {}

  setCodes(deadLockedRobotCodes: Array<code>) {
    if (!deadLockedRobotCodes || deadLockedRobotCodes.length === 0) {
      this.mapData = null;
      return;
    }

    if (!this.mapData) this.mapData = {};
    const mapData = this.mapData;

    let data, curIndex: number;
    for (let key in mapData) {
      data = mapData[key];
      curIndex = deadLockedRobotCodes.findIndex(code => code.toString() == key.toString());
      if (curIndex == -1) {
        this.colors.push(data);
        this.delData(key);
      }
    }

    deadLockedRobotCodes.forEach(code => {
      if (mapData[code]) return;
      if (!this.colors.length) {
        this.colors = this.usedColors;
        this.usedColors = [];
      }

      const color = this.colors.shift();
      mapData[code] = color;
      this.usedColors.push(color);
    });
  }

  getData(code: code) {
    return this.mapData[code];
  }

  getByFloorId(floorId: floorId) {}

  getAll() {
    return this.mapData;
  }

  delData(code: code) {
    delete this.mapData[code];
  }

  uninstall() {
    this.mapData = null;
    this.colors = [
      0xe6f7ff, 0xbae7ff, 0x91d5ff, 0x69c0ff, 0x40a9ff, 0x1890ff, 0x96dd9, 0x50b3, 0x3a8c, 0x2766,
      0xe6fffb, 0xb5f5ec, 0x87e8de, 0x5cdbd3, 0x36cfc9, 0x13c2c2, 0x8979c, 0x6d75, 0x474f, 0x2329,
      0xf0f5ff, 0xd6e4ff, 0xadc6ff, 0x85a5ff, 0x597ef7, 0x2f54eb, 0x1d39c4, 0x10239e, 0x61178,
      0x30852, 0xfffbe6, 0xfff1b8, 0xffe58f, 0xffd666, 0xffc53d, 0xfaad14, 0xd48806, 0xad6800,
      0x874d00, 0x613400, 0xf6ffed, 0xd9f7be, 0xb7eb8f, 0x95de64, 0x73d13d, 0x52c41a, 0x389e0d,
      0x237804, 0x135200, 0x92b00, 0xa6a6a6, 0x999999, 0x8c8c8c, 0x808080, 0x737373, 0x666666,
      0x404040, 0x1a1a1a, 0x0, 0xfcffe6, 0xf4ffb8, 0xeaff8f, 0xd3f261, 0xbae637, 0xa0d911, 0x7cb305,
      0x5b8c00, 0x3f6600, 0x254000, 0xfff0f6, 0xffd6e7, 0xffadd2, 0xff85c0, 0xf759ab, 0xeb2f96,
      0xc41d7f, 0x9e1068, 0x780650, 0x520339, 0xfff7e6, 0xffe7ba, 0xffd591, 0xffc069, 0xffa940,
      0xfa8c16, 0xd46b08, 0xad4e00, 0x873800, 0x612500, 0xf9f0ff, 0xefdbff, 0xd3adf7, 0xb37feb,
      0x9254de, 0x722ed1, 0x531dab, 0x391085, 0x22075e, 0x120338, 0xfff1f0, 0xffccc7, 0xffa39e,
      0xff7875, 0xff4d4f, 0xf5222d, 0xcf1322, 0xa8071a, 0x820014, 0x5c0011, 0xfff2e8, 0xffd8bf,
      0xffbb96, 0xff9c6e, 0xff7a45, 0xfa541c, 0xd4380d, 0xad2102, 0x871400, 0x610b00, 0xfeffe6,
      0xffffb8, 0xfffb8f, 0xfff566, 0xffec3d, 0xfadb14, 0xd4b106, 0xad8b00, 0x876800, 0x614700,
    ];
    this.usedColors = [];
  }

  destroy() {
    this.colors = null;
    this.uninstall();
  }
}

export default DeadRobotData;
