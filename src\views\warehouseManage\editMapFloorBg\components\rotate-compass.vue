<template>
  <div
    ref="rotateCompass"
    class="rotate-compass"
    @mouseup.stop="rotatePointerUp"
    @mousemove.stop="rotatePointerMove"
  >
    <span class="text">{{ rotate }}°</span>
    <div class="content" :style="`transform: translate(-50%, 0) rotateZ(${rotate}deg);`">
      <div class="rotate-pointer" @mousedown="rotatePointerDown" />
    </div>
  </div>
</template>

<script>
export default {
  name: "RotateCompass",
  props: {
    rotate: {
      type: Number,
      require: true,
    },
  },
  data() {
    return {
      isDrag: false,
    };
  },
  created() {
    window.addEventListener("mouseup", this.rotatePointerUp);
    window.addEventListener("mousemove", this.rotatePointerMove);
  },
  destroyed() {
    window.removeEventListener("mouseup", this.rotatePointerUp);
    window.removeEventListener("mousemove", this.rotatePointerMove);
  },
  methods: {
    // 鼠标按下
    rotatePointerDown() {
      this.isDrag = true;
    },

    // 鼠标移动
    rotatePointerMove(event) {
      if (!this.isDrag) return;
      const x = event.clientX,
        y = event.clientY;

      const $rotateCompass = this.$refs.rotateCompass;
      const offsetTop = this._getElementTop($rotateCompass);
      const offsetLeft = this._getElementLeft($rotateCompass);
      // 算出来好像少了5°左右, x18.25刚好 ~
      let rotate = 180 - Math.atan2(x - offsetLeft, y - offsetTop) * Math.PI * 18.25;
      rotate = parseFloat(rotate.toFixed(1), 10);
      this.$emit("change", null, rotate);
    },

    // 鼠标抬起
    rotatePointerUp() {
      this.isDrag = false;
      // todo 记录历史
    },

    _getElementTop(element) {
      let actualTop = element.offsetTop;
      let current = element.offsetParent;

      while (current !== null) {
        actualTop += current.offsetTop;
        current = current.offsetParent;
      }

      return actualTop;
    },
    _getElementLeft(element) {
      let actualLeft = element.offsetLeft;
      let current = element.offsetParent;

      while (current !== null) {
        actualLeft += current.offsetLeft;
        current = current.offsetParent;
      }

      return actualLeft;
    },
  },
};
</script>

<style lang="less" scoped>
.rotate-compass {
  width: 300px;
  height: 300px;
  background: #00000000;
  border-radius: 50%;
  border: 30px solid #9e9e9ea8;
  z-index: 33;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  .text {
    top: 50%;
    left: 50%;
    font-size: 24px;
    cursor: default;
    user-select: none;
    position: absolute;
    transform: translate(-50%, -50%);
  }

  .content {
    position: absolute;
    top: 0;
    left: 50%;
    width: 2px;
    height: 120px;
    background: #795548d9;
    transform-origin: bottom center;
    text-align: center;

    .rotate-pointer {
      width: 60px;
      height: 60px;
      background: rgba(77, 170, 255, 0.27);

      display: inline-block;
      position: absolute;
      top: -45px;
      left: -30px;
      border-radius: 50%;
      cursor: pointer;

      &::before {
        line-height: 60px;
        font-size: 52px;
        color: #ffffff;
      }
    }
  }
}
</style>
