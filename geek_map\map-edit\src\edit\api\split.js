import { orderBy } from "lodash-es";
import LayerManager from "../layerManager/LayerManager";

const split = function() {
  const selectedData = this.getAllSelected()
  const len = selectedData.length
  if(len < 3) return
  //四叉树查询是按照Y轴从大到小排序，只需排序X轴即可
  const locationInfo = selectedData.map(selected => {
    const {properties} = selected
    const {location:{x,y},nodeId} = {...properties}
    return {x,y,nodeId}
  })
  //按照x轴坐标大小排序,从大到小
  const orderX = orderBy(locationInfo,['x'],['asc'])
  const xLen = orderX.length
  const orderY = orderBy(locationInfo,['y'],['asc'])
  const yLen = orderY.length
  const minX = orderX[0].x,maxX = orderX[xLen - 1].x
  const minY = orderY[0].y,maxY = orderY[yLen - 1].y
  const splitX = (maxX - minX) / (len - 1)
  const splitY = (maxY - minY) / (len - 1)
  const splitData = selectedData.map((selected,index) => {
    const {properties} = selected
    const {location:{x,y},nodeId} = {...properties}
    const xIndex = orderX.findIndex(item => item.nodeId === nodeId)
    //计算新的位置
    const newLocation = {x:minX + xIndex * splitX ,y:maxY - splitY * index}
    //计算startBounds
    // const newStartBounds = {}
    return {
      nodeId,
      location: newLocation
    }
  })
  LayerManager.updateElements({
    id:'CELL',
    data:splitData
  })
}
export {
  split
}
