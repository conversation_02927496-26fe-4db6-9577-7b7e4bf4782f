import Chart, { requestCache, parseEchartOption } from "../common";

/**
 * 库区货位状态分布
 */
export default class LocStatusBar extends Chart {
  /**
   * 初始化图表 - 库区货位状态分布
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('bar', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "库区货位状态分布";
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'elDatePicker',
        defaultValue: $utils.Tools.formatDate(new Date(), "yyyy-MM-dd"),
        option: {

        }
      }
    ]
  }

  async request(params = {}) {
    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stats/query/lattice/snapshot', {
      date: $utils.Tools.formatDate(params?.date || new Date(), "yyyy-MM-dd"),
      cycle: 5
    })

    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const latticeSnapshotList = (data.latticeSnapshotList || []).filter(item => item.haveData);
    const xAxisData = latticeSnapshotList.map(item => {
      return $utils.Tools.formatDate(item.snapshotTime, "hh:mm:ss");
    })
    // 被预占货位总数
    const total = {
      name: '被预占货位总数',
      type: 'bar',
      data: latticeSnapshotList.map(item => item.latticeAllocatedUnlockCount)
    }

    // 占用货位总数
    const occupyTotal = {
      name: '占用货位总数',
      type: 'bar',
      data: latticeSnapshotList.map(item => item.latticeOccupiedUnlockCount)
    }

    // 空闲货位总数
    const idleTotal = {
      name: '空闲货位总数',
      type: 'bar',
      data: latticeSnapshotList.map(item => item.latticeVacantUnlockCount)
    }

    return parseEchartOption({
      title: { text: this.title || '' },
      legend: {
        data: ['被预占货位总数', '占用货位总数', '空闲货位总数']
      },
      xAxis: { type: 'category', data: xAxisData },
      yAxis: { type: 'value' },
      tooltip: { show: true, trigger: 'axis' },
      series: [total, occupyTotal, idleTotal]
    })
  }
}