//初始化事件回调管理
import EventBus from "../eventBus/EventBus";
import Store from "../store/Store";
import { pixi2cad } from "../utils/utils";
import LayerManager from "../layerManager/LayerManager";

const initEventBus = function () {
  //添加节点
  EventBus.$on("added", (data = []) => {
    console.log("added", data);
    data.forEach(item => {
      const { properties, layerName } = item;
      const { nodeId, segmentId, id } = properties;
      const elementId = nodeId || segmentId || id;
      this.setSelected({ layerName, id: elementId });
    });
    this.removeBlock();
  });
  //元素更新
  EventBus.$on("updated", (data = []) => {
    console.log("updated", data);
    data.forEach(info => {
      const { $el } = info;
      const { type } = $el;
      //如果为节点，更新与节点相关的线
      if (type === "element") {
        this._updateRelatedLine($el);
        this._updateRelatedDevice($el);
      }
    });
  });
  //元素删除
  EventBus.$on("deleted", (data = []) => {
    console.log("deleted", data);
    //被删除的单元格属性
    const deleteCellProperties = [];
    data.forEach(info => {
      const { properties } = info;
      const { cellType } = properties;
      if (cellType) {
        deleteCellProperties.push({ ...properties });
        this._deleteRelatedDevice(properties);
      }
    });
    if (deleteCellProperties.length) {
      this._deleteRelatedLine(deleteCellProperties);
      // this._deleteRelatedDevice(deleteCellProperties)
    }

    // data.forEach(info => {
    //   const {properties} = info
    //   const {cellType} = properties
    //   //如果为节点，更新与节点相关的线
    //   if(cellType){
    //     this._deleteRelatedLine(properties)
    //     this._deleteRelatedDevice(properties)
    //   }
    // })
  });
  //元素选中
  EventBus.$on("selected", (data = []) => {
    console.log("selected", data);
  });
  //拖拽开始
  EventBus.$on("dragging", (data = []) => {
    data.forEach($el => {
      const { type } = $el;
      //如果为节点，更新与节点相关的线
      if (type === "element") {
        this._updateRelatedLine($el);
        this._updateRelatedDevice($el);
      }
    });
  });
  //拖拽结束
  EventBus.$on("dragEnd", (data = []) => {});
  //历史变动
  EventBus.$on("historyChange", (data = []) => {
    console.log("history", data);
  });
  //模式重置回调
  EventBus.$on("modeReset", () => {
    console.log("模式重置！！！！！！");
  });
};
//添加事件回调
const bindEventBus = function (name, cb) {
  EventBus.$on(name, cb);
};
const offEventBus = function (name, cb) {
  EventBus.$off(name, cb);
};
const emitEvent = function(name,data) {
  EventBus.$emit(name,data)
}
//更新与节点关联的线段
const _updateRelatedLine = function ($el) {
  //获取跟节点相关的线段id
  const { nodeId, cellCode } = $el;
  const segmentIdArr = Store.node2Line.getLine(nodeId);
  if (!segmentIdArr || segmentIdArr.length === 0) return;
  const { x, y } = pixi2cad({ x: $el.x, y: $el.y });
  const updateData = segmentIdArr.map(segmentId => {
    const { properties } = LayerManager.getProperties({ layerName: "LINE", id: segmentId });
    const { points } = properties;
    const newPoints = points.map(p => {
      if (p.nodeId === nodeId) {
        return { ...p, cellCode, x, y };
      }
      return p;
    });
    const newProperties = { ...properties, points: newPoints };
    return newProperties;
  });
  LayerManager.updateElements({
    id: "LINE",
    isEmitData: false,
    data: updateData,
    isSaveHistory: false,
  });
};
const _deleteRelatedLine = function (properties) {
  //获取跟节点相关的线段id
  let deleteSegmentIds = [];
  properties.forEach(property => {
    const { nodeId } = property;
    //获取与节点关联的线
    const segmentIdArr = Store.node2Line.getLine(nodeId);
    if (segmentIdArr) {
      deleteSegmentIds.push(...segmentIdArr);
    }
  });
  //去重
  deleteSegmentIds = [...new Set(deleteSegmentIds)];
  if (!deleteSegmentIds.length) return;
  LayerManager.deleteElements({
    id: "LINE",
    data: deleteSegmentIds,
    isEmitData: false,
    historyContinue: true,
  });
};
//更新与节点关联的设备
const _updateRelatedDevice = function ($el) {
  const { cellCode, nodeId } = $el;
  const relatedId = cellCode || nodeId;
  const deviceArr = Store.cellCode2Device.getDevice(relatedId);
  const { x, y } = pixi2cad({ x: $el.x, y: $el.y });
  deviceArr.forEach(item => {
    const { layerName, id } = item;
    const mapEditItemId = relatedId;
    const updateOp = {
      id,
      location: { x, y },
      cellCode,
      mapEditItemId,
    };
    LayerManager.updateElements({ id: layerName, data: [updateOp], isSaveHistory: false });
  });
};
//删除关联设备
const _deleteRelatedDevice = function (properties) {
  //获取跟节点相关的线段id
  const { cellCode, mapEditItemId } = properties;
  const relatedId = cellCode || mapEditItemId;
  const deviceArr = Store.cellCode2Device.getDevice(relatedId);
  deviceArr.forEach(item => {
    const { layerName, id } = item;
    LayerManager.deleteElements({ id: layerName, data: [id], historyContinue: true });
  });
};

const triggerLayers = layerNames => {
  LayerManager.triggerLayers(layerNames);
};

export {
  initEventBus,
  bindEventBus,
  offEventBus,
  // emitEvent,
  _updateRelatedLine,
  _deleteRelatedLine,
  _updateRelatedDevice,
  _deleteRelatedDevice,
  triggerLayers,
};
