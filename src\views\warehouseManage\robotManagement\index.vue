<template>
  <geek-main-structure style="padding-top: 0">
    <geek-tabs-nav :block="true" :nav-list="permissionNavList" @select="tabsNavChange" class="robot-management-nav" />
    <keep-alive>
      <component :is="activeName" />
    </keep-alive>
  </geek-main-structure>
</template>

<script>
import RobotInstance from "./robotInstance"; // 机器人实例
import RobotModal from "./robotModal"; // 机器人型号
import BusinessFeatureModel from "./businessFeatureModel"; // 业务特征模型
import MechanismComponentModel from "./mechanismComponentModel"; // 机构组件模型
import MechanismModel from "./mechanismModel"; // 机构模型
import InstitutionsModel from "./institutionsModel"; //本体模型

export default {
  components: {
    RobotInstance,
    RobotModal,
    BusinessFeatureModel,
    MechanismComponentModel,
    MechanismModel,
    InstitutionsModel,
  },
  data() {
    return {
      permissionNavList: [],
      navList: [
        { permissionName: "TabInstancePage", id: "RobotInstance", text: "lang.rms.api.result.warehouse.robotExample" },
        { permissionName: "TabModelPage", id: "RobotModal", text: "lang.rms.api.result.warehouse.robotModel" },
        {
          permissionName: "TabBussinessPage",
          id: "BusinessFeatureModel",
          text: "lang.rms.api.result.warehouse.businessModel",
        },
        {
          permissionName: "TabComponentManage",
          id: "MechanismComponentModel",
          text: "lang.rms.api.result.warehouse.mechanismComponentModel",
        },
        {
          permissionName: "TabMechanismPage",
          id: "MechanismModel",
          text: "lang.rms.api.result.warehouse.mechanismModel",
        },
        {
          permissionName: "TabOntologyPage",
          id: "InstitutionsModel",
          text: "lang.rms.api.result.warehouse.ontologyModel",
        },
      ],
      activeName: "",
    };
  },
  activated() {
    let pList = this.navList.filter(item => this.getTabPermission(item.permissionName, "robotManage"));
    this.permissionNavList = pList;
    this.activeName = pList.length ? pList[0].id : "";
    if (!pList.length) {
      this.$error(this.$t("lang.rms.web.noPermissionToThatPage"));
    }
  },
  // 方法集合
  methods: {
    tabsNavChange(id) {
      if (this.activeName === id) return;
      this.activeName = id;
    },
  },
};
</script>
<style lang="less" scoped>
.robot-management-nav {
  padding: 10px 0 0;
  position: sticky;
  top: 0;
  left: 0;
  background: #fff;
  z-index: 9;
}
</style>
