/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import { Button, Card, Select, Input, Tag, message } from "antd";
import { getMap2D, $eventBus } from "../../../../singleton";

import OrderPanelGrid from "../common/order-panel-grid";

const { Option } = Select;
type PropsOrderData = {
  mapReleased: boolean;
  onCancel: () => void;
  refreshList: () => void;
};
function warehouseTaskCreate(props: PropsOrderData) {
  const { t } = useTranslation();
  const [inspectionType, setInspectionType] = useState("0");
  const [workCode, setWorkCode] = useState("");
  const [robotId, setRobotId] = useState("");
  const [rackLayer, setRackLayer] = useState("");
  const [startLocation, setStartLocation] = useState("");
  const [stopLocation, setStopLocation] = useState("");

  // 地图点击
  useEffect(() => {
    $eventBus.on("mapClick", (data: MRender.clickParams) => {
      switch (data?.layer) {
        case "robot":
          setRobotId(data.code.toString());
          break;
      }
    });

    const map2D = getMap2D();
    map2D.mapRender.enableMultiClick(true, ["cell"]);
    map2D.mapRender.triggerLayers(["robot"]);
    return () => {
      $eventBus.off("mapClick");
      map2D.mapRender.triggerLayers([]);
      map2D.mapWorker.stopQuery();
      map2D.mapRender.clearSelects();
      map2D.mapRender.enableMultiClick(false);
    };
  }, []);

  const inspectionSelected = (value: string) => {
    setInspectionType(value);
    if (value === "3") setRackLayer("");
  };

  const areaSelect = () => {
    const map2D = getMap2D();
    map2D.mapRender.clearSelects("cell");
    map2D.mapRender.trigger("rect", data => {
      if (data.code !== 0) {
        message.error("不能超出楼层范围或跨楼层");
        return;
      }
      const start = data["points"][0];
      const stop = data["points"][1];

      setStartLocation(start);
      setStopLocation(stop);

      const type = Number(inspectionType);
      _$utils
        .reqPost("/athena/warehouse/cell/queryByLocationRange", {
          startLocation: start,
          stopLocation: stop,
          type: type === 4 ? null : type,
        })
        .then(res => {
          if (res.code !== 0) return;
          const data = res.data || [];
          const codes = data.map((item: any) => item.cellCode);
          map2D.mapRender.trigger("click", { cell: codes });
        });
    });
  };

  const newWorkSubmit = () => {
    let params: any = {
      inspectionName: workCode || null,
      robotId: robotId || null,
      startLocation: startLocation || null,
      stopLocation: stopLocation || null,
      inspectionType: Number(inspectionType),
      layer: rackLayer || null,
      type: 0,
    };

    if (Number(inspectionType) === 4) params.type = 1;

    _$utils.reqPost("/athena/warehouse/inspection/create", params).then(res => {
      if (res.code === 0) {
        props.onCancel();
        props.refreshList();
      }
    });
  };

  return (
    <Card
      size="small"
      type="inner"
      title={t("lang.rms.fed.buttonNewWork")}
      extra={<CloseOutlined onClick={props.onCancel} />}
      actions={[
        <div style={{ float: "right" }}>
          <Button onClick={props.onCancel} size="small" style={{ marginRight: 5 }}>
            {t("lang.rms.fed.cancel")}
          </Button>
          <Button type="primary" onClick={newWorkSubmit} size="small">
            {t("lang.rms.fed.confirm")}
          </Button>
        </div>,
      ]}
      className="component-operate-detail"
    >
      <OrderPanelGrid
        style={{ border: 0, margin: 0 }}
        items={[
          {
            label: t("lang.rms.fed.listInspectionType"),
            node: (
              <Select
                placeholder={t("lang.rms.fed.choose")}
                dropdownMatchSelectWidth={false}
                onChange={inspectionSelected}
                value={inspectionType}
              >
                <Option value="0">{t("lang.rms.fed.inspectionSelected1")}</Option>
                <Option value="1">{t("lang.rms.fed.inspectionSelected2")}</Option>
                <Option value="2">{t("lang.rms.fed.inspectionSelected3")}</Option>
                <Option value="3">{t("lang.rms.fed.inspectionSelected4")}</Option>
                <Option style={props.mapReleased ? { display: "none" } : null} value="4">
                  {t("lang.rms.fed.QRcodeScan")}
                </Option>
              </Select>
            ),
          },
        ]}
      />
      <Tag
        icon={<ExclamationCircleOutlined />}
        color="warning"
        style={{ margin: "6px 3px", whiteSpace: "break-spaces" }}
      >
        {t("lang.rms.fed.inspectionTip")}
      </Tag>

      <div>
        <Button size="small" type="primary" ghost style={{ marginLeft: 3 }} onClick={areaSelect}>
          {t("lang.rms.fed.areaSelect")}
        </Button>
      </div>

      <OrderPanelGrid
        style={{ borderRight: 0, borderLeft: 0, borderBottom: 0, marginBottom: 0 }}
        items={[
          {
            label: t("lang.rms.fed.inputRobotId"),
            node: (
              <Input
                value={robotId}
                onChange={e => setRobotId(e.target.value)}
                placeholder={t("lang.rms.fed.inputRobotId")}
              />
            ),
          },
          {
            label: t("lang.rms.fed.inputWorkCode"),
            node: (
              <Input
                value={workCode}
                onChange={e => setWorkCode(e.target.value)}
                placeholder={t("lang.rms.fed.inputWorkCode")}
              />
            ),
          },
        ]}
      />

      <OrderPanelGrid
        style={{
          borderRight: 0,
          borderLeft: 0,
          borderTop: 0,
          margin: 0,
          display: inspectionType === "3" ? "block" : "none",
        }}
        items={[
          {
            label: t("lang.rms.fed.rackShelfLayer"),
            node: (
              <Input
                value={rackLayer}
                onChange={e => setRackLayer(e.target.value)}
                placeholder={t("lang.rms.fed.rackShelfLayer")}
              />
            ),
          },
        ]}
      />
    </Card>
  );
}

export default warehouseTaskCreate;
