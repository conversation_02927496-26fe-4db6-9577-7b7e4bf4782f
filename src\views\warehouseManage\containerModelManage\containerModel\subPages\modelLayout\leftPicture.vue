<template>
  <div class="pictureC">
    <div class="picture">
      <!-- 行 -->
      <div
        class="pictureRow"
        v-for="(item, index) in planeAllArr[active].row"
        :style="{ height: heightFun(item.height, planeAllArr[active].row) + '%' }"
      >
      <!-- 格  列 -->
      <div v-for="(inItem,inIndex) in item.cel" :style="{ width: 100 / item.cel + '%', height: '100%' }">
          <span class="picContent">
            {{ planeAllArr[active].row.length - index }} {{ ABCDE(inIndex) }}
          </span>
        </div>
      </div>
    </div>
    <div class="tui">
      <div></div>
      <div></div>
    </div>
    <ul class="hjPictureUl">
      <li :class="{ liActive: active == 0 }" @click="liClick(0)">前面(F)</li>
      <li
        v-if="planeAllArr.length == '2' || planeAllArr.length == '4'"
        @click="liClick(1)"
        :class="{ liActive: active == 1 }"
      >
        后面(B)
      </li>
      <li v-if="planeAllArr.length == '4'" @click="liClick(2)" :class="{ liActive: active == 2 }">
        左面(L)
      </li>
      <li v-if="planeAllArr.length == '4'" @click="liClick(3)" :class="{ liActive: active == 3 }">
        右面(R)
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => {},
    },
    planeAllArr: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    "planeAllArr": {
      handler(nV) {
        console.log(nV,"新值")
        if(nV.length!=this.allLength){
          this.allLength = nV.length
          this.liClick(0)
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      active: 0,
      arrLi: [
        { label: "前面", code: "", index: 0 },
        { label: "后面", code: "", index: 1 },
        { label: "左面", code: "", index: 2 },
        { label: "右面", code: "", index: 3 },
      ],
      allLength:1
    };
  },
  methods: {
    liClick(index) {
      this.active = index;
      this.$emit("planeClick", this.arrLi[index]);
    },
    heightFun(height, arr) {
      let all = eval(arr.map(e => e.height / 1).join("+"));
      return (height / all) * 100;
    },
    ABCDE(index) {
      let view = "";
      switch (index) {
        case 0:
          view = "A";
          break;
        case 1:
          view = "B";
          break;
        case 2:
          view = "C";
          break;
        case 3:
          view = "D";
          break;
        case 4:
          view = "E";
          break;
      }

      return view;
    },
  },
};
</script>

<style scoped>
.tui {
  width: 100%;
  height: 10px;
  background: #dcdfe6;
  position: relative;
}
.tui > div:nth-child(1) {
  width: 20px;
  height: 12px;
  background: #dcdfe6;
  position: absolute;
  left: 0;
  top: 100%;
}
.tui > div:nth-child(2) {
  width: 20px;
  height: 12px;
  background: #dcdfe6;
  position: absolute;
  right: 0;
  top: 100%;
}
.picContent {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  margin: auto;
  display: block;
  width: 100%;
  text-align: center;
}
.pictureRow > div {
  float: left;
  box-sizing: border-box;
  position: relative;
  border: 0.5px solid #dcdfe6;
  border-collapse: collapse;
  /* margin: 0 -1px -1px 0; */
}
.hjPictureUl {
  position: absolute;
  bottom: 1px;
  display: flex;
  -webkit-justify-content: center;
  justify-content: space-around;
  width: 100%;
}
.hjPictureUl li {
  padding: 3px 8px;
  border: 1px solid #eee;
  border-radius: 10px;
  font-size: 12px;
  cursor: pointer;
}
.liActive {
  background: #409eff;
  border: 1px solid #409eff;
  color: #fff;
}
.picture {
  height: 83%;
  border: 0.5px solid #dcdfe6;
}
.pictureC {
  height: 100%;
}
</style>