<template>
  <div class="box">
    <el-radio-group v-model="tabContent" style="margin-bottom: 20px">
      <el-radio-button
        v-for="item in modules"
        :key="item.id"
        :label="item.path"
        @click.native="tabClick(item.path)"
      >
        {{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <el-card
      v-for="item in modules"
      v-show="tabContent == item.path"
      :key="item.id"
      :class="[{ 'new-box-card': alertTipShow }, 'box-card']"
    >
      <div slot="header" class="clearfix">
        <span class="title">{{ item.label }}</span>
        <span v-if="alertIsShow" class="note">
          <i class="d" />{{ $t("lang.rms.fed.redIndicatesParameterNotTakeEffectToRestart") }}
        </span>
        <el-button
          style="float: right; margin-left: 10px"
          size="mini"
          :disabled="!changeData"
          @click="cancel"
        >
          {{ $t("lang.rms.fed.cancel") }}
        </el-button>
        <el-button
          type="primary"
          style="float: right"
          size="mini"
          :disabled="!changeData"
          @click="apply"
        >
          {{ $t("lang.rms.fed.application") }}
        </el-button>
      </div>
      <parameterForm
        :path="modulesPath"
        :list="newModulesList"
        :show="show"
        @changeData="change"
        @itemsDetail="itemsDetail"
      ></parameterForm>
    </el-card>
  </div>
</template>

<script>
import parameterForm from "./parameterForm";
export default {
  components: {
    parameterForm,
  },
  props: {
    show: {
      type: Boolean,
      default() {
        return true;
      },
    },
    path: {
      type: String,
      default() {
        return "";
      },
    },
    currentName: {
      type: String,
      default() {
        return "";
      },
    },
    modules: {
      type: Array,
      default() {
        return [];
      },
    },
    modulesList: {
      type: [Array, Object],
      default() {
        return [];
      },
    },
    alertTipShow: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  data() {
    return {
      newModulesList: [],
      oldModulesList: [],
      tabContent: "",
      modulesPath: "",
      changeData: false,
      // alertIsShow: false,
      // immediateShow: false,
      paramsObj: {},
      itemsDetailObj: {
        code: "",
        contributor: "RMS",
        value: "",
      },
    };
  },
  computed: {
    alertIsShow() {
      let flag = false;
      if (this.newModulesList) {
        for (let key in this.newModulesList) {
          this.newModulesList[key] &&
            this.newModulesList[key].forEach(item => {
              if (item.immediate == false && item.value !== item.currentUpdateValue) {
                flag = true;
              }
            });
        }
      }
      return flag;
    },
  },
  watch: {
    currentName: {
      // 一级tab默认展示
      handler(newval, oldval) {
        this.tabContent = ''
        // console.log(newval);
      },
      deep: true,
      immediate: true,
    },
    modulesList: {
      handler(val) {
        // console.log(val, 'modulesList');
        if (val && Object.prototype.toString.call(val) === "[object Object]") {
          const path = Object.keys(val)[0];
          this.modulesPath = `/${this.currentName}/${path}`;
          setTimeout(() => {
            this.tabContent = `/${this.currentName}/${path}`;
          }, 30)
          // console.log(this.modulesPath);
          if (val[path]) {
            this.newModulesList = JSON.parse(JSON.stringify(val[path]));
            this.oldModulesList = JSON.parse(JSON.stringify(val[path]));
            // console.log(this.newModulesList, "newModulesList");
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // 默认展示
    // this.modulesPath = "/map/resolver";
  },
  methods: {
    tabContentReset() {
      this.tabContent = ""
    },
    tabClick(path) {
      // console.log(path)
      const newPath = path.split("/")[2];
      this.modulesPath = path;
      // this.newModulesList = this.modulesList[newPath];
      this.newModulesList = JSON.parse(JSON.stringify(this.modulesList[newPath]));
      this.oldModulesList = JSON.parse(JSON.stringify(this.modulesList[newPath]));
    },
    change(n) {
      this.changeData = n;
      this.$emit("changeData", this.changeData);
    },
    itemsDetail(items) {
      console.log(items);
      this.paramsObj[items.code] = {
        code: items.code,
        contributor: "RMS",
        errorTipShow: items.errorTipShow,
        value: items.options.defVal,
      };
      if (items.type === "timeslot") {
        this.paramsObj[items.code].value = items.options.defVal
          ? JSON.stringify(items.options.defVal)
          : "";
      }
      for (let key in this.newModulesList) {
        // console.log(key);
        this.newModulesList[key].forEach(item => {
          // console.log(item.limitKey);
          if (item.code === items.code) {
            item.options.defVal = items.options.defVal;
            item.errorTipShow = items.errorTipShow;
            item.errorTipText = items.errorTipText
          }
          if (item.limitKey) {
            const keysArr = Object.keys(item.limitKey);
            if (keysArr[0] == items.code) {
              item.isShow = items.options.defVal == item.limitKey[keysArr[0]];
            }
          }
        });
      }
    },
    apply() {
      // 未立即生效参数提示显示
      this.$emit("alertShow", true);
      // this.alertIsShow = true;
      // this.immediateShow = true;
      let arr = [];
      for (let key in this.paramsObj) {
        arr.push(this.paramsObj[key]);
      }

      let isFlag = false
      arr.forEach(item => {
        if (item.errorTipShow) {
          isFlag = true
        }
      })
      if (isFlag) return
      $req.post("/athena/configs/update", arr, { intercept: false }).then(res => {
        // console.log(res);
        if (res && res.result === "success") {
          this.$message.success(this.$t("lang.common.success"));
          this.changeData = false;
          this.$emit("applySuccess");
        }
      });
    },
    cancel() {
      // 按钮恢复
      this.changeData = false;
      this.paramsObj = {};
      this.$emit("changeData", this.changeData);
      this.newModulesList = JSON.parse(JSON.stringify(this.oldModulesList));
    },
  },
};
</script>

<style>
.box .el-radio-button__inner {
  font-size: 14px !important;
}
</style>

<style lang="less" scoped>
.box {
  width: 100%;
  height: 100%;
  margin-left: 10px;
}
.box-card {
  border-radius: 13px;
  margin-top: -5px;
  height: calc(100% - 110px);
  padding-bottom: 60px;
}
.new-box-card {
  height: calc(100% - 142px);
}
.box-card /deep/.el-card__body {
  height: 100%;
  overflow: auto;
}
.title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #323334;
}
:deep(.el-card__header) {
  padding: 12px 20px;
}
.d {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #f23f3f;
  border-radius: 6px;
  margin: 0px 6px 1px 16px;
}
.note {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #999ea5;
}
</style>
