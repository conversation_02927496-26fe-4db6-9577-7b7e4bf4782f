/* ! <AUTHOR> at 2023/04/20 */

namespace MRender {
  /** MapData 类 */
  export interface MeshDataMain {
    /** cell数据 */
    cell: MeshData;
    /** shelf数据 */
    shelf: MeshData;
    /**rack数据 */
    rack: MeshData;
    /** poppick数据 */
    poppick: MeshData;
    /**
     * 数据卸载
     */
    uninstall(): void;
    /**
     * 数据销毁
     */
    destroy(): void;
  }

  /** layer 类 */
  export interface MeshData {
    /**
     * 添加MapData
     * @param code 唯一code
     * @param data 相对应的数据类型
     */
    setData(code: code, data: any): void;

    /**
     * 根据code获取数据
     * @param code 唯一code
     */
    getData(code: code): any;

    /**
     * 获取所有MapData
     */
    getAll(): { [propName: code]: any };

    /**
     * 根据code删除数据
     * @param code 唯一code
     */
    delData(code: code): void;

    /**
     * 根据floorId删除当前的meshData
     * @param floorId
     */
    delByFloorId(floorId: floorId): void;

    /**
     * 数据卸载
     */
    uninstall(): void;
    /**
     * 数据销毁
     */
    destroy(): void;
  }
}
