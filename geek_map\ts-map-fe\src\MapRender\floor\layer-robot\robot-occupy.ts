/* ! <AUTHOR> at 2022/09/02 */
import * as PIX<PERSON> from "pixi.js";

class LayerRobotTrail implements MRender.Layer {
  private container: PIXI.Container;
  private fragment: number;
  private lineStyle: any = new PIXI.LineStyle();
  private geometries: any = [];
  private meshList: any = [];
  init(mapCore: MRender.MainCore): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "robotTrail";
    container.zIndex = utils.getLayerZIndex("robotOccupy");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
  }

  render(): void {
    const _this = this;
    const geometries = _this.geometries;
    if (!geometries.length) return;

    const fragment = _this.fragment;
    const lineStyle = _this.lineStyle;

    for (let i = 0, len = Math.ceil(geometries.length / fragment); i < len; i++) {
      const arr = geometries.slice(i * fragment, i * fragment + fragment);

      let graphicsGeometry: any = new PIXI.GraphicsGeometry();
      graphicsGeometry.BATCHABLE_SIZE = arr.length;

      arr.forEach((item: any) => {
        graphicsGeometry.drawShape(item.polygon, item.fillStyle, lineStyle);
      });

      const graphics = new PIXI.Graphics(graphicsGeometry);
      graphics.name = "robotOccupy";
      graphics.interactive = graphics.buttonMode = false;

      _this.meshList.push(graphics);
      _this.container.addChild(graphics);
    }
    _this.geometries = [];
  }

  drawGeometry(options: mRobotData): void {
    const _this = this;
    const occupyPoints = options["occupyPoints"];
    const color = options["color"];

    let item, fillStyle;
    for (let i = 0, len = occupyPoints.length; i < len; i++) {
      item = occupyPoints[i]?.points;
      if (!item || item.length < 3) continue;

      let result: any = [];
      item.forEach((p: any) => {
        result.push(p.x);
        result.push(-p.y);
      });

      fillStyle = new PIXI.FillStyle();
      fillStyle.color = color;
      fillStyle.visible = true;

      // console.log(result);
      _this.geometries.push({ polygon: new PIXI.Polygon(result), fillStyle });
    }
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.geometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.lineStyle = null;
    this.geometries = null;
    this.meshList = null;
  }
}
export default LayerRobotTrail;
