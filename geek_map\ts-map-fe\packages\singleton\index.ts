/* ! <AUTHOR> at 2022/08/30 */
import Monitor2D from "./map2d/index";
import $eventBus from "./eventBus/index";
import { checkBtn, getPermissionTab } from "./permission/index";
import * as $audio from "./audio-player/index";

class SingletonMap {
  private static _instance: Monitor2D.Main;
  private static _$map: HTMLElement = null;
  public static getInstance() {
    return this._instance;
  }
  public static get$map() {
    return this._$map || null;
  }
  createMap($dom: HTMLElement, options: Monitor2D.MapOptions) {
    if (!SingletonMap._instance) {
      console.log("[SingletonMap] ----- :: new Map2D");
      SingletonMap._$map = $dom;
      SingletonMap._instance = new Monitor2D($dom, options);
    }
    return SingletonMap._instance;
  }
}
function createMap2D($dom: HTMLElement, options: Monitor2D.MapOptions) {
  return new SingletonMap().createMap($dom, options);
}
function getMap2D() {
  return SingletonMap.getInstance();
}
function getMapDom() {
  return SingletonMap.get$map();
}

export { createMap2D, getMap2D, getMapDom, $eventBus, $audio, checkBtn, getPermissionTab };
