import { Container } from "pixi.js";
import { getGlobalViewport } from "../global";
import { cad2pixi } from "../utils/utils";
import _ from "lodash";
class Base {
  constructor() {
    this.container = null;
    this.viewport = null;
    this.id = null;
    //id -> 对应元素
    this.id2$el = new Map();
    //元素 -> 属性
    this.$el2properties = new WeakMap();
    this.layerName = "BASE";
  }
  initLayer(op) {
    const { id, zIndex } = op;
    this.id = id;
    this.viewport = getGlobalViewport();
    this.container = new Container();
    this.container.id = id;
    this.container.name = id;
    this.container.zIndex = zIndex;
    this.container.interactive = true;
    this.container.interactiveChildren = true;
    this.container.sortableChildren = true;
    this.viewport.addChild(this.container);
  }
  //设置元素属性,现在的更新是增量更新，有时需要覆盖属性
  setProperties(id, $el, properties, isCoverProperties) {
    this.id2$el.set(id, $el);
    let new_properties;
    if (isCoverProperties) {
      this.$el2properties.set($el, properties);
    } else {
      const old_properties = this.$el2properties.get($el);
      new_properties = old_properties ? Object.assign(old_properties, properties) : properties;
      this.$el2properties.set($el, new_properties);
    }
    // const old_properties = this.$el2properties.get($el)
    // new_properties = old_properties ? Object.assign(old_properties,properties) : properties
    // this.$el2properties.set($el,new_properties)
    // this.$el2properties.set($el,properties)
  }
  //通过id获取元素属性
  getProperties(id) {
    const $el = this.id2$el.get(id);
    if (!$el) return null;
    const properties = this.$el2properties.get($el);
    return {
      $el,
      // properties:{...properties},
      properties: _.cloneDeep(properties),
      layerName: this.layerName,
    };
  }
  //显示图层
  showLayer() {
    this.container.visible = true;
  }
  hideLayer() {
    this.container.visible = false;
  }
  //获取素有的元素属性
  getAllData() {
    const data = [...this.id2$el].map(arr => {
      const [id, $el] = arr;
      const properties = this.$el2properties.get($el);
      return properties;
    });
    return data;
  }

  //是否可以被点击
  triggerLayer(isTrigger) {
    // this.container.interactiveChildren = isTrigger;
  }
}
export default Base;
