<template>
  <div class="parameterFromBox">
    <!-- 下拉框 -->
    <div v-for="item in selectList" :key="item.id" class="form-input">
      <i v-show="item.status" class="d" /><span class="form-title">{{ item.title }} :</span>
      <el-select v-model="value" clearable placeholder="请选择">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <img
        src="../../../../imgs/parameterConfig/question.png"
        class="questionImg"
        @click="detail(item)"
      />
    </div>
    <!-- 开关 -->
    <div v-for="item in statusList" :key="item.id" class="form-switch">
      <span class="form-title">{{ item.title }} :</span>
      <el-switch
        v-model="item.status"
        active-color="#409EFF;"
        active-text="开"
        inactive-text="关"
        @change="switchChange"
      >
      </el-switch>
    </div>
    <!-- 输入框 -->
    <div v-for="item in inputList" :key="item.id" class="form-input">
      <span class="form-title">{{ item.title }} :</span>
      <el-input v-model="input" placeholder="请输入内容"> </el-input>
    </div>
    <dialogDetail :dialog-visible="dialog" :item-list="dataList" @close="close"></dialogDetail>
  </div>
</template>

<script>
import dialogDetail from "./dialogDetail";
import map from "../config.js";
import { selectList, options, statusList, inputList } from "../config.js";
export default {
  name: "WorkspaceJsonBasicConfig",
  components: {
    dialogDetail,
  },
  props: {
    path: {
      type: String,
      default() {
        return "";
      },
    },
  },
  data() {
    return {
      value: "",
      switchValue: true,
      statusList,
      dialog: false,
      selectList,
      options,
      inputList,
      dataList: [],
      input: "",
      map,
    };
  },
  mounted() {},
  created() {
    this.getList();
  },
  methods: {
    switchChange() {
      console.log("status", this.switchValue);
    },
    detail(data) {
      if (this.dialog === false) {
        this.dialog = true;
      }
      const item = [];
      item.push(data);
      this.dataList = item;
      console.log("this.dataList", this.dataList);
    },
    close(n) {
      if (n === 1) {
        this.dialog = false;
      }
    },
    getList() {
      console.log("this.List", this.map);
    },
  },
};
</script>

<style lang="less" scoped>
.parameterFromBox {
  line-height: 38.6px;
  margin-top: -6px;
}
.form-title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #686c71;
  margin-right: 6px;
}
.form-switch :deep(.el-switch__label) {
  position: absolute;
  display: none;
  color: #fff;
}
.form-switch :deep(.el-switch__label--right) {
  z-index: 1;
  left: -3px;
}
.form-switch :deep(.el-switch__label--left) {
  z-index: 1;
  left: 19px;
}
.form-switch :deep(.el-switch__label.is-active) {
  display: block;
}
.questionImg {
  width: 17px;
}
.questionImg:hover {
  cursor: pointer;
}
.form-input .el-input {
  width: 160px;
  height: 32px;
  background: #ffffff;
  border-radius: 6px;
}
.d {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #f23f3f;
  border-radius: 6px;
  margin-right: 6px;
}
</style>
