import * as THREE from "three";
import { CELL_SPACE, FLOOR_SPACE, UN_RENDER_CELL_TYPE_ARR } from "../constant/elements";
import globalConfig from "../config";
import { mergeBufferGeometries } from "three/examples/jsm/utils/BufferGeometryUtils.js";
/**
 *  逻辑备注：
 *  1. 考虑到性能，把所有的点位以及背景，当作一个几何体；
 */
class Floors {
  constructor(data = {}) {
    this.floorGeo = new THREE.Group();
    this.floorInfo = {};
    this._createFloorGeo(data);
  }

  /** 选择效果 */
  selectCell(cell, color) {
    if (!cell || !("mergeSeq" in cell)) return;
    this.changeCellColor([[cell.mergeSeq, color]]);
  }
  /** hover效果 */
  hoverCell(cell, color) {
    if (!cell || !("mergeSeq" in cell)) return;
    this.changeCellColor([[cell.mergeSeq, color]]);
  }
  /** 清空交互 */
  cancelCellColor(cell) {
    if (!cell || !("mergeSeq" in cell)) return;
    this.changeCellColor([[cell.mergeSeq, this.__getCellColor(cell)]]);
  }
  dispose() {
    this.floorGeo.dispose();
    this.floorGeo = null;
  }
  /**
   * 选中改变地板颜色暂时不实现
   * @param {*} obj [[seq, color]]
   */
  changeCellColor(arr) {
    const colorAttr = this.floorGeo.children[0].geometry.getAttribute("color");
    const setColor = new THREE.Color();
    for (let i = 0, a; (a = arr[i++]); ) {
      if (!a || !a.length) {
        throw new Error("changeCellColor >>> 传递格式 [[seq, color],....]");
      }
      const [seq, color] = a;
      setColor.set(color);
      new Array(4)
        .fill(0)
        .map((j, n) => setColor.toArray(colorAttr.array, (seq * 4 + n) * colorAttr.itemSize));
    }
    colorAttr.needsUpdate = true;
  }

  _createFloorGeo(data) {
    const { floor, cells } = data;
    if (!cells || !Array.isArray(cells)) throw "生成二维码地图需要传送node数据";
    const floorInfo = this._getFloorLocation(floor, cells);
    this.floorGeo.name = `floorGeo`;
    this.floorGeo.userData.floorId = floorInfo.floorId;
    this.floorGeo.userData.floorInfo = floorInfo;
    const cellMesh = this._createCellsGeo(cells);
    // SLAM版本单独考虑增加
    // const floorMesh = floorInfo.isSlam
    //   ? this._createSlamFloorPanelGeo(floorInfo, floor)
    //   : this._createFloorPanelGeo(floorInfo);
    const floorMesh = this._createFloorPanelGeo(floorInfo);
    this.floorGeo.add(cellMesh);
    this.floorGeo.add(floorMesh);
    this.floorGeo.position.set(0, 0, 0);
    this.floorGeo.rotateX(-Math.PI / 2);
    this.floorInfo = floorInfo;
  }

  __getCellColor(cell) {
    const { cellFlag, cellType } = cell;
    if (cellFlag === "LOCKED") {
      return globalConfig.THEME["LOCKED"];
    } else if (cellFlag === " STOPPED") {
      return globalConfig.THEME["STOPPED"];
    } else {
      return globalConfig.THEME[cellType];
    }
  }

  _createCellsGeo(cells) {
    const buffGeoArr = [];
    const len = cells.length;
    let color = new THREE.Color();
    let matrix = new THREE.Matrix4();
    for (let i = 0; i < len; i++) {
      // 创建网格面
      const { width, length, cellType, startBounds } = cells[i];
      if (UN_RENDER_CELL_TYPE_ARR.includes(cellType)) continue;
      // 增加索引；
      cells[i].mergeSeq = i;
      matrix.makeTranslation(startBounds.x + length / 2, startBounds.y + width / 2, 0);
      const plane = new THREE.PlaneGeometry(
        length - length * CELL_SPACE,
        width - width * CELL_SPACE,
      );
      plane.applyMatrix4(matrix);
      // 这有缓存
      const customColor = new THREE.BufferAttribute(new Float32Array(4 * 3), 3);
      // 创建对应的点位颜色；
      color.set(this.__getCellColor(cells[i]));
      const arr = new Array(4).fill(0);
      arr.map((item, index) => color.toArray(customColor.array, index * customColor.itemSize));
      plane.setAttribute("color", customColor);
      buffGeoArr.push(plane);
    }
    const buffGeo = mergeBufferGeometries(buffGeoArr);
    const mesh = new THREE.Mesh(buffGeo, new THREE.MeshBasicMaterial({ vertexColors: true }));
    mesh.position.set(0, 0, -0.04);
    mesh.name = "cellBox";
    buffGeoArr.map(i => i.dispose());
    return mesh;
  }
  _getFloorLocation(floor, cells) {
    let { resolution, splitImage, floorId, locationX, locationY } = floor;
    const isSlam = !!splitImage;
    let floorLocation = { left: 0, right: 0, top: 0, bottom: 0 };
    if (!isSlam) {
      const origin = cells[0].location || {
        x: cells[0].startBounds.x - cells[0].length / 2,
        y: cells[0].startBounds.y - cells[0].width / 2,
      };
      floorLocation = cells.reduce(
        (pre, cur) => {
          const { x, y } = cur.location || {
            x: cur.startBounds.x - cur.length / 2,
            y: cur.startBounds.y - cur.width / 2,
          };
          if (pre.left > x) pre.left = x;
          if (pre.right < x) pre.right = x;
          if (pre.top < y) pre.top = y;
          if (pre.bottom > y) pre.bottom = y;
          return pre;
        },
        { left: origin.x, right: origin.x, top: origin.y, bottom: origin.y },
      );
    } else {
      const left = locationX || 0;
      const bottom = locationY || 0;
      floorLocation = {
        bottom,
        left,
        right: splitImage.originWidth * resolution + left,
        top: splitImage.originHeight * resolution + bottom,
      };
    }
    return {
      ...floorLocation,
      floorId,
      isSlam,
    };
  }
  // 二维码地图
  _createFloorPanelGeo({ left, right, top, bottom }) {
    const width = Math.abs(right - left) + FLOOR_SPACE;
    const height = Math.abs(top - bottom) + FLOOR_SPACE;
    const floor = new THREE.PlaneGeometry(width, height);
    const floorMesh = new THREE.Mesh(
      floor,
      new THREE.MeshBasicMaterial({
        color: globalConfig.THEME.BG_Color,
        roughness: 1,
        metalness: 0,
        side: THREE.DoubleSide,
      }),
    );
    floorMesh.position.set((left + right) / 2, (bottom + top) / 2, -0.05);
    floorMesh.name = "floorBox";
    return floorMesh;
  }
  // 创建SLAM地图
  _createSlamFloorPanelGeo(floorInfo, originFloorInfo) {
    const floorGroup = new THREE.Group();
    const { left, right, top, bottom } = floorInfo;
    //  增加底图
    const floorPlane = new THREE.PlaneGeometry(right - left, top - bottom);
    const floorMesh = new THREE.Mesh(
      floorPlane,
      new THREE.MeshBasicMaterial({
        color: globalConfig.THEME.BG_Color,
        side: THREE.DoubleSide,
      }),
    );
    floorMesh.position.set((left + right) / 2, (bottom + top) / 2, -0.1);
    floorMesh.name = "floorBox";
    floorGroup.add(floorMesh);
    //  增加slam背景层（threejs没有合并mesh操作只有geo，所以内存优化没办法）
    const { splitImage, resolution } = originFloorInfo;
    const { imageItems } = splitImage;
    const loader = new THREE.TextureLoader();
    for (let i = 0; i < imageItems.length; i++) {
      const item = imageItems[i];
      loader.load(item.imageText, function (texture) {
        const width = item.width * resolution;
        const height = item.height * resolution;
        const mesh = new THREE.Mesh(
          new THREE.PlaneGeometry(width, height),
          new THREE.MeshBasicMaterial({ map: texture }),
        );
        mesh.position.set(
          item.x * resolution + width / 2 + left,
          item.y * resolution + height / 2 + bottom,
          -0.05,
        );
        floorGroup.add(mesh);
      });
    }

    return floorGroup;
  }
}

export default Floors;
