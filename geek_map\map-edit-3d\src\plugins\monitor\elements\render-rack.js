import * as THREE from "three";
import globalConfig from "../../../config";
import { mergeBufferGeometries } from "three/examples/jsm/utils/BufferGeometryUtils.js";
import { nextTick } from "../../../utils/utils";

/** 渲染所有的货箱通过visible控制显隐 */
/**
 *  功能：
 *  1. hover-lattices、select-lattices、hover-box、select-box；
 *  2. layer - show、hide；
 *  3. box-heat；
 */
class RenderRack {
  constructor(options) {
    this.monitor = options.monitor;
    this.dispatch = options.dispatch || {
      renderRack() {},
      selectLattices() {},
      selectBoxs() {},
      updateRack() {}, // 更新rack事件，
    };

    this.rackGroup = new THREE.Group();
    this.rackGroup.name = "goup-rack";

    this.config = {
      lockColor: globalConfig.THEME.LOCKED,
      boxColor: globalConfig.THEME.RACK_BOX,
      layerColor: globalConfig.THEME.RACK_LAYER,
      latticeColor: globalConfig.THEME.RACK_LATTICE,
      hoverColor: globalConfig.THEME.HOVER_3D,
      selectColor: globalConfig.THEME.SELECTED,
      taskRackColor: globalConfig.THEME.RACK_TASK_BOX,
      stationRackColor: globalConfig.THEME.STATION_RACK_TASK_BOX,
      boxH: 0.3,
      latticeH: 0.04,
      layerH: 0.02,
      h: 0.5,
    };
    // const
    this.racks = null;
    this.latticesSeq = {};
    this.boxSeq = {};
    this.latticesSearch = null;
    this.isHeatMode = false;
    this.isTaskMode = false;
  }

  // create rack
  async create(racks) {
    this.racks = racks;
    await this.__handleRacks(racks);
    this.rackGroup.rotateX(-Math.PI / 2);
    this.dispatch.renderRack && this.dispatch.renderRack(this.rackGroup);
  }
  // update rack
  update(racks) {
    this.__updateLattice(racks);
  }

  // 热度模式
  reRenderBox() {
    // 主要考虑到热度模式，重新渲染；
    const all = Object.values(this.racks).reduce((pre, cur) => pre.concat(cur.lattices || []), []);
    const mesh = this.rackGroup.children
      .filter(mesh => mesh.name.includes(`rack-box`))
      .sort((a, b) => a.userData.layerId - b.userData.layerId);
    for (let i = 0, a; (a = all[i++]); ) {
      const { layer, latticeCode, mergeBoxSeq } = a;
      if (!this.__hasBox(layer, latticeCode)) continue;
      this.__changePanelColor(
        mesh[layer],
        mergeBoxSeq,
        this.__getBoxColor(a),
        this.__getBoxAlpha(a),
      );
    }
  }

  // destory rack
  destory() {
    this.racks = null;
    this.latticesSeq = {};
    this.boxSeq = {};
    this.latticesSearch = null;
  }

  // handler actions
  hoverLattices(code, hoverColor) {
    const lattices = this.searchByLatticeCode(code);
    const layerMesh = this.getLayersMesh(lattices.layer);
    this.__changePanelColor(layerMesh, lattices.mergeLatticesSeq, hoverColor);
  }

  selectLattices(code, selectColor) {
    const lattices = this.searchByLatticeCode(code);
    const layerMesh = this.getLayersMesh(lattices.layer);
    this.__changePanelColor(layerMesh, lattices.mergeLatticesSeq, selectColor);
    this.dispatch.selectLattices && this.dispatch.selectLattices(lattices);
  }

  cancelLatticesColor(code) {
    const lattices = this.searchByLatticeCode(code);
    const layerMesh = this.getLayersMesh(lattices.layer);
    const color = this.__isLockLattices(lattices)
      ? this.config.lockColor
      : this.config.latticeColor;
    this.__changePanelColor(layerMesh, lattices.mergeLatticesSeq, color);
  }

  hoverBoxs(latticeCode, hoverColor) {
    const lattices = this.searchByLatticeCode(latticeCode);
    if (!this.__hasBox(lattices.layer, latticeCode)) return;
    const layerMesh = this.getBoxMesh(lattices.layer);
    this.__changePanelColor(layerMesh, lattices.mergeBoxSeq, hoverColor);
  }

  selectBoxs(latticeCode, selectColor) {
    const lattices = this.searchByLatticeCode(latticeCode);
    if (!this.__hasBox(lattices.layer, latticeCode)) return;
    const layerMesh = this.getBoxMesh(lattices.layer);
    this.__changePanelColor(layerMesh, lattices.mergeBoxSeq, selectColor);
    this.dispatch.selectBoxs && this.dispatch.selectBoxs(lattices.relateBox);
  }

  cancelBoxsColor(latticeCode) {
    const lattices = this.searchByLatticeCode(latticeCode);
    if (!this.__hasBox(lattices.layer, latticeCode)) return;
    const layerMesh = this.getBoxMesh(lattices.layer);
    const color = this.__getBoxColor(lattices);
    this.__changePanelColor(layerMesh, lattices.mergeBoxSeq, color);
  }

  // handler visible
  showLayerId(layerId) {
    const mesh = this.getLayersMesh(layerId);
    mesh && (mesh.visible = true);
  }
  showBoxLayerId(layerId) {
    const mesh = this.getBoxMesh(layerId);
    mesh && (mesh.visible = true);
  }
  hideLayerId(layerId) {
    const mesh = this.getLayersMesh(layerId);
    mesh && (mesh.visible = false);
  }
  hideBoxLayerId(layerId) {
    const mesh = this.getBoxMesh(layerId);
    mesh && (mesh.visible = false);
  }

  // handler search mesh
  getLayersMesh(layerId = -1) {
    return !!~layerId
      ? this.rackGroup.getObjectByName(`rack-layer-${layerId}`)
      : this.rackGroup.children.filter(i => i.name.includes("rack-layer-"));
  }
  getBoxMesh(layerId = -1) {
    return !!~layerId
      ? this.rackGroup.getObjectByName(`rack-box-${layerId}`)
      : this.rackGroup.children.filter(i => i.name.includes("rack-box-"));
  }
  getRackLayers() {
    return this.rackGroup.children
      .filter(mesh => mesh.name.includes("rack-layer-"))
      .map((i, n) => n);
  }

  // handler search data;
  searchByLatticeCode(code) {
    const rack = this.racks[this.latticesSeq[code]];
    if (!rack) return false;
    const { lattices } = rack;
    return lattices.find(i => String(i.latticeCode) === String(code));
  }
  searchByBoxCode(code) {
    const box = this.boxSeq[code];
    if (!box) return false;
    const [rackCode, latticesCode] = box;
    const rack = this.racks[rackCode];
    if (!rack) return false;
    const { lattices } = rack;
    const lattice = lattices.find(i => String(i.latticeCode) === String(latticesCode));
    return (lattice && lattice.relateBox) || false;
  }
  __hasBox(layerId, latticeCode) {
    return this.latticesSearch[layerId][latticeCode]?.boxVisible;
  }
  __isLockBox(lattices) {
    return lattices.relateBox && !!lattices.relateBox.lockState;
  }
  __isLockLattices(lattices) {
    return lattices.latticeFlag !== "NORMAL";
  }
  __boxHeatColor(lattice) {
    if (!lattice.relateBox) return false;
    const score = Math.floor((lattice.relateBox.boxHeat || 0) / 10);
    const color = Object.values(globalConfig.THEME.SHELF_HOT_CONF);
    return color[score];
  }

  __getBoxAlpha(lattices) {
    let rb = lattices.relateBox;
    if (!rb && lattices.relateBoxCode) {
      return 0.5;
    }
    if (!rb) return 0;
    if (this.isTaskMode) return "jobIds" in rb && rb.jobIds ? 1 : 0;
    return 1;
  }

  __getBoxColor(lattices) {
    let rb = lattices.relateBox;
    if (!rb) return false;
    // 显示任务中货箱
    const isJob = "jobIds" in rb && rb.jobIds;
    if (this.isTaskMode && isJob) {
      const key = !!rb.goFetchJobs && !lattices.layer ? "stationRackColor" : "taskRackColor";
      return this.config[key];
    }

    // 热力货箱
    if (this.isHeatMode) return this.__boxHeatColor(lattices);

    return this.config[this.__isLockBox(lattices) ? "lockColor" : "boxColor"];
  }
  // just control box visible;
  __updateLattice(data) {
    // control releaxBox visible
    const racks = Object.values(data);
    if (!racks || !racks.length) return;
    const boxVisible = []; // [ {[层]:{ [latticesCode]:[索引，是否显示，地址引用]}}]
    // format data
    for (let i = 0, l = racks.length; i < l; i++) {
      const a = racks[i];
      const olattices = this.racks[a.rackCode]?.lattices;
      const nlattices = a.lattices;
      if (!olattices || !olattices.length) continue;
      // render
      for (let j = 0, b; (b = olattices[j++]); ) {
        // update lattices && delete boxSeq;
        const c = nlattices.find(v => v.latticeCode === b.latticeCode);
        if (!!b.relateBox) {
          delete this.boxSeq[b.relateBox.boxCode];
          boxVisible[b.layer] = Object.assign(boxVisible[b.layer] || {}, {
            [b.latticeCode]: [b.mergeBoxSeq, false, b],
          });
        }
        // merge lattices;
        Object.assign(b, c);
        // update old lattices
        const isBox = c.relateBoxCode || (!!c.relateBox && !!c.relateBox.boxCode);
        if (!isBox) continue;
        boxVisible[c.layer] = Object.assign(boxVisible[c.layer] || {}, {
          [b.latticeCode]: [b.mergeBoxSeq, true, c],
        });
        this.boxSeq[c.relateBox?.boxCode || c.relateBoxCode] = [a.rackCode, c.latticeCode];
      }
    }
    // const boxColor = this.config.boxColor;
    // render
    for (let i = 0, mesh; (mesh = this.rackGroup.children[i++]); ) {
      if (!mesh.name.includes("rack-box")) continue;
      const visibles = boxVisible[mesh.userData.layerId];
      if (!visibles) continue;
      Object.values(visibles).map(v =>
        this.__changePanelColor(
          mesh,
          v[0],
          this.__getBoxColor(v[2]),
          v[1] ? this.__getBoxAlpha(v[2]) : 0,
        ),
      );
    }
    // update search data;
    for (let i = 0, search; (search = boxVisible[i++]); ) {
      if (!search) continue;
      Object.keys(search).map(key => {
        if (this.latticesSearch[i - 1][key]) {
          this.latticesSearch[i - 1][key].boxVisible = search[key][1];
        } else {
          this.latticesSearch[i - 1][key] = { boxVisible: search[key][1] };
        }
      });
    }
    this.dispatch.updateRack && this.dispatch.updateRack();
  }
  // 渲染， 索引，分层
  async __handleRacks(racks) {
    const racksArr = Object.values(racks);
    const matrix = new THREE.Matrix4();
    const boxColor = new THREE.Color().set(this.config.boxColor);
    const layerColor = new THREE.Color().set(this.config.layerColor);
    const latticeColor = new THREE.Color().set(this.config.latticeColor);
    const lockColor = new THREE.Color().set(this.config.lockColor);
    const baseH = this.config.h;
    const layerGeos = [];
    const boxGeos = [];
    const latticesSearch = [];
    for (let i = 0, a; (a = racksArr[i++]); ) {
      const fr = this.__formatRack(a);
      const lattices = this.__formatLattices(a.lattices);
      for (let j = 0, len = lattices.length; j < len; j++) {
        const b = lattices[j];
        if (!b) continue;
        const bh = baseH * (j + 1);
        const layerId = j;
        if (!layerGeos[layerId]) layerGeos[layerId] = [];
        if (!boxGeos[layerId]) boxGeos[layerId] = [];
        if (!latticesSearch[layerId]) latticesSearch[layerId] = {};
        const commonparams = [fr, b, matrix, bh, lockColor];
        this.__renderLayer(...commonparams, layerColor, layerGeos[layerId]);
        this.__renderLattices(
          ...commonparams,
          latticeColor,
          layerGeos[layerId],
          latticesSearch[layerId],
        );
        this.__renderBox(...commonparams, boxColor, boxGeos[layerId]);
      }
    }
    this.latticesSearch = latticesSearch;

    await nextTick();
    // 渲染材质；
    this.__renderGeo(layerGeos, boxGeos, latticesSearch);
    return Promise.resolve();
  }
  __formatRack(a) {
    let { location, width, length, degAngle, rackCode } = a;
    const isVertical = [90, 270].includes(degAngle);
    if (isVertical) {
      const temp = length;
      length = width;
      width = temp;
    }
    const l = length * 0.8,
      w = width * 0.8,
      ls = l * 0.1,
      ws = w * 0.1;
    const { x, y } = location;
    // if (a.rackType === 2) debugger;
    return {
      rackCode,
      isVertical,
      degAngle,
      rackType: a.rackType || 1, // 1单深位 2双深位
      location: { location: { x, y }, l, w },
      left: { location: { x: x - l / 4 - ls / 4, y }, l: (l - ls) / 2, w },
      right: { location: { x: x + l / 4 + ls / 4, y }, l: (l - ls) / 2, w },
      top: { location: { x, y: y + w / 4 + ws / 4 }, l, w: (w - ws) / 2 },
      bottom: { location: { x, y: y - w / 4 - ws / 4 }, l, w: (w - ws) / 2 },
    };
  }
  __getIsOuter(a, b) {
    const { degAngle } = a;
    const { isOuter: iO } = b;
    let isOuter;
    if ([90, 180].includes(degAngle)) {
      isOuter = iO ? 0 : 1;
    } else {
      isOuter = iO;
    }
    return isOuter;
  }
  __formatLattices(a) {
    if (!a || !a.length) return [];
    // isOuter=0 固定放在0的位置；
    return [].concat(a).reduce((pre, cur) => {
      const { isOuter, layer } = cur;
      if (!pre[layer]) {
        pre[layer] = [cur];
        return pre;
      }
      if (pre[layer] && !!layer) {
        pre[layer] = !isOuter ? [cur].concat(pre[layer]) : pre[layer].concat(cur);
      }
      return pre;
    }, []);
  }
  __renderLayer(fr, b, matrix, bh, lockColor, color, layerGeos) {
    // 0层是缓存层所以隔板有两种形态；
    if (b.length > 1 && !!b[0].layer) {
      const { location, l, w } = fr.location;
      matrix.makeTranslation(location.x, location.y, bh);
      layerGeos.push(this.__createPanel(l, w, this.config.layerH, matrix, color, 0.8));
    } else {
      const cood =
        fr.rackType === 1
          ? fr.location
          : (fr.isVertical ? [fr.bottom, fr.top] : [fr.left, fr.right])[
              this.__getIsOuter(fr, b[0])
            ];
      const { location, l, w } = cood;
      matrix.makeTranslation(location.x, location.y, bh);
      layerGeos.push(this.__createPanel(l, w, this.config.layerH, matrix, color, 0.8));
    }
  }
  __renderLattices(fr, b, matrix, bh, lockColor, color, layerGeos, latticesSearch) {
    const choseLattices =
      fr.rackType === 1 ? [fr.location] : fr.isVertical ? [fr.bottom, fr.top] : [fr.left, fr.right];
    const baseH = bh + this.config.layerH + this.config.latticeH / 2;
    for (let i = 0, a; (a = b[i++]); ) {
      if (i > 1 && !a.layer) continue;
      a.mergeLatticesSeq = layerGeos.length;
      const { location, l, w } = choseLattices[this.__getIsOuter(fr, a)] || choseLattices[0];
      const { latticeCode } = a;
      matrix.makeTranslation(location.x, location.y, baseH);
      // 模型
      const c = this.__isLockLattices(a) ? lockColor : color;
      layerGeos.push(this.__createPanel(l, w, this.config.latticeH, matrix, c, 1));
      // 货位搜索
      latticesSearch[latticeCode] = {
        startBounds: { x: location.x - l / 2, y: location.y - w / 2 },
        width: w,
        length: l,
        latticeCode,
        boxVisible: !!a.relateBox?.boxCode,
      };
      // 货位索引
      this.latticesSeq[latticeCode] = fr.rackCode;
    }
  }
  __renderBox(fr, b, matrix, bh, lockColor, color, boxGeos) {
    const choseLattices =
      fr.rackType === 1 ? [fr.location] : fr.isVertical ? [fr.bottom, fr.top] : [fr.left, fr.right];
    const baseH = bh + this.config.layerH + this.config.latticeH + this.config.boxH / 2;
    for (let i = 0, a; (a = b[i++]); ) {
      if (!a) continue;
      // if (a.relateBox) continue;
      const isEmpty = !a.relateBox;
      const boxCode = a.relateBox?.boxCode;
      const { location, l, w } = choseLattices[this.__getIsOuter(fr, a)] || choseLattices[0];
      matrix.makeTranslation(location.x, location.y, baseH);
      a.mergeBoxSeq = boxGeos.length;
      a.isCacheBox = a.layer === 0;
      const c = this.__isLockBox(a) ? lockColor : color;
      // 模型
      boxGeos.push(this.__createPanel(l, w, this.config.boxH, matrix, c, isEmpty ? 0 : 1));
      if (boxCode) {
        // 货位索引
        this.boxSeq[boxCode] = [fr.rackCode, b.latticeCode];
      }
    }
  }
  __renderGeo(layerGeos, boxGeos, latticesSearch) {
    const material = new THREE.MeshStandardMaterial({
      vertexColors: true,
      roughness: 1,
      metalness: 0.2,
      side: THREE.FrontSide,
      transparent: true,
      alphaTest: 0.8, // 修复alpha=0的视角bug
      // vertexAlphas: true,
    });
    // // load all layer
    for (let i = 0, len = layerGeos.length; i < len; i++) {
      const geos = layerGeos[i];
      if (!geos || !geos.length) continue;
      const mesh = new THREE.Mesh(mergeBufferGeometries(geos), material);
      mesh.name = `rack-layer-${i}`;
      mesh.userData.layerId = i;
      this.rackGroup.add(mesh);
      // 增加搜索
      setTimeout(() => {
        this.monitor.Map3d.search.add(mesh, { data: Object.values(latticesSearch[i]) });
      }, 200);
    }
    // load all boxs
    for (let i = 0, len = boxGeos.length; i < len; i++) {
      const geos = boxGeos[i];
      if (!geos || !geos.length) continue;
      const mesh = new THREE.Mesh(mergeBufferGeometries(geos), material);
      mesh.name = `rack-box-${i}`;
      mesh.userData.layerId = i;
      this.rackGroup.add(mesh);
      // 增加搜索
      setTimeout(() => {
        this.monitor.Map3d.search.add(mesh, {
          data: Object.values(latticesSearch[i]),
        });
      }, 300);
    }
  }
  __changePanelColor(mesh, seq, color, alpha = -1) {
    const colorArr = new THREE.Color().set(color);
    const colorAttr = mesh.geometry.getAttribute("color");
    new Array(24).fill(0).map((j, n) => {
      const index = (seq * 24 + n) * colorAttr.itemSize;
      colorArr.toArray(colorAttr.array, index);
      if (!!~alpha) colorAttr.array[index + 3] = alpha;
    });
    colorAttr.needsUpdate = true;
  }
  __createPanel(length, width, depth, matrix, color, alpha = 1) {
    const box = new THREE.BoxBufferGeometry(length, width, depth);
    box.applyMatrix4(matrix);
    const boxCount = box.getAttribute("position").count;
    const boxColorAttr = new THREE.BufferAttribute(new Float32Array(boxCount * 4), 4);
    new Array(boxCount).fill(0).map((i, n) => {
      color.toArray(boxColorAttr.array, n * boxColorAttr.itemSize);
      boxColorAttr.array[n * boxColorAttr.itemSize + 3] = alpha;
    });
    box.setAttribute("color", boxColorAttr);
    return box;
  }
}

export default RenderRack;
