<template>
  <keep-alive>
    <component
      :is="currentComponent"
      :operation="currentOperation"
      :rowData="currentRoleRow"
      @setCurrentOperation="setCurrentOperation"
    />
  </keep-alive>
</template>

<script>
import RoleList from "./components/role-list";
import RoleDetail from "./components/role-detail";
export default {
  name: "RoleListIndex",
  components: { RoleList, RoleDetail },
  data() {
    return {
      currentComponent: "RoleList",
      currentOperation: "list",
      currentRoleRow: null,
    };
  },
  methods: {
    setCurrentOperation(type, rowData) {
      this.currentOperation = type;
      this.currentRoleRow = rowData || null;
      switch (type) {
        case "add":
        case "edit":
          if (this.currentComponent == "RoleDetail") return;
          this.currentComponent = "RoleDetail";
          break;
        case "list":
          if (this.currentComponent == "RoleList") return;
          this.currentComponent = "RoleList";
          break;
      }
    },
  },
};
</script>

<style lang="less" scoped></style>
