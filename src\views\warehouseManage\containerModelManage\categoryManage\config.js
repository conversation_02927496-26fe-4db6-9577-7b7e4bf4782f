import i18n from "@lang";
import mapVerify from "element-geek/utils/formValidateV2";

export const getSearchFormItem = options => [
  // 货架类别名称
  {
    name: "categoryName",
    value: "",
    component: "elInput",
    span: 6,
    placeholder: i18n.t("lang.rms.fed.pleaseEnter"),
    label: i18n.t("lang.rms.fed.shelfCategor.name.msg"),
  },
  // 货架类别类型
  {
    name: "categoryType",
    value: "",
    component: "elSelect",
    filterable: true,
    data: options.categoryType || [],
    span: 6,
    placeholder: i18n.t("lang.rms.fed.choose"),
    label: i18n.t("lang.rms.fed.shelfCategor.type.msg"),
  },
  // 货架类别编号
  {
    name: "categoryNum",
    value: "",
    component: "elInput",
    span: 6,
    placeholder: i18n.t("lang.rms.fed.pleaseEnter"),
    label: i18n.t("lang.rms.fed.shelfCategor.num.msg"),
  },
];

export const getSearchTableItem = options => [
  // 货架类别id
  {
    prop: "id",
    label: i18n.t("lang.rms.fed.shelfCategor.id"),
  },
  // 货架类别编号
  {
    prop: "categoryNum",
    label: i18n.t("lang.rms.fed.shelfCategor.num.msg"),
  },
  // 货架类别名称
  {
    prop: "categoryName",
    label: i18n.t("lang.rms.fed.shelfCategor.name.msg"),
  },
  // 货架类别别名
  {
    prop: "categoryAlias",
    label: i18n.t("lang.rms.fed.shelfCategor.alias.msg"),
  },
  // 货架类别种类
  {
    prop: "categoryType",
    label: i18n.t("lang.rms.fed.shelfCategor.type.msg"),
    formatter(row, column) {
      return (
        options.categoryType.find(i => String(i.value) === String(row[column]))?.label ||
        row[column]
      );
    },
  },
  // 货架层数
  {
    prop: "layer",
    label: i18n.t("lang.rms.fed.shelfCategor.layer.msg"),
  },
  // 货架类别描述
  {
    prop: "categoryDesc",
    label: i18n.t("lang.rms.fed.shelfCategor.desc.msg"),
  },
];

export const editFormItem = (row, options, isView) => [
  // 货架类别编号
  {
    name: "categoryNum",
    value: row.categoryNum || "",
    component: "elInput",
    span: 24,
    disabled: isView,
    placeholder: i18n.t("lang.rms.fed.pleaseEnter"),
    label: i18n.t("lang.rms.fed.shelfCategor.num.msg"),
    rules: mapVerify(["required", "positiveInt"]),
  },
  // 货架类别名称
  {
    name: "categoryName",
    value: row.categoryName || "",
    component: "elInput",
    span: 24,
    disabled: isView,
    placeholder: i18n.t("lang.rms.fed.pleaseEnter"),
    label: i18n.t("lang.rms.fed.shelfCategor.name.msg"),
    rules: mapVerify(["required"]),
  },
  // 货架类别别名
  {
    name: "categoryAlias",
    value: row.categoryAlias || "",
    component: "elInput",
    disabled: isView,
    span: 24,
    placeholder: i18n.t("lang.rms.fed.pleaseEnter"),
    label: i18n.t("lang.rms.fed.shelfCategor.alias.msg"),
  },
  // 货架类别种类
  {
    name: "categoryType",
    value: row.categoryType ?? "",
    component: "elSelect",
    disabled: isView,
    filterable: true,
    data: options.categoryType || [],
    span: 24,
    placeholder: i18n.t("lang.rms.fed.pleaseEnter"),
    label: i18n.t("lang.rms.fed.shelfCategor.type.msg"),
    rules: mapVerify(["required"]),
  },
  // 货架层数
  {
    name: "layer",
    value: row.layer || "",
    component: "elInput",
    disabled: isView,
    span: 24,
    placeholder: i18n.t("lang.rms.fed.pleaseEnter"),
    label: i18n.t("lang.rms.fed.shelfCategor.layer.msg"),
    rules: mapVerify(["positiveInt"]),
  },
  // 货架类别描述
  {
    name: "categoryDesc",
    value: row.categoryDesc || "",
    component: "elInput",
    disabled: isView,
    span: 24,
    placeholder: i18n.t("lang.rms.fed.pleaseEnter"),
    label: i18n.t("lang.rms.fed.shelfCategor.desc.msg"),
  },
];
