<template>
  <div class="map-manage-qrcode">
    <m-dialog
      width="70%"
      :visible="visible"
      :is-need-footer="false"
      class="dialog-qr-custom"
      @closed="handleDialogClosed"
    >
      <template #title>
        <span class="f16">{{ title }}</span>
      </template>
      <div class="map-manage-qrcode__form">
        <div class="select">
          <span>{{ $t("lang.rms.fed.qrcodeStatus") }}：</span>
          <el-select v-model="selectQrStatus" @change="selectChange">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="btnBox">
          <el-button type="primary" @click="handleSearch">{{ $t("lang.rms.fed.query") }}</el-button>
          <el-button type="primary" :disabled="lockBtn" @click="handleConfirmAll">
            {{ $t("lang.rms.fed.allConfirm") }}
          </el-button>
        </div>
      </div>
      <div v-loading="tableLoading" class="map-manage-qrcode__table">
        <m-table
          :table-item="tableItem"
          :table-data="tableData"
          :page-data="null"
          :extend-config="tableExtendConfig"
          :max-height="400"
          @manualConfirm="handleManualConfirm"
        />
      </div>
    </m-dialog>
  </div>
</template>
<script>
/**
 *  国际化：
 *  lang.rms.fed.qrcodeStatus  二维码状态
 *  lang.rms.fed.allConfirm    全部确认
 *  lang.rms.fed.manualConfirm 手工确认
 *  lang.rms.fed.confirmAgain  待二次确认
 *  lang.rms.fed.oldQrCode     原二维码
 *  lang.rms.fed.newQrCode     新二维码
 *  lang.rms.fed.realLocation  实际扫描坐标
 *  lang.rms.fed.manageQrCode  管理二维码
 *  lang.rms.fed.actualScanningAngle  实际扫描角度
 *  lang.rms.fed.errorReportingReason  报错原因
 *  lang.rms.fed.confirmQrCode  确认二维码值
 *
 */
const qrcodeStatusOption = that => [
  { label: that.$t("lang.rms.fed.buttonWhole"), value: "-1" },
  { label: that.$t("lang.rms.fed.unconfirmed"), value: "0" },
  { label: that.$t("lang.rms.fed.confirmed"), value: "1" },
  { label: that.$t("lang.rms.fed.confirmAgain"), value: "2" },
];
export default {
  props: {
    visible: Boolean,
    mapId: String,
    floorId: String,
  },
  data() {
    return {
      lockBtn: true,
      tableLoading: false,
      title: this.$t("lang.rms.fed.manageQrCode"),
      selectQrStatus: "-1",
      options: [],
      tableItem: [
        { prop: "id", label: this.$t("ID") },
        {
          prop: "coordinate",
          label: this.$t("lang.rms.fed.coordinates"),
        },
        { prop: "oldQrCode", label: this.$t("lang.rms.fed.oldQrCode") },
        {
          prop: "status",
          label: this.$t("lang.rms.fed.state"),
          formatter: (row, column) => {
            return qrcodeStatusOption(this).find(i => String(i.value) === String(row[column]))
              .label;
          },
        },
        { prop: "realLocation", label: this.$t("lang.rms.fed.realLocation") },
        {
          prop: "actualScanningAngle",
          label: this.$t("lang.rms.fed.actualScanningAngle"),
          formatter: (row, column) => (row[column] ? `${row[column]}°` : ""),
        },
        { prop: "qrCode", label: this.$t("lang.rms.fed.newQrCode") },
        {
          prop: "errorReportingReason",
          label: this.$t("lang.rms.fed.errorReportingReason"),
          formatter: (row, column) => (row[column] ? this.$t(row[column]) : ""),
        },
      ],
      tableData: [],
      tableExtendConfig: {
        sortNum: false,
        operate: [
          {
            event: "manualConfirm",
            label: this.$t("lang.rms.fed.manualConfirm"),
            condition({ row }) {
              return ["0", "2"].includes(String(row.status));
            },
          },
        ],
      },
    };
  },
  created() {
    this.options = qrcodeStatusOption(this);
    this.fetchQrInfo();
  },
  methods: {
    // 查询
    handleSearch() {
      this.fetchQrInfo(this.selectQrStatus);
    },
    // 查询接口
    async fetchQrInfo(status) {
      this.tableLoading = true;
      let params = { mapId: this.mapId, floorId: this.floorId };
      if (status && status !== "-1") params.status = status;
      const { data } = await $req.get("/athena/map/qrLog/findQrInfo", params);
      this.tableData = data.recordList || [];
      this.lockBtn = false;
      this.tableLoading = false;
    },
    // 部分确认
    async handleManualConfirm({ row }) {
      const { value: qrCode } = await this.$prompt("", this.$t("lang.rms.fed.confirmQrCode"), {
        inputValue: row.qrCode || row.oldQrCode,
      });
      const { code } = await $req.post("/athena/map/qrLog/confirmInfoSingle", {
        id: row.id,
        qrCode,
        mapId: Number(this.mapId),
        floorId: Number(this.floorId),
      });
      !code && this.$message.success(this.$t("lang.common.success"));
      this.fetchQrInfo(this.selectQrStatus);
    },
    // 全部确认
    handleConfirmAll() {
      if (this.lockBtn) {
        return false;
      }
      this.confirmQrCode(this.tableData.map(i => i.id));
    },
    // 确认接口
    async confirmQrCode(ids) {
      const { mapId, floorId } = this;
      const { code } = await $req.post("/athena/map/qrLog/confirmInfoAll", {
        mapId: Number(mapId),
        floorId: Number(floorId),
      });
      !code && this.$message.success(this.$t("lang.common.success"));
      this.fetchQrInfo(this.selectQrStatus);
    },
    handleDialogClosed() {
      this.$emit("update:visible", false);
    },
    selectChange() {
      this.handleSearch();
    },
  },
};
</script>
<style lang="less" scoped>
.map-manage-qrcode__form {
  display: flex;
  justify-content: space-between;
  margin-bottom: 25px;
}
.dialog-qr-custom {
  :deep(.el-dialog__body) {
    overflow-x: hidden !important;
    overflow-y: auto !important;
    max-height: auto !important;
  }
}
</style>
