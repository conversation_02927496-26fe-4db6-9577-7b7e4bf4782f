/**
 * 这个函数用于降低组件之间的耦合
 * 组件即使被卸载, 这里也可以获取到一个空 不会报错,
 * 开发组件时专注组件即可
 */

import { StoreDefinition } from "pinia";

const componentStoreMap: {[k: string]:  StoreDefinition<any> } = {};

export function setComponentStore(storeName: string, store: StoreDefinition<any>) {
  componentStoreMap[storeName] = store;
}

export function getComponentStore(storeName: string) {
  const use = componentStoreMap[storeName];
  if (use) {
    return use;
  }

  return function () {
    return null;
  };
}