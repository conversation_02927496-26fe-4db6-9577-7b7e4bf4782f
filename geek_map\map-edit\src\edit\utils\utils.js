//判断点是否在面内
//缩放比例,
//FIXME 缩放存在必要性，如果坐标大小，细节放大，不够顺滑，有棱角
import {COEFFICIENT} from '../config'
// const COEFFICIENT = 0.02
import Store from "../store/Store";
import LayerManager from '../layerManager/LayerManager'
import { customAlphabet } from 'nanoid/non-secure'
//小数点后三位
const toFixed = (num,fixed = 3) => {
  return Number(num.toFixed(fixed))
}
const intersect = (point, options = {}) => {
  const {x:px,y:py} = point
  const {x,y,width,height} = options
  const halfW = width / 2
  const halfH = height / 2
  const minX = x - halfW
  const maxX = x + halfW
  const minY = y - halfH
  const maxY = y + halfH
  return (px > minX && px < maxX && py > minY && py < maxY)
}
//是否hover到编辑节点,isReverse是否从最新的节点开始检索
const isHoverNode = (p,isReverse = false) => {
  const {x,y} = p
  const searchOps = {
    x,
    y,
    width:1,
    height:1
  }
  let nodes = Store.tree.getTreeNodes(searchOps)
  if(isReverse) nodes = nodes.reverse()
  if(!nodes.length) return null
  for(let i = 0;i < nodes.length; i++) {
    const isHover = intersect(p,nodes[i])
    if(isHover){
      const {x,y,data} = nodes[i]
      return {x,y,...data}
    }
  }
  return null
}
//判断节点是否在面内
const isInsideRect = (rectOp) => {
  const {x,y,width,height} = rectOp
  const minX = x,minY = y,maxX = x + width,maxY = y + height
  const rectArr = Store.tree.getTreeNodes(rectOp)
  const result = []
  rectArr.forEach(rect => {
    let {x,y,width,height,data} =  rect
    // x = x - width / 2,y = y - height / 2
    if(x > minX && x < maxX && y > minY && y < maxY){
      result.push({...data})
    }
  })
  return result
}
//cad坐标 -> pixi坐标
const cad2pixi = (op) => {
  const {x,y} = op
  return {...op,x:toFixed(x / COEFFICIENT),y:toFixed(-y / COEFFICIENT)}
}
//pixi坐标 -> cad坐标
const pixi2cad = (op) => {
  const {x,y} = op
  return {...op,x:toFixed(x * COEFFICIENT),y:toFixed(-y * COEFFICIENT)}
}
//计算两点之间的距离
const distance = (p1,p2) => {
  const {x:x1,y:y1} = p1
  const {x:x2,y:y2} = p2
  const x = x2 - x1;
  const y = y2 - y1;
  return Math.sqrt(Math.pow(x, 2) + Math.pow(y, 2));
}
//根据类型获取元素的id
const getId = ($el) => {
  let id
  const {type} = $el
  if(type === 'area' || type === 'device'){
    id = $el.id
  }else if(type === 'S_LINE' || type === 'BEZIER'){
    id = $el.segmentId
  }else if(type === 'element'){
    id = $el.nodeId
  }
  return id
}
//生成图层内的唯一id
const nanoid = customAlphabet('1234567890', 10)
const createId = () => {
  // const nanoid = customAlphabet('1234567890', 10)
  return Number(nanoid())
}
//计算贝塞尔曲线斜率
const computedBezierSlope = (ops) => {
  const {posX,p1,p2,cp1,cp2} = ops

}
export {
  isHoverNode,
  isInsideRect,
  intersect,
  cad2pixi,
  pixi2cad,
  toFixed,
  distance,
  getId,
  createId
}
