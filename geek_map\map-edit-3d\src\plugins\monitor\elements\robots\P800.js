import { useRobotAnimal } from "../../../../utils/model-func";
import globalConfig from "../../../../config";
import { addMethods } from "../../../../utils/utils";
import * as THREE from "three";

class P800 {
  constructor(Map3d) {
    this.Map3d = Map3d;
  }
  getRobotMesh(item) {
    const mesh = this.Map3d.modelInstances[item.uuid].model;
    return mesh.children[0].children.map(i => {
      i.userData.uuid = item.uuid;
      return i;
    });
  }
  afterRender(item) {
    const model = this.Map3d.modelInstances[item.uuid].model;
    // hover
    const body = model.getObjectByName("robot-p800");
    const box3 = new THREE.Box3().setFromObject(body.clone());
    const hover = new THREE.Box3Helper(box3, globalConfig.THEME.HOVER_3D);
    hover.name = "hover";
    hover.visible = false;
    body.parent.add(hover);
    // select
    const select = new THREE.Box3Helper(box3, globalConfig.THEME.SELECTED);
    select.name = "select";
    select.visible = false;
    body.parent.add(select);

    model.rotation.y = -item.radAngle;
    // 增加功能监听；
    addMethods(body);
  }
  update(oldValue, newVal) {
    if (!this.Map3d.modelInstances[oldValue.uuid]) return; // 数据和模型是异步。
    const mesh = this.Map3d.modelInstances[oldValue.uuid].model;
    const { location: ov, radAngle: r } = oldValue;
    mesh.userData.curLocation = [ov.x, ov.y, r];
    // 更新机器人任务状态；
    this.__renderRobotStatus(newVal, mesh);
    // 坐标更新动画
    this.__useRobotAnimal(newVal, mesh);
  }
  __renderRobotStatus(nv, mesh) {
    const body = mesh.getObjectByName("robot-p800");
    const workEffect = ["GO_SOMEWHERE_TO_STAY"];
    body.status = nv.taskId && !workEffect.includes(nv.taskType) ? "work" : "normal";
  }
  __useRobotAnimal(nv, mesh) {
    const { location, radAngle } = nv;
    useRobotAnimal({ mesh, location: [location.x, location.y, radAngle] });
  }
}
export default P800;
