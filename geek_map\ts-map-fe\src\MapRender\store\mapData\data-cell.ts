/* ! <AUTHOR> at 2023/04/20 */

class CellsData implements MRender.MapData {
  private mapData: { [propName: code]: mCellData } = {};

  private floorIdCodes: { [propName: floorId]: code[] } = {};

  setData(code: code, data: mCellData) {
    const floorId = data.floorId;
    if (!this.floorIdCodes[floorId]) this.floorIdCodes[floorId] = [];
    this.floorIdCodes[floorId].push(data.code);

    this.mapData[code] = data;
  }

  getData(code: code): mCellData {
    return this.mapData[code];
  }

  getFloorDataBySizeType(sizeType: string = ""): { [propName: floorId]: mCellData[] } {
    const mapData = this.mapData;
    const floorIdCodes = this.floorIdCodes;
    const types = sizeType.split(",");

    let data: { [propName: floorId]: mCellData[] } = null;
    let codes: code[];

    for (let floorId in floorIdCodes) {
      codes = floorIdCodes[floorId];
      let cell: mCellData;
      for (let i = 0, len = codes.length; i < len; i++) {
        cell = mapData[codes[i]];
        if (types.indexOf(cell["sizeType"]) != -1) continue;
        if (!data) data = {};
        if (!data[floorId]) data[floorId] = [];
        data[floorId].push(cell);
      }
    }
    return data;
  }

  getByFloorId(floorId: floorId): mCellData[] {
    const mapData = this.mapData;
    const codes = this.floorIdCodes[floorId] || [];

    let data: mCellData[] = [];
    for (let i = 0, len = codes.length; i < len; i++) {
      data.push(mapData[codes[i]]);
    }
    return data;
  }

  getFloorDataByCodes(codes: code[] = []): { [propName: floorId]: mCellData[] } {
    const mapData = this.mapData;
    let data: { [propName: floorId]: mCellData[] } = null;

    let code: code, cell: mCellData, floorId: floorId;
    for (let i = 0, len = codes.length; i < len; i++) {
      if (!data) data = {};
      code = codes[i];
      cell = mapData[code];
      floorId = cell.floorId;
      if (!data[floorId]) data[floorId] = [];
      data[floorId].push(cell);
    }

    return data;
  }

  getAll(): { [propName: code]: mCellData } {
    return this.mapData;
  }

  delData(code: code) {
    delete this.mapData[code];
  }

  uninstall() {
    this.mapData = {};
    this.floorIdCodes = {};
  }

  destroy() {
    this.uninstall();
  }
}

export default CellsData;
