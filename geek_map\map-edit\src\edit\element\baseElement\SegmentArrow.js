import {toFixed,distance} from "../../utils/utils";;
import Control from '../../control/Control'
import {lineStyle} from '../../config'
const {LOAD,UNLOAD} = lineStyle
export default class SegmentArrow {
  static render($el,{paths,color}){
    if(paths.length < 2) return
    //0：不连通   1：1->2    2：1 <-> 2   3：1 <- 2
    const {loadDirs,unloadDirs} = $el
    // const loadDirs = 3,unloadDirs = 3;
    for(let i = 0,len = paths.length; i < len - 1; i++) {
      const p1 = paths[i],p2 = paths[i + 1];
      const {x:x1,y:y1} = p1
      const {x:x2,y:y2} = p2
      const radian = Math.atan2(y2 - y1, x2 - x1);
      const center = {x: (x1 + x2) / 2,y:(y1 + y2) / 2}
      //计算间隔位置
      const space = distance(p1,p2) / 8
      //展示的空负载类型
      const dirType = Control.dirType
      //负载
      if(loadDirs && dirType !== 'unload'){
        //正方向
        if([1,2].includes(loadDirs)){
          const loadParams = {radian,center,space,color:LOAD}
          this._loadDir($el,loadParams)
        }
        //负方向
        if([3,2].includes(loadDirs)){
          const loadParams = {radian:radian + Math.PI,center,space,color:LOAD}
          this._loadDir($el,loadParams)
        }
      }
      //空载
      if(unloadDirs && dirType !== 'load'){
        //正方向
        if([1,2].includes(unloadDirs)){
          const loadParams = {radian,center,space,color:UNLOAD}
          this._unloadDir($el,loadParams)
        }
        //负方向
        if([3,2].includes(unloadDirs)){
          const loadParams = {radian:radian + Math.PI,center,space,color:UNLOAD}
          this._unloadDir($el,loadParams)
        }
      }
    }
  }
  //负载箭头
  static _loadDir($el,info) {
    const {radian,center,space,color} = info
    const {x,y} = center
    const spaceX = space * Math.cos(radian)
    const spaceY = space * Math.sin(radian)
    const centerLoad = {x: x + spaceX,y: y + spaceY}
    const properties = {radian:radian,center:centerLoad,color}
    this._renderLoadArrow($el,properties)
  }
  //空载箭头
  static _unloadDir($el,info){
    const {radian,center,space,color} = info
    const {x,y} = center
    const spaceX = space * Math.cos(radian)
    const spaceY = space * Math.sin(radian)
    const centerLoad = {x: x + spaceX * 2,y: y + spaceY * 2}
    const properties = {radian,center:centerLoad,color}
    this._renderUnloadArrow($el,properties)
  }
  //负载箭头
  static _renderLoadArrow($el,properties){
    const w = 2.4,h = 3.3;
    const {color,radian,center} = properties
    const {x,y} = center
    const tx = toFixed(w * Math.sin(radian))
    const ty = toFixed(w * Math.cos(radian))
    // const endPoint = [toFixed(x + h * Math.cos(radian)),toFixed(y + h * Math.sin(radian))]
    // $el.beginFill(color)
    // $el.lineStyle(0);
    // $el.moveTo(x,y)
    // $el.lineTo(toFixed(x - tx), toFixed(y + ty)); // 这里箭头的大小依需求而定
    // $el.lineTo(endPoint[0],endPoint[1]);
    // $el.lineTo(toFixed(x + tx), toFixed(y - ty));
    // $el.lineTo(x,y);
    // $el.endFill()
    const [endX,endY] = [toFixed(x + h * Math.cos(radian)),toFixed(y + h * Math.sin(radian))]
    const startX = x + (endX - x) / 2,startY = y + (endY - y) / 2
    $el.beginFill(color)
    $el.lineStyle(0);
    $el.moveTo(startX,startY)
    $el.lineTo(toFixed(x - tx), toFixed(y + ty)); // 这里箭头的大小依需求而定
    $el.lineTo(endX,endY);
    $el.lineTo(toFixed(x + tx), toFixed(y - ty));
    $el.lineTo(startX,startY);
    $el.endFill()
  }
  //空载箭头
  static _renderUnloadArrow($el,properties){
    const w = 2.4,h = 3.3;
    const {color,radian,center} = properties
    const {x,y} = center
    const tx = toFixed(w * Math.sin(radian))
    const ty = toFixed(w * Math.cos(radian))
    const [endX,endY] = [toFixed(x + h * Math.cos(radian)),toFixed(y + h * Math.sin(radian))]
    const startX = x + (endX - x) / 2,startY = y + (endY - y) / 2
    $el.beginFill(color)
    $el.lineStyle(0);
    $el.moveTo(startX,startY)
    $el.lineTo(toFixed(x - tx), toFixed(y + ty)); // 这里箭头的大小依需求而定
    $el.lineTo(endX,endY);
    $el.lineTo(toFixed(x + tx), toFixed(y - ty));
    $el.lineTo(startX,startY);
    $el.endFill()
  }
}
