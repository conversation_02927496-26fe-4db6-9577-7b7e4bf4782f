import mapVerify from "element-geek/utils/formValidateV2";

const getValue = value => (value ? String(value) : String(value) === "0" ? "0" : "");

export const getSearchFormData = (that, options) => [
  // 控制器id
  {
    name: "deviceId",
    value: "",
    span: 6,
    component: "elInput",
    label: that.$t("lang.rms.fed.controllerId")
  }
];

// searchTable列表
export const getSearchTableItem = (that, options) => [
  // 控制器id
  {
    prop: "deviceId",
    label: that.$t("lang.rms.fed.controllerId")
  },
  // ip地址
  {
    prop: "ip",
    label: that.$t("lang.rms.fed.IPAdress")
  },
  // 绑定逻辑区
  {
    prop: "referBy",
    label: that.$t("lang.rms.fed.boundLogicIdArea"),
    formatter(row, column) {
      return row[column] === "null" ? "" : row[column];
    }
  },
  // 通道与工作站对应关系
  {
    prop: "channelsStr",
    label: that.$t("lang.rms.fed.channelAndWorkstation")
  }
];

// 编辑框form表单
export const getEditFormData = (that, row) => {
  const formList = [
    // 控制器id
    {
      name: "deviceId",
      value: getValue(row.deviceId),
      span: 12,
      component: "elInput",
      label: that.$t("lang.rms.fed.controllerId"),
      rules: mapVerify(["required", "supportNumbers", "inputLen64"])
    },
    // ip
    {
      name: "ip",
      value: getValue(row.ip),
      span: 12,
      component: "elInput",
      label: that.$t("lang.rms.web.monitor.robot.serverRobotIpMapIp"),
      rules: mapVerify(["required", "inputLen64", "ipRules"])
    },
    // 逻辑区
    {
      name: "referBy",
      value: getValue(row.referBy),
      span: 24,
      component: "elInput",
      placeholder: that.$t('lang.rms.fed.logiclsTip'),
      label: that.$t("lang.rms.fed.textLogicArea"),
      rules: mapVerify(["inputLen255", "numberAndComma"])
    }
  ];

  return formList;
};
