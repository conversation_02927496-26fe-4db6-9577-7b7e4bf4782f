<template>
  <div class="leftView">
    <Ground :auxiliary="true" :ground-data="groundData" />
  </div>
</template>

<script>
import { mapState } from "vuex";
import Ground from "./ground";
export default {
  props: {},
  data() {
    return {};
  },
  computed: {
    ...mapState("containerModal", ["groundData"]),
  },
  components: {
    Ground,
  },
};
</script>

<style scoped lang="less">
.leftView {
  flex: 1;
  height: 100%;
  position: relative;
}
</style>
