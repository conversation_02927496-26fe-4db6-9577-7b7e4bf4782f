// Tween
import TWEEN from "@tweenjs/tween.js";
import * as THREE from "three";
import { updateTime } from "../plugins/monitor/constant/monitor";

// 这边按照[-180 - 180],没有做270这种兼容处理。
const getEndPosition = (startPos, endPos) => {
  const sr = startPos[2];
  const er = endPos[2];
  const isSr = sr < 0,
    isEr = er < 0;
  const diff_0 = Math.abs(sr) + Math.abs(er);
  const diff_180 = Math.PI - Math.abs(sr) + (Math.PI - Math.abs(er));
  if ((isSr && isEr) || (!isSr && !isEr) || diff_0 <= diff_180) {
    return endPos;
  } else {
    const srv = isSr ? Math.PI * 2 + sr : sr;
    const erv = isEr ? Math.PI * 2 + er : er;
    const diff = erv - srv;
    return [endPos[0], endPos[1], (Math.abs(sr) + Math.abs(diff)) * (isSr ? -1 : 1)];
  }
};

export const animalGroup = new TWEEN.Group();

export const removeAllAnimal = TWEEN.removeAll;

export const useCameraAnimal = (camera, control, current, target) => {
  const start = JSON.parse(JSON.stringify(current));
  const tween = new TWEEN.Tween(start);
  tween
    .easing(TWEEN.Easing.Linear.None)
    .onUpdate(e => {
      camera.position.set(start[0], start[1], start[2]);
      control.target.set(start[3], start[4], start[5]);
      control.update();
    })
    .to(target, 1000)
    .start();
};

export const useRSRobotLiftAnimal = (date, timmer = updateTime - 50) => {
  const { mesh, target } = date;
  const cur = [mesh.userData.y || 0];
  const tween = new TWEEN.Tween(cur);
  // 小数会存在精度问题；
  if (Number(cur).toFixed(2) === Number(target).toFixed(2)) return;
  const isUp = cur < target;
  const trayMesh = mesh.getObjectByName("robot-rs-tray");
  const trayMeshBox3 = new THREE.Box3().setFromObject(trayMesh.clone());
  tween
    .easing(TWEEN.Easing.Linear.None)
    .onUpdate(e => {
      const [y] = cur;
      mesh.position.y = y;
    })
    .to([target], timmer)
    .start()
    .onStop(() => {
      mesh.userData.y = target;
      trayMesh.rotation.y = isUp ? -Math.PI / 2 : 0;
      trayMesh.position.z = isUp ? -Math.abs(trayMeshBox3.max.z - trayMeshBox3.min.z) / 2 : 0;
      trayMesh.position.x = isUp ? -Math.abs(trayMeshBox3.max.x - trayMeshBox3.min.x) / 2 : 0;
    })
    .onComplete(e => {
      mesh.userData.y = target;
      trayMesh.rotation.y = isUp ? -Math.PI / 2 : 0;
      trayMesh.position.z = isUp ? -Math.abs(trayMeshBox3.max.z - trayMeshBox3.min.z) / 2 : 0;
      trayMesh.position.x = isUp ? -Math.abs(trayMeshBox3.max.x - trayMeshBox3.min.x) / 2 : 0;
    });
};

// 后端没有数据支撑是否正在执行取箱，还箱高度
export const userRSRobotBoxActionAnimal = (data, timmer = updateTime - 50) => {
  // const { mesh, action } = data;
  // let returnFetchBox = mesh.getObjectByName("robot-rs-returnFetchBox");
  // if (!returnFetchBox) {
  //   const boxMesh = new THREE.Mesh(
  //     new THREE.BoxBufferGeometry(0.3, 0.3, 0.3),
  //     new THREE.MeshStandardMaterial({
  //       color: 0x0077f0,
  //       side: THREE.DoubleSide,
  //       roughness: 1,
  //       metalness: 0,
  //       transparent: true,
  //       opacity: 1,
  //     }),
  //   );
  //   boxMesh.name = "robot-rs-returnFetchBox";
  //   boxMesh.visible = false;
  //   mesh.add(boxMesh);
  //   returnFetchBox = boxMesh;
  // }
  // let startX = action === "return" ? 0 : 0.7;
  // let targetX = action === "return" ? 0.7 : 0;
  // let start = [startX, 0.5],
  //   target = [targetX, 1];
  // returnFetchBox.position.x = start[0];
  // returnFetchBox.material.opacity = start[1];
  // returnFetchBox.visible = true;
  // const tween = new TWEEN.Tween(start);
  // tween
  //   .easing(TWEEN.Easing.Linear.None)
  //   .onUpdate(e => {
  //     const [x, opacity] = start;
  //     returnFetchBox.position.x = x;
  //     returnFetchBox.material.opacity = opacity;
  //   })
  //   .to(target, timmer)
  //   .start()
  //   .onStop(() => {
  //     returnFetchBox.position.x = 0;
  //     returnFetchBox.material.opacity = 1;
  //     returnFetchBox.visible = false;
  //   })
  //   .onComplete(e => {
  //     returnFetchBox.position.x = 0;
  //     returnFetchBox.material.opacity = 1;
  //     returnFetchBox.visible = false;
  //   });
};

export const useRobotAnimal = (data, timmer = updateTime - 50, group = false) => {
  const { mesh, location } = data;
  const start = mesh.userData.curLocation;
  const tween = group ? new TWEEN.Tween(start, animalGroup) : new TWEEN.Tween(start);
  if (start.join(",") === location.join(",")) return;
  const endPosition = start && start.length ? getEndPosition(start, location) : location;
  tween
    .easing(TWEEN.Easing.Linear.None)
    .onUpdate(e => {
      const [x, y, r] = start;
      mesh.rotation.y = -r;
      mesh.position.set(x, 0, -y);
    })
    .to(endPosition, timmer)
    .start()
    .onStop(() => {
      const [x, y, r] = location;
      mesh.rotation.y = -r;
      mesh.position.set(x, 0, -y);
      mesh.userData.curLocation = [x, y, r];
    })
    .onComplete(e => {
      const [x, y, r] = location;
      mesh.rotation.y = -r;
      mesh.position.set(x, 0, -y);
      mesh.userData.curLocation = [x, y, r];
    });
};
