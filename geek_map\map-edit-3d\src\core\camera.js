import * as THREE from "three";
import { useCameraAnimal } from "../utils/model-func";
import { FLOOR_SPACE } from "../constant/elements";

const distance = (p1, p2) => {
  return Math.sqrt((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2);
};
class Camera {
  constructor(Map3d) {
    this.camera = null;
    this.Map3d = Map3d;
    this._initCamera();
  }
  changeCameraAspt() {
    if (!this.camera) return;
    const { offsetWidth, offsetHeight } = this.Map3d.$dom;
    const aspect = offsetWidth / offsetHeight;
    this.camera.aspect = aspect;
    this.camera.updateProjectionMatrix();
  }
  get() {
    return this.camera;
  }
  update() {}
  reset() {}
  flyTo(position, angle = 60) {
    if (!position || !position.length) return;
    const { OrbitControls } = this.Map3d;
    let target = [];
    const current = Object.values(this.camera.position).concat(Object.values(OrbitControls.target));
    this.__caleViewHeight();
    if ([0, 90].includes(angle)) {
      target = [position[0], this.__caleViewHeight(), position[2], ...position];
    } else if (angle > 0 && angle < 90) {
      const camera = [position[0] - 20, 0, position[2] + 20];
      const camera_y =
        distance({ x: position[0], y: position[2] }, { x: camera[0], y: camera[2] }) /
        Math.tan(angle * (Math.PI / 180));
      camera[1] = camera_y;
      target = [...camera, ...position];
    }

    useCameraAnimal(this.camera, OrbitControls, current, target);
  }
  __caleViewHeight() {
    const { floor } = this.Map3d;
    const vFov = (this.camera.fov * Math.PI) / 180;
    const aspect = this.camera.aspect;
    const { right: r, left: l, bottom: b, top: t } = floor.floorInfo;
    const w = Math.abs(r - l) + FLOOR_SPACE;
    const h = Math.abs(t - b) + FLOOR_SPACE;
    const wd = w / (2 * Math.tan(vFov / 2) * aspect);
    const hd = h / (2 * Math.tan(vFov / 2));
    return Math.ceil(Math.max(wd, hd));
  }
  _initCamera() {
    const { offsetWidth, offsetHeight } = this.Map3d.$dom;
    const aspt = offsetWidth / offsetHeight;
    this.camera = new THREE.PerspectiveCamera(45, aspt, 0.1, 1000);
    this.camera.position.set(0, 20, 40);
    this.camera.lookAt(0, 0, 0);
  }
}

export default Camera;
