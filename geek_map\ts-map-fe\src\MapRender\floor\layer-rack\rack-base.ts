/* ! <AUTHOR> at 2022/09/02 */
import * as PIX<PERSON> from "pixi.js";
import LayerRackStatus from "./rack-status";

class LayerRack implements MRender.Layer {
  floorId: floorId;
  layerRackStatus: LayerRackStatus = new LayerRackStatus();
  private mapCore: MRender.MainCore;
  private floor: any;
  private container: PIXI.Container;
  private shader: any;
  private meshList: Array<any> = [];
  private vColor: any;
  private hasBoxVColor: any;
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    const utils = mapCore.utils;
    this.shader = utils.getShader("iconColor", utils.getResources("rack"));
    this.vColor = utils.getShaderColor("RACK"); // 货架vColor
    this.hasBoxVColor = utils.getShaderColor("RACK_EMPTY");

    this.init();
  }

  render(racks: Array<rackData>): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;
    const shader = this.shader;

    const fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    for (let i = 0, len = Math.ceil(racks.length / fragment); i < len; i++) {
      const arr = racks.slice(i * fragment, i * fragment + fragment);
      const meshData = this.resolveRacks(arr);
      if (!meshData) continue;

      const { meshKey, geometries } = meshData;
      let mesh = utils.createMesh(geometries, shader);
      mesh.name = meshKey;
      mesh.mapType = "rack";
      mesh.interactive = mesh.buttonMode = true;

      this.meshList.push(mesh);
      this.container.addChild(mesh);
    }

    this.layerRackStatus.render();
  }

  toggleLayers(): void {
    console.log("racks 不需要这里toggleLayers 在update里做实时的layer toggle");
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.mapCore.meshData.rack.delByFloorId(this.floorId);
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];
    this.layerRackStatus.repaint();
  }

  destroy(): void {
    this.repaint();
    this.layerRackStatus.destroy();
    this.container.destroy({ children: true });
    this.layerRackStatus = null;
    this.container = null;

    this.floorId = null;
    this.mapCore = null;
    this.floor = null;
    this.shader = null;
    this.meshList = null;
    this.vColor = null;
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container(); // cell container
    container.name = "rack";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("rack");
    this.container = container;

    this.layerRackStatus.init(mapCore);

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container, this.layerRackStatus.getContainer());
  }

  private getStationColor(options: mRackData) {
    if (options.boxNum === 0) return this.hasBoxVColor;
    return this.vColor;
  }

  private resolveRacks(arr: Array<rackData>): any {
    const _this = this;
    const mapCore = _this.mapCore,
      utils = mapCore.utils,
      mapData = mapCore.mapData,
      meshData = mapCore.meshData;

    let data = [];
    let geometries = [];
    let meshKey, item, options, code;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatRack(item);
      code = options["code"];
      if (!meshKey) meshKey = code;
      const geometry = utils.drawGeometry(
        "iconColor",
        options["position"],
        this.getStationColor(options),
      );

      // 锁定状态
      // if (options["lockedState"] === "LOCKED") {
      //   _this.layerShelfStatus.drawGeometryLocked(options);
      // }

      data.push(options);
      geometries.push(geometry);
      mapData.rack.setData(code, options);
    }

    if (!meshKey) return null;

    meshData.rack.setData(meshKey, data);
    return { meshKey, geometries };
  }
}
export default LayerRack;
