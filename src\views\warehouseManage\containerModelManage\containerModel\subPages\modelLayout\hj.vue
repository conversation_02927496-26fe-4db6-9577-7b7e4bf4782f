<template>
  <div class="hj">
    <div class="hjContent clear">
      <!-- 货架图形 -->
      <div class="hjPicture">
        <leftPicture
          :form-data="planeAllArr[activePlane.index]"
          :plane-all-arr="planeAllArr"
          @planeClick="planeClickFun"
        />
      </div>

      <!-- 右侧表单 -->
      <div class="hjForm">
        <el-form ref="form" :model="form" :inline="true" label-width="80px">
          <div class="formTop">
            <el-form-item label="容器面">
              <el-select v-model="form.plane" placeholder="请选择容器面">
                <el-option label="单面" value="1" />
                <el-option label="双面" value="2" />
                <el-option label="四面" value="4" />
              </el-select>
            </el-form-item>
            <el-form-item label="层数">
              <el-input-number
                v-model="form.num"
                controls-position="right"
                :min="1"
                :max="5"
                @change="handleChange"
              />
            </el-form-item>
            <el-form-item v-if="form.plane == '4'" label="占用面">
              <el-select v-model="form.occupyEdge" placeholder="请选择占用面">
                <el-option label="窄边占用" value="0" />
                <el-option label="宽边占用" value="1" />
              </el-select>
            </el-form-item>
          </div>
          <div class="formBottom">
            <p>{{ plane }}</p>
            <div
              v-for="(item, index) in planeAllArr[returnIndex(activePlane.index)].row"
              :key="index"
            >
              <el-form-item
                :label="`第${planeAllArr[returnIndex(activePlane.index)].row.length - index}层`"
              >
                <el-input-number v-model="item.cel" controls-position="right" :min="1" :max="5" />
                <span>列</span>
              </el-form-item>
              <el-form-item label="高">
                <el-input
                  v-model="item.height"
                  placeholder="请填写高度"
                  style="width: 150px"
                  @change="
                    value =>
                      heightChange(value, planeAllArr[returnIndex(activePlane.index)].row, index)
                  "
                />
                <span>mm</span>
              </el-form-item>
            </div>
            <div v-if="form.plane == '4'">
              <hr style="height: 1px; background: #eee; margin-bottom: 20px" />
              <el-form-item label="深度">
                <el-input
                  v-model="planeAllArr[returnIndex(activePlane.index)].depth"
                  placeholder="请填写深度"
                  style="width: 150px"
                />
                <span>mm</span>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import leftPicture from "./leftPicture.vue";

export default {
  components: {
    leftPicture,
  },
  data() {
    return {
      form: {
        plane: "1",
        num: "1",
        occupyEdge: "",
        planeArr: [
          {
            row: "1",
            height: "300",
            code: "",
          },
        ],
      },
      activePlane: { label: "前面", code: "", index: 0 },
      planeAllArr: [{ row: [{ cel: 1, height: 300, code: "" }], layerLength: "1" }],
      plane: "前面",
    };
  },
  computed: {
    isEmpty() {
      return this.$store.state.containerModal.emptySwich;
    },
    open() {
      return this.$store.state.containerModal.layoutOpen;
    },
  },

  watch: {
    open: {
      handler() {
        this.initialization();
      },
      deep: true,
      immediate: true,
    },
    "form.plane": {
      handler(nv) {
        const arr = new Array(nv * 1).fill(0).map((e, index) => {
          let fx = "";
          switch (index) {
            case 0:
              fx = "F";
              break;
            case 1:
              fx = "B";
              break;
            case 2:
              fx = "L";
              break;
            case 3:
              fx = "R";
              break;
          }

          return {
            // id: "",
            modelId: "",
            orientationCount: nv,
            occupyEdge: "",
            depth: "100",
            orientation: fx,
            containerFlag: 0,
            row: new Array(this.form.num).fill(0).map(() => ({ cel: 1, height: 300, code: "" })),
          };
        });
        // if(arr.length<this.planeAllArr){
        //   this.activePlane = { label: "前面", code: "", index: 0 }
        // }
        this.planeAllArr = arr;
      },
      deep: true,
      immediate: true,
    },
    "form.num": {
      handler(nv) {
        this.planeAllArr.map(item => {
          item.row = new Array(nv).fill(0).map(() => ({ cel: 1, height: 300, code: "" }));
        });
      },
      deep: true,
      immediate: true,
    },
    "form.occupyEdge": {
      handler(nv) {
        this.planeAllArr.map(item => {
          item.occupyEdge = nv;
        });
      },
      deep: true,
      immediate: true,
    },
    planeAllArr: {
      handler(nv) {
        this.$emit("planeAllArrData", nv);
      },
      deep: true,
      immediate: true,
    },
    isEmpty() {
      setTimeout(() => {
        this.claerData();
      }, 500);
    },
  },
  mounted() {
    // this.initialization();
  },
  methods: {
    // 高度过滤掉 字符串
    heightChange(value, arr, index) {
      arr[index].height = value
        .split("")
        .filter(item => !isNaN(item / 1))
        .join("");
    },
    claerData() {
      // this.planeAllArr = [{ row: [{ cel: 1, height: 300, code: "" }] }]
    },
    // 初始化数据
    initialization() {
      // eslint-disable-next-line
      const _this = this;
      setTimeout(function () {
        // _this.planeAllArr

        const arr = _this.$store.state.containerModal.activeIDData.map(e => {
          return {
            // id: e.id,
            modelId: e.modelId,
            orientation: e.orientation,
            containerFlag: e.containerFlag,
            orientationCount: _this.$store.state.containerModal.activeIDData.length,
            occupyEdge: e.occupyEdge,
            depth: e.depth,
            row: e.layout.map(eIn => ({
              cel: eIn.layerColumns.length,
              height: eIn.layerHeight,
              code: "",
            })),
          };
        });

        if (arr.length) {
          _this.form.plane = arr.length + "";
          _this.form.num = arr[0].row.length + "";
          _this.form.occupyEdge = arr[0].occupyEdge + "";

          setTimeout(function () {
            _this.planeAllArr = arr;
          });
        } else {
          _this.form.plane = "1";
          _this.form.num = "1";
        }
      });
    },
    returnIndex(index) {
      return index > this.planeAllArr.length - 1 ? 0 : index;
    },
    handleChange(data) {
      // eslint-disable-next-line
      var _this = this;
      const obj = {
        row: "1",
        height: "300",
        code: "",
      };
      this.form.planeArr = [];
      for (var i = 0; i < data; i++) {
        _this.form.planeArr.push({ ...obj });
      }
    },
    planeClickFun(data) {
      this.plane = data.label;
      this.activePlane = data;
      //   this.form.planeArr = [...this.planeAllArr[data.index].map(e=>e)]
    },
  },
};
</script>

<style scoped>
.formBottom {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 5px;
  margin-top: 15px;
}
.formTop {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 5px;
}
.hjForm {
  width: calc(65% - 20px);
  margin-left: 20px;
  /* background: green; */
}

.hjPicture {
  width: 35%;
  height: 100%;
  /* background: lightcoral; */
  position: absolute;
}
.clear::after {
  display: block;
  content: "";
  clear: both;
}
.hjContent > div {
  float: right;
}

.hjContent {
  width: 100%;
  position: relative;
  /* background: red; */
}
</style>
