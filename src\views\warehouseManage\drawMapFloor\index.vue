<template>
  <geek-main-structure :space="false">
    <div class="canvas-draw-box">
      <label v-show="drawingStatus" class="status">{{ $t("lang.rms.fed.updatingMap") }}</label>

      <floor-canvas ref="floorCanvas" :zoom.sync="zoom" @setZoomMin="setZoomMin" />

      <div class="zoom-bar">
        <span>{{ $t("lang.rms.fed.zoom") }}</span>
        <el-slider
          v-model="zoom"
          :min="min"
          :max="100"
          height="300px"
          vertical
          @change="scaleImage"
        />
      </div>
    </div>

    <div class="complete-btn">
      <el-button :disabled="!drawingStatus" type="primary" @click="drawComplete">
        {{ $t("lang.rms.fed.finishDrawing") }}
      </el-button>
    </div>
    <dialog-robot-choose
      :visible.sync="robotChooseVisible"
      :map-id="mapId"
      :floor-id="floorId"
      @floorDrawing="floorDrawing"
    />
  </geek-main-structure>
</template>

<script>
import FloorCanvas from "./components/floor-canvas";
import dialogRobotChoose from "./components/dialog-robot-choose";
import img111 from "./mapDemo/demo.png";

export default {
  name: "MapCreate",
  components: { FloorCanvas, dialogRobotChoose },
  data() {
    return {
      mapId: "",
      floorId: "",
      status: "",
      robotId: "",
      robotChooseVisible: true,

      min: -1,
      zoom: -1,

      $floorCanvas: null,

      floorDrawingTimer: null,
      drawingStatus: false, // 地图是否正在绘制
      isComplete: false, // 地图是否绘制完成
    };
  },
  beforeRouteEnter(to, from, next) {
    const { mapId, floorId, status } = to.query;
    if (!mapId || !floorId) {
      next("/warehouseManage/mapManage");
    } else {
      next(vm => {
        Object.assign(vm.$data, vm.$options.data(), {
          mapId,
          floorId,
          status,
          robotChooseVisible: true,
        });
      });
    }
  },
  beforeRouteLeave(to, from, next) {
    this.robotChooseVisible = false;
    this.isComplete = true;
    next();
  },
  methods: {
    // 绘制地图ing
    floorDrawing(robotId) {
      if (robotId) this.robotId = robotId;
      if (this.isComplete) return;

      $req
        .postParams("/athena/map/draw/getCreatingMap", {
          mapId: this.mapId,
          floorId: this.floorId,
          robotId: this.robotId,
        })
        .then(res => {
          let url = "";
          if (res.data && res.data.base64Text) url = res.data.base64Text;
          this.drawImage(url);
          this.floorDrawingTimer = setTimeout(() => {
            this.floorDrawing();
          }, 5000);
        })
        .catch(e => {
          console.log(e);
        });
    },
    drawImage(url) {
      this.drawingStatus = true;
      if (!this.$floorCanvas) {
        this.$floorCanvas = this.$refs.floorCanvas;
      }
      if (!url) return;
      this.$floorCanvas.drawImage(url);
    },
    scaleImage() {
      this.$floorCanvas.scaleImage();
    },
    drawComplete() {
      $req
        .postParams("/athena/map/draw/stopAndSaveMap", {
          mapId: this.mapId,
          floorId: this.floorId,
          robotId: this.robotId,
        })
        .then(res => {
          this.isComplete = true;
          this.toEditPage();
        })
        .catch(e => {
          console.log(e);
        });
    },
    toEditPage() {
      // 是否去编辑地图页面 点击取消则去列表页面
      this.$geekConfirm(this.$t("lang.rms.fed.youCanEditTheMap"), {
        confirmText: this.$t("lang.rms.fed.editMap"),
      })
        .then(res => {
          this.$router.push({
            path: "/warehouseManage/editMap",
            query: {
              mapId: this.mapId,
              floorId: this.floorId,
              status: this.status,
            },
          });
        })
        .catch(e => {
          this.$router.go(-1);
        });
    },
    setZoomMin(min) {
      this.min = min;
    },
  },
};
</script>

<style lang="less" scoped>
@keyframes Processing {
  0% {
    -webkit-transform: scale(0.8);
    transform: scale(0.8);
    opacity: 0.5;
  }

  to {
    -webkit-transform: scale(2.4);
    transform: scale(2.4);
    opacity: 0;
  }
}

.canvas-draw-box {
  position: relative;
  width: 100%;
  height: calc(100% - 60px);
  padding: 20px;
  background: #fff;
  overflow: hidden;

  .status {
    position: absolute;
    left: 30px;
    top: 30px;
    font-size: 12px;
    padding: 5px 10px 5px 22px;
    border-radius: 3px;
    background: #fff;

    &:after {
      position: absolute;
      top: 50%;
      left: 10px;
      width: 8px;
      height: 8px;
      margin-top: -4px;
      border-radius: 50%;
      border: 0;
      background: #f5222d;
      content: "";
    }

    &:before {
      position: absolute;
      top: 50%;
      left: 7px;
      width: 10px;
      height: 10px;
      margin-top: -5px;
      border-radius: 50%;
      border: 1px solid #f5222d;
      background: #f5222d;
      content: "";
      -webkit-animation: Processing 1.2s infinite ease-in-out;
      animation: Processing 1.2s infinite ease-in-out;
    }
  }

  .zoom-bar {
    position: absolute;
    right: 20px;
    top: 50%;
    margin-top: -160px;

    > span {
      display: block;
      font-size: 13px;
      text-align: center;
      padding-bottom: 12px;
    }
  }
}

.complete-btn {
  margin-top: 5px;
  text-align: center;
}
</style>
