import AverageShelfLifeCred from "./cred/averageShelfLifeCred";
import DeliveryShelfLifeCred from "./cred/deliveryShelfLifeCred";
import StayStageCred from "./cred/stayStageCred";
import QueueStageCred from "./cred/queueStageCred";
import ReturnStageCred from "./cred/returnStageCred";
import SumTransportDataByBar from "./bar/sumTransportDataByBar";
import SumTransportDataByLine from "./line/sumTransportDataByLine";

import AverageSumBydistanceCred from "./cred/averageSumBydistanceCred";
import FetchShelfLifeBydistanceCred from "./cred/fetchShelfLifeBydistanceCred";
import DeliveryShelfLifeBydistanceCred from "./cred/deliveryShelfLifeBydistanceCred";
import ReturnStageBydistanceCred from "./cred/returnStageBydistanceCred";
import SumTransportDataDistanceByBar from "./bar/sumTransportDataDistanceByBar";
import SumTransportDataDistanceByLine from "./line/sumTransportDataDistanceByLine";


export default [
  // 平均取货架时长
  new AverageShelfLifeCred({ width: '20%', height: 6 }),
  // 平均送货架时长
  new DeliveryShelfLifeCred({ width: '20%', height: 6 }),
  // 平均货架排队时长
  new QueueStageCred({ width: '20%', height: 6 }),
  // 停留时长
  new StayStageCred({ width: '20%', height: 6 }),
  // 平均还货架时长
  new ReturnStageCred({ width: '20%', height: 6 }),
  // 搬运时长统计
  new SumTransportDataByBar({ width: '50%', height: '400px' }),
  // 搬运时长趋势
  new SumTransportDataByLine({ width: '50%', height: '400px' }),
  
  // 平均送货架距离
  new AverageSumBydistanceCred({ width: '25%', height: 6 }),
  // 平均送货架距离
  new FetchShelfLifeBydistanceCred({ width: '25%', height: 6 }),
  // 平均送货架距离
  new DeliveryShelfLifeBydistanceCred({ width: '25%', height: 6 }),
  // 平均任务距离
  new ReturnStageBydistanceCred({ width: '25%', height: 6 }),
  // 搬运距离统计
  new SumTransportDataDistanceByBar({ width: '50%', height: '400px' }),
  // 搬运距离趋势
  new SumTransportDataDistanceByLine({ width: '50%', height: '400px' }),
]
