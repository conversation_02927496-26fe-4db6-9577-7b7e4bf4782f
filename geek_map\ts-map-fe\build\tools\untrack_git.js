/* ! <AUTHOR> at 2023/03/31 */
const execSync = require("child_process").execSync; // 同步子进程

const UntrackPaths = ["./monitor2D"];
UntrackPaths.forEach(path => {
  try {
    execSync(`git rm --cached ${path} -r`);
  } catch (e) {
    console.log("这个文件夹已经untrack了");
  }
});

// 只对自己本地的文件游泳的 skip git，自己可以选择性执行
// const SkipPaths = ["./build/config.js"];
// SkipPaths.forEach(path => {
//   try {
//     execSync(`git update-index --skip-worktree ${path}`);
//   } catch (e) {
//     console.log("这个文件夹已经skip了");
//   }
// });
