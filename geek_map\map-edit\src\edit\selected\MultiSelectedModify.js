import LayerManager from "../layerManager/LayerManager";
import {Graphics} from "pixi.js";
import Rect from "../element/baseElement/Rect";
import Control from "../control/Control";
import {isInsideRect,getId} from '../utils/utils'
import Selected from '../selected/Selected'
import EventBus from "../eventBus/EventBus";
import Mode from "../Mode";
import {cloneDeep} from "lodash";
export default class MultiSelectedModify {
  constructor() {
    this.startPos = {}
    this.endPos = {}
    this.w = 0
    this.h = 0
    this.$operateLayer = null
    this.$rect = null
    //需要copy的目标点
    this.aim = null
    //不允许复制的功能
    this.disabled = ['AVOID_CELL', 'BACKUP_CELL', 'QUEUE_CELL', 'RECYCLEID', 'WAIT_FOR_TASK_CELL', 'WAIT_CELL', 'VIA_CELL','DOCKING_CELL']
  }
  //设置批量修改目标
  setAim(properties) {
    const {nodeId} = properties
    const {$el} = LayerManager.getProperties({layerName:'CELL',id:nodeId});
    Selected.renderSelected(nodeId,$el)
    this.aim = properties
  }
  //点击修改
  clickModify(e) {
    const target = e.event.target
    if(target.name !== 'element') return
    const $el = target.isSprite ? target : target.parent
    const nodeId = getId($el)
    this.modify([{nodeId}]);
  }
  // setAim(e) {
  //   const target = e.event.target
  //   if(target.name !== 'element') return
  //   // if(['viewport','device'].includes(target.name)) return
  //   const $el = target.isSprite ? target : target.parent
  //   // const {nodeId,segmentId,id} = $el
  //   // const selectedId = nodeId || segmentId || id
  //   const selectedId = getId($el)
  //   if(Selected.isHasSelected(selectedId)){
  //     Selected.resetSelected(selectedId)
  //     this.aim = null
  //   }else{
  //     Selected.renderSelected(selectedId,$el)
  //     const {properties} = Selected.getAllSelected()[0]
  //     this.aim = properties
  //   }
  //   console.log(this.aim)
  // }
  render(endPos= {x:0,y:0}) {
    if(!this.$rect) return
    this.endPos = endPos
    const {x:ex,y:ey} = this.endPos
    const {x:sx,y:sy} = this.startPos
    const w = this.w = ex - sx
    const h = this.h = ey - sy
    const renderOps = {
      x:sx,y:sy,w,h
    }
    this.$rect.clear()
    Rect.render(this.$rect,renderOps)
  }
  start(startPos = {x:0,y:0}) {
    if(!this.aim) return
    Control.enableDrag(false)
    const operateLayerInstance = LayerManager.get('OPERATE')
    this.$operateLayer = operateLayerInstance.container
    this.$rect = new Graphics()
    this.$operateLayer.addChild(this.$rect)
    this.startPos = startPos
  }
  end(){
    const {w,h,startPos,endPos} = this
    if(!startPos || !this.aim) return
    const {x:sx,y:sy} = startPos
    const {x:ex,y:ey} = endPos
    const rectOp = {x:Math.min(sx,ex),y:Math.min(sy,ey),width:Math.abs(w),height:Math.abs(h)}
    const result = isInsideRect(rectOp)
    this.modify(result)
    // if(result.length){
    //   Selected.isMultipleSelected = true
    //   const {location,cellCode,indexX,indexY,id,nodeId,hostCode,qrCode,startBounds,renderID,mapEditItemId,dbId,...others} = this.aim
    //   const updateData = result.map(item => {
    //     const {nodeId} = item
    //     const {properties} = LayerManager.getProperties({layerName:'CELL',id:nodeId})
    //     //判断是否被选中
    //     // const isSelected = Selected.isHasSelected(nodeId)
    //     // if(!isSelected) Selected.renderSelected(nodeId,$el)
    //     return Object.assign({},properties,others)
    //   })
    //   LayerManager.updateElements({
    //     id:'CELL',
    //     data:updateData,
    //     isCoverProperties:true
    //   })
    //   // const selectedData = Selected.getAllSelected()
    //   // EventBus.$emit('selected',selectedData)
    // }
    // this.clear()
    // Control.enableDrag(true)
    // Selected.resetAllSelected()
    // Mode.resetMode()
  }
  //格式刷批量修改函数
  modify(result = []) {
    if(result.length){
      Selected.isMultipleSelected = true
      const {location,cellCode,indexX,indexY,id,nodeId,hostCode,qrCode,startBounds,renderID,mapEditItemId,dbId,...others} = this.aim
      const updateData = result.map(item => {
        const {nodeId} = item
        const {properties} = LayerManager.getProperties({layerName:'CELL',id:nodeId})
        const {functions} = others
        //复制方法
        let copyFunctions = null;
        if(functions){
          copyFunctions = cloneDeep(functions)
          copyFunctions = copyFunctions.filter(fn => {
            return !this.disabled.includes(fn.funcType)
          })
        }
        return Object.assign({},properties,others,{functions:copyFunctions})
      })
      LayerManager.updateElements({
        id:'CELL',
        data:updateData,
        isCoverProperties:true
      })
    }
    this.clear()
    Control.enableDrag(true)
    Selected.resetAllSelected()
    Mode.resetMode()
  }
  clear() {
    this.aim = null
    this.$rect = null
    this.startPos = null
    this.w = null
    this.h = null
    if(this.$operateLayer) this.$operateLayer.removeChildren()
  }
  destroy() {
    this.clear()
    Selected.isMultipleSelected = false
    Selected.resetAllSelected()
    EventBus.$emit('selected',null)
  }
}
