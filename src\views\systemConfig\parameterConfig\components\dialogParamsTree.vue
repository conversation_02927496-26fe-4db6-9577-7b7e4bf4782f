<template>
  <el-dialog
    :title="$t('lang.rms.fed.selectParameters')"
    :visible.sync="dialogVisible"
    :before-close="close"
    width="70%"
  >
    <div class="header-tit">
      <p>
        <label>
          {{ $t("lang.rms.fed.whole") }} / {{ $t("lang.rms.fed.selected") }} /
          {{ $t("lang.rms.fed.unselected") }} :
        </label>
        <span>
          {{ currentTreeNumber }} / {{ checkedKeyLength }} /
          {{ currentTreeNumber - checkedKeyLength }}
        </span>
      </p>
      <div class="grid-content bg-purple">
        <el-button type="text" @click="checkAllTree">
          {{ $t("lang.rms.fed.SelectAll") }}
        </el-button>
        <el-button type="text" @click="uncheckAllTree">
          {{ $t("lang.rms.fed.unSelectAll") }}
        </el-button>
      </div>
    </div>

    <el-tree
      ref="paramsTree"
      :data="treeData"
      show-checkbox
      node-key="code"
      :default-expanded-keys="['system']"
      :default-checked-keys="selectedParams"
      :props="defaultProps"
      @check-change="treeCheckChange"
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.rms.fed.cancel") }}</el-button>
      <el-button type="primary" @click="handleSave">{{ $t("lang.rms.fed.confirm") }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      dialogVisible: false,
      currentId: null,
      treeData: [],
      selectedParams: [],
      currentTreeNumber: 0,
      checkedKeyLength: 0,
      topKeyList: [],
      childKeyList: [],
      defaultProps: {
        children: "childConfig",
        label: "code",
      },
    };
  },
  methods: {
    open(currentLibrary, paramsTree) {
      let selectedData =
        currentLibrary && currentLibrary.hasOwnProperty("items") ? currentLibrary.items : [];

      let selectedParams = [];
      selectedData.map(item => selectedParams.push(item.code));
      const { topKeyList, childKeyList } = this._deepData(paramsTree);

      this.currentId = currentLibrary.template.id;
      this.treeData = paramsTree;
      this.selectedParams = selectedParams;
      this.$nextTick(() => this.treeCheckChange());

      this.currentTreeNumber = childKeyList.length;
      this.topKeyList = topKeyList;
      this.childKeyList = childKeyList;
      this.dialogVisible = true;
    },
    close() {
      Object.assign(this.$data, this.$options.data());
    },

    checkAllTree() {
      const $tree = this.$refs.paramsTree;
      $tree.setCheckedKeys(this.childKeyList);
    },
    uncheckAllTree() {
      const $tree = this.$refs.paramsTree;
      const topKeyList = this.topKeyList;
      topKeyList.forEach(key => {
        $tree.setChecked(key, false, true);
      });
    },
    handleSave() {
      // 配置库-参数项添加
      $req
        .post(
          "/athena/config/template/item/add?templateId=" + this.currentId,
          this.$refs.paramsTree.getCheckedKeys(),
        )
        .then(res => {
          this.$emit("updatedLibrary", this.currentId);
          this.close();
        });
    },

    treeCheckChange() {
      const $tree = this.$refs.paramsTree;
      const checkedKeys = $tree.getCheckedKeys();
      const topKeyList = this.topKeyList;
      let checkedKeyLength = checkedKeys.length;
      topKeyList.forEach(key => {
        if (checkedKeys.includes(key)) {
          checkedKeyLength -= 1;
        }
      });

      this.checkedKeyLength = checkedKeyLength;
    },
    _deepData(list, topKeyList = [], childKeyList = []) {
      list.forEach(item => {
        if (item.childConfig) {
          topKeyList.push(item.code);
          this._deepData(item.childConfig, topKeyList, childKeyList);
        } else {
          childKeyList.push(item.code);
        }
      });

      return {
        topKeyList,
        childKeyList,
      };
    },
  },
};
</script>
<style lang="less" scoped>
.header-tit {
  .g-flex();

  label {
    font-weight: 600;
  }
}
</style>
