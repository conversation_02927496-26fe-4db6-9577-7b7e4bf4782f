import ElementType from "@packages/elements/ElementType";
import { NODE_DEVICE, NODE_IDKEY_MAP, NODE_TYPEKEY_MAP } from "@packages/configure/dict/nodeType";
import { FormItemExPropsType } from "@packages/components/map-panel-attr/components/base/type";
import { useAttrStore } from "@packages/store/attr";

class Station extends ElementType {
  attrStore: any;

  constructor() {
    super({
      name: "station",
      nodeType: NODE_DEVICE,
      nodeIdKey: NODE_IDKEY_MAP[NODE_DEVICE],
      nodeTypeKey: NODE_TYPEKEY_MAP[NODE_DEVICE],
    });

    this.attrStore = useAttrStore();
  }

  getFromItem(): FormItemExPropsType[] {
    return [];
  }
}
