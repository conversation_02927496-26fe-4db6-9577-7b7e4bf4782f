/* ! <AUTHOR> at 2023/04/21 */

type speedLimitArea = { element: any; isShow: boolean };

class SpeedLimitAreasData implements MRender.MapData {
  private mapData: { [propName: code]: speedLimitArea } = {};
  /** 当前限速中的 areaID */
  private currentSpeedLimitAreaIds: Array<code> = [];

  setData(code: code, data: speedLimitArea) {
    this.mapData[code] = data;
  }

  getData(code: code) {
    return this.mapData[code];
  }

  getByFloorId(floorId: floorId): void {}

  getAll() {
    return this.mapData;
  }

  /**
   * 获取当前显示的speedLimitAreaIds
   * @returns ids
   */
  getIds(): Array<code> {
    return this.currentSpeedLimitAreaIds;
  }

  delData(code: code) {
    const element = this.mapData[code]?.element;
    element && element.destroy();
    delete this.mapData[code];
  }

  /**
   * 根据传入的areaIds显示限速area
   * @param areaIds 传入的需要显示的areaId
   */
  showSpeedLimitArea(areaIds: Array<code> = []) {
    const mapData = this.mapData;

    areaIds.forEach(areaId => {
      if (mapData[areaId].isShow) return;
      mapData[areaId].element.visible = true;
    });
  }

  /**
   * 根据传入的areaIds自动判断显隐
   * @param areaIds
   */
  toggleSpeedLimitArea(areaIds: Array<code>) {
    // debugger;
    this.currentSpeedLimitAreaIds = areaIds;
    const speedLimitAreas = this.mapData;
    let speedLimitArea;
    for (let areaId in speedLimitAreas) {
      speedLimitArea = speedLimitAreas[areaId];
      if (areaIds.includes(areaId)) {
        if (speedLimitArea.isShow) continue;
        else {
          speedLimitArea.isShow = true;
          speedLimitArea.element.visible = true;
        }
      } else {
        if (!speedLimitArea.isShow) continue;
        speedLimitArea.isShow = false;
        speedLimitArea.element.visible = false;
      }
    }
  }

  /**
   * 根据当前数据更新一下speedLimitArea
   */
  repaint() {
    const mapData = this.mapData;

    let speedLimitArea;
    for (let areaId in mapData) {
      speedLimitArea = mapData[areaId];
      if (speedLimitArea.isShow) continue;
      else speedLimitArea.element.visible = false;
    }
  }

  uninstall() {
    const data = this.mapData;

    let element;
    for (let key in data) {
      element = data[key]?.element;
      element && element.destroy();
    }
    this.mapData = {};

    this.currentSpeedLimitAreaIds = [];
  }

  destroy() {
    this.uninstall();
  }
}

export default SpeedLimitAreasData;
