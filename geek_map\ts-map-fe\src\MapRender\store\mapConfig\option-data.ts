/* ! <AUTHOR> at 2023/04/21 */

class LayerToggle {
  /**
   * 同时可多选择的layerName
   */
  private multiSelectLayers: MRender.layerName[] = null;
  /**
   * 货架热度数据，热度数据为后来的数据
   */
  private shelfHeatData: shelfHeatApiData = null;
  /**
   * 当前选中的单元格数据
   * 选中的时候用于遍历机器人的终点单元格
   */
  private destCell: mCellData = null;

  setMultiLayers(layerNames: Array<MRender.layerName> = null) {
    this.multiSelectLayers = layerNames;
  }

  getMultiLayers() {
    return this.multiSelectLayers;
  }

  setShelfHeatData(heatData: shelfHeatApiData = null) {
    this.shelfHeatData = heatData;
  }
  getShelfHeatData() {
    return this.shelfHeatData;
  }

  setDestCell(data: mCellData = null) {
    if (!data) this.destCell = null;
    else this.destCell = data;
  }

  getDestCell() {
    return this.destCell;
  }

  uninstall() {
    this.multiSelectLayers = null;
    this.shelfHeatData = null;
    this.destCell = null;
  }

  destroy() {
    this.uninstall();
  }
}
export default LayerToggle;
