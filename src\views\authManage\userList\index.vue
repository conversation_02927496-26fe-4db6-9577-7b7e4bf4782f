<template>
  <!-- 用户列表 -->
  <geek-main-structure>
    <geek-customize-form ref="userForm" :form-config="formConfig" @on-query="getUserList" @on-reset="getUserList" />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @row-add="rowAdd"
      @page-change="pageChange"
      style="margin-top: 10px"
    >
      <template #status="{ row }">
        <el-tag v-if="row.status == 0" size="mini" type="danger">{{ $t("lang.rms.fed.textProhibit") }}</el-tag>
        <el-tag v-else size="mini" type="success">{{ $t("lang.rms.fed.textEnable") }}</el-tag>
      </template>

      <template #operations="{ row }">
        <el-button type="text" size="small" @click="rowEdit(row)">
          {{ $t("lang.rms.fed.buttonEdit") }}
        </el-button>
        <el-popover placement="top" trigger="click" popper-class="change-pwd" width="260">
          <div class="pop-content">
            <label>{{ $t("lang.rms.fed.newPassword") }}: </label>
            <div class="input">
              <el-input :placeholder="$t('lang.rms.fed.pleaseEnterContent')" v-model="newPassword" show-password />
            </div>
          </div>
          <div class="pop-btns">
            <el-button size="mini" @click="newPassword = ''">
              {{ $t("lang.rms.fed.reset") }}
            </el-button>
            <el-button type="primary" size="mini" @click="changePassWord(row)">
              {{ $t("lang.rms.fed.save") }}
            </el-button>
          </div>
          <el-button slot="reference" type="text" @click="popoverVisible = !popoverVisible">
            {{ $t("lang.rms.fed.changePassword") }}
          </el-button>
        </el-popover>
        <el-button
          v-if="checkPermission('AuthQueryUser')"
          type="text"
          size="small"
          :disabled="[1].includes(row.userId)"
          @click="rowChangeStatus(row)"
        >
          <p v-if="row.status == 0">{{ $t("lang.rms.fed.textEnable") }}</p>
          <p v-else>{{ $t("lang.rms.fed.textProhibit") }}</p>
        </el-button>
      </template>
    </geek-customize-table>

    <create-user-dialog ref="addUser" @updateList="getUserList" />
    <edit-user-dialog ref="editUser" @updateList="getUserList" />
  </geek-main-structure>
</template>

<script>
import md5 from "js-md5";
import CreateUserDialog from "./components/dialog-create-user";
import EditUserDialog from "./components/dialog-edit-user";
export default {
  name: "UserList",
  components: {
    CreateUserDialog,
    EditUserDialog,
  },
  data() {
    return {
      passwordEyeShow: false,
      newPassword: "",
      popoverVisible: false,

      formConfig: {
        attrs: {
          labelWidth: "80px",
          inline: true,
        },
        configs: {
          userNameAsFuzz: {
            label: "lang.rms.fed.userName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          realNameAsFuzz: {
            label: "lang.rms.fed.inputFullName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          phoneAsFuzz: {
            label: "lang.rms.fed.inputTelephone",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnterContent",
          },
          status: {
            label: "lang.rms.fed.inputState",
            tag: "select",
            default: "",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [
              {
                value: "1",
                label: "lang.rms.fed.textEnable",
              },
              {
                value: "0",
                label: "lang.rms.fed.buttonProhibit",
              },
            ],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        actions: [
          {
            label: "lang.rms.fed.add",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          {
            label: "lang.rms.fed.number",
            width: "50",
            prop: "userId",
          },
          {
            label: "lang.rms.fed.inputUserName",
            prop: "userName",
          },
          {
            label: "lang.rms.fed.inputFullName",
            prop: "realName",
          },
          {
            label: "lang.rms.fed.inputTelephone",
            prop: "phone",
          },
          {
            label: "lang.rms.fed.inputState",
            prop: "status",
            slotName: "status",
            width: "90",
            align: "center",
          },
          {
            label: "lang.rms.fed.role",
            prop: "roleNames",
          },
          {
            label: "lang.rms.fed.listFounder",
            prop: "creator",
          },
          {
            label: "lang.rms.fed.listCreationTime",
            "show-overflow-tooltip": true,
            width: "130",
            prop: "createTime",
            formatter: (row, column, cellValue, index) => {
              if (!cellValue) return "";
              return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "180",
            slotName: "operations",
            align: "center",
          },
        ],
      },
    };
  },
  activated() {
    this.getUserList();
  },
  methods: {
    rowAdd() {
      this.$refs.addUser.open();
    },
    rowEdit(row) {
      this.$refs.editUser.open(row);
    },
    // 列表接口请求
    getUserList() {
      const formData = this.$refs.userForm.getData();
      const params = Object.assign({}, formData, {
        pageSize: this.tablePage.pageSize,
        currentPage: this.tablePage.currentPage,
      });
      $req
        .get("/athena/api/coreresource/auth/user/pageQuery/v1", {
          ...params,
          params: true,
        })
        .then(res => {
          const data = res.data || {};

          this.tableData = data.recordList;
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: data.currentPage || 1,
            pageCount: data.pageCount,
          });
        });
    },

    pageChange(page) {
      this.tablePage = page;
      this.getRoleList();
    },

    rowChangeStatus(row) {
      // 传入当前状态，请求相反状态
      // Status为用户状态，0表示禁用，1表示启用
      if (row.status === 0) {
        $req
          .post("/athena/api/coreresource/auth/user/enableUser/v1", {
            userId: row.userId,
            status: 1,
          })
          .then(res => {
            if (res.code === 0) {
              this.getUserList();
            }
          });
      } else {
        $req
          .post("/athena/api/coreresource/auth/user/disableUser/v1", {
            userId: row.userId,
            status: 0,
          })
          .then(res => {
            if (res.code === 0) {
              this.getUserList();
            }
          });
      }
    },
    changePassWord(userInfo) {
      // 密码8-20位、数字、字母、特殊符号组成
      let passReg = /^(?=.*?[a-zA-Z])(?=.*?\d)(?=.*?[*?!&￥$%^#,./@";:><\[\]}{\-=+_\\|》《。，、？’‘“”~ ]).{8,20}$/;
      if (!passReg.test(this.newPassword)) {
        this.$message.error(this.$t("lang.auth.PwdMgrAPI.item0002"));
        return;
      }
      $req
        .post("/athena/api/coreresource/auth/user/updateUserPassword/v1", {
          user: {
            userName: userInfo.userName,
            userId: userInfo.userId,
            password: md5(this.newPassword + userInfo.userName),
            expireDate: "2030-01-01 00:00:00",
            status: userInfo.status,
          },
        })
        .then(res => {
          if (res.code === 0) {
            this.popoverVisible = false;
            this.$success(this.$t(res.msg));
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
.pop-content {
  .g-flex();
  > label {
    font-size: 13px;
  }
}

.pop-btns {
  padding-top: 10px;
  text-align: center;
}
</style>
