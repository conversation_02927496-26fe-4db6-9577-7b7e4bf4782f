import { LINE_CAP, LINE_JOIN } from "pixi.js";
import {lineStyle} from '../../config'
const {ACTIVE_LINE} = lineStyle
export default class Line {
  static render($el,paths = [],{width = lineStyle.width,color = ACTIVE_LINE, alpha = 1} = {}) {
    if(paths.length < 2) return
    const styleOp = {
      width,color,alpha,
      cap: LINE_CAP.ROUND,
      join: LINE_JOIN.ROUND
    }
    $el.lineStyle(styleOp)
    paths.forEach((p,index) => {
      const {x,y} = p
      if(index === 0){
        $el.moveTo(x, y);
      }else{
        $el.lineTo(x, y);
      }
    })
    // $el.closePath()
    return $el
  }
}
