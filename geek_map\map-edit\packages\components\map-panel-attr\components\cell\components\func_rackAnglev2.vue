<template>
  <div class="funcRackAngle">
    <!-- 选点 -->
    <PointSelectV2 v-model="selectPoint" :options="associatedCellCode" isMultiplePointDisable filterable multiple />
    <!-- 功能 -->
    <template v-for="item in selectPoint" :key="item">
      <el-divider border-style="dashed">{{ item }}</el-divider>
      <div class="funcRackAngleV1ElBox">
        <FuncRackAngleV1El :fromData="fromData[item]" @change="(data) => changeFromItemData(item, data)" />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import FuncRackAngleV1El from "./func_rackAngle.vue"
import { computed, ComputedRef, toRefs, ref, Ref, watch } from "vue";
import { useAttrStore } from "@packages/store/attr";
import { useAppStore } from "@packages/store/app";

interface FromDataType {
  [k: string]: { limitObj: string; limitAngles: number[] }[];
}
const props = defineProps<{
  fromData: { [k: string]: any };
  updateValue: Function;
}>();

const attrStore = useAttrStore();
const appStore = useAppStore();

const associatedCellCode = computed(() => {
  const cellCodes = attrStore.findAdjacentByCellCodeDictMap[<string>attrStore.curNodeCellCodeByIndex] || [];
  // const curNode = (<any>attrStore.curNodeDataByIndex);
  // //判断是是否重复
  // const index = cellCodes.findIndex(item => item.value === curNode.cellCode)
  // if(index < 0){
  //   cellCodes.push({
  //     label: curNode.cellCode,
  //     value: curNode.cellCode,
  //     nodeId: curNode.id
  //   });
  // }
  return cellCodes
});

const selectPoint: Ref<string[]> = ref([]);
/**
 * 数据变更
 */
const fromData: Ref<{[k: string]: any}> = ref(getInitFromData());

watch(
  fromData,
  // 每当数据变更
  value => props.updateValue(JSON.stringify(value)),
  { deep: true },
);

// 获取初始数据集合
function getInitFromData() {
  const data: { [k: string]: any } = JSON.parse(props.fromData?.funcD || "{}");
  selectPoint.value = Object.keys(data);
  return data;
}

function changeFromItemData(key: string, itemData: {[k:string]: any}) {
  fromData.value[key] = itemData;
}
</script>

<style scoped lang="scss">
.funcRackAngleV1ElBox {

}
</style>
