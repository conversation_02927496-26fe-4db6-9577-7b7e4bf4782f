import { defineStore } from "pinia";
import { setComponentStore } from "@packages/store/piniaSync";
export interface MapMouseMenuStore {
  /**
   * 是否处于批量编辑功能模式
   */
  isBatchModifyCellFun: boolean;

  /**
   * 是否处于复制模式
   */
  isCopyMode: boolean;
}

export const useMapMouseMenuStore = defineStore({
  id: "menuStore",
  state: (): MapMouseMenuStore => {
    return {
      isBatchModifyCellFun: false,
      isCopyMode: false,
    };
  },
  actions: {
    /**
     * 进入批量编辑功能模式
     */
    batchModifyCellFun() {
      this.isBatchModifyCellFun = true;
    },
    /**
     * 退出批量编辑功能模式
     */
    batchModifyCellFunOut() {
      this.isBatchModifyCellFun = false;
    },
    /**
     * 进入复制模式
     */
    copyMode() {
      this.isCopyMode = true;
    },

    /**
     * 退出复制模式
     */
    copyModeOut() {
      this.isCopyMode = false;
    },

    /**
     * 一键重置菜单的所有模式
     */
    reset() {
      this.batchModifyCellFunOut();
      this.copyModeOut();
    },
  },
});

// 用于组件之间的解耦
setComponentStore("menuStore", useMapMouseMenuStore);
