/* ! <AUTHOR> at 2022/08/27 */
import * as PIX<PERSON> from "pixi.js";
import LayerCellBlock from "./cell-block";
import LayerCellElevator from "./cell-elevator";
import LayerCellStatus from "./cell-status";
import LayerCellLocation from "./cell-location";
import LayerCellLoad from "./cell-load";
import LayerCellUnload from "./cell-unload";
import LayerCellFeature from "./cell-feature";
import LayerCellText from "./cell-text";

class LayerCell implements MRender.Layer {
  floorId: floorId;
  layerCellStatus: LayerCellStatus = new LayerCellStatus();
  layerCellBlock: LayerCellBlock = new LayerCellBlock();
  layerCellElevator: LayerCellElevator = new LayerCellElevator();
  layerCellLocation: LayerCellLocation = new LayerCellLocation();
  layerCellLoad: LayerCellLoad = new LayerCellLoad();
  layerCellUnload: LayerCellUnload = new LayerCellUnload();
  layerCellFeature: LayerCellFeature = new LayerCellFeature();
  layerCellText: LayerCellText = new LayerCellText();

  private mapCore: MRender.MainCore;
  private floor: any;
  private container: PIXI.Container;
  private meshList: Array<any> = [];
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const floorId = this.floorId;
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container(); // cell container
    container.name = "cell";
    container.interactiveChildren = false;
    container.zIndex = utils.getLayerZIndex("cell");
    this.container = container;

    this.layerCellBlock.init(mapCore, floorId); // block 点
    this.layerCellElevator.init(mapCore, floorId); // 电梯点
    this.layerCellStatus.init(mapCore, floorId); // cellFlag 状态
    this.layerCellLocation.init(mapCore, floorId); // cell中心点
    this.layerCellLoad.init(mapCore, floorId); // load
    this.layerCellUnload.init(mapCore, floorId); // unload
    this.layerCellFeature.init(mapCore, floorId); // 功能点
    this.layerCellText.init(mapCore, floorId); // text文本

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(
      container,
      this.layerCellBlock.getContainer(),
      this.layerCellElevator.getContainer(),
      this.layerCellStatus.getContainer(),
      this.layerCellLocation.getContainer(),
      this.layerCellUnload.getContainer(),
      this.layerCellLoad.getContainer(),
      this.layerCellFeature.getContainer(),
      this.layerCellText.getContainer(),
    );
  }

  render(cells: Array<cellData>): void {
    const mapCore = this.mapCore;
    const utils = this.mapCore.utils;
    const shader = utils.getShader("rect");

    const fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    for (let i = 0, len = Math.ceil(cells.length / fragment); i < len; i++) {
      const arr = cells.slice(i * fragment, i * fragment + fragment);
      const meshData = this.resolveCells(arr);
      if (!meshData) continue;

      const { meshKey, geometries } = meshData;
      let mesh = utils.createMesh(geometries, shader);
      mesh.name = meshKey;
      mesh.mapType = "cell";
      mesh.interactive = mesh.buttonMode = true;

      this.meshList.push(mesh);
      this.container.addChild(mesh);
    }

    this.layerCellBlock.render();
    this.layerCellElevator.render();
    this.layerCellStatus.render();
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];

    this.mapCore.meshData.cell.delByFloorId(this.floorId);
    this.layerCellBlock.repaint();
    this.layerCellElevator.repaint();
    this.layerCellLocation.repaint();
    this.layerCellLoad.repaint();
    this.layerCellUnload.repaint();
    this.layerCellStatus.repaint();
    this.layerCellFeature.repaint();
    this.layerCellText.repaint();
  }

  destroy(): void {
    this.repaint();
    this.layerCellBlock.destroy();
    this.layerCellElevator.destroy();
    this.layerCellLocation.destroy();
    this.layerCellLoad.destroy();
    this.layerCellUnload.destroy();
    this.layerCellStatus.destroy();
    this.layerCellFeature.destroy();
    this.layerCellText.destroy();
    this.container.destroy({ children: true });
    this.layerCellBlock = null;
    this.layerCellElevator = null;
    this.layerCellLocation = null;
    this.layerCellLoad = null;
    this.layerCellUnload = null;
    this.layerCellStatus = null;
    this.layerCellFeature = null;
    this.layerCellText = null;
    this.container = null;

    this.mapCore = null;
    this.floor = null;
    this.meshList = null;
    this.floorId = undefined;
  }

  private resolveCells(arr: Array<cellData>): any {
    const _this = this;
    const mapCore = _this.mapCore,
      utils = mapCore.utils,
      meshData = mapCore.meshData,
      mapData = mapCore.mapData;

    let data = [];
    let geometries = [];
    let meshKey, item, options, code;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      options = utils.formatCell(item);
      code = options["code"];
      if (options["cellType"] === "BLOCKED_CELL") {
        _this.layerCellBlock.drawGeometry(options);
        continue;
      }

      if (!meshKey) meshKey = code;
      if (options["cellType"] === "ELEVATOR_CELL") {
        _this.layerCellElevator.drawGeometry(options);
      }

      if (options["cellFlag"] === "LOCKED") {
        _this.layerCellStatus.drawLocked(options);
      }
      if (options["cellFlag"] === "STOPPED") {
        _this.layerCellStatus.drawStopped(options);
      }

      const geometry = utils.drawGeometry("rect", options["position"], options["shaderColor"]);
      data.push(options);
      geometries.push(geometry);
      mapData.cell.setData(code, options);
    }

    if (!meshKey) return null;

    meshData.cell.setData(meshKey, data);
    return { meshKey, geometries };
  }

  private resolveCellsData(arr: Array<mCellData>): any {
    const _this = this;
    const mapCore = _this.mapCore,
      utils = mapCore.utils,
      meshData = mapCore.meshData,
      mapData = mapCore.mapData;

    let data = [];
    let geometries = [];
    let meshKey, options, code, vColor;
    for (let i = 0, len = arr.length; i < len; i++) {
      options = arr[i];
      code = options["code"];
      if (options["cellType"] === "BLOCKED_CELL") {
        _this.layerCellBlock.drawGeometry(options);
        continue;
      }

      if (!meshKey) meshKey = code;
      if (options["cellType"] === "ELEVATOR_CELL") {
        _this.layerCellElevator.drawGeometry(options);
      }

      if (options["cellFlag"] === "LOCKED") {
        _this.layerCellStatus.drawLocked(options);
      }
      if (options["cellFlag"] === "STOPPED") {
        _this.layerCellStatus.drawStopped(options);
      }

      const geometry = utils.drawGeometry("rect", options["position"], options["shaderColor"]);
      data.push(options);
      geometries.push(geometry);
      mapData.cell.setData(code, options);
    }

    if (!meshKey) return null;

    meshData.cell.setData(meshKey, data);
    return { meshKey, geometries };
  }
}
export default LayerCell;
