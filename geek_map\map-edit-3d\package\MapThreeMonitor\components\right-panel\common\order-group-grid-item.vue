<script>
export default {
  name: "OrderGroupGridItem",
  render(h) {
    const colspan = this.label ? 1 : 2;
    let valueText = this.value;

    const type = Object.prototype.toString.call(valueText);
    if (type === "[object Object]" || type === "[object Array]") {
      valueText = JSON.stringify(valueText);
    } else if (type === "[object Number]") {
      valueText = valueText.toString();
    }

    return h(
      "tr", // 标签名称
      [
        colspan === 1 &&
          h(
            "td",
            {
              class: {
                "item-label": true,
              },
            },
            this.label,
          ),
        valueText === "" &&
          h(
            "td",
            {
              class: {
                "item-value": true,
              },
              attrs: {
                colspan: colspan,
              },
            },
            this.$slots.default,
          ),

        valueText !== "" &&
          h(
            "td",
            {
              class: {
                "item-value": true,
              },
              attrs: {
                colspan: colspan,
              },
            },
            valueText,
          ),
      ],
    );
  },
  props: {
    label: {
      type: String,
      default: "",
    },
    value: {
      // eslint-disable-next-line vue/require-prop-type-constructor
      type: String | Number | Object | Array,
      default: "",
    },
  },
};
</script>
<style lang="scss" scoped>
tr {
  border-bottom: 1px solid #eee;
  line-height: 1.2;

  > td {
    padding: 6px;
    vertical-align: middle;
  }
  &:nth-child(2n) {
    background: #f6f6f6;
  }
}

.item-label {
  color: #606266;
  text-align: right;
  font-weight: 700;
  font-size: 13px;
  user-select: text !important;
  width: 86px;
  border-right: 1px solid #eee;
  vertical-align: middle;
}

.item-value {
  //width: 100%;
  user-select: text !important;

  ::v-deep .el-tag {
    font-size: 14px;
    letter-spacing: 0.5px;
    word-break: break-all;
    height: auto;
    white-space: unset;
  }

  > input {
    width: 100%;
  }

  > button.submit {
    float: right;
  }
}
</style>
