<template>
  <div>
    <el-alert title="非立即生效的参数，需重启后系统生效" type="warning" show-icon :closable="false">
    </el-alert>
    <el-button type="text" class="checkBtn" @click="checkOn">{{
      $t("lang.rms.fed.buttonView")
    }}</el-button>
  </div>
</template>

<script>
export default {
  name: "WorkspaceJsonRestartAlert",

  data() {
    return {};
  },

  mounted() {},

  methods: {
    checkOn() {},
  },
};
</script>

<style lang="less" scoped>
:deep(.el-alert__title) {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #313234;
}
:deep(.el-alert) {
  background: #fffbe6;
}
.checkBtn {
  top: 56px;
  left: 290px;
  position: absolute;
}
</style>
