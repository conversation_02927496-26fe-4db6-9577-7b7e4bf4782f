<template>
  <el-card shadow="never" class="component-operate-detail">
    <div slot="header" class="header">
      <span>{{ detailTitle + $t("lang.rms.fed.textInformationOverview") }}</span>
    </div>
    <order-group-grid>
      <grid-item :label="$t('lang.rms.fed.robotId')" :value="detailData.id || '--'" />
      <grid-item :label="$t('lang.rms.fed.type')" :value="detailData.robotType || '--'" />
      <grid-item
        :label="$t('lang.rms.fed.textNodeCode')"
        :value="detailData.locationCell || '--'"
      />
      <grid-item
        :label="$t('lang.rms.web.monitor.robot.location')"
        :value="detailData.location || '--'"
      />
      <grid-item
        :label="$t('lang.rms.web.monitor.robot.robotPathMode')"
        :value="detailData.robotPathMode || '--'"
      />
      <grid-item :label="$t('lang.rms.fed.confirmState')" :value="confirmState" />
      <grid-item
        :label="$t('lang.rms.fed.errorState')"
        :value="detailData.hasOwnProperty('errorCode') ? detailData.errorCode : '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.taskId')"
        :value="detailData.taskId ? detailData.taskId.toString() : '--'"
      />
      <grid-item :label="$t('lang.rms.fed.taskType')" :value="detailData.taskType || '--'" />
      <grid-item
        :label="$t('lang.rms.fed.shelfCoding')"
        :value="detailData.onloadShelfCode || '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.power')"
        :value="detailData.hasOwnProperty('powerPercent') ? `${detailData.powerPercent}%` : '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.obstacleCount')"
        v-show="detailData.robotType === 'P40'"
        :value="detailData.hasOwnProperty('obstacleCount') ? detailData.obstacleCount : '--'"
      />
      <grid-item
        :label="$t('lang.rms.fed.boxHoldingTaskId')"
        :value="detailData.hasOwnProperty('jobIds') ? detailData.jobIds : '--'"
      />
    </order-group-grid>
  </el-card>
</template>

<script>
import OrderGroupGrid from "../common/order-group-grid.vue";
import GridItem from "../common/order-group-grid-item.vue";

export default {
  name: "RobotDetail",
  components: { OrderGroupGrid, GridItem },
  props: {
    detailData: {
      type: Object,
      require: true,
    },
    detailTitle: {
      type: String,
      require: true,
    },
  },
  computed: {
    confirmState() {
      let value = this.detailData.hasOwnProperty("posConfirmed");
      if (!value) return "--";
      else {
        if (value) {
          return this.$t("lang.rms.fed.confirmed");
        } else {
          return this.$t("lang.rms.fed.unconfirmed");
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.component-operate-detail {
  background: #fbfbfb;

  ::v-deep tr > td {
    padding-bottom: 0;
  }
}
</style>
