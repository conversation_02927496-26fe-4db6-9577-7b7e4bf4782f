/* ! <AUTHOR> at 2022/08/02 */
const { resolve } = require("path");
const webpack = require("webpack");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const ForkTsCheckerWebpackPlugin = require("fork-ts-checker-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const CssMinimizerPlugin = require("css-minimizer-webpack-plugin"); // 压缩css
const TerserPlugin = require("terser-webpack-plugin");
const { StaticPath, PORT, API_URL } = require("./config");

const isPro = process.env.CONF_ENV === "pro";
const isBuild = process.env.CONF_ENV !== "serve";
const buildPath = process.env.CONF_ENV === "serve" ? resolve("./dist") : resolve(StaticPath);
module.exports = {
  mode: isBuild ? "production" : "development",
  devtool: isBuild ? false : "inline-source-map",
  entry: {
    "monitor2d.min": ["./src/index.ts"],
    app: ["./packages/html/index.tsx"],
  },
  output: {
    filename: `js/[name].js?${Date.now()}`,
    path: buildPath,
    publicPath: isBuild ? "." : "/map-fe-2d",
    environment: {
      arrowFunction: false,
      const: false,
    },
  },
  target: "web", // webpack5需要添加此属性实现HRM
  devServer: {
    static: {
      directory: buildPath,
    },
    client: {
      logging: "warn",
    },
    port: PORT,
    hot: true,
    open: ["/map-fe-2d"],
    proxy: {
      "/athena": {
        target: API_URL,
        changeOrigin: true,
        secure: false,
        onProxyRes(proxyRes, req, res) {
          let url = proxyRes.headers.location;
          if (proxyRes.statusCode === 302 && url.indexOf(API_URL) !== -1) {
            console.error("接口出现权限问题了，去主架构获取一下sessionId吧！！！");
          }
        },
      },
    },
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js", ".jsx", ".json"],
  },
  module: {
    rules: [
      {
        test: /\.(tsx?)$/,
        use: [
          "babel-loader",
          {
            loader: "ts-loader",
            options: {
              // 关闭类型检查，即只进行转译
              // 类型检查交给 fork-ts-checker-webpack-plugin 在别的的线程中做
              // transpileOnly: true,
              // 如果设置了 happyPackMode 为 true 会隐式的设置 transpileOnly: true
              happyPackMode: true,
            },
          },
        ],
        exclude: /node_modules/,
      },
      { test: /\.(jsx?)$/, loader: "babel-loader", exclude: /node_modules/ },
      {
        test: /\.css$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: { publicPath: "../" },
          },
          "css-loader",
          "postcss-loader",
        ],
      },
      {
        test: /\.less$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: { publicPath: "../" },
          },
          "css-loader",
          "postcss-loader",
          "less-loader",
          {
            loader: "sass-resources-loader",
            options: {
              resources: [resolve("./packages/less/mixin/mixins.less")],
            },
          },
        ],
      },
      {
        test: /\.scss$/,
        use: [
          {
            loader: MiniCssExtractPlugin.loader,
            options: { publicPath: "../" },
          },
          "css-loader",
          "postcss-loader",
          "sass-loader",
        ],
      },
      {
        test: /\.(png|jpg|gif|svg)$/,
        type: "asset",
        parser: {
          dataUrlCondition: {
            maxSize: 30 * 1024, // 4kb
          },
        },
        generator: {
          filename: "imgs/[name].[hash:7][ext]",
        },
      },
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/,
        type: "asset/resource",
        generator: {
          filename: "fonts/[name].[hash:7][ext]",
        },
      },
      {
        test: /\.(mp3)$/,
        type: "asset/resource",
        generator: {
          filename: "imgs/[name].[hash:7][ext]",
          publicPath: isBuild ? "../monitor2D/" : "/map-fe-2d",
        },
      },
    ],
  },
  plugins: [
    new CleanWebpackPlugin(),
    new webpack.DefinePlugin({
      __rms_env_conf: { isPro: isBuild, API_URL: JSON.stringify(API_URL) },
    }),
    // fork 一个进程进行检查
    new ForkTsCheckerWebpackPlugin({ async: !isPro }),
    new MiniCssExtractPlugin({
      filename: `css/[name].[chunkhash:7].css`,
      chunkFilename: `css/[name].[chunkhash:7].chunk.css`,
      ignoreOrder: false,
    }),
    new HtmlWebpackPlugin({
      title: "monitor2D",
      minify: {
        removeComments: false, // 移除HTML中的注释
        collapseWhitespace: false, // 删除空白符与换行符
        minifyCSS: true, // 压缩内联css
      },
      template: "./packages/html/index.html",
      chunks: ["vendor", "vendor.ui", "monitor2d", "app"],
    }),
  ].concat(isPro ? [] : new webpack.ProgressPlugin()),
  optimization: {
    splitChunks: {
      chunks: "all", // 'all'对同步和异步引入模块都进行代码分割;'async: 只对异步引入模块进行代码分割;'initial': 只对同步代码进行代码分割
      minSize: 30000, // 代码分割模块的最小大小要求，不满足不会进行分割，单位byte
      minChunks: 1, // 最小被引用次数，只有在模块上述条件并且至少被引用过一次才会进行分割
      maxAsyncRequests: 30, // 最大的异步按需加载次数
      maxInitialRequests: 30, // 初始话并行请求不得超过30个
      enforceSizeThreshold: 50000,
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/](react|react-dom|axios|qs|i18next|react-i18next)[\\/]/,
          name: "vendor",
          chunks: "all",
          priority: 10,
        },
        vendor2: {
          test: /[\\/]node_modules[\\/]antd[\\/]/,
          name: "vendor.ui",
          chunks: "all",
          priority: 9,
        },
        vendor3: {
          test: /[\\/]src[\\/]/,
          name: "monitor2d",
          chunks: "all",
          priority: 8,
        },
      },
    },
    minimize: isPro ? true : false,
    minimizer: [
      new CssMinimizerPlugin(),
      new TerserPlugin({
        test: /\.js(\?.*)?$/i,
        extractComments: false,
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true, // 干掉那些debugger;
          },
          mangle: true,
          output: {
            comments: false,
          },
        },
      }),
    ],
  },
};
