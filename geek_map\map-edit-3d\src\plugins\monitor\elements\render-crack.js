// PPP场景货箱
import * as THREE from "three";
import { useRobotAnimal } from "../../../utils/model-func";
import globalConfig from "../../../config";
class RenderCRack {
  constructor(options) {
    this.isHeatMode = false;
    this.monitor = options.monitor;
    this.dispatch = options.dispatch || {
      renderCRack() {},
    };
    this.robotUUid = {};
    this.modelCache = {};
    this.shelfModelObject3D = {};
    this.cracks = [];
    this.config = {
      space: v => v * 0.9,
      latticeSpace: v => v * 0.05,
      layerColor: globalConfig.THEME.CRACK_LAYER,
      latticeColor: globalConfig.THEME.CRACK_LATTICE,
      boxFColor: globalConfig.THEME.CRACK_BOX,
      boxBColor: globalConfig.THEME.CRACK_BACK_BOX,
      layerH: 0.02,
      latticeH: 0.04,
      boxH: 0.3,
      h: 0.6,
    };
  }
  destory() {
    this.robotUUid = {};
    this.modelCache = {};
    this.shelfModelObject3D = {};
    this.cracks = [];
  }
  create(cracks) {
    this.cracks = cracks;
    this.__renderCRacks(cracks);
  }
  update(cracks) {
    this.__updateCRacks(cracks);
  }
  delete(cracks) {
    const { Map3d } = this.monitor;
    for (let i = 0, crack; (crack = cracks[i++]); ) {
      const model = this.shelfModelObject3D[crack.shelfCode];
      if (!model) continue;
      Map3d.scene.remove(model);
      delete this.shelfModelObject3D[crack.shelfCode];
      const index = this.cracks.findIndex(a => a.shelfCode === crack.shelfCode);
      this.cracks.splice(index, 1);
    }
  }
  __updateCRacks(cracks) {
    const { Store } = this.monitor;
    const robotData = Store.getModelData("ROBOT");
    this.robotUUid = robotData.reduce((pre, cur) => Object.assign(pre, { [cur.id]: cur.uuid }), {});
    let nCRacks = cracks,
      oCRacks = this.cracks;
    for (let i = 0, ov; (ov = oCRacks[i++]); ) {
      const index = nCRacks.findIndex(a => a.shelfCode === ov.shelfCode);
      if (!~index) continue;
      const nv = nCRacks[index];
      this.__CRackMove(ov, nv);
      Object.assign(ov, nv);
      nCRacks.splice(index, 1);
    }
    // 处理新增PPP货架；
    if (nCRacks.length) {
      this.cracks.concat(nCRacks);
      this.__renderCRacks(nCRacks);
    }
  }
  __renderCRacks(cracks) {
    const { Map3d } = this.monitor;
    for (let i = 0, a; (a = cracks[i++]); ) {
      const { layerCount, latticeNum, layerLattices, length, width, location } = a;
      const columnCount = latticeNum / layerCount / 2;
      const count = layerCount + latticeNum * 2;
      const model = this.__createCRack(count, layerCount, columnCount, length, width);
      const { FRONT, BACK } = layerLattices;
      this.__renderBoxVisible(model, FRONT, "F");
      this.__renderBoxVisible(model, BACK, "B");
      model.children[0].instanceMatrix.needsUpdate = true;
      model.position.set(location.x, 0, -location.y);
      model.rotation.y = a.radAngle * (Math.PI / 180);
      Map3d.scene.add(model);
      this.shelfModelObject3D[a.shelfCode] = model;
    }
  }
  __renderBoxVisible(model, data, face) {
    const matrix = new THREE.Matrix4();
    const seq = model.userData.seq;
    for (let row in data) {
      const { displayLatticeList } = data[row];
      displayLatticeList.forEach((l, column) => {
        const [startIndex, el, wh, boxH, x, y, z] = seq[`${row}-${face}-${column}-box`];
        let boxItem = l["relateBox"];
        if (!boxItem || !boxItem.boxCode) matrix.makeScale(0, 0, 0);
        else matrix.makeScale(el, wh, boxH);
        matrix.setPosition(x, y, z);
        model.children[0].setMatrixAt(startIndex, matrix);
      });
    }
  }
  __CRackMove(ov, nv) {
    const { Map3d } = this.monitor;
    const mesh = this.shelfModelObject3D[ov.shelfCode];
    // 解除绑定；
    if (!nv.robotId || !~nv.robotId) {
      mesh.removeFromParent();
      mesh.rotation.y = -nv.radAngle * (Math.PI / 180);
      mesh.position.set(nv.location.x, 0, -nv.location.y);
      Map3d.scene.add(mesh);
      return;
    }
    const robot = Map3d.modelInstances[this.robotUUid[nv.robotId]].model;
    if (!robot) return;
    mesh.userData.curLocation = [0, 0, ov.radAngle * (Math.PI / 180)];
    useRobotAnimal({ mesh, location: [0, 0, nv.radAngle * (Math.PI / 180)] });
    if (mesh.parent === robot) return;
    mesh.position.set(0, 0, 0);
    // mesh.rotation.y = 0;
    robot.add(mesh);
  }
  // 描述几行几列（同一个货架内部不太可能存在异形， 遇到再改）
  __createCRack(count, layerCount, columCount, length, width) {
    const key = `${count}-${layerCount}-${columCount}-${length}-${width}`;
    const model = this.modelCache[key] || null;
    if (model) return model.clone();
    const group = new THREE.Group();
    group.name = "Model-Group-CRack";
    const matrix = new THREE.Matrix4();
    const color = new THREE.Color();
    const geometry = new THREE.BoxGeometry(1, 1, 1);
    const material = new THREE.MeshStandardMaterial({
      color: 0xffffff,
      transparent: true,
    });
    const { latticeH, boxH, layerH, h, layerColor, latticeColor, boxFColor, boxBColor } =
      this.config;
    const mesh = new THREE.InstancedMesh(geometry, material, count);
    let l = this.config.space(length),
      w = this.config.space(width),
      ls = this.config.latticeSpace(length),
      ws = this.config.latticeSpace(width);
    const seq = {};
    let startIndex = -1;
    for (let i = 0; i < layerCount; i++) {
      const baseZ = h * (i + 1);
      const latticeZ = baseZ + layerH + latticeH / 2;
      const boxZ = baseZ + layerH + latticeH + boxH / 2;
      const wh = (w - ws) / 2;
      const lh = l / 2;
      const el = (l - (columCount - 1) * ls) / 3;

      // 画货架
      ++startIndex;
      matrix.makeScale(length, width, layerH);
      matrix.setPosition(0, 0, baseZ);
      mesh.setMatrixAt(startIndex, matrix);
      mesh.setColorAt(startIndex, color.set(layerColor));

      for (let j = 0; j < columCount; j++) {
        // 画货位
        const x = j * (el + ls) - lh + el / 2;
        const fy = wh - (wh - ws) / 2;
        const by = -wh + (wh - ws) / 2;

        ++startIndex;
        matrix.makeScale(el, wh, latticeH);
        matrix.setPosition(x, fy, latticeZ);
        mesh.setMatrixAt(startIndex, matrix);
        mesh.setColorAt(startIndex, color.set(latticeColor));
        seq[`${i}-F-${j}`] = [startIndex, el, wh, latticeH, x, fy, latticeZ];

        ++startIndex;
        matrix.makeScale(el, wh, boxH);
        matrix.setPosition(x, fy, boxZ);
        mesh.setMatrixAt(startIndex, matrix);
        mesh.setColorAt(startIndex, color.set(boxFColor));
        seq[`${i}-F-${j}-box`] = [startIndex, el, wh, boxH, x, fy, boxZ];

        ++startIndex;
        matrix.makeScale(el, wh, latticeH);
        matrix.setPosition(x, by, latticeZ);
        mesh.setMatrixAt(startIndex, matrix);
        mesh.setColorAt(startIndex, color.set(latticeColor));
        seq[`${i}-B-${j}`] = [startIndex, el, wh, latticeH, x, by, latticeZ];

        ++startIndex;
        matrix.makeScale(el, wh, boxH);
        matrix.setPosition(x, by, boxZ);
        mesh.setMatrixAt(startIndex, matrix);
        mesh.setColorAt(startIndex, color.set(boxBColor));
        seq[`${i}-B-${j}-box`] = [startIndex, el, wh, boxH, x, by, boxZ];
      }
    }
    mesh.rotation.x = -Math.PI / 2;
    mesh.rotation.z = -Math.PI;
    group.add(mesh);
    this.modelCache[key] = group;
    group.userData.seq = seq;
    return group;
  }
}

export default RenderCRack;
