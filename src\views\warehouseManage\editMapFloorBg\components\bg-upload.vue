<template>
  <div class="upload-main">
    <div v-if="!openBgBtn" class="upload-btn">
      <el-button type="primary" @click="openDialog(true)">{{
        $t("lang.rms.fed.uploadResource")
      }}</el-button>
      <div class="el-upload__tip">{{ $t("lang.rms.fed.notExistBackground") }}</div>
    </div>
    <el-dialog
      :title="
        addData
          ? $t('lang.rms.fed.uploadBackgroundImage')
          : $t('lang.rms.fed.updateBackgroundImage')
      "
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        ref="ruleForm"
        v-loading="loading"
        element-loading-spinner="el-icon-loading"
        element-loading-text="Loading"
        :label-position="'left'"
        :model="ruleForm"
        :rules="rules"
        label-width="180px"
        class="demo-ruleForm"
      >
        <el-form-item
          v-if="!isUpdateBgImage"
          :label="$t('lang.rms.fed.robotType') + ':'"
          prop="robotType"
        >
          <el-select
            v-model="ruleForm.robotType"
            multiple
            filterable
            :disabled="!addData"
            :no-data-text="$t('lang.rms.fed.notExistData')"
            :placeholder="$t('lang.rms.fed.choose')"
          >
            <el-option v-for="item in robotTypeList" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
        <!-- 禁止了 是否设置为默认背景图 -->
        <!-- <el-form-item :label="$t('lang.rms.fed.isDefaultBackgroundImage')+':'" prop="display">
          <el-checkbox v-model="ruleForm.display">{{ $t('lang.rms.api.result.edit.map.yes') }}</el-checkbox>
        </el-form-item> -->
        <el-form-item
          v-if="!isUpdateBgImage"
          :label="$t('lang.rms.fed.resourceType') + ':'"
          prop="resourceType"
        >
          <el-radio-group v-model="ruleForm.resourceType" @change="changeRadio">
            <el-radio :label="0">{{ $t("lang.rms.fed.pictureResource") }}</el-radio>
            <el-radio :label="1">{{ $t("lang.rms.fed.standAloneResourcePackage") }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="ruleForm.resourceType === 0">
          <el-upload
            ref="uploadBg"
            drag
            class="upload-bg"
            accept=".png"
            action=""
            :on-remove="handleRemove"
            :file-list="fileList"
            :http-request="handleUploadBg"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              {{ $t("lang.rms.api.result.warehouse.dragFileHere") }}
            </div>
            <div slot="tip" class="el-upload__tip">{{ $t("lang.rms.fed.imageUploadLimit") }}</div>
          </el-upload>
          <el-form-item :label="$t('lang.rms.fed.offsetByXorY', ['X']) + ':'" prop="x">
            <el-input
              v-model.trim="ruleForm.x"
              :aria-placeholder="$t('lang.rms.web.placeHolder')"
            />
          </el-form-item>
          <el-form-item :label="$t('lang.rms.fed.offsetByXorY', ['Y']) + ':'" prop="y">
            <el-input
              v-model.trim="ruleForm.y"
              :aria-placeholder="$t('lang.rms.web.placeHolder')"
            />
          </el-form-item>
          <el-form-item :label="$t('lang.rms.fed.offsetAngle') + ':'" prop="yaw">
            <el-input
              v-model.trim="ruleForm.yaw"
              :aria-placeholder="$t('lang.rms.web.placeHolder')"
            />
          </el-form-item>
          <el-form-item :label="$t('lang.rms.fed.resolution') + ':'" prop="resolution">
            <el-input
              v-model.trim="ruleForm.resolution"
              :aria-placeholder="$t('lang.rms.web.placeHolder')"
            />
          </el-form-item>
        </div>

        <div v-else>
          <el-upload
            accept=".zip"
            class="upload-bg"
            drag
            action=""
            multiple
            :on-remove="handleRemoveResource"
            :limit="1"
            :file-list="fileResourceList"
            :http-request="handleUploadBgResource"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              {{ $t("lang.rms.api.result.warehouse.dragFileHere") }}
            </div>
            <div slot="tip" class="el-upload__tip">{{ $t("lang.rms.fed.uploadZipFileLimit") }}</div>
          </el-upload>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button type="primary" :disabled="loading" @click="handleSave">{{
          $t("lang.rms.fed.save")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import dealImage from "../../../../libs/dealImage";
// import floorUtils from '../floorBg/utils'
export default {
  name: "BgUpload",
  props: {
    openBgBtn: {
      type: Boolean,
      default: false,
    },
    queryData: {
      type: Object,
      require: true,
    },
    mapData: {
      type: Object,
      require: true,
    },
    addData: {
      type: Boolean,
      default: false,
    },
    isUpdateBgImage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fileList: [],
      fileResourceList: [],
      dialogVisible: false,
      robotTypeList: [],
      ruleForm: {
        display: true,
        robotType: [],
        resourceType: 0,
        x: 0,
        y: 0,
        yaw: 0,
        resolution: 0.02,
      },
      rules: {
        robotType: [{ required: true, message: "lang.rms.fed.inputRobotType", trigger: "blur" }],
        x: [
          {
            required: true,
            message: this.$t("lang.rms.fed.inputBackgroundOffsetX"),
            trigger: "blur",
          },
          {
            pattern: /^(\-|\+)?\d+(\.\d+)?$/,
            message: this.$t("lang.rms.fed.backgroundImageOffsetIsNumber"),
          },
        ],
        y: [
          {
            required: true,
            message: this.$t("lang.rms.fed.inputBackgroundOffsetY"),
            trigger: "blur",
          },
          {
            pattern: /^(\-|\+)?\d+(\.\d+)?$/,
            message: this.$t("lang.rms.fed.backgroundImageOffsetIsNumber"),
          },
        ],
        yaw: [
          {
            required: true,
            message: this.$t("lang.rms.fed.inputBackgroundImageYaw"),
            trigger: "blur",
          },
          {
            pattern: /^(\-|\+)?\d+(\.\d+)?$/,
            message: this.$t("lang.rms.fed.backgrounfImageYawIsNumber"),
          },
          {
            pattern:
              /^([1-9]|([1-9][0-9])|([1-2][0-9][0-9])|([3][0-5][0-9])|([0]{1}))$|^[3][6][0]$/,
            message: this.$t("底图旋转角度范围0-360"),
          },
        ],
        resolution: [
          { required: true, message: this.$t("lang.rms.fed.inputResolution"), trigger: "blur" },
          { pattern: /^0.1$|0.[0][\d]+|0$/, message: this.$t("lang.rms.fed.resolutionRange") },
        ],
      },
      file: undefined,
      loading: false,
    };
  },
  watch: {
    openBgBtn(data) {
      this.handleRemove();
      this.handleRemoveResource();
      if (data) this.openDialog();
    },
  },
  methods: {
    changeRadio() {
      this.handleRemove();
      this.handleRemoveResource();
    },
    getunSupportRobot() {
      const { mapId, floorId } = this.queryData;
      const params = "/" + mapId + "/" + floorId;
      $req.get("/athena/map/draw/unSupportRobotTypes" + params).then(res => {
        if (res.code === 0) {
          this.robotTypeList = res.data;
        }
      });
    },
    getRobotTypesaAll() {
      $req.get("/athena/robotType/allName").then(res => {
        if (res.code === 0) {
          this.robotTypeList = res.data;
        }
      });
    },
    openDialog(add) {
      if (add) this.$emit("openDialog");
      if (this.mapData && !this.addData) {
        this.fileList = [{ name: this.$t("lang.rms.fed.existingBackgroundImage"), url: "" }];
        if (!this.addData) this.ruleForm.id = this.mapData.backgroundId;
        const { robotType } = this.mapData;
        const robotTypeList = robotType ? robotType.split(",") : [];
        this.ruleForm = {
          display: this.mapData.display,
          robotType: robotTypeList,
          resourceType: this.mapData.resourceType,
          x: this.mapData.offsetX,
          y: this.mapData.offsetY,
          yaw: this.mapData.yaw,
          resolution: this.mapData.resolution,
          imageData: this.mapData.imageData,
          compressData: this.mapData.compressData,
          height: this.mapData.height,
          width: this.mapData.width,
        };
      } else {
        this.ruleForm = {
          display: true,
          robotType: [],
          resourceType: 0,
          x: 0,
          y: 0,
          yaw: 0,
          resolution: 0.02,
        };
      }
      this.dialogVisible = true;
      if (this.addData) {
        this.getunSupportRobot();
      } else {
        this.getRobotTypesaAll();
      }
    },
    handleRemove(file, fileList) {
      this.fileList = [];
      this.ruleForm.imageData = null;
      this.ruleForm.compressData = null;
    },
    handleUploadBg(params) {
      this.fileList = [params.file];
      this.loading = true;
      const fileName = params.file.name.substring(params.file.name.lastIndexOf(".") + 1);
      if (fileName !== "png" || params.file.size > 50 * 1024 * 1024) {
        this.$message({
          message: this.$t("lang.rms.fed.importBackgroundPNG"),
          type: "error",
        });
        this.handleRemove();
        return false;
      }
      this.getBase64(params.file).then(resBase64 => {
        this.getImg(resBase64);
      });
    },
    getImg(resBase64) {
      const printing = (base64, newImage) => {
        // 获取压缩后的base64大小
        this.ruleForm.width = newImage.width;
        this.ruleForm.height = newImage.height;
        this.ruleForm.compressData = base64;
        this.ruleForm.imageData = resBase64;
        this.loading = false;
      };
      dealImage(resBase64, printing);
    },
    getBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        let fileResult = "";
        reader.readAsDataURL(file);
        // 开始转
        reader.onload = function () {
          fileResult = reader.result;
        };
        // 转 失败
        reader.onerror = function (error) {
          reject(error);
        };
        // 转 结束  咱就 resolve 出去
        reader.onloadend = function () {
          resolve(fileResult);
        };
      });
    },
    importZip(formData) {
      $req
        .post("/athena/map/draw/importGridMapReource", formData)
        .then(res => {
          this.getImg(res.data.imageBase64);
        })
        .catch(e => {
          console.log(e);
        });
    },
    handleSuccess() {
      this.$refs.uploadBg.clearFiles();
    },
    beforeDestroy() {
      this.fileList = [];
    },
    handleClose() {
      this.$emit("closeBgBtn", false);
      this.dialogVisible = false;
    },
    handleSave() {
      const { mapId, floorId } = this.queryData;
      this.ruleForm.mapId = mapId;
      this.ruleForm.floorId = floorId;
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          this.ruleForm.resourceType === 0 ? this.savePng() : this.saveZip();
        } else {
          return false;
        }
      });
    },
    savePng() {
      if (this.fileList.length <= 0) return false;
      const option = this.ruleForm;
      if (this.isUpdateBgImage) {
        option.robotType = "";
        option.display = true;
      } else {
        const robotType = this.ruleForm.robotType.join(",");
        option.robotType = robotType;
      }
      $req
        .post("/athena/map/draw/importBackgroundImage", { ...option })
        .then(res => {
          this.$emit("exportBgShow", option.robotType);
          this.dialogVisible = false;
          this.handleRemove();
        })
        .catch(e => {
          console.log(e);
        });
    },
    saveZip() {
      if (this.fileResourceList.length <= 0 && this.addData) return false;
      if (!this.file) this.file = new FormData();
      this.file.append("display", this.ruleForm.display);
      this.file.append("mapId", this.ruleForm.mapId);
      this.file.append("floorId", this.ruleForm.floorId);
      this.file.append("robotType", this.ruleForm.robotType);
      this.file.append("width", this.ruleForm.width);
      this.file.append("height", this.ruleForm.height);
      this.file.append("compressData", this.ruleForm.compressData);
      this.file.append("resourceType", this.ruleForm.resourceType);
      if (!this.addData) this.file.append("id", this.mapData.backgroundId);
      $req
        .put("/athena/map/draw/backgroundCompress", this.file)
        .then(res => {
          this.$emit("exportBgShow", this.ruleForm.robotType);
          this.dialogVisible = false;
          this.handleRemoveResource();
        })
        .catch(e => {
          console.log(e);
        });
    },
    handleRemoveResource() {
      this.file = null;
      this.fileResourceList = [];
    },
    handleUploadBgResource(params) {
      this.fileResourceList = [params.file];
      this.loading = true;
      const fileName = params.file.name.substring(params.file.name.lastIndexOf(".") + 1);
      if (fileName !== "zip" || params.file.size > 50 * 1024 * 1024) {
        this.$message({
          message: this.$t("只能上传zip文件，且不超过50MB"),
          type: "error",
        });
        this.handleRemoveResource();
        return false;
      }
      const formData = new FormData();
      formData.append("file", params.file);
      this.file = new FormData();
      this.file.append("file", params.file);
      this.importZip(formData);
    },
  },
};
</script>

<style lang="less" scoped>
.upload-main {
  margin-top: 15%;
  :deep(.el-select) {
    width: 100%;
  }
  .upload-btn {
    text-align: center;
  }
  .upload-bg {
    text-align: center;
    margin-bottom: 20px;
  }
}
</style>
