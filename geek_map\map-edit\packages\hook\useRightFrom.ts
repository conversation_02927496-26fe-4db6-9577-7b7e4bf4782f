import { useAttrStore } from "@packages/store/attr";
import {
  NODE_CELL,
  NODE_LINE,
  NODE_DEVICE,
  NODE_AREA,
  NODE_IDKEY_MAP,
  NODE_TYPEKEY_MAP,
} from "@packages/configure/dict/nodeType";

import {
  DEVICE_PANEL_DICT,
  AREA_PANEL_DICT,
  CELL_PANEL_DICT,
  LINE_PANEL_DICT,
  PanelDcitType,
} from "@packages/configure/disabledByNodeType";

/**
 * 根据不同的元素类型来返回对应的设置
 * @param nodeType
 * @returns
 */
export function getPanelConfByNodeType(nodeType: string): PanelDcitType {
  const option: { [k: string]: PanelDcitType } = {
    [NODE_CELL]: CELL_PANEL_DICT,
    [NODE_LINE]: LINE_PANEL_DICT,
    [NODE_DEVICE]: DEVICE_PANEL_DICT,
    [NODE_AREA]: AREA_PANEL_DICT,
  };
  return option[nodeType];
}

/**
 * 获取当前选中元素的类型
 * @param selectNodes 入参可不填, 如果该函数在computed中不能实时更新, 则可以传入attrStore来解决
 * @returns
 */
export function getSelectNodeType(selectNodes?: any): string | undefined {
  let curNode = selectNodes;

  if (!selectNodes) {
    const curAttrStore = useAttrStore();
    curNode = curAttrStore.curNodeDataByIndex;
  }

  return Object.keys(NODE_TYPEKEY_MAP).find(key => {
    return NODE_TYPEKEY_MAP[key] in curNode;
  });
}
