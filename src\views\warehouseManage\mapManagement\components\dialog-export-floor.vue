<template>
  <div class="app-container">
    <el-dialog
      :title="dialogExportFloor.title"
      :visible.sync="visible"
      width="30%"
      :close-on-click-modal="false"
    >
      <span>
        <el-radio-group v-model="radio">
          <el-radio :label="'DXF'">{{ $t("lang.rms.fed.map.import.dxfFile") }}</el-radio>
          <el-radio :label="'XSLX'">{{ $t("lang.rms.fed.map.import.xslxFile") }}</el-radio>
        </el-radio-group>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">{{ $t("lang.rms.fed.confirm") }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'DialogExportFloor',
  data() {
    return {
      radio: 'DXF'
    }
  },
  computed: {
    ...mapState('mapManagement', ['dialogExportFloor']),
    visible: {
      get() {
        return this.dialogExportFloor.visible
      },
      set(val) {
        const { visible } = this.dialogExportFloor
        if (!val && val !== visible) {
          this.hideDialog()
        }
      }
    },
    rowData() {
      return this.dialogExportFloor.rowData
    }
  },
  methods: {
    ...mapMutations('mapManagement', ['hideDialog']),
    handleSubmit() {
      $req
        .post('/athena/map/manage/exportMap', {
          mapId: this.rowData.id,
          floorId: this.dialogExportFloor.floorId,
          type: this.radio
        })
        .then(res => {
          const data = res.data
          this.visible = false
          const url = data.url ? data.url : data
          if ($req.isDev) window.open($req.API_URL + url)
          else window.open(window.location.origin + url)
        })
    }
  }
}
</script>

<style lang="less" scoped></style>
