<template>
  <div>
    <el-card>
      <div slot="header">
        <span>{{ warehouseName }}</span>
        <el-switch
          v-model="autoAdvancedSelection"
          class="switch ml-20"
          :active-text="$t('lang.rms.fed.advanced')"
          :inactive-text="$t('lang.rms.fed.optionNormal')"
        />
        <el-switch
          v-if="checkPermission('MapManageZone4Automanual', 'natural')"
          v-model="autoLogicSelection"
          class="switch"
          :active-text="$t('lang.rms.fed.manualZone')"
          :inactive-text="$t('lang.rms.fed.automaticZone')"
        />
      </div>
      <el-form ref="form" :model="form" label-width="80px" :rules="rules" label-position="top">
        <el-row>
          <el-col :span="5" />
        </el-row>
        <el-row :gutter="15">
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.shelfNumber')" prop="shelfCode">
              <el-input
                ref="shelfCodeInput"
                v-model="form.shelfCode"
                :placeholder="$t('lang.rms.fed.pleaseEnter')"
                @keyup.enter.native="handleShelfCodeInput"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.enteringPoint')" prop="cellCode">
              <el-input
                ref="cellCodeInput"
                v-model="form.cellCode"
                :placeholder="$t('lang.rms.fed.pleaseEnter')"
                :disabled="!shelfCodeComplate"
                @keyup.enter.native="handleCellCodeInput"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="autoAdvancedSelection" :span="5">
            <el-form-item :label="$t('lang.rms.fed.placementCode')" prop="placementCellCode">
              <el-input
                ref="placementCellCodeInput"
                v-model="form.placementCellCode"
                :placeholder="$t('lang.rms.fed.pleaseEnter')"
                @keyup.enter.native="handlePlacementCellCodeInput"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item :label="$t('lang.rms.fed.selectTheZone')">
              <div v-if="!autoLogicSelection">{{ $t("lang.rms.fed.scanTheShelf") }}</div>
              <el-select
                v-if="autoLogicSelection"
                v-model="form.logicSelection"
                :placeholder="$t('lang.rms.fed.pleaseChoose')"
              >
                <el-option v-for="item in logics" :key="item.logicId" :label="item.logicName" :value="item.logicId" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="15">
          <el-col v-if="autoAdvancedSelection" :span="5">
            <el-form-item :label="$t('lang.rms.fed.angle') + ':'">
              <el-input-number
                v-model="form.angle"
                controls-position="right"
                :step="90"
                :min="-180"
                :max="180"
                @change="handleChange"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="autoAdvancedSelection" :span="5">
            <el-form-item :label="$t('lang.rms.fed.shelfModel') + ':'">
              <el-select v-model="form.classCode">
                <el-option v-for="item in shelfModels" :key="item.id" :label="item.modelName" :value="item.modelType" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="autoAdvancedSelection" :span="5">
            <el-form-item :label="$t('lang.rms.fed.shelfCodeRule') + ':'">
              <el-select
                v-model="form.pattern"
                filterable
                allow-create
                clearable
                default-first-option
                :placeholder="$t('lang.rms.fed.selectShelfCodeRule')"
                @change="validatePattern"
              >
                <el-option v-for="item in shelfPatterns" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <div v-if="checkPermission('MapManageZoneSubmit', 'natural')" class="btnwarp">
              <el-button
                v-if="autoLogicSelection || autoAdvancedSelection"
                type="primary"
                :disabled="(!autoLogicSelection && !autoAdvancedSelection) || !shelfCodeComplate || !cellCodeComplate"
                @click="requestShelfEnter"
              >
                {{ $t("lang.rms.fed.submit") }}
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-row :gutter="40">
      <el-col :span="12">
        <el-card class="mt-5">
          <div slot="header">
            <span>{{ $t("lang.rms.fed.shelfInTheTask") }}</span>
          </div>
          <el-table :data="shelfStatus" style="width: 100%">
            <el-table-column type="index" width="50" />
            <el-table-column prop="shelfCode" :label="$t('lang.rms.fed.shelfNumber')" />
            <el-table-column prop="logicName" :label="$t('lang.rms.fed.allocateZone')" />
            <el-table-column prop="placement" :label="$t('lang.rms.fed.coordinate')">
              <template slot-scope="scope">
                {{ scope.row.placement.x }}, {{ scope.row.placement.y }},
                {{ scope.row.placement.z }}
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="$t('lang.rms.fed.state')">
              <template slot-scope="scope">
                <span v-if="scope.row.status === 'GO_RETURN'">{{ $t("lang.rms.fed.beingCarried") }}</span>
                <span v-if="scope.row.status === 'FETCHING'">{{ $t("lang.rms.fed.robotIsOnTheWay") }}</span>
                <span v-if="scope.row.status !== 'FETCHING' && scope.row.status !== 'GO_RETURN'">{{
                  $t("lang.rms.fed.malfunction")
                }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <el-card class="mt-5">
          <div slot="header">
            <span>{{ $t("lang.rms.fed.queryShelfCode") }}</span>
            <el-input
              v-model="searchShelfCode"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              style="float: right; width: 200px; margin: -5px 0"
            >
              <el-button slot="append" icon="el-icon-search" @click="handleShelfSearch" />
            </el-input>
          </div>
          <el-table :data="shelfSearchData" style="width: 100%">
            <el-table-column type="index" width="50" />
            <el-table-column prop="shelfCode" :label="$t('lang.rms.fed.shelfNumber')" />
            <el-table-column prop="logicId" :label="$t('lang.rms.fed.allocateZone')">
              <template slot-scope="scope">{{ logicsDict[scope.row.logicId] }}</template>
            </el-table-column>
            <el-table-column prop="placement" :label="$t('lang.rms.fed.coordinate')">
              <template slot-scope="scope">
                {{ scope.row.placement.x }}, {{ scope.row.placement.y }},
                {{ scope.row.placement.z }}
              </template>
            </el-table-column>
            <el-table-column prop="placement" :label="$t('lang.rms.fed.state')">
              <template slot-scope="scope">{{ scope.row.state }}</template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :span="12" style="padding-left: 0px">
        <el-card class="mt-5">
          <div slot="header">
            <span>{{ $t("lang.rms.fed.warehouseShelfLocationAvailable") }}</span>
            <span style="float: right">
              {{ $t("lang.rms.fed.shelfEntered") }}：{{ shelfCount }};
              {{ $t("lang.rms.fed.available") }}
              / {{ $t("lang.rms.fed.total") }}：{{ idleCount }}/{{ maxCount }}
            </span>
          </div>
          <el-row :gutter="20">
            <el-col v-for="item in logicAreas" :key="item.logicId" :span="12" class="logicareas">
              <div class="title">
                <span class="count">{{ item.maxCount - item.shelfCount }}/{{ item.maxCount }}</span>
                {{ item.logicName }}
              </div>
              <div class="range">
                <span
                  :style="{
                    width: Math.floor(((item.maxCount - item.shelfCount) / item.maxCount) * 100) + '%',
                  }"
                />
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  data() {
    return {
      form: {
        shelfCode: "",
        cellCode: "",
        placementCellCode: "",
        logicSelection: "",
        angle: "",
        classCode: "",
        pattern: "",
      },
      autoLogicSelection: false,
      autoAdvancedSelection: false,
      shelfCodeComplate: false,
      cellCodeComplate: false,
      shelfStatusDict: {
        FETCHING: this.$t("lang.rms.fed.robotIsOnTheWay"),
        GO_RETURN: this.$t("lang.rms.fed.beingCarried"),
      },
      rules: {
        shelfCode: [{ validator: this.validateShelfCode, trigger: "blur" }],
        cellCode: [{ validator: this.validateCellCode, trigger: "blur" }],
        placementCellCode: [{ validator: this.validatePlacementCode, trigger: "blur" }],
        pattern: [{ validator: this.validateShelfCode1, trigger: "blur" }],
      },
      logics: [],
      logicsDict: {},
      shelfCount: 0,
      idleCount: 0,
      maxCount: 0,
      searchShelfCode: "",
      shelfSearchData: [],
      warehouseName: "",
      warehouseId: 0,
      logicAreas: [],
      shelfStatus: [],
      shelfStatusHead: [
        {
          prop: "shelfCode",
          label: this.$t("lang.rms.fed.shelfNumber"),
        },
        {
          prop: "logicName",
          label: this.$t("lang.rms.fed.allocateZone"),
        },
        {
          prop: "placement",
          label: this.$t("lang.rms.fed.coordinate"),
        },
        {
          prop: "status",
          label: this.$t("lang.rms.fed.state"),
        },
      ],
      tasksetInterval: null,
      shelfModels: [],
      shelfPatterns: [
        {
          value: "^[A-Z]{1}[0-9]{1,7}$",
          label: "^[A-Z]{1}[0-9]{1,7}$",
        },
        {
          value: "^[0-9]{2,8}$",
          label: "^[0-9]{2,8}$",
        },
        {
          value: "^[A-Z]{1}[0-9]{6}$",
          label: "^[A-Z]{1}[0-9]{6}$",
        },
      ],
    };
  },
  activated() {
    this.initLogic();
    this.queryAllWarehouse();
    this.initShelfModelInfo();
  },
  deactivated() {
    clearInterval(this.tasksetInterval);
    this.tasksetInterval = null;
  },
  methods: {
    initLogic() {
      $req.get("/athena/warehouse/logic/findAll").then(res => {
        if (res.data && res.data.length > 0) {
          this.logics = res.data.filter(logic => logic.areaType === "SHELF");
          this.logics.forEach(item => {
            this.logicsDict[item.logicId] = item.logicName;
          });
        }
      });
    },
    validatePattern() {
      this.$refs["form"].validateField("shelfCode");
    },
    validateShelfCode(rule, value, callback) {
      var regx = this.form.pattern;
      if (regx) {
        regx = new RegExp(regx);
        if (!regx.test(this.form.shelfCode)) {
          var msg = this.$t("lang.rms.fed.noMatchRules");
          callback(new Error(msg));
          this.shelfCodeComplate = false;
          this.$refs["shelfCodeInput"].select();
          return;
        }
      }
      $req.get("/athena/shelf/exists", { shelfCode: value }).then(res => {
        if (res.data.status === 2) {
          callback();
          this.shelfCodeComplate = true;
          this.$nextTick(function () {
            // this.$refs["cellCodeInput"].focus();
          });
        } else {
          const str = res.data.descr.split(",");
          const mag = this.$t(this.$t(...str));
          callback(new Error(mag));
          this.$refs["shelfCodeInput"].select();
        }
      });
    },
    validateCellCode(rule, value, callback) {
      $req.get("/athena/map/cell/exists", { cellCode: value }).then(res => {
        if (res.data.status === 0) {
          callback();
          this.cellCodeComplate = true;
          if (!this.autoLogicSelection && !this.autoAdvancedSelection) {
            this.requestShelfEnter();
          }
        } else {
          const str = res.data.descr.split(",");
          const mag = this.$t(this.$t(...str));
          callback(new Error(mag));
          this.$refs["cellCodeInput"].select();
        }
      });
    },
    requestShelfEnter() {
      $req
        .post("/athena/shelf/add", {
          shelfCode: this.form.shelfCode,
          cellCode: this.form.cellCode,
          logicId: this.autoLogicSelection ? this.form.logicSelection : "",
          warehouseId: this.warehouseId,
          classCode: this.autoAdvancedSelection ? this.form.classCode : "",
          angle: this.autoAdvancedSelection ? this.form.angle : "",
          placementCellCode: this.autoAdvancedSelection ? this.form.placementCellCode : "",
        })
        .then(res => {
          if (res.code === 0) {
            this.$message({
              message: this.$t(res.msg),
              type: "success",
            });
          }
        });
    },
    handleShelfSearch() {
      $req
        .get("/athena/shelf/find", {
          shelfCode: this.searchShelfCode,
          warehouseId: this.warehouseId,
        })
        .then(res => {
          this.shelfSearchData = res.data;
        });
    },
    handleShelfCodeInput(event) {
      event.target.blur();
    },
    handleCellCodeInput(event) {
      event.target.blur();
    },
    handlePlacementCellCodeInput(event) {
      event.target.blur();
    },
    handlePatternInput(event) {
      event.target.blur();
    },
    intervlaInfo() {
      this.tasksetInterval = setInterval(() => {
        this.initShelfStatus();
        this.initLogicShelfInfo();
      }, 2000);
    },
    queryAllWarehouse() {
      $req.get("/athena/warehouse/findAll").then(res => {
        const warehouse = res.data[0];
        this.warehouseId = warehouse.warehouseId;
        this.warehouseName = warehouse.warehouseName;
        this.intervlaInfo();
      });
    },
    initShelfStatus() {
      $req.get("/athena/shelf/getEnterProcessing", { warehouseId: this.warehouseId }).then(res => {
        this.shelfStatus = res.data;
      });
    },
    initLogicShelfInfo() {
      $req.get("/athena/warehouse/getInfo", { warehouseId: this.warehouseId }).then(res => {
        this.shelfCount = res.data.shelfCount;
        this.idleCount = res.data.idleCount;
        this.maxCount = res.data.maxCount;
        this.logicAreas = res.data.logicAreas || [];
      });
    },
    initShelfModelInfo() {
      $req.get("/athena/shelfModel/list", { type: "shelf" }).then(res => {
        this.shelfModels = res.data;
      });
    },
    validatePlacementCode(rule, value, callback) {
      if (value === "") {
        // FIXME:为什么这么写 什么都没有写个if判断 zhl
      } else {
        const data = new FormData();
        data.append("placementCellCode", value);
        data.append("logicId", this.autoLogicSelection ? this.form.logicSelection : "");

        $req.post("/athena/shelf/isPlacementLegal", data).then(res => {
          if (res.data.status === 0) {
            callback();
            // this.$refs["placementCellCodeInput"].values("");
            this.$nextTick(function () {
              // this.$refs["cellCodeInput"].focus();
            });
          } else {
            const str = res.data.descr.split(",");
            const mag = this.$t(this.$t(...str));
            callback(new Error(mag));
            // this.$refs["placementCellCodeInput"].select();
          }
        });
      }
    },
    handleChange(value) {
      console.log(value);
    },
  },
};
</script>

<style lang="less" scoped>
.el-select {
  width: 100%;
}

.btnwarp {
  padding: 43px 0 0;
}

.switch {
  float: right;
  padding: 7px 0 0;
}

.ml-20 {
  margin-left: 20px;
}

.mt-5 {
  margin-top: 5px;
}

.logicareas {
  margin: 0 0 20px;
  font-size: 12px;

  .title {
    margin: 0 0 5px;
  }

  .count {
    float: right;
  }

  .range {
    background: #ebebeb;
    width: 100%;
    height: 10px;

    span {
      display: inline-block;
      background: #049c50;
      height: 10px;
      width: 0%;
    }
  }
}
</style>
