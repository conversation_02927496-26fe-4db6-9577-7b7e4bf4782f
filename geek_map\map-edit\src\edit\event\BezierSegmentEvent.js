import {getGlobalViewport} from "../global";
import CtrlPoint from '../element/baseElement/CtrlPoint';
import BezierLine from '../element/baseElement/BezierLine'
import { SmoothGraphics as Graphics } from '@pixi/graphics-smooth';
import Mode from '../Mode'
import LayerManager from '../layerManager/LayerManager'
import {isHoverNode, pixi2cad,createId} from "../utils/utils";
import BezierArrow from "../element/baseElement/BezierArrow";
import {lineStyle} from "../config";
import EventBus from "../eventBus/EventBus";
const {ACTIVE_LINE} = lineStyle
export default class SegmentEvent {
  static addEvents(defConfig) {
    const vp = getGlobalViewport()
    const operateLayerInstance = LayerManager.get('OPERATE')
    const $operateLayer = operateLayerInstance.container
    const $line = new Graphics()
    //设置默认空负载方向
    $line.loadDirs = 1
    $line.unloadDirs = 1
    const $container = new Graphics();
    let paths = []
    $container.addChild($line)
    $operateLayer.addChild($container)
    //根据不同的曲线类型生成paths
    const computedPathsByBezierType = (p1,p2) => {
      const {options: {bezierType}} = Mode.mode
      let ctrlPoint1,ctrlPoint2;
      //控制点,根据不同的曲线类型，计算控制点位置
      const {x:x1,y:y1} = p1
      const {x:x2,y:y2} = p2
      switch(bezierType){
        case 'CCW':
          ctrlPoint1 = ctrlPoint2 = {x:x1,y:y2,nodeId:null,cellCode:null}
          break;
        case 'CW':
          ctrlPoint1 = ctrlPoint2 = {x:x2,y:y1,nodeId:null,cellCode:null}
          break;
        case 'S':
          ctrlPoint1 = {x:(x1 + x2) / 2,y:y1}
          ctrlPoint2 = {x:(x1 + x2) / 2,y:y2}
          break;
      }
      const renderPaths = [p1,ctrlPoint1,ctrlPoint2,p2]
      return renderPaths
    }
    const events = {
      clicked:e => {
        const p = e.world
        const hoverNode = isHoverNode(p)
        let $point;
        let pathLen = paths.length
        if(!hoverNode) return EventBus.$emit('message',{type:'warning',text:'请点击在单元格上'});
        const {x,y,nodeId,cellCode} = hoverNode
        //进行重复校验，新的点与前一个点不允许相等
        if(pathLen && (paths[pathLen - 1].nodeId === nodeId)) return
        $point = CtrlPoint.render(x,y)
        paths.push({x,y,nodeId,cellCode})
        // if(hoverNode){
        //   const {x,y,nodeId,cellCode} = hoverNode
        //   //进行重复校验，新的点与前一个点不允许相等
        //   if(pathLen && (paths[pathLen - 1].nodeId === nodeId)) return
        //   $point = CtrlPoint.render(x,y)
        //   paths.push({x,y,nodeId,cellCode})
        // }else{
        //   $point = CtrlPoint.render(p.x,p.y)
        //   paths.push({x:p.x,y:p.y,nodeId:null,cellCode:null})
        // }
        $container.addChild($point)
        //渲染线
        $line.clear()
        BezierLine.render($line,paths)
        BezierArrow.render($line,{color:ACTIVE_LINE})
        if(paths.length === 2){
          const renderPaths = computedPathsByBezierType(paths[0],paths[1])
          //将pixi坐标转化为cad坐标
          const invertPaths = renderPaths.map(path => {
            return pixi2cad(path)
          })
          const addOp = {
            id: 'LINE',
            data:[{
              segmentId:createId(),
              points:invertPaths,
              segmentType:'BEZIER',
              loadDirs:1,
              unloadDirs:1,
              ...defConfig
            }]
          }
          Mode.resetMode()
          $operateLayer.removeChildren()
          LayerManager.addElements(addOp)
        }
      },
      mousemove:e => {
        const moveP = e.data.getLocalPosition(vp);
        if(!paths.length) return
        let renderP;
        const hoverNode = isHoverNode(moveP)
        if(hoverNode){
          const {x,y,nodeId,cellCode} = hoverNode
          renderP = {x,y,nodeId,cellCode}
        }else{
          renderP = {x:moveP.x,y:moveP.y,nodeId:null,cellCode:null}
        }
        // const ctrlPoint = {x:paths[0].x,y:moveP.y,nodeId:null,cellCode:null}
        // const movePaths = [...paths,ctrlPoint,ctrlPoint,renderP]
        const movePaths = computedPathsByBezierType(paths[0],renderP)
        //渲染线
        $line.clear()
        BezierLine.render($line,movePaths)
        BezierArrow.render($line,{color:ACTIVE_LINE})
      },
      keydown:e => {
        const {key} = e
        if(key === 'Escape'){
          $operateLayer.removeChildren()
          Mode.resetMode()
          EventBus.$emit('keydown:Escape')
        }
      }
    }
    return events
  }
}
