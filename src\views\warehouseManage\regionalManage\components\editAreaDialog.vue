<template>
  <el-dialog
    :title="$t('lang.rms.fed.buttonEdit')"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="520px"
  >
    <geek-customize-form ref="areaForm" :form-config="formConfig" />

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditAreaDialog",
  data() {
    return {
      dialogVisible: false,
      rowData: {},
      formConfig: {
        attrs: {
          labelWidth: "150px",
          labelPosition: "right",
        },
        configs: {
          robotDemand: {
            label: "lang.rms.fed.robotProportion",
            default: "",
            tag: "input-number",
            "controls-position": "right",
          },
        },
      },
    };
  },
  methods: {
    open(data) {
      const params = {
        robotDemand: data.robotDemand || 0,
      };
      this.rowData = data;
      this.dialogVisible = true;
      this.$nextTick(() => this.$refs.areaForm.setData(params));
    },
    close() {
      this.dialogVisible = false;
    },
    // 保存
    save() {
      const formData = this.$refs.areaForm.getData();
      const params = Object.assign({}, formData, {
        logicId: this.rowData.logicId,
      });
      $req.post("/athena/area/updateById", params).then(res => {
        this.$success();
        this.dialogVisible = false;
        this.$emit("updateMainList");
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
