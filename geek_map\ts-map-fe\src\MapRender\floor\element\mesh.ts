/* ! <AUTHOR> at 2022/08/27 */
import * as PIX<PERSON> from "pixi.js";

class Mesh implements MRender.Mesh {
  createMesh(geometries: any[], shader: any): any {
    const merges = PIXI.Geometry.merge(geometries);
    return new PIXI.Mesh(merges, shader);
  }

  drawGeometryRect(position: Array<number>, vColor: any): any {
    return new PIXI.Geometry()
      .addAttribute("aVertexPosition", position, 2) // position
      .addAttribute("aColor", vColor, 3) // r, g, b
      .addIndex([0, 1, 2, 0, 2, 3]);
  }
  drawGeometryIcon(position: Array<number>): any {
    return new PIXI.Geometry()
      .addAttribute("aVertexPosition", position, 2) // position
      .addAttribute("aUvs", [0, 0, 1, 0, 1, 1, 0, 1], 2)
      .addIndex([0, 1, 2, 0, 2, 3]);
  }
  drawGeometryIconColor(position: Array<number>, vColor: any): any {
    return new PIXI.Geometry()
      .addAttribute("aVertexPosition", position, 2) // position
      .addAttribute("aColor", vColor, 3) // r, g, b
      .addAttribute("aUvs", [0, 0, 1, 0, 1, 1, 0, 1], 2)
      .addIndex([0, 1, 2, 0, 2, 3]);
  }

  getShaderRect(): any {
    return PIXI.Shader.from(
      `
        precision mediump float;
        attribute vec2 aVertexPosition;
        attribute vec3 aColor;

        uniform mat3 translationMatrix;
        uniform mat3 projectionMatrix;

        varying vec3 vColor;
        void main() {
          vColor = aColor;
          gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        }
      `,
      `
        precision mediump float;
        varying vec3 vColor;
        void main() {
          gl_FragColor = vec4(vColor, 1);
        }`,
    );
  }

  getShaderIcon(texture: any): any {
    return PIXI.Shader.from(
      `
        precision mediump float;
        attribute vec2 aVertexPosition;
        attribute vec2 aUvs;

        uniform mat3 translationMatrix;
        uniform mat3 projectionMatrix;

        varying vec2 vUvs;
        void main() {
          vUvs = aUvs;
          gl_Position =vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        }
      `,
      `
        precision mediump float;
        varying vec2 vUvs;
        uniform sampler2D uSampler2;
        void main() {
          gl_FragColor = texture2D(uSampler2, vUvs);

        }
      `,
      { uSampler2: texture },
    );
  }

  getShaderIconColor(texture: any): any {
    return PIXI.Shader.from(
      `
        precision mediump float;
        attribute vec2 aVertexPosition;
        attribute vec3 aColor;
        attribute vec2 aUvs;

        uniform mat3 translationMatrix;
        uniform mat3 projectionMatrix;

        varying vec2 vUvs;
        varying vec3 vColor;
        void main() {
          vUvs = aUvs;
          vColor = aColor;
          gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
        }
      `,
      `
        precision mediump float;
        varying vec2 vUvs;
        varying vec3 vColor;
        uniform sampler2D uSampler2;
        void main() {
          gl_FragColor = texture2D(uSampler2, vUvs) * vec4(vColor, 1.0);
        }`,
      {
        uSampler2: texture,
      },
    );
  }
}
export default Mesh;
