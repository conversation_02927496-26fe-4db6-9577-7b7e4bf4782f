<template>
  <div class="current-operation">
    <strong>{{ $t("lang.rms.fed.currentOperation") }}:</strong>
    <span v-html="currentName" />
  </div>
</template>

<script>
export default {
  name: "CurrentOperation",
  props: {
    type: {
      type: String,
      default: "movement",
    },
  },
  data() {
    return {
      eventName: {
        ruler: ["lang.rms.fed.ruler"], // 尺子
        movement: ["lang.rms.fed.movement"],
        rotateCompass: ["lang.rms.fed.rotate", "lang.rms.fed.compass1"],
        rotateFree: ["lang.rms.fed.rotate", "lang.rms.fed.free"],
        revoke: ["lang.rms.fed.revoke"],
        wall: ["lang.rms.fed.wall"],
        wallFree: ["lang.rms.fed.wall", "lang.rms.fed.freeRepair"],
        wallErase: ["lang.rms.fed.wall", "lang.rms.fed.erase"],
        wallStraightLine: ["lang.rms.fed.wall", "lang.rms.web.map.segment.type.lineRepair"],
        ground: ["lang.rms.fed.ground"],
        groundErase: ["lang.rms.fed.ground", "lang.rms.fed.erase"],
        groundFree: ["lang.rms.fed.ground", "lang.rms.fed.freeRepair"],
        groundStraightLine: ["lang.rms.fed.ground", "lang.rms.web.map.segment.type.lineRepair"],
        groundRegion: ["lang.rms.fed.ground", "lang.rms.fed.regionalPatch"],
      },
    };
  },
  computed: {
    currentName() {
      let arr = this.eventName[this.type];
      const lang = arr.map(item => {
        return this.$t(item);
      });
      return lang.join(" - ");
    },
  },
};
</script>

<style lang="less" scoped>
.current-operation {
  font-size: 14px;

  > strong {
    font-weight: 600;
  }

  > span {
    font-size: 13px;
    color: #409eff;
  }
}
</style>
