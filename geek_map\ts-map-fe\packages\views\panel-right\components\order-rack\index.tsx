/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>Complete, Button, Drawer } from "antd";
import { getMap2D, $eventBus } from "../../../../singleton";

import ListCurrent from "./list-current";
import ListTarge from "./list-target";
import TargetRackHandle from "./rack-target-handle";
import RackDetail from "./rack-detail";

type PropsOrderData = { isCurrent: boolean };
type SearchDataType = { type: "container" | "lattice" | "box"; value: string; subCode?: code };
type currentSelectType = { type: "box" | "lattice"; latticeCodes: Array<code>; boxCode: "" } | null;

let isCurrentRack = true; // 是否为当前货箱架
function OrderRack(props: PropsOrderData) {
  const { t } = useTranslation();
  const [rackResults, setRackResults] = useState([]); // rack查找结果
  const [rackCode, setRackCode] = useState<string>("");
  const [searchData, setSearchData] = useState<SearchDataType>(null);

  const [operation, setOperation] = useState<"update" | "move">(undefined);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const [currentRack, setCurrentRack] = useState<rackData>(null);
  const [targetRack, setTargetRack] = useState<rackData>(null);

  const [currentSelect, setCurrentSelect] = useState<currentSelectType>(null);
  const [targetSelect, setTargetSelect] = useState("");

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.isCurrent) return;
    $eventBus.on("wsDataQueryRightTab", wsData => {
      if (!props.isCurrent || !wsData) return;
      const { params, data } = wsData;
      const layer = params.layer;
      if (layer !== "robot" && layer !== "rack") return;

      let rackData = data;
      if (layer === "robot") {
        rackData = data?.onloadRack;
      }
      if (rackData) {
        if (isCurrentRack) setCurrentRack(rackData);
        else setTargetRack(rackData);
      } else {
        if (isCurrentRack) {
          clearRack();
          setDrawerOpen(false);
          setOperation(undefined);
        } else {
          setTargetRack(null);
        }
      }
    });

    return () => {
      $eventBus.off("wsDataQueryRightTab");
      clearRack();
    };
  }, [props.isCurrent]);

  // 地图点击 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;

    isCurrentRack = true;
    setOperation(undefined);
    const map2D = getMap2D();
    map2D.mapRender.triggerLayers(["rack", "robot"]);
    $eventBus.on("mapClick", (data: MRender.clickParams) => {
      const layer = data?.layer;
      const code = data.code;
      switch (layer) {
        case "rack":
        case "robot":
          setDrawerOpen(true);
          setTargetRack(null);
          if (isCurrentRack) {
            setCurrentRack(null);
            if (layer === "robot") {
              const robotData = map2D.mapWorker.getQueryData({ layer: "robot", code });
              setRackCode(robotData?.onloadRack?.rackCode || "");
            } else setRackCode(code.toString());
          }
          map2D.mapWorker.reqQuery({ layer, code });
          break;
      }
    });

    return () => {
      $eventBus.off("mapClick");
    };
  }, [props.isCurrent]);

  useEffect(() => {
    if (!props.isCurrent) return;
    const code: any = currentRack?.rackCode || "";
    if (code !== rackCode) setRackCode(code);
  }, [currentRack?.rackCode]);

  const controlHandler = (cmd: string) => {
    switch (cmd) {
      case "lockRack": // 锁定货箱架
        rackOperateFn("LOCK_RACK");
        return;
      case "unlockRack": // 解锁货箱架
        rackOperateFn("UNLOCK_RACK");
        return;
      case "lockLattice": // 锁定货位
        latticeOperateFn("LOCK_LATTICE");
        return;
      case "unlockLattice": // 解锁货位
        latticeOperateFn("UNLOCK_LATTICE");
        return;
      case "lockBox": // 锁定货箱
        boxOperateFn("LOCK_BOX");
        return;
      case "unlockBox": // 解锁货箱
        boxOperateFn("UNLOCK_BOX");
        return;
      case "update": // 更新
      case "move": // 移动
        setOperation(cmd);
        isCurrentRack = false;
        return;
    }
  };

  // 货箱架操作: 锁定/解锁
  const rackOperateFn = (instruction: string) => {
    if (!currentRack?.rackCode) return;

    const map2D = getMap2D();
    const msgType = "BoxInstructionRequestMsg";
    map2D.mapWorker
      .reqSocket(msgType, { instruction, rackCode: currentRack.rackCode })
      .then(res => {
        if (res.msgType !== "BoxInstructionResponseMsg") return;
        _$utils.wsCmdResponse(res?.body || {});
      });
    setOperation(undefined);
    isCurrentRack = true;
  };
  // 货位操作: 锁定/解锁
  const latticeOperateFn = (instruction: string) => {
    if (currentSelect?.type !== "lattice") return;

    const map2D = getMap2D();
    const msgType = "BoxInstructionRequestMsg";
    map2D.mapWorker
      .reqSocket(msgType, { instruction, latticeCodeList: currentSelect.latticeCodes })
      .then(res => {
        if (res.msgType !== "BoxInstructionResponseMsg") return;
        _$utils.wsCmdResponse(res?.body || {});
      });
    setOperation(undefined);
    isCurrentRack = true;
  };
  // 货箱操作: 锁定/解锁
  const boxOperateFn = (instruction: string) => {
    if (currentSelect?.type !== "box") return;

    const map2D = getMap2D();
    const msgType = "WarehouseInstructionRequestMsg";
    map2D.mapWorker
      .reqSocket(msgType, { instruction, boxCode: currentSelect.boxCode })
      .then(res => {
        if (res.msgType !== "WarehouseInstructionResponseMsg") return;
        _$utils.wsCmdResponse(res?.body || {});
      });
    setOperation(undefined);
    isCurrentRack = true;
  };

  // rack 选中
  const rackSelect = (value: string, option: any) => {
    if (!value) return;

    setRackCode(value);
    setSearchData(option);

    const map2D = getMap2D();
    map2D.mapRender.trigger("click", { rack: [value] });
    map2D.mapRender.setEleCenter({ layer: "rack", code: value });
  };
  // rack 查询
  const searchResult = (query: string) => {
    if (!props.isCurrent) return;
    if (query.length < 1) {
      setRackResults([]);
      return;
    }

    const map2D = getMap2D();
    const rackData = map2D.mapWorker.getQueryData({ layer: "rack", code: query });
    if (rackData) {
      setRackResults([{ value: query, type: "container" }]);
    } else {
      _searchLattice(query);
    }
  };
  // 查找货位
  const _searchLattice = (query: string) => {
    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket("QueryInstructionRequestMsg", {
        instruction: "BOX_LATTICE",
        latticeCode: query,
      })
      .then(res => {
        if (!["QueryInstructionResponseMsg"].includes(res.msgType)) return;
        const { rackCode } = res.body?.data || {};
        if (rackCode) {
          setRackResults([{ value: rackCode.toString(), type: "lattice", subCode: query }]);
        } else {
          _searchBox(query);
        }
      });
  };
  // 查找货箱
  const _searchBox = (query: string) => {
    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket("QueryInstructionRequestMsg", {
        instruction: "BOX",
        boxCode: query,
      })
      .then(res => {
        if (!["QueryInstructionResponseMsg"].includes(res.msgType)) return;
        const { rackCode } = res.body?.data || {};
        if (rackCode) {
          setRackResults([{ value: rackCode.toString(), type: "box", subCode: query }]);
        } else {
          setRackResults([]);
        }
      });
  };

  // 货箱清除
  const clearRack = () => {
    setRackCode("");
    setSearchData(null);
    setRackResults([]);

    setCurrentRack(null);
    setTargetRack(null);
    setDrawerOpen(false);
    const map2D = getMap2D();
    map2D.mapWorker.stopQuery();
    map2D.mapRender.clearSelects();
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component"
    >
      <AutoComplete
        style={{ width: "100%" }}
        placeholder={`${t("lang.rms.fed.rackCode")}/${t("lang.rms.fed.latticeCode")}/${t(
          "lang.rms.fed.boxCode",
        )}`}
        allowClear={true}
        value={rackCode}
        options={rackResults}
        onSearch={searchResult}
        onChange={(value: string, option: any) => setRackCode(value)}
        onSelect={rackSelect}
        onClear={() => {
          clearRack();
          setOperation(undefined);
        }}
      />

      <div className="component-btn-group">
        <Button
          type="primary"
          disabled={currentSelect?.type !== "box"}
          block
          onClick={() => controlHandler("update")}
        >
          {t("lang.rms.fed.update")}
        </Button>
        <Button
          type="primary"
          disabled={currentSelect?.type !== "box"}
          block
          onClick={() => controlHandler("move")}
        >
          {t("lang.rms.fed.move")}
        </Button>
        <Button
          type="primary"
          disabled={currentSelect?.type !== "box"}
          block
          onClick={() => controlHandler("lockBox")}
        >
          {t("lang.rms.fed.lockBox")}
        </Button>
        <Button
          type="primary"
          disabled={currentSelect?.type !== "box"}
          block
          onClick={() => controlHandler("unlockBox")}
        >
          {t("lang.rms.fed.unlockBox")}
        </Button>
        <Button
          type="primary"
          disabled={currentSelect?.type !== "lattice"}
          block
          onClick={() => controlHandler("lockLattice")}
        >
          {t("lang.rms.fed.lockLattice")}
        </Button>
        <Button
          type="primary"
          disabled={currentSelect?.type !== "lattice"}
          block
          onClick={() => controlHandler("unlockLattice")}
        >
          {t("lang.rms.fed.UnlockLattice")}
        </Button>
        <Button
          type="primary"
          disabled={!currentRack?.rackCode}
          block
          onClick={() => controlHandler("lockRack")}
        >
          {t("lang.rms.fed.lockRack")}
        </Button>
        <Button
          type="primary"
          disabled={!currentRack?.rackCode}
          block
          onClick={() => controlHandler("unlockRack")}
        >
          {t("lang.rms.fed.unlockRack")}
        </Button>
      </div>

      <TargetRackHandle
        rackCode={targetRack?.rackCode}
        operation={operation}
        currentSelect={currentSelect}
        targetSelect={targetSelect}
        onClear={() => {
          setTargetSelect("");
          setTargetRack(null);
        }}
        onCancel={() => {
          isCurrentRack = true;
          setTargetSelect("");
          setCurrentSelect(null);
          setOperation(undefined);

          const currentRackCode = currentRack?.rackCode;
          if (!currentRackCode) return;
          const map2D = getMap2D();
          map2D.mapRender.trigger("click", { rack: [currentRackCode] });
        }}
      />

      <RackDetail rack={currentRack} currentSelect={currentSelect} />

      <Drawer
        title={null}
        placement="left"
        mask={false}
        onClose={() => setDrawerOpen(false)}
        open={drawerOpen}
        className="map2d-rack-drawer"
        width={300}
      >
        <ListCurrent
          rackData={currentRack}
          currentSelect={currentSelect}
          setCurrentSelect={setCurrentSelect}
          searchData={searchData}
        />
        <ListTarge
          rackData={targetRack}
          targetSelect={targetSelect}
          setTargetSelect={setTargetSelect}
        />
      </Drawer>
    </div>
  );
}

export default OrderRack;
