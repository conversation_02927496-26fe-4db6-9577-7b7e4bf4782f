/* ! <AUTHOR> at 2021/01 */
import _fn from "../_fn";

export default {
  isObject(o) {
    return _fn.dataType(o) === "Object";
  },
  isArray(o) {
    return _fn.dataType(o) === "Array";
  },
  isDate(o) {
    return _fn.dataType(o) === "Date";
  },
  isBoolean(o) {
    return _fn.dataType(o) === "Boolean";
  },
  isNumber(o) {
    return _fn.dataType(o) === "Number";
  },
  isUndefined(o) {
    return _fn.dataType(o) === "Undefined";
  },
  isString(o) {
    return _fn.dataType(o) === "String";
  },
};
