<template>
  <section>
    <div class="detail-info">
      <div class="btn-group">
        <el-button v-if="checkPermission('ParamsCopyToCreateLibrary', 'natural')" type="text" @click="copyLibrary">
          {{ $t("lang.rms.fed.copyToCreateLibrary") }}
        </el-button>
        <i class="spacer">|</i>
        <el-button v-if="checkPermission('ParamsApplyThisConfiguration', 'natural')" type="text" @click="applyLibrary">
          {{ $t("lang.rms.fed.applyThisConfiguration") }}
        </el-button>
        <i class="spacer">|</i>
        <el-button
          v-if="checkPermission('ParamsEditMasterInformation', 'natural')"
          type="text"
          @click="editLibrary('edit')"
        >
          {{ $t("lang.rms.config.page.editTemplate") }}
        </el-button>
        <i class="spacer">|</i>
        <el-button v-if="checkPermission('ParamsEditParams', 'natural')" type="text" @click="handleParmaTree">
          {{ $t("lang.rms.fed.editParams") }}
        </el-button>
        <i class="spacer">|</i>
        <el-button
          v-if="checkPermission('ParamsDeleteLibrary', 'natural')"
          type="text"
          class="red-color"
          @click="deleteLibrary"
        >
          {{ $t("lang.rms.fed.deleteLibrary") }}
        </el-button>
      </div>
      <div class="params-btn">
        <el-input
          v-model="searchValue"
          :placeholder="`${$t('lang.rms.fed.pleaseEnter')}${$t('lang.rms.fed.configs.search.param')}`"
          class="input-with-select"
        >
          <!-- <el-select slot="prepend" v-model="searchType" :default-first-option="true">
            <el-option :label="$t('lang.rms.fed.parameterValues')" value="1" />
            <el-option :label="$t('lang.rms.fed.nameOfParameter')" value="2" />
            <el-option :label="$t('lang.rms.fed.effectiveImmediately')" value="3" />
            <el-option :label="$t('lang.rms.fed.parameterLabel')" value="4" />
            <el-option :label="$t('lang.rms.fed.describe')" value="5" />
          </el-select>
          <el-select
            v-if="searchType === '3'"
            slot="prepend"
            v-model="isImmediate"
            :placeholder="$t('lang.rms.fed.pleaseChoose')"
            class="cover-original-input"
          >
            <el-option
              v-for="item in immediateList"
              :key="`immediate-${item.key}`"
              :label="$t(item.value)"
              :value="item.key"
            />
          </el-select> -->
          <el-button slot="append" icon="el-icon-search" @click="onSearch" />
        </el-input>
        <!-- <el-switch
        v-model="showDisabledParams"
        :inactive-text="$t('lang.rms.fed.displayDisabledParameters')"
      /> -->
      </div>
    </div>
    <div class="content">
      <el-table
        ref="paramsTable"
        v-loading="tableLoading"
        :data="tableData"
        :row-class-name="toggleEnableParams"
        style="width: 100%"
        height="100%"
      >
        <el-table-column prop="paramsName" :label="$t('lang.rms.fed.nameOfParameter')" />
        <el-table-column prop="code" :label="$t('lang.venus.web.common.param')" />
        <el-table-column prop="descr" :label="$t('lang.rms.fed.describe')" width="200">
          <template slot-scope="scope">
            <span>{{ $t(scope.row.descr) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="immediate"
          :label="$t('lang.rms.fed.effectiveImmediately')"
          :formatter="formatterImmediate"
          align="center"
        />
        <el-table-column :label="$t('lang.rms.fed.parameterValues')" width="276">
          <template slot-scope="scope">
            <span v-if="scope.$index == cellIndex" class="editShowBox">
              <input type="textarea" :title="scope.row.value" v-model="parameterValues" />
              <span>
                <i class="el-icon-check" @click="save"></i>
                <i class="el-icon-close" @click="close"></i>
              </span>
            </span>
            <span v-else>
              {{ scope.row.value }}
              <i class="el-icon-edit-outline" @click="editParams(scope)"></i>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <dialog-library-edit ref="libraryEdit" @updatedLibrary="updatedLibrary" />
    <dialog-params-tree ref="paramsTree" @updatedLibrary="updatedLibrary" />
    <!-- <dialog-params-edit
      ref="paramsEdit"
      :immediate-list="immediateList"
      @save="updateTemplateParams"
    /> -->
  </section>
</template>

<script>
import DialogLibraryEdit from "./dialogLibraryEdit";
import DialogParamsTree from "./dialogParamsTree";
import DialogParamsEdit from "./dialogParamsEdit";

export default {
  name: "LibraryDetail",
  components: { DialogLibraryEdit, DialogParamsTree, DialogParamsEdit },
  props: ["currentLibrary"],
  data() {
    return {
      currentId: "",
      searchValue: "",
      searchType: "",
      isImmediate: "1",
      immediateList: [
        {
          key: "0",
          value: "lang.rms.fed.no",
        },
        {
          key: "1",
          value: "lang.rms.fed.yes",
        },
      ],
      totalParmaNum: 0,
      paramsTree: [],
      tableData: [],
      isIndeterminate: false,
      checkAll: true,
      showDisabledParams: false,
      tableLoading: false,
      rowData: {},
      data: {},
      parameterValues: "",
      cellIndex: null,
    };
  },
  watch: {
    currentLibrary(data) {
      this.cellIndex = null;
      if (!data) return;

      if (this.currentId !== data.template.id) {
        this.searchValue = "";
        this.searchType = "";
        this.isImmediate = "1";
      }

      this.currentId = data.template.id;
      this.getCurrentLibraryTree();
      if (data.hasOwnProperty("items") && data.items) {
        let tableData = data.items;
        tableData.forEach(item => {
          item._status = !!item.status;
        });
        this.tableData = tableData;
        const newTableList = this.tableData;
        newTableList.forEach(item => {
          item.paramsName = this.$t(`lang.configs.${item.code}.label`);
        });
      }
      this.getCheckStatus();
    },
  },
  filters: {
    timeformatter(value) {
      if (!value) return "--";
      return $utils.Tools.formatDate(value, "yyyy-MM-dd hh:mm:ss");
    },
  },
  methods: {
    editLibrary(type) {
      let template = this.currentLibrary.template;
      this.$refs.libraryEdit.open(type, template);
    },
    copyLibrary() {
      let template = this.currentLibrary.template;
      $req
        .post("/athena/config/template/copy?templateId=" + template.id, {
          name: template.name + "_copy",
          description: template.description,
        })
        .then(res => {
          if (res.code === 0) {
            this.updatedLibrary(res.data.id);
          }
        });
    },
    applyLibrary() {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmTheOperation"))
        .then(() => {
          $req
            .postParams("/athena/config/template/apply", {
              templateId: this.currentId,
            })
            .then(() => {
              this.updatedLibrary(null);
            });
        })
        .catch(e => console.log(e));
    },
    handleParmaTree() {
      this.$refs.paramsTree.open(this.currentLibrary, this.paramsTree);
    },
    deleteLibrary() {
      this.$geekConfirm(this.$t("lang.rms.fed.confirmDelete"), {
        confirmText: this.$t("lang.rms.fed.delete"),
      })
        .then(() => {
          $req
            .get("/athena/config/template/delete", {
              templateId: this.currentId,
            })
            .then(res => {
              // 配置库-删除配置库
              if (res.code === 0) {
                this.updatedLibrary(null);
              }
            });
        })
        .catch(e => console.log(e));
    },

    onSearch() {
      this.$emit("updatedLibraryParams", this.currentId, {
        language: $utils.Data.getLocalLang(),
        searchType: this.searchType || 1,
        searchValue: this.searchValue,
        isImmediate: this.isImmediate,
      });
    },

    editParams({ $index, row }) {
      this.rowData = row;
      this.parameterValues = row.value;
      this.cellIndex = $index;
    },
    close() {
      this.cellIndex = null;
    },
    save() {
      this.data = Object.assign({}, this.rowData, { value: this.parameterValues });
      this.data.status = Number(this.data.status);
      $req
        .post(
          "/athena/config/template/item/update?templateId=" + this.currentId, // 配置库-参数项更新
          this.data,
        )
        .then(res => {
          this.updatedLibrary(this.currentId);
        });
      this.close();
    },
    updatedLibrary(updatedId) {
      this.$emit("updatedLibrary", updatedId);
    },
    allParameterDisableAll(checked) {
      this.tableLoading = true;
      const status = checked ? 1 : 0;
      $req
        .postParams("/athena/config/template/item/all/updateStatus", {
          templateId: this.currentId,
          status: status,
        })
        .then(res => {
          if (res.code === 0) {
            this.tableData.forEach(item => {
              item.status = status;
              item._status = checked;
            });
            this.getCheckStatus();
          }
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    paramsStatusChange(rowData) {
      this.tableLoading = true;
      const _status = rowData._status;
      let data = Object.assign({}, rowData, { status: _status ? 1 : 0 });
      delete data._status;
      $req
        .post(
          "/athena/config/template/item/update?templateId=" + this.currentId, // 配置库-参数项更新
          data,
        )
        .then(res => {
          rowData.status = _status ? 1 : 0;
          this.getCheckStatus();
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableLoading = false;
        });
    },
    getCurrentLibraryTree() {
      $req
        .get("/athena/config/template/item/select", {
          templateId: this.currentId,
        })
        .then(res => {
          // 配置库-参数项选择（参数树结构）
          if (res.code !== 0) return;
          let data = res.data;
          this.totalParmaNum = data.existCodes.length;
          data.configList &&
            data.configList.forEach(item => {
              item.paramsLabel = this.$t(`lang.configs.${item.code}.label`);
            });
          const obj = this.jsonFormat(data.configList);
          const list = this.formatArr(obj);
          // console.log(list)
          this.paramsTree = list;
        });
    },
    formatArr(obj) {
      let arr = [];
      for (let key in obj) {
        let brr = [];

        for (let cKey in obj[key]) {
          // console.log(obj[key][cKey].list)
          brr.push({
            code: cKey,
            paramsLabel: this.$t(`lang.configs.${key}.${cKey}.label`),
            id: "1-1-" + cKey,
            childConfig: obj[key][cKey].list || [],
          });
        }

        arr.push({
          code: key,
          paramsLabel: this.$t(`lang.configs.${key}.label`),
          id: "1-" + key,
          childConfig: brr,
        });
      }

      return arr;
    },
    jsonFormat(data) {
      // console.log(data);
      const dataObj = {};
      let mapObj = {};
      let taskObj = {};
      let pathObj = {};
      let systemObj = {};
      let fesystemObj = {};
      // 二级菜单
      let mapModulesObj = {};
      let taskModulesObj = {};
      let pathModulesObj = {};
      let systemModulesObj = {};
      let frontEndModulesObj = {};
      this.mapModules = [];
      this.taskModules = [];
      this.pathModules = [];
      this.systemModules = [];
      this.frontEndModules = [];
      data.forEach(item => {
        const path = item.path;
        if (path) {
          const pathArr = path.split("/");
          pathArr.shift();
          let firstKey = pathArr[0];
          let secondKey = pathArr[1];
          dataObj[firstKey] = null;
          let childObj = {};
          if (item.code) {
            item.errorTipShow = false;
            const restrictions = item.restrictions ? JSON.parse(item.restrictions) : null;
            // console.log(item.id)
            if (item.limitKey) {
              const keysArr = Object.keys(item.limitKey);
              item.isShow = this.limitShowObj[keysArr[0]] == item.limitKey[keysArr[0]];
            }
            let currentUpdateValue = item.currentUpdateValue != null ? item.currentUpdateValue : item.value;

            let val = item.immediate ? item.value : currentUpdateValue;
            if (item.widgetType === "timeslot" && val) {
              val = JSON.parse(val);
            }
            childObj[item.code] = {
              ...item,
              i18nCode: item.code,
              label: item.paramsName,
              type: item.widgetType,
              options: {
                componentName: item.widgetType,
                selectList: item.widgetType === "select" && restrictions ? restrictions["options"] : [],
                defVal: val,
              },
              restrictions,
            };
            if (item.validValueRange) {
              let limitArr = item.validValueRange.split("~");
              childObj[item.code].options.limitMax = limitArr[1];
              childObj[item.code].options.limitMin = limitArr[0];
            }
          }
          switch (firstKey) {
            case "map":
              mapObj[secondKey] = Object.assign({}, mapObj[secondKey], childObj);
              mapModulesObj[item.path] = item.path;
              break;
            case "task":
              taskObj[secondKey] = Object.assign({}, taskObj[secondKey], childObj);
              taskModulesObj[item.path] = item.path;
              break;
            case "path":
              pathObj[secondKey] = Object.assign({}, pathObj[secondKey], childObj);
              pathModulesObj[item.path] = item.path;
              break;
            case "system":
              systemObj[secondKey] = Object.assign({}, systemObj[secondKey], childObj);
              systemModulesObj[item.path] = item.path;
              break;
            case "fesystem":
              fesystemObj[secondKey] = Object.assign({}, fesystemObj[secondKey], childObj);
              frontEndModulesObj[item.path] = item.path;
              break;
          }
        }
      });
      const obj = {
        // map: mapObj,
        // task: taskObj,
        // path: pathObj,
        // system: systemObj,
        // fesystem: fesystemObj,
      };
      if (this.isNull(mapObj)) {
        for (let key in mapObj) {
          const sections = this.groupBy(Object.values(mapObj[key]), "section");
          mapObj[key] = sections;
        }
        obj.map = mapObj;
      }
      if (this.isNull(pathObj)) {
        for (let key in pathObj) {
          const sections = this.groupBy(Object.values(pathObj[key]), "section");
          pathObj[key] = sections;
        }
        obj.path = pathObj;
      }
      if (this.isNull(taskObj)) {
        for (let key in taskObj) {
          const sections = this.groupBy(Object.values(taskObj[key]), "section");
          taskObj[key] = sections;
        }
        obj.task = taskObj;
      }
      if (this.isNull(systemObj)) {
        for (let key in systemObj) {
          const sections = this.groupBy(Object.values(systemObj[key]), "section");
          systemObj[key] = sections;
        }
        obj.system = systemObj;
      }
      if (this.isNull(fesystemObj)) {
        for (let key in fesystemObj) {
          const sections = this.groupBy(Object.values(fesystemObj[key]), "section");
          fesystemObj[key] = sections;
        }
        obj.fesystem = fesystemObj;
      }
      this.modules = [];
      for (let key in dataObj) {
        switch (key) {
          case "map":
            this.modules.push({ label: "地图", id: 1, code: "map" });
            break;
          case "path":
            this.modules.push({ label: "路径", id: 2, code: "path" });
            break;
          case "task":
            this.modules.push({ label: "任务", id: 3, code: "task" });
            break;
          case "system":
            this.modules.push({ label: "系统", id: 4, code: "system" });
            break;
          case "fesystem":
            this.modules.push({ label: "前端", id: 5, code: "fesystem" });
            break;
        }
      }
      for (let key in mapModulesObj) {
        switch (key) {
          case "/map/resolver":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/resolver"),
              id: 1,
              path: "/map/resolver",
            });
            break;
          case "/map/base":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/base"),
              id: 2,
              path: "/map/base",
            });
            break;
          case "/map/station":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/station"),
              id: 3,
              path: "/map/station",
            });
            break;
          case "/map/area":
            this.mapModules.push({
              label: this.secondTabLabelFun("/map/area"),
              id: 4,
              path: "/map/area",
            });
            break;
        }
      }
      for (let key in taskModulesObj) {
        switch (key) {
          case "/task/shelfTask":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/shelfTask"),
              id: 1,
              path: "/task/shelfTask",
            });
            break;
          case "/task/box":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/box"),
              id: 2,
              path: "/task/box",
            });
            break;
          case "/task/rsp":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/rsp"),
              id: 3,
              path: "/task/rsp",
            });
            break;
          case "/task/sorting":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/sorting"),
              id: 4,
              path: "/task/sorting",
            });
            break;
          case "/task/charging":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/charging"),
              id: 5,
              path: "/task/charging",
            });
            break;
          case "/task/GoRest":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/GoRest"),
              id: 6,
              path: "/task/GoRest",
            });
            break;
          case "/task/click":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/click"),
              id: 7,
              path: "/task/click",
            });
            break;
          case "/task/inspection":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/inspection"),
              id: 8,
              path: "/task/inspection",
            });
            break;
          case "/task/TaskStrategy":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/TaskStrategy"),
              id: 9,
              path: "/task/TaskStrategy",
            });
            break;
          case "/task/allocationStrategy":
            this.taskModules.push({
              label: this.secondTabLabelFun("/task/allocationStrategy"),
              id: 10,
              path: "/task/allocationStrategy",
            });
            break;
        }
      }
      for (let key in pathModulesObj) {
        switch (key) {
          case "/path/dispatching":
            this.pathModules.push({
              label: this.secondTabLabelFun("/path/dispatching"),
              id: 1,
              path: "/path/dispatching",
            });
            break;
          case "/path/planning":
            this.pathModules.push({
              label: this.secondTabLabelFun("/path/planning"),
              id: 2,
              path: "/path/planning",
            });
            break;
          case "/path/system":
            this.pathModules.push({
              label: this.secondTabLabelFun("/path/system"),
              id: 3,
              path: "/path/system",
            });
            break;
        }
      }
      for (let key in systemModulesObj) {
        switch (key) {
          case "/system/rms":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/rms"),
              id: 1,
              path: "/system/rms",
            });
            break;
          case "/system/api":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/api"),
              id: 2,
              path: "/system/api",
            });
            break;
          case "/system/robot":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/robot"),
              id: 3,
              path: "/system/robot",
            });
            break;
          case "/system/stop":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/stop"),
              id: 4,
              path: "/system/stop",
            });
            break;
          case "/system/monitor":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/monitor"),
              id: 5,
              path: "/system/monitor",
            });
            break;
          case "/system/URL":
            this.systemModules.push({
              label: this.secondTabLabelFun("/system/URL"),
              id: 6,
              path: "/system/URL",
            });
            break;
        }
      }
      for (let key in frontEndModulesObj) {
        switch (key) {
          case "/fesystem/hide":
            this.frontEndModules.push({
              label: this.secondTabLabelFun("/fesystem/hide"),
              id: 1,
              path: "/fesystem/hide",
            });
            break;
          case "/fesystem/base":
            this.frontEndModules.push({
              label: this.secondTabLabelFun("/fesystem/base"),
              id: 2,
              path: "/fesystem/base",
            });
            break;
        }
      }
      if (this.modules.length) {
        const key = this.modules[0].code;
        this.getModules = obj[key];
        switch (key) {
          case "map":
            this.tabModules = this.mapModules;
            break;
          case "path":
            this.tabModules = this.pathModules;
            break;
          case "task":
            this.tabModules = this.taskModules;
            break;
          case "system":
            this.tabModules = this.systemModules;
            break;
          case "fesystem":
            this.tabModules = this.frontEndModules;
            break;
        }
        this.activeName = key;
      }
      return obj;
    },
    secondTabLabelFun(path) {
      let secondTabLabel = "";
      const reg = new RegExp("/", "g");
      const newPath = path.replace(reg, ".");
      secondTabLabel = this.$t(`lang.configs${newPath}.label`);
      return secondTabLabel;
    },
    isNull(data) {
      if (Object.prototype.toString.call(data) === "[object Object]") {
        return Object.keys(data).length;
      }
      return null;
    },
    groupBy(result, group) {
      let obj = {};
      if (Array.isArray(result)) {
        result.forEach(item => {
          if (obj.list) {
            obj.list.push(item);
          } else {
            obj.list = [];
            obj.list.push(item);
          }
          // if (item[group]) {
          //   if (obj[item[group]]) {
          //     obj[item[group]].push(item);
          //   } else {
          //     obj[item[group]] = [];
          //     obj[item[group]].push(item);
          //   }
          // } else {
          //   if (obj.list) {
          //     obj.list.push(item);
          //   } else {
          //     obj.list = [];
          //     obj.list.push(item);
          //   }
          // }
        });
      }
      return obj;
    },
    getCheckStatus() {
      const tableLen = this.tableData.length;
      const checkLen = this.tableData.filter(item => item.status).length;
      this.isIndeterminate = checkLen !== 0 && checkLen !== tableLen;
      this.checkAll = tableLen === checkLen;
    },

    toggleEnableParams(scope) {
      if (!scope.row.status) {
        if (this.showDisabledParams) {
          return "row-gray";
        } else {
          return "row-hide";
        }
      }
      return "";
    },
    formatterImmediate(row, column, cellValue, index) {
      if (cellValue == 1) {
        return this.$t("lang.rms.api.result.edit.map.yes");
      } else if (cellValue == 0) {
        return this.$t("lang.venus.common.dict.no");
      }
    },
  },
};
</script>

<style lang="less" scoped>
.detail-info {
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;

  .info {
    .g-flex();
    justify-content: flex-start;
    padding: 16px 16px 5px;
    flex-wrap: wrap;

    > p {
      margin-right: 20px;
      font-size: 14px;
      font-weight: 600;
      padding: 0 0 5px;

      span {
        font-weight: 400;
        color: #666;
      }
    }
  }

  .btn-group {
    .el-button {
      line-height: 16px;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      color: #409eff;
    }

    .red-color {
      color: #f56c6c;
    }

    .spacer {
      font-style: italic;
      color: #ddd;
      margin: 0 5px;
    }
  }
}

.params-btn {
  .g-flex();
  justify-content: flex-start;
  padding-bottom: 15px;
  label {
    font-size: 14px;
    font-weight: 600;
    padding: 0 8px 0 0;
  }

  :deep(.el-input-group) {
    width: 280px;

    .el-input-group__prepend {
      width: 100px;
    }
  }

  .el-switch {
    padding-left: 26px;
    width: 30%;
  }

  .cover-original-input {
    position: absolute;
    top: 0;
    left: 100px;
    margin: 0 !important;
    z-index: 99;
    width: 134px;
    background: #fff;
    height: 30px;
    border-right: 1px solid #dcdfe6;
  }
}

.check-text {
  font-weight: 800;
  color: #555;
}

.check-btn {
  :deep(.el-checkbox__original) {
    min-height: unset;
  }
}

.content {
  height: 86%;
}
/deep/.el-table__header {
  height: 50px;
}
/deep/.el-table th > .cell {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #686c71;
}
/deep/.el-table th {
  background: #f8f9fa;
}

.editShowBox {
  display: inline-block;
  height: 30px;
  width: 100%;
  border-radius: 6px;
  background: #f2f6f8;
  input {
    display: inline-block;
    width: 61%;
    background: white;
    height: 100%;
    border-radius: 6px;
    border: 1.3px solid rgb(65, 181, 227);
    padding-left: 5px;
    line-height: 30px;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 5px;
  }
  /deep/.el-icon-check {
    color: rgb(4, 194, 55);
    font-weight: 550;
    font-size: 15px;
    margin-right: 6px;
    cursor: pointer;
  }
  /deep/.el-icon-close {
    color: rgb(228, 28, 28);
    font-weight: 500;
    font-size: 15px;
    cursor: pointer;
  }
}
/deep/.el-icon-edit-outline {
  margin-left: 6px;
  font-size: 15px;
  color: #a5a8aa;
  cursor: pointer;
  position: relative;
  top: 1px;
}
</style>
