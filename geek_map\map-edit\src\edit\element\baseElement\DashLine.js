import {LINE_CAP,LINE_JOIN} from "pixi.js";
import DL from './DL'
export default class DashLine {
  static dl = new DL()
  static render($el,paths = [],{dashArr = [7, 4],width = 1,color = 0xff0000, alpha = 1} = {}) {
    if(paths.length < 2) return
    //初始化
    this.dl.init($el,{
      dash: dashArr,
      width,
      color,
      options: {
        cap: LINE_CAP.ROUND,
        join: LINE_JOIN.ROUND
      }
    })
    paths.forEach((p,index) => {
      const {x,y} = p
      if(index === 0){
        this.dl.moveTo(x, y);
      }else{
        this.dl.lineTo(x, y);
      }
    })
    this.dl.destroy()
  }
}
