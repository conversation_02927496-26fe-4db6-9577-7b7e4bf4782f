module.exports = {
  parser: "vue-eslint-parser",
  parserOptions: {
    parser: "babel-eslint",
    sourceType: "module",
  },
  plugins: ["prettier"],
  extends: ["plugin:vue/recommended", "plugin:prettier/recommended"],
  settings: {
    "import/resolver": {
      webpack: {
        config: "./build/webpack.base.config.js",
        "config-index": 1,
      },
    },
  },
  rules: {
    // prettier
    "prettier/prettier": "error",
    // js
    "eol-last": "error",
    "no-trailing-spaces": "error",
    "comma-style": ["error", "last"],
    "comma-dangle": ["error", "always-multiline"],
    "no-multi-spaces": "error",
    // "no-undef": "error",
    // "no-unused-vars": "error",
    quotes: ["error", "double", { avoidEscape: true, allowTemplateLiterals: true }],
    indent: ["error", 2, { SwitchCase: 1, ignoredNodes: ["ConditionalExpression"] }],
    "object-curly-spacing": ["error", "always"],
    "arrow-parens": ["error", "as-needed"],
    "spaced-comment": ["error", "always"],
    // "max-len": ["error", { code: 100, ignoreUrls: true }],
    // vue
    "vue/no-v-html": "off",
    "vue/singleline-html-element-content-newline": "off",
    "vue/max-attributes-per-line": [
      "error",
      {
        singleline: 10,
        multiline: 1,
      },
    ],
    "vue/order-in-components": "off",
    "vue/require-default-prop": "off",
    "vue/html-closing-bracket-spacing": "error",
    "vue/require-prop-types": "off",
    "vue/prop-name-casing": "off",
    "vue/no-template-shadow": "off",
    "vue/no-side-effects-in-computed-properties": "off",
    "vue/no-mutating-props": "off",
    "vue/no-use-v-if-with-v-for": "off",
  },
};
