/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerAreaStop implements MRender.Layer {
  floorId: floorId;
  private floor: any;
  private container: PIXI.Container;
  private mapCore: MRender.MainCore;
  private fillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  constructor(mapCore: MRender.MainCore, floor: any) {
    this.mapCore = mapCore;
    this.floorId = floor.floorId;
    this.floor = floor;

    this.init();
  }

  init(): void {
    const mapCore = this.mapCore;
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "areaStop";
    container.interactiveChildren = false;
    container.visible = true;
    container.alpha = 0.4;
    container.zIndex = utils.getLayerZIndex("area");
    this.container = container;

    this.fillStyle.visible = true;
    this.fillStyle.color = 0xff0000;

    const layerFloor = this.floor.getLayerFloor();
    layerFloor.addChild(container);
  }

  render(arr: Array<any>): void {
    let item, cellCodes;
    for (let i = 0, len = arr.length; i < len; i++) {
      item = arr[i];
      cellCodes = item["cellCodes"];
      this.drawArea(cellCodes, item.areaId);
    }
  }

  triggerLayer(isTrigger: boolean) {
    this.container.interactiveChildren = isTrigger;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer() {
    return this.container;
  }

  repaint(): void {
    console.log("stop area repaint, mapData会处理");
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.mapCore = null;
    this.container = null;
    this.floor = null;
    this.fillStyle = null;
    this.lineStyle = null;
  }

  private drawArea(cellCodes: Array<code>, areaId: code) {
    const _this = this;
    const mapCore = _this.mapCore,
      mapData = mapCore.mapData,
      lineStyle = _this.lineStyle,
      fillStyle = _this.fillStyle;

    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    graphicsGeometry.BATCHABLE_SIZE = cellCodes.length;

    let options, nsPosition, rect;
    for (let i = 0, len = cellCodes.length; i < len; i++) {
      options = mapData.cell.getData(cellCodes[i]);
      if (!options) continue;
      nsPosition = options["nsPosition"];

      rect = new PIXI.Polygon(...nsPosition);
      graphicsGeometry.drawShape(rect, fillStyle, lineStyle);
    }
    const curStopAreaIds = mapData.stopArea.getIds();
    const isShow = curStopAreaIds.includes(areaId.toString()) ? true : false;
    const graphics: any = new PIXI.Graphics(graphicsGeometry);
    graphics.name = areaId;
    graphics.floorId = this.floorId;
    graphics.mapType = "areaStop";
    graphics.visible = isShow;
    graphics.interactive = graphics.buttonMode = true;

    mapData.stopArea.setData(areaId, {
      element: graphics,
      isShow,
    });

    this.container.addChild(graphics);
  }
}
export default LayerAreaStop;
