import * as THREE from "three";
import Base from "../../../core/abstractPlugin";

class SelectMapCellPlugin extends Base {
  constructor(options) {
    super(options);
    this._raycaster = new THREE.Raycaster();
    this._pointer = new THREE.Vector2();
    this.PluginName = "selectMapCellPlugin";
  }
  activated() {
    this.EventInstance.add("SelectMapCell", {
      clickHandle: event => this._selectMapCell(event),
    });
  }
  deactivated() {
    this.EventInstance.off("SelectMapCell");
  }
  destroyed() {
    this.EventInstance.off("SelectMapCell");
  }
  _selectMapCell(event) {
    this._pointer.set(
      (event.offsetX / this.$dom.offsetWidth) * 2 - 1,
      -(event.offsetY / this.$dom.offsetHeight) * 2 + 1,
    );
    this._raycaster.setFromCamera(this._pointer, this.Map3d.camera.get());
    const intersects = this._raycaster.intersectObject(
      this.Map3d.scene.getObjectByName("floorBox"),
      false,
    );
    if (intersects.length) {
      const { x, z } = intersects[0].point;
      const cell = this.Store.findSelectCell({ x, y: z }) || { cellCode: null };
      this.Emitter.emit("after:selectCell", cell);
    }
  }
}

export default new SelectMapCellPlugin();
