/* ! <AUTHOR> at 2022/08/29 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { DownOutlined } from "@ant-design/icons";
import { Button, Dropdown, Menu } from "antd";
import { getMap2D, $eventBus } from "../../../singleton";

function FloorListBox() {
  const { t } = useTranslation();
  const [floorList, setFloorList] = useState([]);
  const [floorText, setFloorText] = useState("ALL");

  const menuClick = ({ key }: any) => {
    setFloorText(key);
    const map2D = getMap2D();
    let floorIds = [];
    if (key !== "ALL") floorIds.push(Number(key));

    $eventBus.emit("wsMapLoading", { loading: true }); // 还有可能断线重连呢
    $eventBus.emit("wsMapFloorChangeRight", { floorChanging: true });
    map2D.mapWorker.reqFloors(floorIds);
  };

  /** 获取floorList，即floorId的信息 */
  useEffect(() => {
    $eventBus.on("wsFloorListTop", floorList => {
      let arr = [{ label: t("lang.rms.fed.buttonGeneralView"), key: "ALL", disabled: false }];
      floorList.forEach((item: any) => {
        arr.push({
          key: item.floorId,
          label: t("lang.rms.fed.buttonFloor") + " : " + item.floorId,
          disabled: !item.enable,
        });
      });
      setFloorList(arr);
    });
    return () => {
      $eventBus.off("wsFloorListTop");
    };
  }, []);

  return (
    <Dropdown
      className="map2d-top-floor-change"
      overlay={
        <Menu
          items={floorList}
          defaultSelectedKeys={["ALL"]}
          selectable
          onSelect={menuClick}
          className="map2d-top-panel-dropdown"
        />
      }
      placement="bottomRight"
      arrow
    >
      <Button size="small" style={{ fontSize: "13px" }}>
        {t("lang.rms.fed.buttonFloor")}
        <span style={{ paddingLeft: "5px" }}>{floorText}</span>
        <DownOutlined />
      </Button>
    </Dropdown>
  );
}

export default FloorListBox;
