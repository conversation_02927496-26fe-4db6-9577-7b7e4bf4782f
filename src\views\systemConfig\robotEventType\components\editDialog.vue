<template>
  <el-dialog
    :title="$t('lang.rms.fed.robotEventType')"
    :visible.sync="dialogVisible"
    :append-to-body="true"
    :before-close="close"
    :close-on-click-modal="false"
    width="50%"
  >
    <el-form ref="dataForm" :model="formData" :rules="rules" :inline="true" label-position="top" label-width="80px">
      <el-form-item :label="$t('lang.rms.fed.eventGroupName')" prop="eventGroupNameI18n">
        <el-input v-model="formData.eventGroupNameI18n" :disabled="pageType === 'edit'" />
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.eventGroupValue')" prop="eventGroup">
        <el-input v-model="formData.eventGroup" />
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.eventTypeName')" prop="eventTypeNameI18n">
        <el-input v-model="formData.eventTypeNameI18n" :disabled="pageType === 'edit'" />
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.eventTypeValue')" prop="eventType">
        <el-input v-model="formData.eventType" />
      </el-form-item>
      <el-form-item :label="$t('lang.rms.fed.eventDes')" prop="eventDesc">
        <el-input v-model="formData.eventDesc" />
      </el-form-item>
      <el-form-item v-show="!isEdit" :label="$t('lang.rms.fed.enable.saveDatabase')" prop="enableSaveDatabase">
        <el-switch v-model="formData.enableSaveDatabase" />
      </el-form-item>
      <el-form-item v-show="!isEdit" :label="$t('lang.rms.fed.enable.publish')" prop="enablePublish">
        <el-switch v-model="formData.enablePublish" />
      </el-form-item>
      <el-form-item v-show="!isEdit" :label="$t('lang.rms.fed.robot.event.type.enableCallback')" prop="enableCallback">
        <el-switch v-model="formData.enableCallback" />
      </el-form-item> 
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close"> {{ $t("lang.rms.fed.cancel") }}</el-button>
      <el-button type="primary" @click="save">{{ $t("lang.rms.fed.confirm") }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
// TODO 后续优化统一风格
export default {
  name: "RobotEventTypeEditDialog",
  data() {
    return {
      dialogVisible: false,

      formList: {
        id: null,
        robotId: null, // 需要注册到系统的机器人id
        robotType: null, // 机器人类型
        sizeTypes: null, // 机器人能在哪些尺寸的单元格上行走，多值可以用逗号分隔：A,B
        classCodes: null, // 机器人能搬运哪些类别的货架，多值用逗号分隔：A,B
        warehouseId: null, // 机器人所在物理仓库id
        status: 0, // 记录状态
      },
      formData: {
        enableCallback: false,
        enablePublish: false,
        enableSaveDatabase: false,
        eventDesc: "",
        eventGroup: "",
        eventGroupNameI18n: "",
        eventType: "",
        eventTypeNameI18n: "",
      },
      rules: {
        // eventGroupNameI18n: [
        //   { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
        //   { max: 64, message: this.$t("lang.rms.fed.lengthLimit64"), trigger: "blur" },
        // ],
        eventGroup: [
          { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
          { max: 64, message: this.$t("lang.rms.fed.lengthLimit64"), trigger: "blur" },
        ],
        // eventTypeNameI18n: [
        //   { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
        //   { max: 64, message: this.$t("lang.rms.fed.lengthLimit64"), trigger: "blur" },
        // ],
        eventType: [
          { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
          { max: 64, message: this.$t("lang.rms.fed.lengthLimit64"), trigger: "blur" },
        ],
        // eventDesc: [
        //   { required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" },
        //   { max: 256, message: this.$t("lang.rms.fed.lengthLimit256"), trigger: "blur" },
        // ],
        enableSaveDatabase: [{ required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" }],
        enablePublish: [{ required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" }],
        enableCallback: [{ required: true, message: this.$t("lang.rms.fed.isRequired"), trigger: "blur" }],
      },

      pageType: "add",
    };
  },
  computed:{
    isEdit(){
      return this.pageType === 'edit'
    }
  },
  methods: {
    open(data) {
      if (data) {
        this.pageType = "edit";
        this.formData = JSON.parse(JSON.stringify(data));
        this.formData.enableSaveDatabase = Boolean(this.formData.enableSaveDatabase);
        this.formData.enablePublish = Boolean(this.formData.enablePublish);
        this.formData.enableCallback = Boolean(this.formData.enableCallback);
      } else {
        this.pageType = "add";
        this.formData = {
          enableCallback: false,
          enablePublish: false,
          enableSaveDatabase: false,
          eventDesc: "",
          eventGroup: "",
          eventGroupNameI18n: "",
          eventType: "",
          eventTypeNameI18n: "",
        };
      }
      this.dialogVisible = true;
    },
    close() {
      this.$refs.dataForm.resetFields();
      this.dialogVisible = false;
    },
    // 保存
    save() {
      this.$refs["dataForm"].validate(valid => {
        if (valid) {
          this.submit();
        } else {
          return false;
        }
      });
    },
    submit() {
      const param = this.formData;
      const data = {
        enableCallback: param.enableCallback,
        enablePublish: param.enablePublish,
        enableSaveDatabase: param.enableSaveDatabase,
        eventDesc: param.eventDesc,
        eventGroup: param.eventGroup,
        eventGroupNameI18n: param.eventGroupNameI18n,
        eventType: param.eventType,
        eventTypeNameI18n: param.eventTypeNameI18n,
      };
      if (param.id) data.id = param.id;

      $req.post("/athena/robot/robotEventType/saveOrUpDate", data).then(res => {
        if (res.code !== 0) return;
        this.$emit("updateTableList");
        this.$success(this.$t(res.msg));
        this.dialogVisible = false;
      });
    },
  },
};
</script>

<style lang="less" scoped>
:deep(.el-form-item) {
  width: 22%;
}

:deep(.el-form-item__label) {
  padding-bottom: 0;
  font-size: 13px;
  font-weight: 800;
}
</style>
