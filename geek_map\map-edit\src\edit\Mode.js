import Event from './event/Event'
import Selected from './selected/Selected'
import LayerManager from "./layerManager/LayerManager";
import EventBus from './eventBus/EventBus'
export default class Mode {
  static mode = {
    action: 'DEFAULT',
    options: null
  }
  static changeMode(mode) {
    this.mode = mode
    Event.reset()
    Selected.resetAllSelected()
    Event.bindEventByMode(mode)
  }
  static resetMode() {
    this.mode = {
      action: 'DEFAULT',
      options: null
    }
    Event.reset()
    const operateLayerInstance = LayerManager.get('OPERATE')
    operateLayerInstance.removeChildren()
    Event.bindEventByMode(this.mode)
    EventBus.$emit('modeReset')
  }
}
