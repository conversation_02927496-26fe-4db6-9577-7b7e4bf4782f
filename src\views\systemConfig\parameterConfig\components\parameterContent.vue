<template>
  <div v-show="show" class="box">
    <el-radio-group v-model="tabContent" style="margin-bottom: 20px">
      <el-radio-button
        v-for="(item, index) in modules"
        :key="index"
        :label="item.id"
        @click.native="tabClick(item.path)"
      >
        {{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <el-card
      v-for="(item, index) in modules"
      v-show="tabContent == item.id"
      :key="index"
      class="box-card"
    >
      <div slot="header" class="clearfix">
        <span class="title">{{ item.label }}</span>
        <sapn class="note"><i class="d" />标红表示当前参数暂未生效，重启以后生效</sapn>
        <el-button style="float: right; margin-left: 10px" size="mini">{{
          $t("lang.rms.fed.cancel")
        }}</el-button>
        <el-button type="primary" style="float: right" size="mini">{{
          $t("lang.rms.fed.application")
        }}</el-button>
      </div>
      <parameterForm :path="modulesPath"></parameterForm>
    </el-card>
  </div>
</template>

<script>
import parameterForm from "./parameterForm";
export default {
  components: {
    parameterForm,
  },
  props: {
    show: {
      type: Boolean,
      default() {
        return false;
      },
    },
    modules: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      tabContent: 1,
      modulesPath: "",
    };
  },
  created() {},
  mounted() {},
  methods: {
    tabClick(path) {
      this.modulesPath = path;
      console.log("this.modulesPath", this.modulesPath);
    },
  },
  watch: {},
};
</script>

<style lang="less" scoped>
.box {
  width: 100%;
  height: 100%;
  margin-left: 10px;
}
.box-card {
  border-radius: 13px;
  margin-top: -5px;
  height: 65%;
  padding-bottom: 60px;
}
.box-card :deep(.el-card__body) {
  height: 100%;
  overflow: auto;
}
.title {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #323334;
}
:deep(.el-card__header) {
  padding: 12px 20px;
}
.d {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #f23f3f;
  border-radius: 6px;
  margin: 0px 6px 1px 16px;
}
.note {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #999ea5;
}
</style>
