<template>
  <section v-show="dialogVisible" class="pallet-detail">
    <div class="detail-head">
      <h5>{{ title }}</h5>
      <div>
        <el-button type="primary" size="small" @click="save">
          {{ $t("lang.rms.fed.save") }}
        </el-button>
        <el-button size="small" @click="cancel">
          {{ $t("lang.common.cancel") }}
        </el-button>
      </div>
    </div>

    <el-alert
      :title="$t('lang.rms.palletPositionManage.baseinfo')"
      type="info"
      :closable="false"
      style="margin: 10px 0"
    />

    <geek-customize-form ref="refForm" :form-config="formConfig" style="width: 600px">
      <template #locationCellCode>
        <el-input :value="locationCellCode" :readonly="true" :placeholder="$t('lang.rms.fed.choose')">
          <el-button slot="append" icon="el-icon-search" @click="openMapSelect" />
        </el-input>
      </template>
    </geek-customize-form>

    <keep-alive>
      <Map2DSelect ref="mapSelectDialog" :multiple="false" @change="value => onSelect(value)" />
    </keep-alive>
  </section>
</template>
<script>
import Map2DSelect from "geek-monitor2d-select";

export default {
  components: { Map2DSelect },
  data() {
    return {
      dialogVisible: false,
      type: "",
      locationCellCode: "",

      rowData: {},
    };
  },
  computed: {
    title() {
      const type = this.type;
      switch (type) {
        case "add":
          return this.$t("lang.rms.fed.add");
        case "edit":
          return this.$t("auth.rms.mapManage.button.edit");
        default:
          return "--";
      }
    },

    formConfig() {
      let disabled = false;
      if (this.operation == "view") disabled = true;

      return {
        attrs: {
          labelWidth: "200px",
          labelPosition: "right",
          disabled,
        },
        configs: {
          palletRackCode: {
            label: "lang.rms.palletPositionManage.palletRackCode",
            default: "",
            tag: "input",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterContent"),
                trigger: "change",
              },
            ],
          },
          locationCellCode: {
            label: "lang.rms.palletRackManage.locationCellCode",
            slotName: "locationCellCode",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterContent"),
                trigger: "change",
              },
            ],
          },
          angle: {
            label: "lang.rms.palletRackManage.angle",
            default: "",
            tag: "input",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterContent"),
                trigger: "change",
              },
              {
                pattern: /^([0-9]|[1-9][0-9]|[1-2][0-9][0-9]|[3][0-5][0-9]|(360))$/,
                message: this.$t("lang.rms.fed.pleaseEnter11"),
              },
            ],
          },
          totalFloors: {
            label: "lang.rms.palletRackManage.totalFloors",
            default: "",
            tag: "input",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.fed.pleaseEnterContent"),
                trigger: "change",
              },
              {
                pattern: /^[1-9]\d*$/,
                message: this.$t("lang.rms.fed.pleaseEnterAnNumber"),
              },
            ],
          },
          length: {
            label: "lang.rms.web.monitor.cell.fieldPrefix.length",
            default: "",
            tag: "input",
            rules: [
              {
                pattern: /(^[0-9]*$)|(^[0-9]*[.]{1}[0-9]{1}$)/,
                message: this.$t("lang.rms.fed.pleaseEnter12"),
              },
            ],
          },
          width: {
            label: "lang.rms.web.monitor.cell.fieldPrefix.width",
            default: "",
            tag: "input",
            rules: [
              {
                pattern: /(^[0-9]*$)|(^[0-9]*[.]{1}[0-9]{1}$)/,
                message: this.$t("lang.rms.fed.pleaseEnter12"),
              },
            ],
          },
          obstacle: {
            label: "lang.rms.palletRackManage.isObstacle",
            tag: "select",
            default: "",
            options: [
              { value: true, label: "lang.rms.fed.yes" },
              { value: false, label: "lang.rms.fed.no" },
            ],
          },
        },
      };
    },
  },
  methods: {
    open(type, row) {
      this.type = type;
      this.rowData = row || {};
      this.dialogVisible = true;

      const params = {
        palletRackCode: row?.palletRackCode || "",
        totalFloors: row?.totalFloors || "",
        length: row?.length || "",
        width: row?.width || "",
        obstacle: row?.isObstacle || "",
        angle: row?.angle || "",
        locationCellCode: row?.locationCellCode || "",
      };

      this.locationCellCode = row?.locationCellCode || "";
      this.$nextTick(() => {
        this.$refs.refForm.reset();
        this.$refs.refForm.setData(params);
      });
    },

    openMapSelect() {
      this.$refs.mapSelectDialog.open({
        wsUrl: this.getWsUrl(),
        mapData: null,
        codes: this.locationCellCode ? this.locationCellCode : "",
      });
    },
    onSelect(cellDatas) {
      let value = "";
      if (cellDatas.length > 0) {
        if (cellDatas.length === 1) {
          value = cellDatas[0].cellCode;
        } else {
          cellDatas.map(item => {
            if (value === "") {
              value = item.cellCode;
            } else {
              value += "," + item.cellCode;
            }
          });
        }
      }

      this.locationCellCode = value;
      this.$refs.refForm.setData({ locationCellCode: value });
    },
    save() {
      this.$refs.refForm.validate().then(data => {
        const url = this.type === "add" ? "/athena/palletRack/add" : "/athena/palletRack/update";
        const formData = Object.assign({}, this.rowData, data);

        $req.post(url, formData).then(res => {
          if (res.code !== 0) return;
          this.$success();
          this.cancel();
          this.$emit("updateList");
        });
      });
    },
    // 取消
    cancel() {
      this.dialogVisible = false;
      this.$emit("update:listVisible", true);
    },
    getWsUrl() {
      let protocol = window.location.protocol === "http:" ? "ws" : "wss";
      let hostname = window.location.host;
      if ($req.isDev) {
        hostname = new URL($req.API_URL).hostname;
      }

      const RMSPermission = $utils.Data.getRMSPermission();
      const token = RMSPermission ? `?token=${$utils.Data.getToken()}` : "";

      return `${protocol}://${hostname}/athena-monitor${token}`;
    },
  },
};
</script>

<style lang="less" scoped>
.pallet-detail {
  width: 100%;
  padding: 10px 15px;
  background: #fff;

  .detail-head {
    .g-flex();
    h5 {
      font-size: 16px;
      display: flex;
      align-items: center;
      &::before {
        content: "";
        display: inline-block;
        height: 21px;
        width: 4px;
        border-radius: 4px;
        background: #409eff;
        margin-right: 10px;
        vertical-align: text-bottom;
      }
    }
  }
}
</style>
