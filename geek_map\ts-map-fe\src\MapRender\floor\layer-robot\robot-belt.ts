/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerRobotBelt implements MRender.Layer {
  private utils: any;
  private container: PIXI.Container;
  private shader: any;
  private fragment: number;
  private geometries: Array<any> = [];
  private meshList: any = [];
  init(mapCore: MRender.MainCore): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "robotBelt";
    container.zIndex = utils.getLayerZIndex("robot");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.shader = utils.getShader("icon", utils.getResources("robot_belt"));
    this.utils = utils;
  }

  render(): void {
    const _this = this;
    const geometries = _this.geometries;
    if (!geometries.length) return;

    const utils = _this.utils;
    const fragment = _this.fragment;
    const shader = _this.shader;

    for (let i = 0, len = Math.ceil(geometries.length / fragment); i < len; i++) {
      const arr = geometries.slice(i * fragment, i * fragment + fragment);

      let mesh = utils.createMesh(arr, shader);
      mesh.name = "robotBelt";
      mesh.mapType = "robotBelt";
      mesh.interactive = mesh.buttonMode = false;

      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }
    this.geometries = [];
  }

  drawGeometry(options: mRobotData): void {
    const _this = this;
    const position = _this.formatBeltPosition(options);
    const geometry = _this.utils.drawGeometry("icon", position);
    _this.geometries.push(geometry);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.geometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.geometries = null;
    this.utils = null;
    this.shader = null;
    this.meshList = null;
  }

  private formatBeltPosition(options: mRobotData) {
    const { width, height, location, beltDir } = options;
    const boxSize = Math.min(width, height) * 0.5;
    let x = Number((location.x - boxSize / 2).toFixed(3));
    let y = Number((location.y - boxSize / 2).toFixed(3));
    let x1 = Number((x + boxSize).toFixed(3));
    let y1 = Number((y + boxSize).toFixed(3));

    // 左上、右上、右下、左下
    switch (beltDir) {
      case 90:
        return [x1, y, x1, y1, x, y1, x, y]; // 90
      case 180:
        return [x1, y1, x, y1, x, y, x1, y]; // 180
      case 270:
        return [x, y1, x, y, x1, y, x1, y1]; // 270
      default:
        return [x, y, x1, y, x1, y1, x, y1]; // 0
    }
  }
}
export default LayerRobotBelt;
