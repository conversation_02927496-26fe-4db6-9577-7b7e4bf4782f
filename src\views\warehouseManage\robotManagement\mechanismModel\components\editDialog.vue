<template>
  <el-dialog
    :title="$t(title)"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="520px"
  >
    <geek-customize-form ref="refForm" :form-config="formConfig">
      <template #title1="{ config }">
        <span class="f-title">{{ $t(config.label) }}:</span>
      </template>
      <template #file>
        <el-upload
          v-if="!file && !rowFile"
          action="javascript:void(0)"
          drag
          accept=".glb, .gltf"
          :limit="1"
          :before-upload="() => false"
          :on-change="handleUpload"
          class="edit-upload"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">
            <p>{{ $t("lang.rms.api.result.warehouse.dragFileHere") }}</p>
            <p style="font-size: 12px; color: #bbb">
              {{ $t("lang.rms.api.result.warehouse.uploadOntologyModel") }}
            </p>
          </div>
        </el-upload>
        <el-tag v-else closable @close="delFile">
          {{ file || rowFile }}
        </el-tag>
      </template>
      <template #title2="{ config }">
        <span class="f-title">{{ $t(config.label) }}:</span>
      </template>
      <template #title3="{ config }">
        <span class="f-title">{{ $t(config.label) }}:</span>
      </template>
    </geek-customize-form>

    <span slot="footer" class="dialog-footer">
      <el-button @click="close">{{ $t("lang.common.cancel") }}</el-button>
      <el-button type="primary" @click="save">
        {{ $t("lang.rms.fed.confirm") }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: "EditMechanismModelDialog",
  data() {
    return {
      dialogVisible: false,
      operation: "",
      file: "",
      rowFile: "",
      rowData: {},
      behavior: [],
    };
  },
  computed: {
    title() {
      switch (this.operation) {
        case "add":
          return "lang.rms.api.result.warehouse.createRobotMechanismModel";
        case "edit":
          return "lang.rms.api.result.warehouse.editRobotMechanismModel";
        case "view":
          return "lang.rms.api.result.warehouse.viewRobotMechanismModel";
        default:
          return "lang.rms.api.result.warehouse.createRobotMechanismModel";
      }
    },

    formConfig() {
      let disabled = false;
      if (this.operation == "view") disabled = true;
      return {
        attrs: {
          labelWidth: "160px",
          labelPosition: "right",
          disabled,
        },
        configs: {
          title1: {
            label: "lang.rms.api.result.warehouse.baseProperies",
            slotTitle: "title1",
          },
          name: {
            label: "lang.rms.api.result.warehouse.mechanism.spuName",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterInstitutionSPU",
            rules: [
              {
                required: true,
                message: this.$t("lang.rms.web.robot.pleaseEnterProductSpu"),
                trigger: "blur",
              },
            ],
          },
          file: {
            label: "lang.rms.api.result.warehouse.robotMechanismModel",
            slotTitle: "file",
          },
          title2: {
            label: "lang.rms.api.result.warehouse.physicalProperty",
            slotTitle: "title2",
          },
          length: {
            label: "lang.rms.fed.length",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMerchanismLength",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (value == 0) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          width: {
            label: "lang.rms.fed.width",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMerchanismWidth",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (value == 0) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          height: {
            label: "lang.rms.fed.high",
            default: "",
            tag: "input",
            required: true,
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMerchanismHeight",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (value == 0) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          maxLoad: {
            label: "lang.rms.api.result.warehouse.maxLoad",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMerchanismMaxLoad",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          maxWorkHeight: {
            label: "lang.rms.fed.maxWorkingHeight",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.maxWorkingHeight",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          diameter: {
            label: "lang.rms.fed.rotationDiameter",
            default: "",
            tag: "input",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          locationOffset: {
            label: "lang.rms.fed.locationOffset",
            default: "",
            tag: "input",
            rules: [
              {
                trigger: "change",
                validator: (rule, value, callback) => {
                  if (!value) callback();
                  else if (!/^[1-9]\d*$/.test(value)) {
                    callback(new Error(this.$t("lang.rms.fed.pleaseEnterAnNumber")));
                  } else callback();
                },
              },
            ],
          },
          dockingSides: {
            label: "lang.rms.fed.dockingSide",
            tag: "select",
            default: "",
            multiple: true,
            placeholder: "lang.rms.fed.dockingSide",
            options: [
              { value: "F", label: "F" },
              { value: "B", label: "B" },
              { value: "L", label: "L" },
              { value: "R", label: "R" },
            ],
          },
          deliverStrategy: {
            label: "lang.rms.fed.deliverStrategy.aGetaDrop",
            tag: "select",
            default: "",
            options: [
              { value: 1, label: "lang.rms.fed.yes" },
              { value: 0, label: "lang.rms.fed.no" },
            ],
          },
          hasBackupSensor: {
            label: "lang.rms.fed.isHasBackupSensor",
            tag: "switch",
            default: false,
          },
          title3: {
            label: "lang.rms.fed.mechanismCapability",
            slotTitle: "title3",
          },
          controlAbilities: {
            label: "lang.rms.api.result.warehouse.movementAbility",
            tag: "select",
            default: "",
            multiple: true,
            options: [
              { value: "BEEP", label: "lang.rms.api.result.warehouse.whistle" },
              { value: "LIGHT", label: "lang.rms.api.result.warehouse.light" },
            ],
          },
          actionAbilities: {
            label: "lang.rms.api.result.warehouse.capacity",
            tag: "select",
            default: "",
            multiple: true,
            required: true,
            options: this.behavior,
          },
        },
      };
    },
  },
  methods: {
    open(type, data) {
      this.getDictionary();
      this.operation = type;
      this.rowData = data || {};
      this.dialogVisible = true;
      this.rowFile = data.mechanismImage || "";

      const params = {
        name: data?.name || "",
        length: data.hasOwnProperty("length") ? data.length : "",
        width: data.hasOwnProperty("width") ? data.width : "",
        height: data.hasOwnProperty("height") ? data.height : "",
        maxLoad: data.hasOwnProperty("maxLoad") ? data.maxLoad : "",
        maxWorkHeight: data.hasOwnProperty("maxWorkHeight") ? data.maxWorkHeight : "",
        diameter: data.hasOwnProperty("diameter") ? data.diameter : "",
        locationOffset: data.hasOwnProperty("locationOffset") ? data.locationOffset : "",
        dockingSides: data?.dockingSides || "",
        deliverStrategy: data?.deliverStrategy || "",
        hasBackupSensor: data.hasBackupSensor == 1 ? true : false,
        controlAbilities: data?.controlAbilities || "",
        actionAbilities: data?.actionAbilities || "",
      };

      this.$nextTick(() => {
        this.$refs.refForm.reset();
        this.$refs.refForm.setData(params);
      });
    },
    close() {
      this.file = "";
      this.rowFile = "";
      this.dialogVisible = false;
    },
    // 保存
    save() {
      if (this.operation == "view") {
        this.close();
        return;
      }

      this.$refs.refForm.validate().then(data => {
        let params = Object.assign({}, data, {
          hasBackupSensor: data.hasBackupSensor ? 1 : 0,
        });
        if (this.operation == "edit") params.id = this.rowData.id;

        const formData = new FormData();
        for (const key in params) {
          formData.append(key, params[key] ?? "");
        }
        if (this.file) formData.append('file', this.file);
        $req.post("/athena/robot/manage/mechanismSave", formData).then(res => {
          this.$success();
          this.close();
          this.$emit("updateList");
        });
      });
    },

    handleUpload(file) {
      if (file.size >= 52428800) {
        this.$error(this.$t("lang.rms.api.result.warehouse.uploadOntologyModel"));
        return;
      }

      const demoData = new FormData();
      demoData.append("file", file.raw);
      this.file = demoData.get("file");
    },
    delFile() {
      this.file = "";
      this.rowFile = "";
    },

    getDictionary() {
      $req.post("/athena/dict/query", { types: ["ROBOT_BEHAVIOR"] }).then(res => {
        if (res.code !== 0) return;
        const list = res?.data["ROBOT_BEHAVIOR"] || [];
        this.behavior = list.map(item => {
          let label = item.descr && this.$t(item.descr) !== item.descr
            ? `${item.fieldValue}(${this.$t(item.descr)})`
            : `${item.fieldValue}(${item.fieldValue})`
          return { label: label, value: item.fieldValue }
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.f-title {
  font-weight: 700;
  padding-bottom: 12px;
  display: block;
  color: #929292;
  font-size: 16px;
}
.edit-upload {
  :deep(.el-upload) {
    width: 100%;
    .el-upload-dragger {
      width: unset;
    }
  }
}
</style>
