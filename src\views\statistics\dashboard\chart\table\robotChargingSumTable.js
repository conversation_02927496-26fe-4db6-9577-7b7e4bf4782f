import Chart, { requestCache } from "../common";

/**
 * 3.2 机器人充电汇总
 */
export default class RobotChargingSumTable extends Chart {
  /**
   * 初始化图表 - 3.2 机器人充电无电量统计
   * @param {Object} option 
   * @param {number|string} option.width  宽度, 1~48 或 px
   * @param {number|string} option.height  宽度, 1~48 或 px
   * @param {string} option.intervalTimer  轮询时间
   * @param {boolean} option.isFilterParams  是否过滤请求参数
   * @param {array} option.filterList  过滤的请求参数keys
   * @param {string} option.title
   * @param {number} option.x  x坐标(暂时不用了)
   * @param {number} option.y  y坐标(暂时不用了)
   */
  constructor(option = {}) {
    super('table', { x: 0, y: 0, width: 8, height: 6, ...option });

    this.title = option.title || "机器人充电汇总统计";
    
    this.filterList = [
      {
        label: '日期',
        prop: 'date',
        type: 'elDatePicker',
        defaultValue: [new Date().setHours(0, 0, 0, 0) - 86400000 * 7, new Date().setHours(23, 59, 59, 0)],
        option: {
          type: "datetimerange"
        }
      },
      // 机器人类型
      // https://confluence.geekplus.cc/pages/viewpage.action?pageId=177542368
      // athena/map/version/findRobotType
    ]
  }

  async request(params) {
    const date = params?.date || [];
    let startTime = +date[0] || new Date().setHours(0, 0, 0, 0) - 86400000 * 7;
    let endTime = +date[1] || new Date().setHours(23, 59, 59, 0);

    // 需要请求  执行中的任务数和已经下发的任务数
    const { data } = await requestCache('/athena/stat/charger/sumByRobotId', {
      startTime,
      endTime,
      currentPage: 1,
      pageSize: 20,
      ...params
    })
    
    return data;
  }

  /**
   * 这里进行接口数据的处理
   * @param {*} data 
   * @returns 
   */
  async handler(data) {
    const dataItem = [];
    Object.values(data).forEach(item => {
      dataItem.push(...item)
    })

    let tableCloumns = [
      { label: '机器人编号', prop: 'robotId' },
      { label: '总充电次数', prop: 'chargeCount' },
      { label: '充电成功次数', prop: 'chargeSuccessCount' },
      { label: '充电成功率', prop: 'chargeSuccessProportion' },
      { label: '总充电时长(S)', prop: 'chargeDuration' },
      { label: '电池充电循环', prop: 'batteryCycles' },
      { label: '电池健康度', prop: 'batterySoh' },
    ];
    return {
      title: this.title,
      tableCloumns,
      tableData: dataItem,
    }
  }
}