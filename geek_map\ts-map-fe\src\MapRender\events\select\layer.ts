/* ! <AUTHOR> at 2022/09/02 */
import * as PIXI from "pixi.js";

class LayerSelected {
  private mapCore: MRender.MainCore;
  private vColor: number;
  private selectedTexture: any;
  private destTexture: any;
  private store: any;
  private destCircle: any = null;
  private destText: any = null;
  constructor(mapCore: MRender.MainCore, store: any) {
    this.mapCore = mapCore;
    this.store = store;

    const utils = mapCore.utils;

    this.selectedTexture = utils.getResources("status_selected");
    this.destTexture = utils.getResources("dest");
    this.vColor = utils.getOriginColor("SELECTED");
  }

  render(layerName: MRender.layerName, data: selectData): void {
    const store = this.store;
    const mapConfig: MRender.RenderConfigMain = this.mapCore.mapConfig;

    const isMulti = mapConfig.getRenderConfig("isMultiSelect");
    const multiLayers = mapConfig.data.getMultiLayers();
    const code = data["code"];
    const selectCode = `${layerName}__${code}`;

    if (isMulti && multiLayers) {
      if (multiLayers.includes(layerName)) {
        const select = store.getSelect(selectCode);
        if (select) {
          store.removeSelects(layerName, [code]);
          return;
        }
      } else {
        store.removeSelects(layerName);
      }
    } else if (isMulti) {
      const select = store.getSelect(selectCode);
      if (select) {
        store.removeSelects(layerName, [code]);
        return;
      }
    } else {
      this.repaint();
    }

    let sprite: any, placement: any;
    switch (layerName) {
      case "cell":
        sprite = this.renderCellSelected(data as mCellData);
        this.removeDestCircle();
        mapConfig.data.setDestCell(data);
        break;
      case "shelf":
        sprite = this.renderMeshSelected(data as mShelfData);
        placement = this.renderPlacement(data as mShelfData);
        break;
      case "rack":
        sprite = this.renderMeshSelected(data as mRackData);
        break;
      case "robot":
      case "charger":
      case "device":
        sprite = this.renderSpriteSelected(data as mRobotData | mChargerData | mDeviceData);
        break;
    }

    if (sprite) store.setSelect(selectCode, sprite);
    if (placement) store.setPlacement(selectCode, placement);
  }

  /** 只有shelf robot poppick 才会有 select 更新 */
  update(layerName: MRender.layerName, options: selectData): boolean {
    let select: any;
    select = this.store.getSelect(`${layerName}__${options.code}`);
    if (!select) return false;

    const floorId = options.floorId;
    if (select.floorId !== floorId) {
      select.parent && select.parent.removeChild(select);
      const mapFloor = this.mapCore.mapFloors[floorId];
      const selectLayer = mapFloor?.getLayerFloor("select");
      selectLayer.addChild(select);
    }
    switch (layerName) {
      case "shelf":
        const { hitArea } = options as mShelfData;
        select.position.set(hitArea[0], hitArea[1]);
        break;
      case "robot":
        const { position: p, radAngle: r } = options as mRobotData;
        select.position.set(p.x, p.y); // 使图片居中
        if (r) select.rotation = r; // 设置角度
        break;
    }

    return true;
  }

  renderDestCircle(options: mCellData, destNum: number) {
    if (this.destCircle) return;

    const mapFloor = this.mapCore.mapFloors[options["floorId"]];
    const selectLayer = mapFloor?.getLayerFloor("select");
    if (!selectLayer) return false;

    const hitArea = options["hitArea"];
    const x = hitArea[0],
      y = hitArea[1],
      width = hitArea[2] - x,
      height = hitArea[3] - y;

    let position = { x: x + width / 2, y: y + height / 2 };
    let size = Math.max(width, height) * 2;

    let destCircle: any = new PIXI.Sprite(this.destTexture);
    destCircle.width = size;
    destCircle.height = size;
    destCircle.position.set(position.x, position.y); // 使图片居中
    destCircle.anchor.set(0.5, 0.5);
    destCircle.tint = 0x31824;
    destCircle.alpha = 0.6;

    let destText = new PIXI.Text(destNum.toString(), {
      fontFamily: "Arial",
      fontSize: 60,
      fill: 0xffffff,
    });
    destText.anchor.set(0.5, 0.5);
    destText.setTransform(position.x, position.y, 0.015, 0.015);
    selectLayer.addChild(destCircle, destText);

    this.destCircle = destCircle;
    this.destText = destText;
  }
  removeDestCircle() {
    const destCircle = this.destCircle;
    const destText = this.destText;
    if (destCircle) {
      destCircle.parent && destCircle.parent.removeChild(destCircle);
      destCircle.destroy();
      this.destCircle = null;
    }
    if (destText) {
      destText.parent && destText.parent.removeChild(destCircle);
      destText.destroy();
      this.destText = null;
    }
  }

  toggle(isShow: boolean): void {}

  getContainer() {}

  repaint(): void {
    this.store.removeSelects();
  }

  destroy(): void {
    this.repaint();
    this.removeDestCircle();

    this.mapCore = null;
    this.store = null;
    this.vColor = null;
    this.selectedTexture = null;
    this.destTexture = null;
    this.destCircle = null;
    this.destText = null;
  }

  private renderSpriteSelected(options: mRobotData | mChargerData | mDeviceData): any {
    const { floorId, width, height, radAngle, position, anchor } = options;
    const mapFloor = this.mapCore.mapFloors[floorId];
    const selectLayer = mapFloor?.getLayerFloor("select");
    if (!selectLayer) return false;

    let sprite: any = new PIXI.Sprite(this.selectedTexture);
    sprite.floorId = floorId;
    sprite.width = width;
    sprite.height = height;
    sprite.position.set(position.x, position.y); // 使图片居中
    if (radAngle) sprite.rotation = radAngle; // 设置角度
    if (anchor) sprite.anchor.set(...anchor);
    else sprite.anchor.set(0.5, 0.5);
    sprite.tint = this.vColor;

    selectLayer.addChild(sprite);
    return sprite;
  }

  private renderMeshSelected(options: mRackData | mShelfData): any {
    const mapFloor = this.mapCore.mapFloors[options["floorId"]];
    const selectLayer = mapFloor?.getLayerFloor("select");
    if (!selectLayer) return false;

    const hitArea = options["hitArea"];
    const x = hitArea[0],
      y = hitArea[1],
      width = hitArea[2] - x,
      height = hitArea[3] - y;

    let sprite = new PIXI.Sprite(this.selectedTexture);
    sprite.width = width;
    sprite.height = height;
    sprite.position.set(x, y); // 使图片居中
    sprite.tint = this.vColor;
    selectLayer.addChild(sprite);
    return sprite;
  }

  private renderPlacement(options: mShelfData) {
    let cellCode = options["placementCell"];
    let cellData = this.mapCore.mapData.cell.getData(cellCode);
    if (cellData) {
      return this.renderCellSelected(cellData);
    }
    return false;
  }

  private renderCellSelected(options: mCellData): any {
    const mapFloor = this.mapCore.mapFloors[options["floorId"]];
    const selectLayer = mapFloor?.getLayerFloor("select");
    if (!selectLayer) return false;
    const hitArea = options["hitArea"];
    const x = hitArea[0],
      y = hitArea[1],
      width = hitArea[2] - x,
      height = hitArea[3] - y;

    let sprite = new PIXI.Sprite(PIXI.Texture.WHITE);
    sprite.width = width;
    sprite.height = height;
    sprite.position.set(x, y); // 使图片居中
    sprite.tint = this.vColor;
    selectLayer.addChild(sprite);
    return sprite;
  }
}

type selectData = mCellData | mRackData | mShelfData | mRobotData | mChargerData | mDeviceData;
export default LayerSelected;
