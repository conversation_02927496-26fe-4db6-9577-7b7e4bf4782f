/* ! <AUTHOR> at 2022/08/27 */
import * as PIXI from "pixi.js";
class LayerCellBlock implements MRender.Layer {
  private floorId: floorId;
  private utils: any;
  private geometries: Array<any> = [];
  private meshList: Array<any> = [];
  private container: PIXI.Container;
  private fragment: number;
  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "block";
    container.zIndex = utils.getLayerZIndex("cell");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.utils = utils;
    this.floorId = floorId;
  }

  render(): void {
    const _this = this;
    const geometries = _this.geometries;
    if (!geometries.length) return;

    const utils = _this.utils;
    const fragment = this.fragment;
    const shader = utils.getShader("rect");

    for (let i = 0, len = Math.ceil(geometries.length / fragment); i < len; i++) {
      const arr = geometries.slice(i * fragment, i * fragment + fragment);
      let mesh = utils.createMesh(arr, shader);
      mesh.name = "block";
      mesh.mapType = "block";
      mesh.interactive = mesh.buttonMode = false;
      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }

    this.geometries = [];
  }

  drawGeometry(options: mCellData): void {
    const _this = this;
    const geometry = _this.utils.drawGeometry("rect", options["position"], options["shaderColor"]);
    _this.geometries.push(geometry);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];
    this.geometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.geometries = null;
    this.meshList = null;
    this.utils = null;
    this.floorId = undefined;
  }
}
export default LayerCellBlock;
