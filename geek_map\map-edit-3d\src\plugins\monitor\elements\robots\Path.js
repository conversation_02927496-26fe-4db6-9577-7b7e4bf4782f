import * as THREE from "three";
import CanvasWorker from "web-worker:./worker.js";
// const myWorker = new Worker(new URL("./worker.js", import.meta.url));
const myWorker = new CanvasWorker();
class RobotPath {
  constructor(Map3d) {
    this.Map3d = Map3d;
    this.canvasEle = null;
    this.canvasTexture = null;
    this.lw = 1; // 修复线显示不全的bug
  }
  create() {
    const fr = Object.assign({}, this.Map3d.floor.floorInfo);
    fr.w = Math.abs(fr.right - fr.left);
    fr.h = Math.abs(fr.top - fr.bottom);
    this.__createCanvas(fr);
    this.__createPanel(fr);
    myWorker.addEventListener("message", this.__loadImage.bind(this));
  }
  clearPath() {
    myWorker.postMessage({ action: "clearRefresh" });
  }
  drawPath(value) {
    myWorker.postMessage({ action: "clear" });
    const { left: l, bottom: b } = this.Map3d.floor.floorInfo;
    let drawValue = value
      .filter(i => i.paths && i.paths.length)
      .map(i => [i.color, ...i.paths.map(a => [a.x - l + this.lw, a.y - b + this.lw])]);
    myWorker.postMessage({
      action: "draw",
      value: drawValue,
    });
  }
  destory() {
    this.canvasEle && (this.canvasEle = null);
    myWorker && myWorker.postMessage({ action: "destory" });
    myWorker.removeEventListener("message", this.__loadImage.bind(this));
    // myWorker && myWorker.terminate();
  }
  __loadImage({ data }) {
    if (!this.canvasEle) return;
    const canvasWorker = this.canvasEle.getContext("bitmaprenderer");
    const { action } = data;
    if (action === "draw") {
      canvasWorker && canvasWorker.transferFromImageBitmap(data.imageBitmap);
      this.canvasTexture.needsUpdate = true;
    }
  }
  __createCanvas(fr) {
    const scale = 16;
    const canvas = document.createElement("canvas");
    const space = this.lw * 2;
    canvas.width = (fr.w + space) * scale;
    canvas.height = (fr.h + space) * scale;
    canvas.style.width = (fr.w + space) * scale;
    canvas.style.height = (fr.h + space) * scale;
    myWorker.postMessage({
      action: "init",
      value: {
        floorInfo: fr,
        space,
        config: { scale },
      },
    });
    this.canvasEle = canvas;
  }
  __createPanel(fr) {
    const { left: l, right: r, top: t, bottom: b, w, h } = fr;
    const texture = new THREE.CanvasTexture(this.canvasEle);
    texture.minFilter = THREE.NearestFilter;
    texture.magFilter = THREE.NearestFilter;
    texture.anisotropy = this.Map3d.renderer.capabilities.getMaxAnisotropy();
    texture.needsUpdate = true;
    const space = this.lw * 2;

    this.canvasTexture = texture;

    const mesh = new THREE.Mesh(
      new THREE.PlaneBufferGeometry(w + space, h + space),
      new THREE.MeshBasicMaterial({
        map: texture,
        transparent: true,
        side: THREE.BackSide,
        // alphaTest: 0.6,
      }),
    );

    mesh.rotateX(Math.PI / 2);
    mesh.position.set((l + r) / 2, 0.01, -(b + t) / 2);
    this.Map3d.scene.add(mesh);
  }
}

export default RobotPath;
