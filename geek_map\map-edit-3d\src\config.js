export default {
  THEME: {
    SCENE_COLOR: 0xf2f2f2,
    BG_Color: 0x474854,

    // PPP场景下颜色设置
    CRACK_LAYER: 0x8f8983, // crack层
    CRACK_LATTICE: 0x21252d, // crack货位
    CRACK_BOX: 0xf38527, // crack货箱
    CRACK_BACK_BOX: 0xfc9b03, // rack背面货箱颜色

    // 货箱场景下的颜色设置
    RACK_LAYER: 0x111111, // rack层
    RACK_LATTICE: 0xd7d7e7, // rack货位
    RACK_BOX: 0xf38527, // rack货箱
    ROBOT_RACK_BOX: 0x0077f0, // 机器人上的货箱颜色
    RACK_BACK_BOX: 0x538ab2, // rack背面货箱颜色
    RACK_TASK_BOX: 0xff6633, // 有任务货箱颜色
    STATION_RACK_TASK_BOX: 0x42b983, // 去工作站的任务货箱

    // hot
    SHELF_HOT_CONF: {
      // 货架热度 0->10热度从低到高
      0: 0x72fc03,
      1: 0xa1fc03,
      2: 0xbefc03,
      3: 0xf9fc03,
      4: 0xfcd003,
      5: 0xfc9b03,
      6: 0xfc7203,
      7: 0xfc4303,
      8: 0xfc2c03,
      9: 0xfc0303,
      10: 0xfc0303,
    },

    ROBOT_PATH_COLOR: {
      0: 0xff0000,
      1: 0x330099,
      2: 0x9900ff,
      3: 0xff00ff,
      4: 0x8b6508,
      5: 0x698b22,
      6: 0x9b30ff,
      7: 0xeeb422,
      8: 0xfc0303,
      9: 0xfc0303,
    },

    // cell 相关配置
    HOVER_3D: 0xff5a13,
    SELECTED: 0xf95959, // Cell、货架、机器人选中颜色
    LOCKED: 0x767676, // Cell锁定颜色
    STOPPED: 0xe83532, // Cell停止(暂停)颜色

    NULL_CELL: 0x3f9cfb,
    BLOCKED_CELL: 0xdddddd,
    CHARGER_CELL: 0xffde79, // 0xffe699
    CHARGER_PI_CELL: 0xdddddd,
    DROP_CELL: 0x90dd9f,
    ELEVATOR_CELL: 0xd7c7ff,
    OMNI_DIR_CELL: 0x69baff,
    QUEUE_CELL: 0xcee3a5,
    SHELF_CELL: 0x00688b, // 0xd7e3fb
    STATION_CELL: 0xfb8050, // 0xeec6a6
    TURN_CELL: 0xbff7e6,
    E2W_PATH_CELL: 0x69baff,
    W2E_PATH_CELL: 0x69baff,
    S2N_PATH_CELL: 0x69baff,
    N2S_PATH_CELL: 0x69baff,
    E2W_S2N_PATH_CELL: 0x69baff,
    E2W_N2S_PATH_CELL: 0x69baff,
    W2E_S2N_PATH_CELL: 0x69baff,
    W2E_N2S_PATH_CELL: 0x69baff,
    E2W_W2E_PATH_CELL: 0x69baff,
    N2S_S2N_PATH_CELL: 0x69baff,
    E2W_W2E_N2S_PATH_CELL: 0x69baff,
    E2W_W2E_S2N_PATH_CELL: 0x69baff,
    N2S_S2N_E2W_PATH_CELL: 0x69baff,
    N2S_S2N_W2E_PATH_CELL: 0x69baff,
  },
};
