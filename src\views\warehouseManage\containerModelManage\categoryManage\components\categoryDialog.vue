<template>
  <m-dialog
    :visible="visible"
    :before-save="beforeSave"
    :is-need-footer="mode !== 'view'"
    @closed="closed"
    @save="save"
  >
    <!-- 标题 -->
    <template #title>
      <span class="f16">{{ title }}</span>
    </template>
    <!-- 编辑 -->
    <div class="ui-containerBox tc">
      <div class="dib pct80 tl">
        <m-form
          ref="myForm"
          :form-data="formData"
          label-position="right"
          :extend-config="{
            isNeedBtn: false,
          }"
          :label-width="150"
        >
        </m-form>
      </div>
    </div>
  </m-dialog>
</template>
<script>
import { editFormItem } from "../config";
export default {
  props: ["categoryTypeDict", "visible", "initRow", "mode"],
  data() {
    return {};
  },
  computed: {
    formData() {
      return editFormItem(
        this.initRow,
        { categoryType: this.categoryTypeDict },
        this.mode === "view",
      );
    },
    title() {
      if (this.mode === "add")
        return `${this.$t("lang.rms.fed.newlyAdded")}-${this.$t("lang.rms.fed.shelfCategory")}`;
      if (this.mode === "view")
        return `${this.$t("lang.rms.fed.buttonView")}-${this.$t("lang.rms.fed.shelfCategory")}`;
      return `${this.$t("lang.rms.fed.buttonEdit")}-${this.$t("lang.rms.fed.shelfCategory")}`;
    },
  },
  methods: {
    async beforeSave() {
      try {
        const formModel = await this.$refs.myForm.getValidateFormModel();
        const initRow = this.initRow || {};
        const { code } = await $req.post(
          "/athena/shelfCategory/save",
          Object.assign({ id: initRow.id }, formModel),
        );
        if (code) return Promise.reject();
        return Promise.resolve();
      } catch (e) {
        return Promise.reject();
      }
    },
    // 保存成功关闭弹框
    save() {
      this.closed();
      this.$emit("saveSuccess");
    },
    // 关闭
    closed() {
      this.$emit("update:visible", false);
    },
  },
};
</script>
