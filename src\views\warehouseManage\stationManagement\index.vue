<template>
  <geek-main-structure>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />

    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @selectionChange="handleSelection"
      @enableStation="changeStationStatus(1)"
      @disableStation="changeStationStatus(0)"
      @page-change="pageChange"
      @row-edit="rowEdit"
      @row-cache-shelf="cacheShelfSet"
      style="margin-top: 10px"
    >
      <!-- <template #rowExpand="{ row }">
        <geek-customize-table
          :table-config="expandTableConfig"
          :data="row.stationPoints"
          @expand-row-edit="expandRow => expandRowEdit(expandRow, row)"
        >
          <template #isWorking="{ row }">
            <el-tag v-if="row.isWorking == 'true'" size="mini" type="success">{{ $t("lang.rms.fed.enable") }}</el-tag>
            <el-tag v-else size="mini" type="danger">{{ $t("lang.rms.fed.stopStatus") }}</el-tag>
          </template>
        </geek-customize-table>
      </template> -->
      <template #manageStatus="{ row }">
        <el-tag v-if="row.manageStatus == 1" size="mini" type="success">{{ $t("lang.rms.fed.enable") }}</el-tag>
        <el-tag v-else size="mini" type="danger">{{ $t("lang.rms.fed.stopStatus") }}</el-tag>
      </template>
    </geek-customize-table>

    <cache-rack-dialog
      v-if="dialogRackVisible"
      :visible.sync="dialogRackVisible"
      :init-row="dialogInitRow"
      @save="getTableList"
    />
    <EditSingleStation ref="editSingleStationDialog" @updateMainList="getTableList" />
    <EditUnionStation ref="editUnionStationDialog" @updateMainList="getTableList" />
    <EditStation ref="editStationDialog" :robotTypes="robotTypes" @updateMainList="getTableList" />
    <EditPark ref="editParkDialog" :robotTypes="robotTypes" @updateMainList="getTableList" />
  </geek-main-structure>
</template>

<script>
import CacheRackDialog from "./components/cacheRackDialog";
import EditSingleStation from "./components/editSingleStation";
import EditUnionStation from "./components/editUnionStation";
import EditStation from "./components/editStation";
import EditPark from "./components/editPark";

export default {
  components: { CacheRackDialog, EditSingleStation, EditUnionStation, EditStation, EditPark },
  data() {
    return {
      robotTypes: [],
      form: {
        stationId: "",
        hostCode: "",
        type: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "120px",
          inline: true,
        },
        configs: {
          // 工作站id
          stationId: {
            label: "lang.rms.web.station.stationId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.web.station.stationIdPlaceHolder",
          },
          // 外部编码
          hostCode: {
            label: "lang.rms.fed.hostCode",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterExternalCode",
          },
          // 工作站type
          type: {
            label: "lang.rms.web.station.stationType",
            default: "",
            tag: "select",
            placeholder: "lang.rms.fed.pleaseChoose",
            options: [],
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {
          selection: true,
          "row-key": "stationId",
          "reserve-selection": true,
          // "row-class-name": ({ row }) => {
          // if (!row?.stationPoints || !row.stationPoints.length) return "no-expand";
          // },
        },
        // expand: { slotName: "rowExpand" },
        actions: [
          {
            label: "lang.rms.fed.enable",
            type: "primary",
            handler: "enableStation",
          },
          {
            label: "lang.rms.fed.stopStatus",
            type: "primary",
            handler: "disableStation",
          },
        ],
        columns: [
          { label: "lang.rms.web.station.stationId", prop: "stationId", width: "80" },
          {
            label: "lang.rms.web.station.status",
            prop: "manageStatus",
            slotName: "manageStatus",
            width: "90",
            align: "center",
          },
          {
            label: "lang.rms.web.station.stationType",
            prop: "typeDesc",
            width: "100",
            formatter: (row, column, cellValue, index) => {
              return this.$t(cellValue);
            },
          },
          { label: "lang.rms.web.station.cellCode", prop: "cellCode", width: "100" },
          {
            label: "lang.rms.fed.WorkStationFace",
            prop: "place",
            width: "86",
            formatter: (row, column, cellValue, index) => {
              switch (cellValue) {
                case "east":
                  return this.$t("lang.rms.fed.east");
                case "south":
                  return this.$t("lang.rms.fed.south");
                case "west":
                  return this.$t("lang.rms.fed.west");
                case "north":
                  return this.$t("lang.rms.fed.north");
                default:
                  return "";
              }
            },
          },
          {
            label: "lang.rms.fed.isPreDeployRobot",
            prop: "preDeployRobot",
            width: "90",
            formatter: (row, column, cellValue, index) => {
              return !cellValue ? this.$t("lang.rms.fed.no") : this.$t("lang.rms.fed.yes");
            },
          },
          { label: "lang.rms.fed.layout", prop: "layOut", width: "180" },
          { label: "lang.rms.fed.maxQueueNumber", prop: "maxRobotQueueSize", width: "60" },
          {
            label: "lang.rms.fed.ongoingRobot",
            prop: "deliverRobotIds",
            formatter: (row, column, cellValue, index) => {
              if ($utils.Type.isArray(cellValue)) {
                return cellValue.join(", ");
              } else {
                return cellValue;
              }
            },
          },
          { label: "lang.rms.fed.hostCode", prop: "hostCode", width: "80" },
          {
            label: "lang.rms.fed.listOperation",
            width: "170",
            operations: [
              {
                label: "lang.rms.web.station.queueNumber",
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.cacheShelfSet",
                handler: "row-cache-shelf",
              },
            ],
          },
        ],
      },

      expandTableConfig: {
        columns: [
          { label: "lang.rms.web.station.parkId", prop: "parkId", width: "80" },
          {
            label: "lang.rms.fed.status",
            prop: "isWorking",
            slotName: "isWorking",
            width: "90",
            align: "center",
          },
          { label: "lang.rms.fed.type", prop: "parkType" },
          { label: "lang.rms.web.station.cellCode", prop: "cellCode" },
          {
            label: "lang.rms.fed.WorkStationFace",
            prop: "place",
            width: "86",
            formatter: (row, column, cellValue, index) => {
              switch (cellValue) {
                case "east":
                  return this.$t("lang.rms.fed.east");
                case "south":
                  return this.$t("lang.rms.fed.south");
                case "west":
                  return this.$t("lang.rms.fed.west");
                case "north":
                  return this.$t("lang.rms.fed.north");
                default:
                  return "";
              }
            },
          },
          { label: "lang.rms.fed.maxQueueNumber", prop: "maxQueueSize" },
          // {
          //   label: "lang.rms.fed.listOperation",
          //   width: "170",
          //   operations: [
          //     {
          //       label: "lang.rms.fed.buttonEdit",
          //       handler: "expand-row-edit",
          //     },
          //   ],
          // },
        ],
      },

      stationIds: [],

      dialogInitRow: {},
      dialogRackVisible: false,
    };
  },
  activated() {
    this.getStationType();
    this.getRobotType();
    this.getTableList();
  },
  methods: {
    rowEdit(row) {
      switch (row.type) {
        case 4:
          this.$refs.editUnionStationDialog.open(row);
          break;
        default:
          this.$refs.editSingleStationDialog.open(row);
          break;
      }
    },
    expandRowEdit(expandRow, row) {
      this.$refs.editParkDialog.open(expandRow, row?.stationId || "");
    },
    cacheShelfSet(row) {
      this.dialogRackVisible = true;
      this.dialogInitRow = row;
    },
    changeStationStatus(status) {
      const stationIds = this.stationIds;
      if (!stationIds.length) return;
      if (status == 1) {
        $req.post("/athena/station/enable", stationIds).then(res => {
          this.$success();
          this.getTableList();
        });
      } else {
        $req.post("/athena/station/disable", stationIds).then(res => {
          this.$success();
          this.getTableList();
        });
      }
    },
    handleSelection(selections) {
      this.stationIds = selections.map(item => item.stationId);
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },

    getTableList() {
      const data = {
        ...this.form,
        pageSize: this.tablePage.pageSize,
        currentPage: this.tablePage.currentPage,
      };

      $req.get("/athena/station/stationPageList", data).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList;

        let stationPointsIndex = this.tableData.findIndex(
          item => item.hasOwnProperty("stationPoints") && item.stationPoints,
        );
        if (stationPointsIndex == -1) {
          this.tableConfig.expand = null;
        }

        this.tablePage = Object.assign({}, this.tablePage, {
          currentPage: result.currentPage || 1,
          pageCount: result.pageCount || 0,
        });
      });
    },
    getStationType() {
      $req.get("/athena/station/stationType").then(res => {
        const data = res?.data || {};
        let stationTypes = [];
        for (let key in data) {
          stationTypes.push({ label: data[key], value: key });
        }
        this.formConfig.configs.type.options = stationTypes;
      });
    },

    getRobotType() {
      $req.post("/athena/map/version/findRobotType").then(res => {
        const data = res?.data || [];
        this.robotTypes = data.map(item => {
          return { label: item.displayName, value: item.displayName };
        });
      });
    },
  },
};
</script>
<style lang="less" scoped></style>
