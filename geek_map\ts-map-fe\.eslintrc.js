module.exports = {
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaFeature: { jsx: true },
    sourceType: "module",
  },
  plugins: ["react", "react-hooks", "prettier"],
  extends: [
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:prettier/recommended",
  ],
  settings: {
    "import/resolver": {
      webpack: {
        config: "./build/webpack.config.js",
        "config-index": 1,
      },
    },
  },
  rules: {
    // prettier
    "prettier/prettier": "error",
    // js
    "eol-last": "error",
    "no-trailing-spaces": "error",
    "comma-style": ["error", "last"],
    "comma-dangle": ["error", "always-multiline"],
    "no-multi-spaces": "error",
    quotes: ["error", "double", { avoidEscape: true, allowTemplateLiterals: true }],
    indent: ["error", 2, { SwitchCase: 1, ignoredNodes: ["ConditionalExpression"] }],
    "object-curly-spacing": ["error", "always"],
    "arrow-parens": ["error", "as-needed"],
    "spaced-comment": ["error", "always"],
    // react
    "react/jsx-uses-react": "error",
    "react-hooks/rules-of-hooks": "error", // 检查 Hook 的规则
    "react-hooks/exhaustive-deps": "warn", // 检查 effect 的依赖
    "react/jsx-filename-extension": [
      1,
      {
        extensions: [".js", ".jsx", ".ts", ".tsx"],
      },
    ],
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off",
  },
};
