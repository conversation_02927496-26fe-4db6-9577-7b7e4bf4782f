<template>
  <geek-main-structure
    :space="false"
    class="dashboard-container"
    :class="isShowGeekIcon ? 'dashboard-container-bg' : 'dashboard-container-no-bg'"
  />
</template>

<script>
export default {
  name: "Dashboard",
  data() {
    return {
      isShowGeekIcon: $utils?._staticConfig?.showGeekIcon || false,
    };
  },
};
</script>

<style lang="less" scoped>
.dashboard-container {
  // margin: -12px;
  // margin: -@g-main-padding;
  // height: calc(100% + 24px);
  background-size: 100% 100%;
}

.dashboard-container-bg {
  background: #fff url(~@imgs/dashboard/dashboard-bg.jpg) no-repeat 0 0;
  background-size: 100% 100%;
}

.dashboard-container-no-bg {
  background: #fff url(~@imgs/dashboard/dashboard-no-logo.png) no-repeat 0 0;
  background-size: 100% 100%;
}
</style>
