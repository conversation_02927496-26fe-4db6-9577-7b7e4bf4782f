import {Graphics} from 'pixi.js'
import Line from "../element/baseElement/Line";
import SegmentArrow from '../element/baseElement/SegmentArrow'
import BezierLine from "../element/baseElement/BezierLine";
import BezierArrow from "../element/baseElement/BezierArrow";
import DashLine from "../element/baseElement/DashLine";
import Polygon from "../element/baseElement/Polygon";
import {lineStyle,areaStyle} from '../config'
const {INACTIVE_AREA,ACTIVE_AREA} = areaStyle
const {ACTIVE_LINE,INACTIVE_LINE} = lineStyle
export default class RenderSelected {
  static renderSelectedByType($el,isActive = true){
    const {type} = $el
    if(type === 'area'){
      this.areaSelected($el,isActive)
    }else if(type === 'S_LINE'){
      this.lineSelected($el,isActive)
    }else if(type === 'BEZIER'){
      this.bezierLineSelected($el,isActive)
    }else{
      this.elementSelected($el,isActive)
    }
  }
  //渲染面的选中态
  static areaSelected($el,isActive = true){
    $el.selected = isActive
    if($el.isSingleLane){
      $el.alpha = isActive ? 0.5 : 1
      return
    }
    const $points = $el.children.filter(child => child.name === 'point')
    const $noActivePoint = $el.children.filter(child => child.name === 'noActivePoint')
    $noActivePoint.forEach($p => $p.visible = isActive)
    //根据pIndex进行排序
    $points.sort((a, b) => {
      return a.pIndex - b.pIndex;
    });
    const paths = $points.map(p => {
      p.visible = isActive
      const {x,y} = p
      return {x,y}
    })
    //闭合数据
    const closePaths = [...paths,paths[0]]
    const $line = $el.getChildByName('line')
    const $polygon = $el.getChildByName('polygon')
    if($line){
      const lineColor = isActive ? ACTIVE_LINE : INACTIVE_LINE
      $line.clear()
      Line.render($line,closePaths,{color:lineColor})
    }
    if($polygon){
      const areaColor = isActive ? ACTIVE_AREA : INACTIVE_AREA
      $polygon.clear()
      Polygon.render($polygon,closePaths,{color:areaColor})
    }
  }
  static lineSelected($el,isActive = true) {
    const $points = $el.children.filter(child => child.name === 'point')
    const paths = $points.map(p => {
      p.visible = isActive
      const {x,y} = p
      return {x,y}
    })
    const $line = $el.getChildByName('line')
    const color = isActive ? ACTIVE_LINE : INACTIVE_LINE
    if($line) {
      $line.clear()
      Line.render($line,paths,{color})
      SegmentArrow.render($line,{paths,color})
    }
  }
  //渲染贝塞尔曲线选中态
  static bezierLineSelected($el,isActive = true) {
    const $points = $el.children.filter(child => child.name === 'point')
    const paths = $points.map(p => {
      p.visible = isActive
      const {x,y} = p
      return {x,y}
    })
    const $line = $el.getChildByName('line')
    const $dashLine = $el.getChildByName('dashLine')
    const color = isActive ? ACTIVE_LINE : INACTIVE_LINE
    if($dashLine){
      $dashLine.visible = isActive
    }
    if($line){
      $line.clear()
      BezierLine.render($line,paths,{color})
      BezierArrow.render($line,{color})
    }
  }
  //元素选中态，包括单元格和设备
  static elementSelected($el,isActive = true){
    $el.selected = isActive
    $el.alpha = isActive ? 0.6 : 1
    if(!isActive){
      let selected = $el.getChildByName('selected')
      if(selected){
        selected.destroy()
        $el.removeChild(selected)
      }
      selected = null
      return
    }
    const bounds = $el.getLocalBounds()
    const {x,y,width,height} = bounds
    const $border = new Graphics()
    $border.name = 'selected'
    $el.addChild($border)
    const borderPath = [
      {x,y},
      {x:x + width,y},
      {x:x + width,y:y + height},
      {x,y:y + height},
      {x,y},
    ]
    const lineW = $el.type === 'device' ? 2 : 0.5
    const dashArr = $el.type === 'device' ? [4.5,2] : [1.5,1]
    DashLine.render($border,borderPath,{color:0xf14f6f,width:lineW,dashArr:dashArr})
  }
}
