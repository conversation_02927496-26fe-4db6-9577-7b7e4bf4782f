/* ! <AUTHOR> at 2023/04/18 */

/**
 * 货架接口数据类型
 * @param shelfCode 唯一货架code
 * @param shelfType 类型
 * @param lockedState 锁定状态
 * @param shelfStatus 错误状态
 * @param radAngle 角度，如：80 90
 * @param score 货架热度
 * @param width 宽
 * @param length 长
 * @param placementCell 老家位置，数据为单元格code
 * @param location 位置数据
 * @param 其他 可选
 */
type shelfData = {
  shelfCode: code;
  shelfType: string;
  lockedState: string;
  shelfStatus: string;
  radAngle: number;
  score: number;
  width: number;
  length: number;
  placementCell: code;
  location: location;
  [propName: string]: any;
};

/**
 * 货架地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code
 * @param floorId 楼层
 * @param shelfStatus
 * @param lockedState
 * @param shelfType
 * @param placementCell 负载，1无方向，0有方向
 * @param position mesh位置，length为8的数组number
 * @param hitArea xy的四个最大最小值，length为4的数组number
 * @param 其他 可选
 */
type mShelfData = {
  code: code;
  floorId: floorId;
  shelfStatus: string;
  lockedState: string;
  shelfType: string;
  placementCell: code;
  position: meshPosition;
  hitArea: hitArea;
  [propName: string]: any;
};
