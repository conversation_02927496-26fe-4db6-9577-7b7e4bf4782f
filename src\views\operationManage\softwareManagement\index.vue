<template>
  <geek-main-structure class="software-control">
    <!-- 机器人软件管理 -->
    <div class="software-control-header">
      <div class="btnwarp">
        <el-button
          v-if="checkPermission('RobotSoftwareManagerAdd', 'natural')"
          type="primary"
          @click="dialogFormVisible = true"
        >
          {{ $t("lang.rms.fed.newlyAdded") }}
        </el-button>
        <el-button
          v-if="checkPermission('RobotSoftwareManagerDelete', 'natural')"
          type="danger"
          @click="handleDeleteRobotControl"
        >
          {{ $t("lang.rms.fed.delete") }}
        </el-button>
      </div>
    </div>
    <div class="table-content">
      <el-table :data="robotStatus" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="version" :label="$t('lang.rms.fed.edition')" />
        <el-table-column
          prop="type"
          :formatter="statusformatter"
          :label="$t('lang.rms.fed.type')"
        />
        <el-table-column prop="oldName" :label="$t('lang.rms.fed.fileName')" />
        <el-table-column min-width="400px" prop="path" :label="$t('lang.rms.fed.storagePath')" />
        <el-table-column
          prop="createTime"
          :formatter="timeformatter"
          :label="$t('lang.rms.fed.creationTime')"
        />
        <el-table-column
          prop="updateTime"
          :formatter="timeformatter"
          :label="$t('lang.rms.fed.updateTime')"
        />
        <el-table-column fixed="right" :label="$t('lang.rms.fed.operation')" width="180">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.$index, scope.row)">
              {{ $t("lang.rms.fed.edit") }}
            </el-button>
            <el-button type="text" size="small" @click="handleActive(scope.$index, scope.row)">
              {{ $t("lang.rms.fed.application") }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: right">
        <geek-pagination
          :current-page="currentPage"
          :page-size="pageSize"
          :total-page="totalPage"
          @currentPageChange="currentPageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
    </div>
    <el-dialog
      :title="$t('lang.rms.fed.addSoftware')"
      :visible.sync="dialogFormVisible"
      width="640px"
      @close="clearAddForm"
    >
      <el-form ref="addForm" :model="addForm" label-width="80px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.edition')" prop="version">
              <!-- :placeholder="x1" -->
              <el-input v-model="addForm.version" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('lang.rms.fed.type')" prop="type">
              <el-select v-model="addForm.type">
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="$t(item.label)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('lang.rms.fed.describe')" prop="descr">
              <!-- :placeholder="x2" -->
              <el-input v-model="addForm.descr" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-upload
        ref="upload"
        class="upload"
        action="/athena/robot/software/upload"
        :limit="1"
        :file-list="fileList"
        :on-success="handleUploadSuccess"
        :auto-upload="false"
      >
        <el-button slot="trigger" size="small" type="primary">{{
          $t("lang.rms.fed.chooseAFile")
        }}</el-button>
        <el-button style="margin-left: 10px" size="small" type="success" @click="submitUpload">
          {{ $t("lang.rms.fed.upload") }}
        </el-button>
        <div slot="tip" class="el-upload__tip">{{ $t("lang.rms.fed.uploadOneFile") }}</div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="clearAddForm">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button
          type="primary"
          :disabled="addForm.version == '' || !uploadInfo.newName"
          @click="handleSubmitRobotControl"
        >
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="
        $t('lang.rms.fed.pleaseEnterTheNumber') +
        ',' +
        $t('lang.rms.fed.multipleNumbersAreSeparatedByComma')
      "
      :visible.sync="dialogRobotVisible"
    >
      <el-form :model="formRobot">
        <el-form-item>
          <el-input v-model="formRobot.ids" autocomplete="off" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogRobotVisible = false">{{ $t("lang.rms.fed.cancel") }}</el-button>
        <el-button type="primary" :disabled="formRobot.ids == ''" @click="handleSubmitActive">
          {{ $t("lang.rms.fed.confirm") }}
        </el-button>
      </div>
    </el-dialog>
  </geek-main-structure>
</template>

<script>
import GeekPagination from "@plugins/components/geek-pagination";

export default {
  name: "SoftwareControl",
  components: { GeekPagination },
  data() {
    return {
      formRobot: {
        softwareId: "",
        ids: "",
      },
      addForm: {
        version: "",
        type: 0,
        descr: "",
      },
      isEdit: false,
      fileList: [],
      dialogFormVisible: false,
      dialogRobotVisible: false,
      totalPage: 1,
      currentPage: 1,
      pageSize: 10,

      loading: true,
      tableSelection: [],
      uploadInfo: {},
      recordCount: 0,
      pageSizes: [10, 20, 50, 100],
      statusList: [],
      robotStatus: [],
    };
  },
  activated() {
    this.getAllRobotFirmwareType();
    this.findAllRobotControl();
  },
  methods: {
    checkButtonPermission() {
      return true;
    },
    getAllRobotFirmwareType() {
      $req.get('/athena/robot/software/allRobotFirmwareType').then(res => {
        const data = res?.data || [];
        this.statusList = data.map(item => {
          return {
            value: item.type,
            label: item.name,
          };
        });
      });
    },
    findAllRobotControl() {
      const { currentPage, pageSize } = this.$data;

      $req
        .get("/athena/robot/software/findAll", {
          currentPage,
          pageSize,
          category: 0,
        })
        .then(res => {
          const data = res.data;
          this.currentPage = data.currentPage || 1;
          this.totalPage = data.pageCount;
          this.$nextTick(() => {
            this.robotStatus = res.data.recordList;
          });
        });
    },
    handleEdit(index, data) {
      this.dialogFormVisible = true;
      this.isEdit = true;
      this.addForm = {
        version: data.version || "",
        type: data.type || 0,
        descr: data.descr || "",
        id: data.id,
      };
      this.uploadInfo = data;
      this.fileList = [
        {
          name: data.newName || "",
          url: data.path,
        },
      ];
    },
    handleActive(index, data) {
      this.dialogRobotVisible = true;
      this.formRobot.softwareId = data.id;
    },
    handleSubmitActive() {
      // this.$store.dispatch('setLoading', true)
      $req
        .post("/athena/robot/software/active", {
          softwareId: this.formRobot.softwareId,
          robotIds: this.formRobot.ids.split(","),
          params: true,
        })
        .then(res => {
          // this.$store.dispatch('setLoading', false)
          if (res.code === 0) {
            this.$message.success(this.$t(res.msg))
          }
        }).finally(() => {
          this.dialogRobotVisible = false;
        });
    },
    handleSelectionChange(selection) {
      this.tableSelection = selection.map(item => {
        return item.id;
      });
    },
    handleDeleteRobotControl() {
      if (this.tableSelection.length > 0) {
        // this.$store.dispatch('setLoading', true)
        $req.post("/athena/robot/software/delete", this.tableSelection).then(res => {
          this.findAllRobotControl();
          // this.$store.dispatch('setLoading', false)
        });
      }
    },
    handleSubmitRobotControl() {
      const self = this;
      // this.$store.dispatch('setLoading', true)
      const data = Object.assign({}, this.uploadInfo, this.addForm);
      if (this.isEdit) {
        $req.post("/athena/robot/software/update", data).then(res => {
          self.findAllRobotControl();
          self.clearAddForm();
          // self.$store.dispatch('setLoading', false)
          self.isEdit = false;
        });
      } else {
        $req.post("/athena/robot/software/add", data).then(res => {
          self.findAllRobotControl();
          self.clearAddForm();
          // self.$store.dispatch('setLoading', false)
        });
      }
    },
    clearAddForm() {
      this.dialogFormVisible = false;
      this.$refs.upload.clearFiles();
      this.addForm = {
        version: "",
        type: 0,
        descr: "",
      };

      this.uploadInfo = {};
      this.$refs["addForm"].resetFields();
    },
    submitUpload() {
      this.$refs.upload.submit();
    },
    handleUploadSuccess(res) {
      this.uploadInfo = res.data;
    },
    statusformatter(row, column) {
      return this.$t(this.statusList[row[column["property"]]].label);
    },
    timeformatter(row, column) {
      return new Date(row[column["property"]])?.toLocaleString();
    },
    currentPageChange(page) {
      this.currentPage = page;
      this.findAllRobotControl();
    },
    pageSizeChange(size) {
      this.pageSize = size;
      this.findAllRobotControl();
    },
  },
};
</script>

<style lang="less" scoped></style>
