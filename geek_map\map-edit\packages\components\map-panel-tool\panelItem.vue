<template>
  <!-- 不知道为啥TS报错了... -->
  <el-tooltip
    v-if="modelValue.describe"
    effect="dark"
    :enterable="false"
    :content="$t(modelValue.describe)"
    :placement="placement"
  >
    <div class="tool-panel-item" :class="className" @click="handelClick">
      <span class="mapFont" :class="modelValue.icon"></span>
      {{ $t(modelValue.title || "") }}
    </div>
  </el-tooltip>

  <div v-else class="tool-panel-item" :class="className" @click="handelClick">
    <span class="mapFont" :class="modelValue.icon"></span>
    {{ $t(modelValue.title || "") }}
  </div>
</template>

<script setup lang="ts">
import { defineProps, withDefaults, computed, ComputedRef } from "vue";
import { getPanelToolDisabled } from "./common";
interface ToolPanelItem {
  icon?: string; // 图标
  name: string;
  title?: string;
  className?: string;
  describe?: string; // 详情
  eventName?: string; // 事件名称, 点击时
  active?: boolean; // 是否选中, 注意, 如果这里的isSelect为false, 且当前没有children数据, 则该参数不生效
  isSelect?: boolean; // 是否是可切换选中状态
  disabled?: Function;
  defDisabled?: boolean;
  data?: { [key: string]: any };
  children?: ToolPanelItem[];
  [key: string]: any;
}

interface PropsType {
  modelValue: ToolPanelItem;
  placement: string;
}

const props = withDefaults(defineProps<PropsType>(), {});

/* 组件可以抛出的事件 */
const emits = defineEmits<{
  (event: "click"): void;
}>();

const handelClick = () => {
  if (!getPanelToolDisabled(props.modelValue)) {
    emits("click");
  }
};

const className: ComputedRef<string> = computed(() => {
  const { modelValue } = props;
  let className = modelValue.className || "";
  modelValue.active && (className += " active");
  getPanelToolDisabled(modelValue) && (className += " disabled");
  return className;
});
</script>

<style scoped lang="scss">
.tool-panel-item {
  display: inline-block;
  padding: 0 10px;
  height: 32px;
  line-height: 32px;
  font-size: 12px;
  cursor: pointer;
  user-select: none;

  &.active {
    background: #d6eafb;
    color: #409eff;
  }

  &.disabled {
    color: #ccc;
  }
}
</style>
