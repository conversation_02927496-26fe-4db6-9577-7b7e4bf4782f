<template>
  <div class="geek-header-menu">
    <a href="javascript:;" class="arrow-btn" :class="{ 'arrow-show': arrowShow }" @click.stop="menuMove('left')">
      <i class="el-icon-caret-left" />
    </a>

    <div class="geek-header-menu-wrap" :class="{ 'menu-arrow-show': arrowShow }">
      <div ref="menuContent" class="menu-content">
        <el-tag
          v-for="item in headerTabs"
          :key="item.path"
          size="medium"
          disable-transitions
          :closable="item.path !== '/dashboard'"
          :class="{ active: item.path === currentPath }"
          @click="goPage(item)"
          @close="closePage(item)"
        >
          {{ $t(item.title) }}
        </el-tag>
      </div>
    </div>

    <a
      href="javascript:;"
      class="arrow-btn right-btn"
      :class="{ 'arrow-show': arrowShow }"
      @click.stop="menuMove('right')"
    >
      <i class="el-icon-caret-right" />
    </a>
  </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";

export default {
  name: "GeekHeaderMenu",
  data() {
    return {
      currentPath: "",
      $menuContent: null,
      arrowShow: false,
      left: 0,
    };
  },
  computed: {
    ...mapState(["headerTabs", "selectedHeaderTab"]),
  },
  watch: {
    selectedHeaderTab(tab) {
      this.currentPath = tab.path;
    },
  },
  created() {
    let arr = this.headerTabs;
    this.currentPath = arr[arr.length - 1].path;
  },
  mounted() {
    this.$menuContent = this.$refs.menuContent;
    this.toggleArrow();
  },
  updated() {
    if (!this.$menuContent) return;
    this.toggleArrow();

    this.scrollToActiveTab();
  },
  methods: {
    ...mapMutations(["delHeaderTabs"]),
    goPage(item) {
      if (item.path === this.currentPath) return;

      this.currentPath = item.path;
      this.$router.push(item.path);
    },
    closePage(item) {
      if (this.currentPath === item.path) {
        this.delHeaderTabs(item);
        this.$nextTick(() => {
          const tabs = this.headerTabs;
          this.currentPath = tabs[tabs.length - 1].path;
          this.$router.push(this.currentPath);
        });
      } else {
        this.delHeaderTabs(item);
      }
    },
    menuMove(type) {
      const $menuContent = this.$menuContent;
      let totalWidth = $menuContent.parentElement.offsetWidth,
        menuWidth = $menuContent.offsetWidth;
      if (type === "left") {
        this.left += 120;
        if (this.left > 0) {
          this.left = 0;
        }
        $menuContent.style.transform = `translateX(${this.left}px)`;
      } else if (type === "right") {
        this.left -= 120;
        if (Math.abs(this.left) + totalWidth > menuWidth) {
          this.left = -Math.abs(totalWidth - menuWidth);
        }
        $menuContent.style.transform = `translateX(${this.left}px)`;
      }
    },

    toggleArrow() {
      const $menuContent = this.$menuContent;
      let totalWidth = $menuContent.parentElement.offsetWidth,
        menuWidth = $menuContent.offsetWidth;
      if (menuWidth <= totalWidth) {
        this.arrowShow = false;
      } else {
        this.arrowShow = true;
      }
    },

    scrollToActiveTab() {
      const $menuContent = this.$menuContent;
      let $active = $menuContent.querySelector(".active");
      if (!$active) return;

      let totalWidth = $menuContent.parentElement.offsetWidth;
      let lastRight = $active.offsetWidth + $active.offsetLeft + 5;

      if (totalWidth < lastRight) {
        this.left = -Math.abs(lastRight - totalWidth);
      } else {
        this.left = 0;
      }
      $menuContent.style.transform = `translateX(${this.left}px)`;
    },
  },
};
</script>

<style lang="less" scoped>
@menu-height: @g-header-height - 3;
.geek-header-menu {
  position: relative;
  flex: 1;
  line-height: @menu-height;
  height: @menu-height;
  margin: 0 10px 0 0;
  overflow: hidden;

  .el-tag {
    font-size: 13px;
    margin-right: 5px;
    border-color: #343a46;
    border-radius: 2px;
    color: #fff;
    background: #343a46;

    .el-tag__close {
      color: #495060;

      &:hover {
        background: rgba(0, 0, 0, 0.2);
      }
    }

    &.active {
      padding-left: 18px;
      color: #fff;
      border-color: #0097ec;
      background: #0083ff;
      .g-before-absolute();

      &:before {
        top: 50%;
        margin-top: -4px;
        left: 6px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #fff;
      }

      .el-tag__close {
        color: #fff;
      }
    }
  }
}
.geek-header-menu-wrap {
  overflow: hidden;

  &.menu-arrow-show {
    margin: 0 16px;
  }

  :deep(.el-tag) {
    cursor: pointer;
  }
}

.menu-content {
  display: inline-flex;
  transition-duration: 0.1s;
  -moz-transition-duration: 0.1s; /* Firefox 4 */
  -webkit-transition-duration: 0.1s; /* Safari 和 Chrome */
  -o-transition-duration: 0.1s; /* Opera */
}

.arrow-btn {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  color: #fff;

  &.right-btn {
    left: auto;
    right: 0;
  }

  &.arrow-show {
    display: inline-block;
  }
}
</style>
