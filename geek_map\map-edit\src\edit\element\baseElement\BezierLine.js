import {LINE_CAP, LINE_JOIN} from "pixi.js";
import {lineStyle} from '../../config'
const {ACTIVE_LINE} = lineStyle
export default class BezierLine {
  static render($el,paths = [],{width = lineStyle.width,color = ACTIVE_LINE, alpha = 1} = {}) {
    if(paths.length !== 4) return
    const {x:x1,y:y1} = paths[0]
    const {x:cpx1,y:cpy1} = paths[1]
    const {x:cpx2,y:cpy2} = paths[2]
    const {x:x2,y:y2} = paths[3]
    const styleOp = {
      width,color,alpha,
      cap: LINE_CAP.ROUND,
      join: LINE_JOIN.ROUND
    }
    $el.lineStyle(styleOp);
    $el.moveTo(x1, y1);
    $el.bezierCurveTo(
      cpx1, cpy1,
      cpx2, cpy2,
      x2, y2,
    );
    return $el
  }
}
