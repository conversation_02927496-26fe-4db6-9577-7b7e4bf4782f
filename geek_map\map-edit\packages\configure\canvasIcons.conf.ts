import { IconConfType } from "../type/editUiType";
export const iconConf: IconConfType[] = [
  {
    icon: "map-font-info",
    name: "info",
    type: "hover",
    innerType: "descriptions",
    size: 18,
    color: "#409EFF",

    // 默认提供了参数透传
    innerData: [
      { label: "lang.rms.fed.mapID", value: "{data:mapId}" },
      { label: "lang.rms.fed.mapName", value: "{data:mapName}" },
      { label: "lang.rms.fed.FloorID", value: "{data:floorId}" },
      { label: "lang.rms.fed.mapResolution", value: "{data:resolution}" },
    ],
  },
  {
    icon: "map-font-bangzhu1",
    name: "info",
    type: "hover",
    innerType: "descriptions",
    size: 18,
    color: "#409EFF",

    // 默认提供了参数透传
    innerData: [
      { label: "lang.rms.fed.zoom", value: "lang.rms.fed.mouseZoom" },
      { label: "lang.rms.fed.selectElement", value: "lang.rms.fed.selectElementOnMapByClick" },
      { label: "lang.rms.fed.map.edit.multipleChoice", value: "lang.rms.fed.map.edit.multipleChoiceOption" },
      { label: "lang.rms.fed.map.edit.copyElement", value: "lang.rms.fed.map.edit.copyElementOption" },
      { label: "lang.rms.fed.dragCell", value: "lang.rms.fed.keyupAltToMoveCell" },
      {
        label: "lang.rms.fed.dragLineBezier",
        value: "lang.rms.fed.dragLineBezierByRedPoint",
      },
      {
        label: "lang.rms.fed.editElement",
        value: "lang.rms.fed.editCellOnRightPanel",
      },
      {
        label: "lang.rms.fed.map.edit.batchEditElement",
        value: "lang.rms.fed.map.edit.batchEditElementOption",
      },
      {
        label: "lang.rms.fed.deleteElement",
        value: "lang.rms.fed.deleteElementMenuBySelect",
      },
    ],

  },
];
