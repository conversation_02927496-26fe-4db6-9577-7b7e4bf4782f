/* ! <AUTHOR> at 2022/08/27 */
import * as PIXI from "pixi.js";

/** shelf状态 */
class LayerShelfStatus implements MRender.Layer {
  private utils: any;
  private container: PIXI.Container;
  private lockedShader: any;
  private faultShader: any;
  private fragment: number;
  private lockedGeometries: any = [];
  private faultGeometries: any = [];
  private meshList: any = [];
  init(mapCore: MRender.MainCore): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "shelfStatus";
    container.zIndex = utils.getLayerZIndex("shelf");
    container.interactiveChildren = false;
    container.visible = true;
    this.container = container;

    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.lockedShader = utils.getShader("icon", utils.getResources("shelfLocked"));
    this.faultShader = utils.getShader("icon", utils.getResources("shelfFault"));
    this.utils = utils;
  }

  render(): void {
    const utils = this.utils;
    const fragment = this.fragment;

    const lockedGeometries = this.lockedGeometries;
    const lockedShader = this.lockedShader;
    for (let i = 0, len = Math.ceil(lockedGeometries.length / fragment); i < len; i++) {
      const arr = lockedGeometries.slice(i * fragment, i * fragment + fragment);

      let mesh = utils.createMesh(arr, lockedShader);
      mesh.name = "shelfStatus";
      mesh.mapType = "shelfStatus";
      mesh.interactive = mesh.buttonMode = false;

      this.meshList.push(mesh);
      this.container.addChild(mesh);
    }

    const faultGeometries = this.faultGeometries;
    const faultShader = this.faultShader;
    for (let i = 0, len = Math.ceil(faultGeometries.length / fragment); i < len; i++) {
      const arr = faultGeometries.slice(i * fragment, i * fragment + fragment);

      let mesh = utils.createMesh(arr, faultShader);
      mesh.name = "shelfStatus";
      mesh.mapType = "shelfStatus";
      mesh.interactive = mesh.buttonMode = false;

      this.meshList.push(mesh);
      this.container.addChild(mesh);
    }
  }

  drawGeometryLocked(options: mShelfData): void {
    const _this = this;
    const geometry = _this.utils.drawGeometry("icon", options["position"]);
    _this.lockedGeometries.push(geometry);
  }

  drawGeometryFault(options: mShelfData): void {
    const _this = this;
    const geometry = _this.utils.drawGeometry("icon", options["position"]);
    _this.faultGeometries.push(geometry);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.faultGeometries = [];
    this.lockedGeometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.lockedGeometries = null;
    this.faultGeometries = null;
    this.lockedShader = null;
    this.faultShader = null;
    this.container = null;
    this.fragment = null;
    this.utils = null;
    this.meshList = null;
  }
}
export default LayerShelfStatus;
