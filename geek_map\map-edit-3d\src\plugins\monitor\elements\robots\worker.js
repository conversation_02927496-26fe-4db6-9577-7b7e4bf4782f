let canvas = null,
  ctx = null;
let canvasScale = 1;

const initOffScreenCanvas = ({ floorInfo, config, space }) => {
  canvasScale = config.scale;
  canvas = new OffscreenCanvas(
    (floorInfo.w + space) * canvasScale,
    (floorInfo.h + space) * canvasScale,
  );
  ctx = canvas.getContext("2d");
};

const clearCanvas = () => {
  ctx.clearRect(0, 0, canvas.width, canvas.height);
};

const drawCanvas = value => {
  const lw = 4;
  for (let i = 0, l; (l = value[i++]); ) {
    ctx.beginPath();
    ctx.lineCap = "round";
    ctx.lineWidth = lw;
    ctx.lineJoin = "miter";
    const color = l[0];
    const [start] = l[1];
    ctx.shadowBlur = 0.5;
    ctx.strokeStyle = `#${color.toString(16)}`;
    ctx.moveTo(start[0] * canvasScale, start[1] * canvasScale);
    for (let j = 1, s; (s = l[j++]); ) {
      ctx.lineTo(s[0] * canvasScale, s[1] * canvasScale);
    }
    ctx.stroke();
    ctx.closePath();
  }
};

self.addEventListener("message", ({ data }) => {
  const { action, value } = data;
  if (action === "init") initOffScreenCanvas(value);
  if (action === "draw") {
    drawCanvas(value);
    const imageBitmap = canvas.transferToImageBitmap();
    self.postMessage({ action: "draw", imageBitmap: imageBitmap }, [imageBitmap]);
  }
  if (action === "clear") {
    clearCanvas();
  }
  if (action === "destory") {
    canvas = null;
    ctx = null;
  }
  if (action === "clearRefresh") {
    if (!canvas || !ctx) return;
    clearCanvas();
    const imageBitmap = canvas.transferToImageBitmap();
    self.postMessage({ action: "draw", imageBitmap: imageBitmap }, [imageBitmap]);
  }
});
