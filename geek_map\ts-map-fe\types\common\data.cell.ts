/* ! <AUTHOR> at 2023/04/18 */

/** 单元格空负载方向. 0:有方向; 1:无方向; */
type cellDir = 0 | 1;

/**
 * 单元格接口数据类型
 * @param cellCode 唯一单元格code，数据可能不给（遇到过）
 * @param cellType 类型
 * @param cellFlag
 * @param cellStatus 状态
 * @param sizeType
 * @param width 宽
 * @param length 长
 * @param loadDirs 负载，1无方向，0有方向
 * @param unloadDirs 空载，1无方向，0有方向
 * @param placementZone 热度数据，0-7
 * @param location 位置数据
 * @param startBounds 左下角数据
 * @param stationId 单元格上面的工作站ID（可选）
 * @param 其他 可选
 */
type cellData = {
  cellCode?: any;
  cellType: string;
  cellFlag: string;
  cellStatus: string;
  sizeType: string;
  width: number;
  length: number;
  loadDirs: [cellDir, cellDir, cellDir, cellDir];
  unloadDirs: [cellDir, cellDir, cellDir, cellDir];
  placementZone: number;
  location: location;
  startBounds?: location;
  stationId?: code;
  [propName: string]: any;
};

/**
 * 单元格地图数据类型，formate接口数据之后的数据类型
 * @param code 唯一code，数据可能不给（遇到过）
 * @param floorId 楼层
 * @param cellType
 * @param cellFlag
 * @param sizeType
 * @param loadDirs 负载，1无方向，0有方向
 * @param unloadDirs 空载，1无方向，0有方向
 * @param location 原始接口数据位置信息
 * @param stationId 单元格上面的工作站ID（可选）
 * @param shaderColor shader颜色，4维数组
 * @param score 热度数据，-1 - 7：其中-1为没有热度数据维持原色
 * @param position 乘以space系数之后的mesh位置，length为8的数组number
 * @param nsPosition mesh位置，length为8的数组number
 * @param hitArea xy的四个最大最小值，length为4的数组number
 * @param 其他 可选
 */
type mCellData = {
  code: code;
  floorId: floorId;
  cellType: string;
  cellFlag: string;
  sizeType: string;
  loadDirs: [cellDir, cellDir, cellDir, cellDir] | [] | null;
  unloadDirs: [cellDir, cellDir, cellDir, cellDir] | [] | null;
  location: location;
  stationId: code;
  shaderColor: Array<any>;
  score: number;
  position: meshPosition;
  nsPosition: meshPosition;
  hitArea: hitArea;
};
