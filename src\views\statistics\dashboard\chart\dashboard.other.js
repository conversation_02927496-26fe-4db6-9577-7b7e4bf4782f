import QRCodeLossRateTable from "./table/qRCodeLossRateTable";
import QRCodeLossRateLine from "./line/qRCodeLossRateLine";
import PlanPathNumCred from "./cred/planPathNumCred";
import AverageSpeedCred from "./cred/averageSpeedCred";
import AverageSpeedLine from "./line/averageSpeedLine";
import RobotChargingTable from "./table/robotChargingTable";
import StatAbnormalitiesTable from "./table/statAbnormalitiesTable"
import RobotChargingSumTable from "./table/robotChargingSumTable"
import RobotChargeBar from "./bar/robotChargeBar"
import RobotChargeNumBar from "./bar/robotChargeNumBar"
import RobotChargeLine from "./line/robotChargeLine"
import QRCodeBitLossRateTable from "./table/qRCodeBitLossRateTable";
import QRCodeBitLossRateLine from "./line/qRCodeBitLossRateLine";
import AverageSpeedDistanceLine from "./line/averageSpeedDistanceLine";

export default [
  new PlanPathNumCred({ width: '25%', height: '300px' }),
  new AverageSpeedCred({ width: '25%', height: '300px' }),
  new AverageSpeedLine({ width: '50%', height: '300px' }),
  new AverageSpeedDistanceLine({ width: '50%', height: '300px' }),
  new QRCodeLossRateTable({ width: '50%', height: '300px' }),
  new QRCodeLossRateLine({ width: '50%', height: '300px' }),
  new QRCodeBitLossRateTable({ width: '50%', height: '300px' }),
  new QRCodeBitLossRateLine({ width: '50%', height: '300px' }),
  new RobotChargingTable({ width: '50%', height: '300px' }),
  new StatAbnormalitiesTable({ width: '50%', height: '300px' }),
  new RobotChargingSumTable({ width: '50%', height: '300px' }),
  new RobotChargeBar({ width: '50%', height: '300px' }),
  new RobotChargeNumBar({ width: '50%', height: '300px' }),
  new RobotChargeLine({ width: '50%', height: '300px' }),
]