const eventMap = new Map();

class EventBus {
  static $getBus() {
    return eventMap;
  }

  static $on(event, cb) {
    //判断是否存在绑定的事件组
    let cbArr = eventMap.get(event);
    cbArr = cbArr ? cbArr : [];
    cbArr.push(cb);
    eventMap.set(event, cbArr);
  }

  static $off(event,cb) {
    const cbArr = eventMap.get(event)
    if(!cbArr || cbArr.length === 0) return
    const index = cbArr.findIndex(cbFn => cbFn === cb)
    cbArr.splice(index, 1)
    eventMap.set(event, cbArr);
  }

  static $emit(event, data) {
    let cbArr = eventMap.get(event);
    if (!cbArr) return console.warn('绑定事件不存在');
    cbArr.forEach(cb => {
      cb(data);
    });
  }

  static $clear(event) {
    eventMap.delete(event);
  }

  static $destroy() {
    eventMap.clear();
  }
}

export default EventBus
