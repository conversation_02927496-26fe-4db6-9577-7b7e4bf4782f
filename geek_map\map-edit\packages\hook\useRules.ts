/**
 * 获取一个必填的校验规则
 * @returns
 */
export function useRequired() {
  return {
    required: true,
    message: "请输入",
  };
}

/**
 * 获取一个纯数字的校验规则
 * @returns
 */
export function usePureNumber() {
  return {
    pattern: /^\d+$/,
    message: "请输入数字",
  };
}

/**
 * 获取一个字母和数字组合的校验规则
 * @returns
 */
export function useLetterNumber() {
  return {
    pattern: /^\w+$/,
    message: "请输入数字字母的组合",
  };
}

/**
 * 获取一个字母和数字组合的校验规则
 * @returns
 */
export function useCodeQualified() {
  return {
    pattern: /^[\w-]+$/,
    message: "请输入数字字母_-的组合",
  };
}
