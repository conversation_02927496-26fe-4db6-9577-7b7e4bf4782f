/* ! <AUTHOR> at 2022/09/02 */
import path from "path";
import * as PIXI from "pixi.js";

class LayerRobotTrail implements MRender.Layer {
  private utils: any;
  private container: PIXI.Container;
  private fragment: number;
  private geometries: Array<any> = [];
  private meshList: any = [];
  private lineWidth: number;
  init(mapCore: MRender.MainCore): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "robotTrail";
    container.zIndex = utils.getLayerZIndex("robot");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.lineWidth = utils.getSettings("robotPathWidth");
    this.utils = utils;
  }

  render(): void {
    const _this = this;
    const geometries = _this.geometries;
    if (!geometries.length) return;
    const utils = _this.utils;
    const fragment = _this.fragment;
    const shader = utils.getShader("rect");

    for (let i = 0, len = Math.ceil(geometries.length / fragment); i < len; i++) {
      const arr = geometries.slice(i * fragment, i * fragment + fragment);

      let mesh = utils.createMesh(arr, shader);
      mesh.name = "robotTrail";
      mesh.mapType = "robotTrail";
      mesh.interactive = mesh.buttonMode = false;

      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }
    this.geometries = [];
  }

  drawGeometry(options: mRobotData, color?: number): void {
    const path = options["path"];
    if (!path.length) return;
    const _this = this,
      utils = _this.utils;

    let vColor;
    if (color) {
      vColor = utils.getShaderRGB(color);
    } else {
      vColor = utils.getShaderRGB(options["color"]);
    }
    let p1, p2, geometry;
    for (let i = 1, len = path.length; i < len; i++) {
      p1 = path[i - 1];
      p2 = path[i];
      if (!p1 || !p2) continue;

      const position = _this.formatTrailLine([p1, p2]);
      geometry = utils.drawGeometry("rect", position, vColor);
      _this.geometries.push(geometry);
    }
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach((mesh: any) => mesh.destroy(true));
    this.meshList = [];
    this.geometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.geometries = null;
    this.utils = null;
    this.meshList = null;
    this.lineWidth = null;
  }

  private formatTrailLine(points: Array<any>): any {
    const lineWidth = Number((this.lineWidth / 100).toFixed(3));
    const p1 = { x: points[0].x, y: -points[0].y };
    const p2 = { x: points[1].x, y: -points[1].y };

    let shapeData: Array<number>, angle: number;
    if (p1.x === p2.x) {
      // 竖向直线
      if (p2.y > p1.y) angle = -90;
      else angle = 90;
    } else if (p1["y"] === p2["y"]) {
      // 横向直线
      if (p2.x > p1.x) angle = 0;
      else angle = 180;
    } else {
      // 斜向直线
      angle = (Math.atan2(p2.y - p1.y, p2.x - p1.x) * 180) / Math.PI;
    }

    const angle1 = ((angle + 90) * Math.PI) / 180;
    const angle2 = ((angle - 90) * Math.PI) / 180;
    const topX = lineWidth * Math.cos(angle1);
    const topY = lineWidth * Math.sin(angle1);
    const botX = lineWidth * Math.cos(angle2);
    const botY = lineWidth * Math.sin(angle2);
    shapeData = [
      p1.x + botX,
      p1.y + botY,
      p1.x + topX,
      p1.y + topY,
      p2.x + topX,
      p2.y + topY,
      p2.x + botX,
      p2.y + botY,
    ];

    return shapeData;
  }
}
export default LayerRobotTrail;
