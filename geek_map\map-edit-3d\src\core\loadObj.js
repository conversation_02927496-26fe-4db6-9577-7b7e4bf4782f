import { FB<PERSON>Loader } from "three/examples/jsm/loaders/FBXLoader";
import { OBJLoader } from "three/examples/jsm/loaders/OBJLoader";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import { RoughnessMipmapper } from "three/examples/jsm/utils/RoughnessMipmapper.js";
import * as THREE from "three";
import { station2, station6 } from "../elements/customGeo/station";
import { P40, RS, P800 } from "../elements/customGeo/robot";

const objExtension = ["obj"];
const gltfExtension = ["glft", "glb"];
const fbxExtension = ["fbx"];

class LoadObj {
  constructor(Map3d) {
    this.waitExec = [];
    this.cache = [];
    this.status = {}; // ready - loading - success - fail;
    this.modelMap = Map3d.config.modelMap;
    this.baseUrl = Map3d.baseUrl;
    this.Map3d = Map3d;
    this.__setStatus();
  }
  get(type) {
    return this.cache[type];
  }
  getLazy(type, success, fail) {
    const mesh = this.cache[type];
    if (mesh) return success(mesh);
    this.waitExec[type] = (this.waitExec[type] || []).concat([[success, fail]]);
    this.load(type);
  }
  load(type) {
    const types = [].concat(type);
    for (let i = 0, t; (t = types[i++]); ) {
      const status = this.status[t];
      if (!status || ["loading", "success"].includes(status)) continue;
      this.status[t] = "loading";
      let { path } = this.modelMap[t];
      if (!path) path = this.modelMap[t];
      Promise.all([].concat(path).map(p => this.__fetchObj(p))).then(
        data => this.__loadSuccess.apply(this, [t, path, data]),
        () => this.__loadFail.call(this, t),
      );
    }
  }
  destory() {
    this.waitExec = [];
    this.cache = [];
    this.status = {};
  }
  __loadSuccess(type, path, data) {
    const success = model => {
      this.cache[type] = model;
      const watiExec = this.waitExec[type];
      if (watiExec && watiExec.length) {
        watiExec && watiExec.forEach(([success]) => success && success(model));
        this.waitExec[type] = [];
      }
    };
    this.status[type] = "success";
    let obj = Array.isArray(path) ? data : data[0];
    this.__extendModel(type, obj, success);
  }
  __extendModel(type, object, next) {
    const { insetShape, scale, setAttributes } = this.modelMap[type];
    if (insetShape === "station2") {
      station2(object, { next });
    } else if (insetShape === "station6") {
      station6(object, { next });
    } else if (insetShape === "P40") {
      P40(object, { next });
    } else if (insetShape === "RS") {
      RS(object, { next });
    } else if (insetShape === "P800") {
      P800(object, { next });
    } else {
      const model = object.scene || object;
      scale && model.scale.set(scale, scale, scale);
      next && next(setAttributes ? setAttributes(model) : model);
    }
  }
  __loadFail(type) {
    this.status[type] = "fail";
    this.waitExec[type].forEach(([success, fail]) => fail && fail(type));
    this.waitExec[type] = [];
  }
  __fetchObj(path) {
    const that = this;
    return new Promise((resolve, reject) => {
      const loader = that.__getLoader(path);
      loader.load(
        path,
        object => {
          // gltf涉及到材质，需要特殊处理下；
          if (loader instanceof GLTFLoader) {
            const roughnessMipmapper = new RoughnessMipmapper(that.Map3d.renderer);
            object.scene.traverse(child => {
              if (child.isMesh) {
                roughnessMipmapper.generateMipmaps(child.material);
                child.frustumCulled = false;
                child.castShadow = false;
                child.material.emissive = child.material.color;
                child.material.emissiveMap = child.material.map;
                child.material.emissiveIntensity = 0.8;
                let specularColor;
                if (child.material.type == "MeshStandardMaterial") {
                  if (child.material.metalness) {
                    child.material.metalness *= 0.1;
                  }
                  if (child.material.glossiness) {
                    child.material.glossiness *= 0.25;
                  }
                  specularColor = new THREE.Color(12, 12, 12);
                } else if (c.material.type == "MeshPhongMaterial") {
                  child.material.shininess = 0.1;
                  specularColor = new THREE.Color(20, 20, 20);
                }
                if (child.material.specular && child.material.specular.isColor) {
                  child.material.specular = specularColor;
                }
              }
            });
            roughnessMipmapper.dispose();
          }
          resolve(object);
        },
        null,
        reject,
      );
    });
  }

  __getLoader(path) {
    if (!path) return;
    let loader = null;
    const fileExtension = path.split(".").pop().toLowerCase();
    if (objExtension.includes(fileExtension)) {
      loader = new OBJLoader();
    } else if (fbxExtension.includes(fileExtension)) {
      loader = new FBXLoader();
    } else if (gltfExtension.includes(fileExtension)) {
      loader = new GLTFLoader();
    }
    return loader;
  }

  __setStatus() {
    this.status = Object.keys(this.modelMap).reduce(
      (pre, cur) => Object.assign(pre, { [cur]: "ready" }),
      {},
    );
  }
}

export default LoadObj;
