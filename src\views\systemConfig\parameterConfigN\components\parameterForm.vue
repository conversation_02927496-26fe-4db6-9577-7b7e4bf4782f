<template>
  <div class="parameterFromBox">
    <div v-for="item in newList" :key="item.id">
      <div
        v-for="(itemb, index) in item"
        :key="itemb.id"
        :class="
          itemb.code == 'charging.safePowerPercent' ||
          itemb.code == 'charging.chargingTime' ||
          itemb.code == 'charging.task.allowedPowerToBePreempted' ||
          itemb.code == 'charging.task.triggerPreemptionPower' ||
          itemb.code == 'charging.task.electricityPreemptionDifference' ||
          itemb.code == 'charging.idleCharging.lowPowerPercent' ||
          itemb.code == 'charging.idleCharging.idleTime'
            ? 'inline'
            : ''
        "
      >
        <template v-if="path == itemb.path && itemb.isShow">
          <div v-if="itemb.sectionLabel && index === 0" class="third-menu">
            {{ itemb.sectionLabel }}
          </div>
          <div v-if="itemb.code == 'charging.lowPowerPercent'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotPowerLowerThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{
              $t("lang.rms.config.page.willSendTaskRobotButMaybeFail")
            }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div
            v-else-if="itemb.code == 'robot.task.unworkableWhenLowPower'"
            class="form-input flex_w"
          >
            <span class="form-text">{{ $t("lang.rms.config.page.robotPowerLowerThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.onlyAcceptChargingTask") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.upperPowerPercent'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotPowerHigherThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.willStopChargingTask") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.safePowerPercent'" class="form-input flex_w">
            <span class="form-text">{{
              $t("lang.rms.config.page.chargingTaskCanEarlyEndAndMeetSafytyPower")
            }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
          </div>
          <div v-else-if="itemb.code == 'charging.chargingTime'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.chargingDurationGreaterThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">min</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.task.waitTimeMinutes'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.assigneChargingTaskToRobotMoreThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">min</span>
            <span class="form-text">{{ $t("lang.rms.config.page.reassignNewChargingPileToRobot") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div
            v-else-if="itemb.code == 'charging.idleCharging.lowPowerPercent'"
            class="form-input flex_w"
          >
            <span class="form-text">{{ $t("lang.rms.config.page.robotFreeAndPowerLessThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div v-else-if="itemb.code == 'charging.idleCharging.idleTime'" class="form-input flex_w">
            <span class="form-text">{{ $t("lang.rms.config.page.robotFreeTimeGreaterThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">min</span>
            <span class="form-text">{{ $t("lang.rms.config.page.willTargetChargingTask") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div
            v-else-if="itemb.code == 'charging.task.allowedPowerToBePreempted'"
            class="form-input flex_w"
          >
            <span class="form-text">{{ $t("lang.rms.config.page.powerLessThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div
            v-else-if="itemb.code == 'charging.task.triggerPreemptionPower'"
            class="form-input flex_w"
          >
            <span class="form-text">{{ $t("lang.rms.config.page.raceToControlPowerGreaterThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.robotChargingPile") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div
            v-else-if="itemb.code == 'charging.task.electricityPreemptionDifference'"
            class="form-input flex_w"
          >
            <span class="form-text">{{ $t("lang.rms.config.page.formerPowerLessThanLatterPower") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <span class="form-text">{{ $t("lang.rms.config.page.raceToControlSuccessfully") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div
            v-else-if="itemb.code == 'shelf.dynamicAdjustment.globalConcurrency'"
            class="form-input flex_w"
          >
            <span class="form-text">{{ $t("lang.rms.config.page.generateMaxNumberTask") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">{{ $t("lang.rms.web.piece") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div
            v-else-if="itemb.code == 'shelf.dynamicAdjustment.idleRobotCount'"
            class="form-input flex_w"
          >
            <span class="form-text">{{ $t("lang.rms.config.page.dynamicAdjustRobotFreeSizeGreaterThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">{{ $t("lang.rms.config.page.robotSize") }}</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <div
            v-else-if="itemb.code == 'shelf.dynamicAdjustment.idleRobotRate'"
            class="form-input flex_w"
          >
            <span class="form-text">{{ $t("lang.rms.config.page.dynamicAdjustRobotFreeScaleGreaterThan") }}</span>
            <el-input
              v-if="
                itemb.valueType === 'java.lang.Integer' &&
                itemb.code !== 'algorithm.job.match.needToTurnPara'
              "
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              type="number"
              @change="change(itemb)"
            />
            <el-input
              v-else
              v-model="itemb.options.defVal"
              :placeholder="$t('lang.rms.fed.pleaseEnter')"
              :max="itemb.options.limitMax"
              :min="itemb.options.limitMin"
              @change="change(itemb)"
            />
            <span class="form-text">%</span>
            <detailPopover :current-row="itemb" />
            <span v-show="itemb.errorTipShow" class="error-tip">
              {{ itemb.errorTipText }}
            </span>
          </div>
          <template v-else>
            <!-- 下拉框 -->
            <div v-if="itemb.type == 'select'" class="form-input">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="itemb.immediate == false && itemb.value !== itemb.currentUpdateValue"
                  class="d"
                />
                <span class="form-title">{{ itemb.label }} :</span>
                <el-tooltip
                  v-if="itemb.immediate == false && itemb.value !== itemb.currentUpdateValue"
                  effect="dark"
                  :content="`${$t('lang.rms.fed.currentEffectiveValues')}：${itemb.currentUpdateValue}`"
                  placement="top"
                >
                  <el-select
                    v-model="itemb.options.defVal"
                    clearable
                    :placeholder="$t('lang.rms.fed.pleaseChoose')"
                    @change="change(itemb)"
                  >
                    <el-option
                      v-for="itemc in itemb.options.selectList"
                      :key="itemc"
                      :label="itemc"
                      :value="itemc"
                    >
                    </el-option>
                  </el-select>
                </el-tooltip>
                <el-select
                  v-else
                  v-model="itemb.options.defVal"
                  clearable
                  :placeholder="$t('lang.rms.fed.pleaseChoose')"
                  @change="change(itemb)"
                >
                  <el-option
                    v-for="itemc in itemb.options.selectList"
                    :key="itemc"
                    :label="itemc"
                    :value="itemc"
                  >
                  </el-option>
                </el-select>
                <detailPopover :current-row="itemb" />
              </span>
            </div>
            <!-- 输入框 -->
            <div v-if="itemb.type == 'text'" class="form-input">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="itemb.immediate == false && itemb.value !== itemb.currentUpdateValue"
                  class="d"
                />
                <span class="form-title">{{ itemb.label }} :</span>
                <el-tooltip
                  v-if="itemb.immediate == false && itemb.value !== itemb.currentUpdateValue"
                  effect="dark"
                  :content="`${$t('lang.rms.fed.currentEffectiveValues')}：${itemb.currentUpdateValue}`"
                  placement="top"
                >
                  <el-input
                    v-if="
                      itemb.valueType === 'java.lang.Integer' &&
                      itemb.code !== 'algorithm.job.match.needToTurnPara'
                    "
                    v-model="itemb.options.defVal"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    :max="itemb.options.limitMax"
                    :min="itemb.options.limitMin"
                    type="number"
    
                    @change="change(itemb)"
                  />
                  <el-input
                    v-else
                    v-model="itemb.options.defVal"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    :max="itemb.options.limitMax"
                    :min="itemb.options.limitMin"
                    @change="change(itemb)"
                  />
                </el-tooltip>
                <template v-else>
                  <el-input
                    v-if="
                      itemb.valueType === 'java.lang.Integer' &&
                      itemb.code !== 'algorithm.job.match.needToTurnPara'
                    "
                    v-model="itemb.options.defVal"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    :max="itemb.options.limitMax"
                    :min="itemb.options.limitMin"
                    type="number"
    
                    @change="change(itemb)"
                  />
                  <el-input
                    v-else
                    v-model="itemb.options.defVal"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    :max="itemb.options.limitMax"
                    :min="itemb.options.limitMin"
                    @change="change(itemb)"
                  />
                </template>
                <detailPopover :current-row="itemb" />
                <span v-show="itemb.errorTipShow" class="error-tip">
                  {{ itemb.errorTipText }}
                </span>
              </span>
            </div>
            <!-- 文本域 -->
            <div v-if="itemb.type == 'textarea'" class="form-input">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="itemb.immediate == false && itemb.value !== itemb.currentUpdateValue"
                  class="d"
                />
                <span class="form-title">{{ itemb.label }} :</span>
                <el-tooltip
                  v-if="itemb.immediate == false && itemb.value !== itemb.currentUpdateValue"
                  effect="dark"
                  :content="`${$t('lang.rms.fed.currentEffectiveValues')}：${itemb.currentUpdateValue}`"
                  placement="top"
                >
                  <el-input
                    v-model="itemb.options.defVal"
                    type="textarea"
                    :rows="1"
                    :placeholder="$t('lang.rms.fed.pleaseEnter')"
                    class="textarea"
                    @change="change(itemb)"
                  />
                </el-tooltip>
                <el-input
                  v-else
                  v-model="itemb.options.defVal"
                  type="textarea"
                  :rows="1"
                  :placeholder="$t('lang.rms.fed.pleaseEnter')"
                  class="textarea"
                  @change="change(itemb)"
                />
                <detailPopover :current-row="itemb" />
              </span>
            </div>
            <!-- 开关 -->
            <div v-if="itemb.type == 'checkbox'" class="form-switch">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="itemb.immediate == false && itemb.value !== itemb.currentUpdateValue"
                  class="d"
                />
                <span class="form-title">{{ itemb.label }} :</span>
                <el-switch
                  v-model="itemb.options.defVal"
                  active-color="#409EFF;"
                  :active-text="$t('lang.rms.fed.on')"
                  :inactive-text="$t('lang.rms.fed.off')"
                  active-value="true"
                  inactive-value="false"
                  @change="change(itemb)"
                >
                </el-switch>
                <detailPopover :current-row="itemb" />
              </span>
            </div>
            <!-- 单独时间选择器 -->
            <div v-if="itemb.type == 'timeslot'" class="form-input">
              <span v-if="itemb.immediate == false ? true : show">
                <i
                  v-if="itemb.immediate == false && itemb.value !== itemb.currentUpdateValue"
                  class="d"
                />
                <span class="form-title">{{ itemb.label }} :</span>
                <el-time-picker
                  v-model="itemb.options.defVal"
                  is-range
                  range-separator="—"
                  value-format="HH:mm:ss"
                  @change="change(itemb)"
                />
                <detailPopover :current-row="itemb" />
              </span>
            </div>
            <!-- 可添加时间选择器 -->
            <div v-if="itemb.type == 'timeslots'" class="form-input">
              <div v-if="itemb.immediate == false ? true : show" class="flex_c">
                <div>
                  <i
                    v-if="itemb.immediate == false && itemb.value !== itemb.currentUpdateValue"
                    class="d"
                  />
                  <span class="form-title">{{ itemb.label }} :</span>
                </div>
                <div>
                  <timePickerSlots
                    :value="itemb.options.defVal"
                    @change="data => timePickerSlotsChange(itemb, data)"
                  ></timePickerSlots>
                </div>
                <detailPopover :current-row="itemb" />
              </div>
            </div>
          </template>
        </template>
      </div>
    </div>
    <!-- <dialogDetail v-if="dialog" :items="data" @close="close"></dialogDetail> -->
  </div>
</template>

<script>
import detailPopover from "./detailPopover";
import timePickerSlots from "./timePickerSlots";
export default {
  name: "WorkspaceJsonBasicConfig",
  components: {
    detailPopover,
    timePickerSlots,
  },
  props: {
    path: {
      type: String,
      default() {
        return "";
      },
    },
    list: {
      type: [Array, Object],
      default() {
        return [];
      },
    },
    show: {
      type: Boolean,
      default() {
        return true;
      },
    },
    // immediateShow: {
    //   type: Boolean,
    //   default() {
    //     return false;
    //   },
    // },
  },
  data() {
    return {
      newList: {},
      value: "",
      switchValue: true,
      dialog: false,
      data: {},
      input: "",
      changeData: false,
      isShow: true,
    };
  },
  watch: {
    // change: {
    //   // 一级tab默认展示
    //   handler(newval, oldval) {
    //     console.log("newval", newval);
    //     console.log("oldval", oldval);
    //   },
    //   deep: true,
    // },
    list: {
      handler(val) {
        // console.log(val, 11);
        this.newList = JSON.parse(JSON.stringify(val));
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // console.log(this.list, "parameterForm")
  },
  methods: {
    detail(data) {
      this.dialog = true;
      this.data = data;
    },
    close(n) {
      if (n === 1) {
        this.dialog = false;
      }
    },
    change(data) {
      // data.valueType === 'java.lang.Double' ||
      // data.valueType === 'java.lang.Integer'
      let value = data.options.defVal;
      data.errorTipShow = false;
      data.errorTipText = ""
      if (data.valueType === "java.lang.Integer") {
        let intReg = /^[0-9]*$/;
        if (data.options.limitMin && data.options.limitMin == -1) {
          intReg = /^(-1|([0-9]*))$/;
        }
        if (!intReg.test(value)) {
          data.errorTipShow = true;
          data.errorTipText = `${this.$t("lang.rms.fed.pleaseEnter")}${data.validValueRange}`
          // this.$emit("changeData", this.changeData);
          // this.$emit("itemsDetail", data);
          // return false;
        }
        if (
          data.options.limitMin &&
          (Number(value) < data.options.limitMin || Number(value) > data.options.limitMax)
        ) {
          data.errorTipShow = true;
          data.errorTipText = `${this.$t("lang.rms.fed.pleaseEnter")}${data.validValueRange}`
          console.log("错误开始");
          // this.changeData = true;
          // this.$emit("changeData", this.changeData);
          // this.$emit("itemsDetail", data);
          // return false;
        }
      }
      this.changeData = true;
      this.$emit("changeData", this.changeData);
      this.$emit("itemsDetail", data);
    },
    timePickerSlotsChange(item, timeSlotsVal) {
      this.changeData = true;
      this.$emit("changeData", this.changeData);
      item.options.defVal = timeSlotsVal;
      this.$emit("itemsDetail", item);
    },
    // 单独时间选择
    // timePickerSlotChange(item) {
    //   this.changeData = true;
    //   this.$emit("changeData", this.changeData);
    //   item.options.defVal = timeSlotsVal;
    //   this.$emit("itemsDetail", item);
    // },
  },
};
</script>

<style lang="less" scoped>
.parameterFromBox {
  line-height: 38.6px;
  margin-top: -6px;
}
.form-title,
.form-text {
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 22px;
  color: #686c71;
  margin-right: 6px;
}
.form-switch :deep(.el-switch__label) {
  position: absolute;
  display: none;
  color: #fff;
  font-size: 12px;
}
.form-switch :deep(.el-switch__label--right) {
  z-index: 1;
  left: -3px;
}
.form-switch :deep(.el-switch__label--left) {
  z-index: 1;
  left: 19px;
}
.form-switch :deep(.el-switch__label.is-active) {
  display: block;
}
.questionImg {
  width: 17px;
}
.questionImg:hover {
  cursor: pointer;
}
.form-input .el-input {
  width: 200px;
  height: 32px;
  background: #ffffff;
  border-radius: 6px;
}
.d {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: #f23f3f;
  border-radius: 6px;
  margin-right: 6px;
}
/deep/.el-date-editor {
  width: 240px;
  height: 30px;
  background: #ffffff;
  border-radius: 6px;
}
/deep/.el-range-separator {
  color: #999ea5;
  margin-left: -10px;
}
.textarea {
  width: 222px;
}
.ss {
  display: none;
}
.third-menu {
  font-size: 12px;
  &::before {
    content: "";
    display: inline-block;
    height: 18px;
    width: 4px;
    border-radius: 4px;
    background: #409eff;
    margin-right: 4px;
    vertical-align: text-bottom;
  }
}
.inline {
  display: inline-block;
}
.flex_c {
  display: flex;
}
.flex_w {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.error-tip {
  font-size: 14px;
  color: red;
}
</style>
