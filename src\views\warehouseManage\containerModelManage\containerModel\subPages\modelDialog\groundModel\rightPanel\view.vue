<template>
  <!-- 预览任务 -->
  <div class="viewTask">
    <p class="taskTitle">{{ $t("lang.rms.fed.createGroundSupportModel") }}</p>
    <div class="createTask">
      <el-input v-model="createName" class="createTaskInput" spellcheck="false" clearable />
      <el-button class="createTaskBtn" type="primary" @click="createTask">
        {{ $t("lang.rms.fed.createModel") }}
      </el-button>
    </div>
    <div class="taskGroupDesc">
      {{ $t("lang.rms.fed.dockModelGroundDesc") }}
    </div>

    <div class="dividerStyle" />

    <el-row v-loading="requsetAllTasksLoading" class="taskListsMain">
      <el-col v-if="!taskList.length" :span="24">
        {{ $t("lang.rms.fed.noModelData") }}
      </el-col>
      <el-col v-else :span="24">
        <div v-for="(item, index) in taskList" :key="index" class="taskList">
          <p class="taskListItemTitle">{{ item.name }}</p>
          <div class="taskListItemOpen">
            <el-tooltip class="item" effect="dark" :content="$t('lang.rms.fed.edit')" placement="bottom">
              <el-button type="primary" icon="el-icon-edit" circle size="mini" @click="editTask(index)" />
            </el-tooltip>
            <el-tooltip class="item" effect="dark" :content="$t('lang.rms.fed.delete')" placement="bottom">
              <el-button
                type="danger"
                icon="el-icon-delete"
                :loading="item.removeLoading"
                circle
                size="mini"
                @click="removeTask(index)"
              />
            </el-tooltip>
          </div>
        </div>
      </el-col>
    </el-row>

    <div class="dividerStyle" />
    <!--<el-button type="primary" class="resetBtn" @click="resetBoot">{{$t('lang.rms.fed.restartTakesEffect')}}</el-button>-->
    <el-button type="primary" class="resetBtn" @click="renewalModel">
      {{ $t("lang.rms.fed.renewalModel") }}
    </el-button>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  props: {},
  data() {
    return {
      apiStore: {},
      // 创建任务名称
      createName: "",
      // loading
      requsetAllTasksLoading: false,
      exportModelFileLoad: false,
      taskList: [],
    };
  },
  created() {
    this.getAllShelfModels();
  },
  methods: {
    getAllShelfModels() {
      // 获取所有的任务
      this.requsetAllTasksLoading = true;
      $req.get("/athena/shelfModel/list", { mapId: "" }).then(res => {
        if (res.code === 0) {
          this.taskList = [];
          this.requsetAllTasksLoading = false;
          res.data.forEach(item => {
            if (item.type === "holder") {
              this.taskList.push({
                ...item,
                removeLoading: false,
              });
            }
          });
        }
      });
    },

    /**
     * 创建任务
     */
    createTask() {
      const { createName, taskList } = this;
      if (!createName) {
        this.$message.error(`${this.$t("lang.rms.fed.pleaseEnterModelName")}!`);
        return;
      }

      if ((taskList || []).find(({ name }) => name === createName)) {
        this.$message.error(`${this.$t("lang.rms.fed.duplicateWithExisName")}!`);
        return;
      }

      this.$emit("createTask", createName);
      this.createName = "";
    },

    /**
     * 删除任务
     */
    removeTask(index) {
      const item = this.taskList[index];
      item.removeLoading = true;
      $req.postParams("/athena/shelfModel/delete", { id: item.id }).then(res => {
        item.removeLoading = false;
        if (res.code === 0) {
          this.taskList.splice(index, 1);
          this.getAllShelfModels();
        }
      });
    },

    /**
     * 编辑任务
     */
    editTask(index) {
      const item = this.taskList[index];
      this.$emit("editTask", item.id);
    },

    // 重启机器人
    resetBoot() {},

    // 导出文件
    exportModelFile() {
      this.exportModelFileLoad = true;
      $req.get("/athena/shelfModel/exportModelFile").then(res => {
        this.exportModelFileLoad = false;
        if (res.code === 0) {
          window.open(res.url);
        }
      });
    },
    renewalModel() {
      $req.postParams("/athena/shelfModel/sendModelToRobot", { id: 0 }).then(res => {
        if (res.code === 0) {
          this.$message.success(this.$t("lang.common.success"));
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.resetBtn {
  margin-bottom: 10px;
}

.viewTask {
  height: 100%;
  display: flex;
  flex-direction: column;

  .taskListsMain {
    flex: 1;
    overflow: auto;
    padding: 10px;
    border-radius: 10px;
    border: 1px solid #eee;
  }

  .taskTitle {
    text-align: left;
  }

  .modelTitle {
    text-align: left;
    padding-bottom: 10px;
  }

  .createTask {
    height: 35px;
    width: 100%;
    display: flex;
    margin-bottom: 15px;
    background-color: #ffffff;

    .createTaskInput {
      height: 100%;
    }

    .createTaskBtn {
      width: 100px;
      height: 100%;
      padding: 0 10px;
    }
  }

  .taskGroupDesc {
    text-align: left;
  }

  .taskList {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid #dcdfe6;

    .taskListItemTitle {
      flex: 1;
      text-align: left;
      text-indent: 10px;
      line-height: 30px;
    }

    .taskListItemOpen {
      padding-right: 10px;
    }
  }
}

.divider-hr {
  margin: 12px 0;
}

.mb5 {
  margin-bottom: 5px;
}

.mb15 {
  margin-bottom: 15px;
}

.dividerStyle {
  display: block;
  height: 1px;
  width: 100%;
  margin: 24px 0;
  background-color: #dcdfe6;
  position: relative;
}

.tc {
  text-align: center;
}
</style>
