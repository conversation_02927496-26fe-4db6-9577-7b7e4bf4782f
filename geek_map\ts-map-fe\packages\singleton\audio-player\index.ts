class SingletonVoice {
  private static canPlay: boolean = false;
  private static textList: any[] = [];
  private static _audio: any;
  private static _instance: SingletonVoice;

  constructor() {
    if (SingletonVoice._audio) return;
    const _audio = new Audio();
    document.body.appendChild(_audio);

    _audio.addEventListener("ended", () => {
      this.playText();
    });
    _audio.addEventListener("error", () => {
      _audio.pause();
    });
    SingletonVoice._audio = _audio;
  }

  static getInstance() {
    if (!this._instance) {
      console.log("[SingletonVoice] ----- :: 创建 Audio");
      this._instance = new SingletonVoice();
    }
    return this._instance;
  }

  play() {
    SingletonVoice.canPlay = true;
    this.playText();
  }

  pause() {
    SingletonVoice.canPlay = false;
  }

  addText(text: any) {
    const _this = SingletonVoice;
    _this.textList.push(text);
    const len = _this.textList.length;
    // 大于50条就删除大于50条的内容，每次语音播报会播报最新内容
    if (len > 50) {
      _this.textList.splice(0, len - 5);
    }
    this.playText();
  }

  private playText() {
    const _this = SingletonVoice;
    if (!_this._audio.paused || !_this.canPlay) return;

    const lastText = _this.textList.pop();
    if (!lastText) return;
    _this._audio.src = `https://tts.baidu.com/text2audio?cuid=baike&lan=zh&ctp=1&pdt=301&vol=15&per=1&tex=${encodeURI(
      lastText,
    )}`;
    _this._audio.play();
  }
}

function play() {
  return SingletonVoice.getInstance().play();
}

function pause() {
  return SingletonVoice.getInstance().pause();
}

function addText(text: any) {
  return SingletonVoice.getInstance().addText(text);
}

export { play, pause, addText };
