<template>
  <div class="app-container">
    <el-dialog
      :title="$t('lang.rms.fed.exportBackgroundResource')"
      :visible.sync="exportVisible"
      width="30%"
      :close-on-click-modal="false"
      :before-close="handleClose"
      center
    >
      <span>
        <el-radio-group v-model="radio">
          <el-radio :label="'zip'">{{ $t("lang.rms.fed.exportZipResource") }}</el-radio>
          <el-radio :label="'png'">{{ $t("lang.rms.fed.exportPngResource") }}</el-radio>
        </el-radio-group>
      </span>
      <div class="mt15" v-if="radio === 'zip'">{{ $t('lang.rms.fed.exportDesc') }}</div>
      <div class="mt15" v-else>{{ $t('lang.rms.fed.exportPngDirectly') }}</div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSubmit">{{ $t("lang.rms.fed.confirm") }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
export default {
  name: 'ExportResources',
  props: {
    exportVisible: {
      type: Boolean,
      default: false
    },
    activeName: {
      type: String,
      require: true
    }
  },
  data() {
    return {
      radio: 'zip'
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleSubmit() {
      const { mapId, floorId } = this.$route.query
      const params = '/' + mapId + '/' + floorId + '/' + this.activeName
      if (this.radio === 'png') {
        axios({
          method: 'get',
          url: '/athena/map/draw/exportBackgroundImage?mapId=' + mapId + '&floorId=' + floorId + '&robotType=' + this.activeName,
          responseType: 'blob'
        }).then((res) => {
          if (!res) {
            return
          }
          const blob = new Blob([res.data], {
            type: 'application/octet-stream'
          })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.download = 'background' + '.png'
          document.body.appendChild(link)
          link.click()
        })
      } else {
        axios({
          method: 'get',
          url: '/athena/map/draw/backgroundResource' + params,
          responseType: 'blob'
        }).then((res) => {
          if (!res) {
            return
          }
          const blob = new Blob([res.data], {
            type: 'application/octet-stream'
          })
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.style.display = 'none'
          link.href = url
          link.download = 'background' + '.zip'
          document.body.appendChild(link)
          link.click()
        })
      }
    }
  }
}
</script>

<style lang="less" scoped></style>
