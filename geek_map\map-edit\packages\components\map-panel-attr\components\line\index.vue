<template>
  <div class="attrPanel">
    <TabFromBase
      :fromData="curSelectNode"
      ref="baseTabsRef"
      :title="$t(title)"
      :tabs="tabData"
      @change="change"
    >
      <template #coordinatesSlot> </template>
      <template #controlPointSlot> </template>
    </TabFromBase>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia";
import { computed, ComputedRef, ref } from "vue";
import TabFromBase from "../base/tabBase.vue";
import { useAttrStore } from "@packages/store/attr";
import { getTabConf, changeBaseTabData } from "../common";
import { NODE_LINE } from "@packages/configure/dict/nodeType";

const attrStore = useAttrStore();
const attrStoreRef = storeToRefs(useAttrStore());

const props = defineProps<{
  panelConf?: {
    hiddenTabs: string[];
    hiddenCodes: string[];
    disabledCodes: string[];
  };
  title: string;
}>();

const hiddenTabs: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenTabs || []);
const hiddenCodes: ComputedRef<string[]> = computed(() => props.panelConf?.hiddenCodes || []);
const disabledCodes: ComputedRef<string[]> = computed(() => props.panelConf?.disabledCodes || []);
const baseTabsRef = ref();

const tabData = computed(() => {
  return getTabConf(
    {
      hiddenTabs: hiddenTabs.value,
      hiddenCodes: hiddenCodes.value,
      disabledCodes: disabledCodes.value,
      type: NODE_LINE,
    },
    attrStore,
    baseTabsRef.value,
  );
});

const curSelectNode = computed(() => {
  const curSelectNode = attrStoreRef.curNodeDataByIndex.value || {};
  return curSelectNode;
});

function change(option: any) {
  changeBaseTabData(option, attrStore);
}

function getRef() {
  return baseTabsRef.value;
}

defineExpose({
  getRef,
});
</script>

<style scoped lang="scss">
.attrPanel {
  min-width: 3px;
  height: 100%;
  border-left: 1px solid #eee;
  box-shadow: 0 3px 5px 1px #eee;
  font-size: 0px;
}
</style>
