import Base from "../../core/abstractPlugin";
import hoverPluginInstance from "./actions/hoverPlugin";
import addPluginInstance from "./actions/addPlugin";
import selectPluginInstance from "./actions/selectPlugin";
import compassPluginInstance from "../common/compassPlugin";
import moveModelPluginInstance from "./actions/moveModelPlugin";
import selectMapCellPluginInstance from "./actions/selectMapCellPlugin";

const use_plugins = [
  "selectPlugin",
  "addPlugin",
  "hoverPlugin",
  "movePlugin",
  "selectMapCellPlugin",
  "compassPlugin",
];

class EditPlugin extends Base {
  constructor(options) {
    super(options);
    this.PluginName = "editPlugin";
  }
  created() {
    const plugins = [
      selectPluginInstance,
      hoverPluginInstance,
      addPluginInstance,
      compassPluginInstance,
      moveModelPluginInstance,
      selectMapCellPluginInstance,
    ];
    this.Map3d.registerPlugin(plugins);
    this.Map3d.loadObj.load(Object.keys(this.Map3d.config.modelMap));
  }
  activated() {
    this.Map3d.enablePlugin(use_plugins);
  }
  deactivated() {
    this.Map3d.disabledPlugin(use_plugins);
  }
}

export default EditPlugin;
