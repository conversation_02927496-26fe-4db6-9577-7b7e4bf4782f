/* ! <AUTHOR> at 2021/01 */

import Vue from "vue";
import Vuex from "vuex";
import createLogger from "vuex/dist/logger"; // 打印store状态
import config from "../../config/_conf/app.config";

import rootState from "./rootState";
import mapManagement from "./modules/monitor/map-management";
import containerModal from "./modules/warehouseManage/containerModal";
import shelfStatic from "./modules/warehouseManage/shelfStatic"; // 仓库管理/静态货架调整
Vue.use(Vuex);
export default new Vuex.Store({
  strict: config.isDev,
  plugins: config.isDev ? [createLogger()] : [],
  state: rootState.state,
  getters: rootState.getters,
  mutations: rootState.mutations,
  actions: rootState.actions,
  modules: {
    mapManagement,
    containerModal,
    shelfStatic,
  },
});
