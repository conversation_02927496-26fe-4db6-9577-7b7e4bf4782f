import Base from "./base";
import mapDataInstance from "../store/data";
import { ANGLE, MODEL_GROUP_PREFIX } from "../constant/elements";
import * as THREE from "three";
import selectPluginInstance from "../plugins/edit/actions/selectPlugin";
class GeoModel extends Base {
  constructor(options) {
    super(options);
    this._object3d = options.object3d;
    this._data = options.data;
    this._group = new THREE.Group();
    this._group.name = `${MODEL_GROUP_PREFIX}-${options.category}`;
    this._group.userData.uuid = options.data.uuid; // uuid
    this._group.userData.isAdsorb = options.isAdsorb; // 是否吸附
    this._category = options.category; // 模型类型；
    this._group.add(this._object3d);
    this.position = [];
    this.isAdsorb = options.isAdsorb;
    this._render();
  }
  get model() {
    return this._group;
  }
  update(nv) {
    this._data = nv;
    this._render();
    mapDataInstance.updateModelData(this._category, nv);
    //todo: 需要整改
    selectPluginInstance.updateModelSelect(nv);
  }
  _render() {
    const { direction, cellCode, location } = this._data;
    const nextDirection = (direction || "NORTH").toLocaleUpperCase();
    this._group.rotation.y = ANGLE[nextDirection];
    this.position = [location.x, 0, -location.y];
    this._group.position.set(...this.position);
  }
}
export default GeoModel;
