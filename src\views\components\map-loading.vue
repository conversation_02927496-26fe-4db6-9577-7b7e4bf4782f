<template>
  <div class="map-loading-mask">
    <div class="map-loading-spinner">
      <div class="map-loading_box">
        <span />
        <span />
        <span />
        <span />
        <span />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Loading",
};
</script>

<style lang="less" scoped>
@span-bg: #eee;
@span-bg-active: #409eff;
.map-loading-mask {
  position: absolute;
  z-index: 2000;
  background-color: rgba(0, 0, 0, 0.5);
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  -webkit-transition: opacity 0.3s;
  transition: opacity 0.3s;
}
.map-loading-spinner {
  top: 50%;
  margin-top: -20px;
  width: 100%;
  text-align: center;
  position: absolute;
}
.map-loading_box {
  display: inline-block;
  height: 40px;
  line-height: 40px;
  position: relative;
  text-align: center;
  span {
    width: 6px;
    height: 6px;
    margin: 0 1px;
    border-radius: 3px;
    display: inline-block;
    vertical-align: middle;
    background: @span-bg;
    animation: map-loading_box 1.5s infinite ease-in-out;
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
    &:nth-child(4) {
      animation-delay: 0.6s;
    }
    &:nth-child(5) {
      animation-delay: 0.8s;
    }
  }
}
@keyframes map-loading_box {
  0% {
    height: 6px;
    background: @span-bg;
  }
  25% {
    height: 30px;
    background: @span-bg-active;
  }
  50% {
    height: 6px;
    background: @span-bg;
  }
  100% {
    height: 6px;
    background: @span-bg;
  }
}
</style>
