/* ! <AUTHOR> at 2022/08/29 */
import BoxTool from "./components/box-tool";
import MsgInfo from "./components/msg-info";
import FloorListBox from "./components/change-floor";
import FilterSelect from "./components/filter-select";
import MapLeftTools from "./components/map-left-tools";
import MapRightTools from "./components/map-right-tools";

type PropsType = {
  messageCount: { backLogCount: number; notificationCount: number };
};
function TopPanel(props: PropsType) {
  return (
    <div className="map2d-top-panel">
      <div style={{ display: "flex", alignItems: "center" }}>
        <MapLeftTools />
        <MsgInfo messageCount={props.messageCount} />
      </div>
      <div style={{ display: "flex", alignItems: "center" }}>
        <FilterSelect />
        <MapRightTools />
        <FloorListBox />
        <BoxTool />
      </div>
    </div>
  );
}

export default TopPanel;
