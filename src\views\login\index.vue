<template>
  <section class="login-container">
    <div class="bg"><img src="@imgs/login/login-bg.png" /></div>
    <div class="login-box">
      <h1 class="login-title">
        <div class="title-text"><img v-if="isShowGeekIcon" src="@imgs/common/logo.png" /><span>RMS</span></div>
        <geek-lang-selection icon="blue" class="login-lang-selection" />
      </h1>

      <div class="item-box user">
        <input v-model="username" :placeholder="$t('lang.mb.login.userName')" type="text" autocomplete="on" />
      </div>

      <div class="item-box pwd">
        <input v-model="password" :placeholder="$t('lang.mb.login.passWord')" :type="passwordType" autocomplete="on" />
        <password-eye :is-show="passwordShow" @changeEye="changePwdShow" />
      </div>

      <button :disabled="disabled" @click="login">{{ $t("lang.rms.fed.signIn") }}</button>
    </div>
    <ChangePasswordPop
      :change-password-show="changePasswordShow"
      :change-name="username"
      :overdue-status="overdueStatus"
      @updateVisible="updateVisible"
    />
  </section>
</template>

<script>
import md5 from "js-md5";
import PasswordEye from "./components/password-eye";
import ChangePasswordPop from "./components/changePasswordPop";

export default {
  name: "Login",
  components: { PasswordEye, ChangePasswordPop },
  data() {
    return {
      username: "guest",
      password: "",

      passwordType: "password",
      passwordShow: false,
      disabled: false,
      changePasswordShow: false,
      overdueStatus: false,
      isShowGeekIcon: $utils?._staticConfig?.showGeekIcon || false,
    };
  },
  created() {
    this.keyLogin();
  },
  destroyed() {
    document.body.onkeydown = null;
  },
  methods: {
    login() {
      this.username = this.username.trim();
      if (!this._validate()) return;

      this.disabled = true;
      const RMSPermission = $utils.Data.getRMSPermission();
      if (RMSPermission) this.permissionLogin();
      // 不使用权限系统登录
      else this.noPermissionLogin(); // 使用权限系统登录
    },
    permissionLogin() {
      $req
        .post("/athena/api/coreresource/auth/login/v1", {
          userName: this.username,
          // password: this.password,
          password: md5(this.password + this.username),
        })
        .then(res => {
          if (res.code === 0 && res.data.passwordWarning) {
            this.$message({
              message: $utils.Tools.transMsgLang(res.data.passwordWarning),
              type: "warning",
              duration: 5000
            });
          }
          $utils.Data.loginResetStorage();
          this._resolvePermissionLoginData(res.data);
        })
        .catch(e => {
          this.disabled = false;
          if (e && e.code == 1) {
            if (e.data && e.data.pwdExpire) {
              this.overdueStatus = true;
              this.overduePop();
            }
          }
        });
    },
    noPermissionLogin() {
      const { username, password } = this.$data;
      const adminPwdConf = $utils.Data.getRMSConfig().adminPassword;
      const guestPwdConf = $utils.Data.getRMSConfig().guestPassword;
      const md5Pwd = md5(password).toUpperCase();
      if (
        (username.trim() === "admin" && md5Pwd === adminPwdConf) ||
        (username === "guest" && md5Pwd === guestPwdConf)
      ) {
        $utils.Data.loginResetStorage();
        this._resolveNoPermissionLoginData({ username });
      } else {
        this.$error(this.$t("lang.rms.fed.wrongUsernameAndPassword"));
        this.disabled = false;
      }
    },
    changePwdShow() {
      const ps = !this.passwordShow;
      this.passwordShow = ps;
      this.passwordType = ps ? "text" : "password";
    },
    keyLogin() {
      document.body.onkeydown = e => {
        if (e.keyCode === 13) this.login();
      };
    },
    _resolvePermissionLoginData(data) {
      const { sessionId, user } = data;
      const { setToken, setMenuList, setUserInfo, setRoleInfo } = $utils.Data;

      setToken(sessionId);
      setUserInfo(user.userName);
      setRoleInfo(user.roleNames);
      if (user.menuList && user.menuList.length > 0) {
        setMenuList(JSON.stringify(user.menuList));
      }

      this.disabled = false;
      this.$router.push("/dashboard");
    },
    _resolveNoPermissionLoginData(data) {
      const { username } = data;
      $utils.Data.setUserInfo(username);
      $utils.Data.setRoleInfo(username);

      this.disabled = false;
      this.$router.push("/dashboard");
    },
    _validate() {
      if (!this.username) {
        this.$error(this.$t("lang.auth.UserAPI.item0120"));
        return false;
      }
      if (this.password.length < 6) {
        this.$error(this.$t("lang.rms.fed.warnPasswordShort"));
        return false;
      }

      return true;
    },
    updateVisible(val) {
      this.changePasswordShow = val;
    },
    overduePop() {
      this.$confirm(this.$t("lang.auth.PwdMgrAPI.item0010"), {
        confirmButtonText: this.$t("lang.rms.fed.confirm"),
        cancelButtonText: this.$t("lang.rms.fed.cancel"),
        showClose: false,
        type: "warning",
      })
        .then(() => {
          this.changePasswordShow = true;
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="less" scoped>
@login-bg: rgba(19, 41, 65);
@login-bg-linear: rgba(6, 37, 105);
@login-input-color: #eee;
.login-container {
  position: fixed;
  height: 100%;
  width: 100%;
  // background-color: @login-bg-linear;
  background-image: linear-gradient(90deg, @login-bg, @login-bg-linear, 80%, @login-bg);
}

.bg {
  position: fixed;
  height: 100%;
  width: 100%;
  opacity: 0.5;
  img {
    width: 100%;
    height: 100%;
  }
}

.login-box {
  position: absolute;
  left: 0;
  right: 0;
  width: 450px;
  padding: 35px;
  margin: 120px auto;
  font-size: 13px;
  border-top: 8px solid @login-bg;
  background: rgba(255, 255, 255, 0.65);
  .g-box-shadow-no-top(#444);

  h1.login-title {
    position: relative;
    text-align: center;
    margin: 10px auto 30px;
    height: 42px;

    .title-text {
      .g-flex();
      justify-content: center;
      position: relative;

      img {
        width: 130px;
      }
      span {
        font-weight: 600;
        font-size: 32px;
        padding-left: 16px;
        color: #008bd6;
      }
    }
    .login-lang-selection {
      top: 0;
      position: absolute;
      right: 0;
    }
  }

  .item-box {
    position: relative;
    margin: 18px 0 26px;
    border: 1px solid #c3c4c7;
    border-radius: 2px;
    overflow: hidden;
    background-color: #fff;
    background-position: 14px 50%;
    background-repeat: no-repeat;

    &.user {
      background-image: url(~@imgs/login/icon-user.png);
      background-size: 22px;
    }

    &.pwd {
      padding-right: 50px;
      background-image: url(~@imgs/login/icon-password.png);
      background-size: 16px;
      background-position: 17px 50%;
    }

    input {
      display: block;
      width: 100%;
      padding: 6px 5px 6px 46px;
      height: 40px;
      line-height: 40px;
      border: 0;
      font-size: 100%;
      color: #444;

      &:-webkit-autofill,
      &:-webkit-autofill:focus,
      &:-internal-autofill-selected {
        background-color: transparent !important;
        transition: background-color 5000s ease-in-out 0s;
        -webkit-box-shadow: 0 0 0 1000px transparent inset !important;
        -webkit-text-fill-color: #444 !important;
      }
    }
  }

  button {
    display: block;
    margin: 38px auto 18px;
    width: 100%;
    height: 43px;
    line-height: 43px;
    font-weight: 500;
    letter-spacing: 3px;
    cursor: pointer;
    font-size: 16px;
    background: @login-bg;
    color: #fff;
    border-radius: 2px;
  }
}
</style>
