<template>
  <section>
    <geek-customize-form
      :form-config="formConfig"
      @on-query="onQuery"
      @on-reset="onReset"
      class="instance-search-form"
    />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      @row-view="rowView"
      @row-add="rowAdd"
      @row-copy="rowCopy"
      @row-edit="rowEdit"
      @row-del="rowDel"
    >
      <template #dockingSides="{ row }">
        <el-tag v-for="item in row.dockingSides" :key="item" type="info" style="margin-right: 6px; margin-bottom: 2px">
          {{ item }}
        </el-tag>
      </template>
      <template #controlAbilities="{ row }">
        <el-tag
          v-for="item in row.controlAbilities"
          :key="item"
          type="info"
          style="margin-right: 6px; margin-bottom: 2px"
        >
          {{ item }}
        </el-tag>
      </template>
      <template #actionAbilities="{ row }">
        <el-tag
          v-for="item in row.actionAbilities"
          :key="item"
          type="info"
          style="margin-right: 6px; margin-bottom: 2px"
        >
          {{ item }}
        </el-tag>
      </template>
    </geek-customize-table>

    <EditDialog ref="editDialog" @updateList="getTableList" />
  </section>
</template>

<script>
import EditDialog from "./components/editDialog";
export default {
  name: "RobotInstance",
  components: { EditDialog },
  data() {
    const permission = !this.isRoleGuest();
    return {
      form: {
        name: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "150px",
          inline: true,
        },
        configs: {
          name: {
            label: "lang.rms.api.result.warehouse.mechanism.spuName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterInstitutionSPU",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {},
        actions: [
          {
            label: "lang.rms.api.result.warehouse.createRobotMechanismModel",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.api.result.warehouse.mechanism.spuName", prop: "name" },
          {
            label: "lang.rms.api.result.warehouse.lengthWidthHeight",
            prop: "length",
            formatter: (row, column, cellValue, index) => {
              let width = row.width,
                height = row.height;
              if (!cellValue && cellValue != 0) cellValue = "--";
              if (!width && width != 0) width = "--";
              if (!height && height != 0) height = "--";
              return `${cellValue}/${width}/${height}`;
            },
          },
          {
            label: "lang.rms.api.result.warehouse.maxLoad",
            prop: "maxLoad",
          },
          {
            label: "lang.rms.api.result.warehouse.maxWorkingHeight",
            prop: "maxWorkHeight",
          },
          {
            label: "lang.rms.fed.rotationDiameter",
            prop: "diameter",
          },
          {
            label: "lang.rms.fed.locationOffset",
            prop: "locationOffset",
          },
          {
            label: "lang.rms.fed.dockingSide",
            prop: "dockingSides",
            slotName: "dockingSides",
          },
          {
            label: "lang.rms.fed.deliverStrategy.aGetaDrop",
            prop: "deliverStrategy",
            formatter: (row, column, cellValue, index) => {
              return cellValue == 1 ? this.$t("lang.rms.fed.yes") : this.$t("lang.rms.fed.no");
            },
          },
          {
            label: "lang.rms.fed.isHasBackupSensor",
            prop: "hasBackupSensor",
            formatter: (row, column, cellValue, index) => {
              switch (cellValue) {
                case 0:
                  return this.$t("lang.venus.common.dict.no");
                case 1:
                  return this.$t("lang.rms.api.result.edit.map.yes");
                default:
                  return "--";
              }
            },
          },
          {
            label: "lang.rms.api.result.warehouse.movementAbility",
            prop: "controlAbilities",
            slotName: "controlAbilities",
          },
          {
            label: "lang.rms.api.result.warehouse.capacity",
            prop: "actionAbilities",
            slotName: "actionAbilities",
            "min-width": "180",
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "180",
            align: "center",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.buttonView",
                handler: "row-view",
              },
              {
                label: "lang.rms.web.map.version.copy",
                permission,
                handler: "row-copy",
              },
              {
                label: "lang.rms.fed.buttonEdit",
                permission,
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.delete",
                permission,
                handler: "row-del",
              },
            ],
          },
        ],
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.$refs.editDialog.open("add", {});
    },
    rowCopy(row) {
      this.$refs.editDialog.open("copy", row);
    },
    rowView(row) {
      this.$refs.editDialog.open("view", row);
    },
    rowEdit(row) {
      this.$refs.editDialog.open("edit", row);
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.api.result.warehouse.willDeleteToContinue")).then(() => {
        $req.get("/athena/robot/manage/mechanismDelete", { id: row.id }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t("lang.venus.web.common.successfullyDeleted"));
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { pageSize, currentPage } = this.tablePage;
      const url = `/athena/robot/manage/mechanismPageList?pageSize=${pageSize}&currentPage=${currentPage}`;
      const searchData = this.form;

      $req.post(url, searchData).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          pageCount: result.pageCount || 0,
          currentPage: result.currentPage || 1,
        });
      });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less" scoped>
.instance-search-form {
  padding: 5px 0;
}
</style>
