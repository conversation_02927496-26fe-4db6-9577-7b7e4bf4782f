<template>
  <div>
    <!-- <el-tabs v-model="activeName">
      <el-tab-pane label="货架货位模型" name="goodsShelves">
        <div class="tabContent">
          <hj @planeAllArrData="planeAllArrDataFun" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="托盘货位模型" name="tray">托盘模型</el-tab-pane>
    </el-tabs> -->
    <div class="tabContent">
      <hj @planeAllArrData="planeAllArrDataFun" />
    </div>
  </div>
</template>

<script>
import hj from "./hj.vue";
export default {
  components: {
    hj,
  },
  data() {
    return {
      activeName: "goodsShelves",
    };
  },
  methods: {
    planeAllArrDataFun(data) {
      this.$emit("planeAllArrData", data);
    },
  },
};
</script>

<style></style>
