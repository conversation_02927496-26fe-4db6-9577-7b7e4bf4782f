<template>
  <section>
    <geek-customize-form
      :form-config="formConfig"
      @on-query="onQuery"
      @on-reset="onReset"
      class="instance-search-form"
    />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      @row-view="rowView"
      @row-add="rowAdd"
      @row-edit="rowEdit"
      @row-del="rowDel"
    />

    <EditDialog ref="editDialog" @updateList="getTableList" />
  </section>
</template>

<script>
import EditDialog from "./components/editDialog";
export default {
  name: "MechanismModel",
  components: { EditDialog },
  data() {
    const permission = !this.isRoleGuest();
    return {
      form: {
        name: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "150px",
          inline: true,
        },
        configs: {
          name: {
            label: "lang.rms.api.result.warehouse.mechanism.component.spuName",
            default: "",
            tag: "input",
            placeholder: "lang.rms.api.result.warehouse.pleaseEnterMechanismComponentSPU",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: {},
        actions: [
          {
            label: "lang.rms.api.result.warehouse.createRobotCompentModel",
            type: "primary",
            handler: "row-add",
          },
        ],
        columns: [
          { label: "lang.rms.api.result.warehouse.mechanism.component.spuName", prop: "name", width: "200" },
          {
            label: "lang.rms.api.result.warehouse.affiliatedMechanism",
            prop: "mechanismModelId",
            formatter: (row, column, cellValue, index) => {
              return `${this.$t(row.mechanismCode)}(${this.$t(cellValue)})`;
            },
          },
          {
            label: "lang.rms.api.result.warehouse.lengthWidthHeight",
            prop: "length",
            formatter: (row, column, cellValue, index) => {
              let width = row.width,
                beginHeight = row.beginHeight;
              if (!cellValue && cellValue != 0) cellValue = "--";
              if (!width && width != 0) width = "--";
              if (!beginHeight && beginHeight != 0) height = "--";
              return `${cellValue}/${width}/${beginHeight}`;
            },
          },
          {
            label: "lang.rms.api.result.warehouse.mechainsmComponentSn",
            prop: "sn",
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "180",
            align: "center",
            fixed: "right",
            operations: [
              {
                label: "lang.rms.fed.buttonView",
                handler: "row-view",
              },
              {
                label: "lang.rms.fed.buttonEdit",
                permission,
                handler: "row-edit",
              },
              {
                label: "lang.rms.fed.delete",
                permission,
                handler: "row-del",
              },
            ],
          },
        ],
      },
    };
  },
  activated() {
    this.getTableList();
  },
  methods: {
    rowAdd() {
      this.$refs.editDialog.open("add", {});
    },
    rowView(row) {
      this.$refs.editDialog.open("view", row);
    },
    rowEdit(row) {
      this.$refs.editDialog.open("edit", row);
    },
    rowDel(row) {
      this.$geekConfirm(this.$t("lang.rms.api.result.warehouse.willDeleteToContinue")).then(() => {
        $req.get("/athena/robot/manage/mechanismComponentDelete", { id: row.id }).then(res => {
          if (res.code !== 0) return;
          this.getTableList();
          this.$success(this.$t("lang.venus.web.common.successfullyDeleted"));
        });
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      const { pageSize, currentPage } = this.tablePage;
      const url = `/athena/robot/manage/mechanismComponentPageList?pageSize=${pageSize}&currentPage=${currentPage}`;
      const searchData = this.form;

      $req.post(url, searchData).then(res => {
        let result = res.data || {};
        this.tableData = result.recordList || [];
        this.tablePage = Object.assign({}, this.tablePage, {
          pageCount: result.pageCount || 0,
          currentPage: result.currentPage || 1,
        });
      });
    },

    isRoleGuest() {
      if (!$utils && !$utils.Data) return true;
      const roleInfo = $utils.Data.getRoleInfo();
      return roleInfo === "guest";
    },
  },
};
</script>
<style lang="less" scoped>
.instance-search-form {
  padding: 5px 0;
}
</style>
