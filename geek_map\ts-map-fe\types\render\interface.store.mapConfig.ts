/* ! <AUTHOR> at 2023/04/20 */

namespace MRender {
  export type mapConfigKey = keyof MRender.renderConfigs | "mapAngle" | "showTopologicalGraph";
  /** MapConfig 类 */
  export interface RenderConfigMain {
    /** config相关数据 */
    data: any;
    /**
     * 设置MapOptions
     * @param key keyof MRender.mapOptions
     * @param value
     */
    setRenderConfig(
      key: keyof MRender.renderConfigs,
      value: number | boolean | MRender.mapPosition,
    ): void;

    /**
     * 获取config
     * @param key
     */
    getRenderConfig(key: mapConfigKey): any;

    /**
     * 设置layer的显示隐藏属性
     * @param key
     * @param visible
     */
    setLayerVisible(key: keyof MRender.layerToggleOptions, visible: boolean): void;

    /**
     * 获取layer的显示隐藏属性
     * @param key
     */
    getLayerVisible<T>(key?: keyof MRender.layerToggleOptions): any;
    /**
     * 数据卸载
     */
    uninstall(): void;
    /**
     * 数据销毁
     */
    destroy(): void;
  }

  /** layer 类 */
  export interface MapData {
    /**
     * 添加MapData
     * @param code 唯一code
     * @param data 相对应的数据类型
     */
    setData(code: code, data: any): void;

    /**
     * 根据code获取数据
     * @param code 唯一code
     */
    getData(code: code): any;

    /**
     * 获取所有MapData
     */
    getAll(): { [propName: code]: any };

    /**
     * 根据code删除数据
     * @param code 唯一code
     */
    delData(code: code): void;

    /**
     * 数据卸载
     */
    uninstall(): void;
    /**
     * 数据销毁
     */
    destroy(): void;
  }
}
