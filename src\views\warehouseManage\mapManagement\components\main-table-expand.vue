<template>
  <geek-customize-table :table-config="expandTableConfig" :data="floods">
    <template #expandOperation="{ row }">
      <el-button
        v-if="rowData.status >= 4"
        :disabled="btnDisabled(row.uploadStatus)"
        type="text"
        @click="importFloor(row.floorId)"
      >
        {{ $t("lang.rms.fed.importFloor") }}
      </el-button>
      <!-- <el-button
        v-if="[4, 5, 6].includes(rowData.status)"
        :disabled="btnDisabled(row.uploadStatus)"
        type="text"
        @click="drawFloor(row.floorId)"
      >
        {{ $t("lang.rms.fed.drawingAMap") }}
      </el-button> -->
      <el-button :disabled="btnDisabled(row.uploadStatus)" type="text" @click="exportFloor(row.floorId)">
        {{ $t("lang.rms.fed.exportFloor") }}
      </el-button>
      <el-button
        v-if="[4, 5, 6].includes(rowData.status)"
        :disabled="btnDisabled(row.uploadStatus)"
        type="text"
        @click="editViewFloor(row.floorId)"
      >
        {{ $t("lang.rms.fed.edit") }}
      </el-button>
      <el-button
        v-if="[1, 2, 3].includes(rowData.status)"
        :disabled="btnDisabled(row.uploadStatus)"
        type="text"
        @click="editViewFloor(row.floorId)"
      >
        {{ $t("lang.rms.fed.buttonView") }}
      </el-button>
      <el-button
        v-if="[4, 5, 6].includes(rowData.status)"
        :disabled="btnDisabled(row.uploadStatus)"
        type="text"
        @click="deleteFloor(row.floorId)"
      >
        {{ $t("lang.rms.web.map.version.deleteFloor") }}
      </el-button>
      <el-button
        v-if="[4, 5, 6].includes(rowData.status)"
        :disabled="btnDisabled(row.uploadStatus)"
        type="text"
        @click="$emit('openManageQrCode', String(rowData.id), String(row.floorId))"
      >
        {{ $t("lang.rms.fed.manageQrCode") }}
      </el-button>
      <!-- <el-button
        :disabled="btnDisabled(row.uploadStatus)"
        type="text"
        @click="$emit('openManageWaitTaskGroup', String(rowData.id), String(row.floorId))"
      >
        {{ $t("lang.rms.fed.manageWaitaskGroup") }}
      </el-button> -->
    </template>

    <template #uploadStatus="{ row }">
      <p :class="getStatusClassName(row.uploadStatus)">{{ $t(mapStatus.get(row.uploadStatus)) }}</p>
    </template>

    <template #phaseI18N="{ row }">
      <p :class="getStatusClassName(row.uploadStatus)">{{ row.phaseI18N }}</p>
    </template>
  </geek-customize-table>
</template>

<script>
import { mapMutations } from "vuex";
import dealImage from "../../../../libs/dealImage";

export default {
  name: "MainTableExpand",
  props: {
    rowData: {
      type: Object,
      require: true,
    },
  },
  data() {
    return {
      updateTimestamp: null,
      mapStatus: new Map([
        [0, "lang.rms.fe.map.import.status.waiting"],
        [1, "lang.rms.fe.map.import.status.processing"],
        [2, "lang.rms.fe.map.import.status.success"],
        [3, "lang.rms.fe.map.import.status.failed"],
      ]),

      expandTableConfig: {
        attrs: { "show-header": false },
        columns: [
          {
            label: "floorName",
            prop: "floorId",
            formatter: (row, column, cellValue, index) => {
              return this.$t("lang.rms.fed.floor") + cellValue;
            },
          },
          {
            label: "uploadStatus",
            prop: "uploadStatus",
            slotName: "uploadStatus",
            width: "150",
          },
          {
            label: "phaseI18N",
            prop: "phaseI18N",
            slotName: "phaseI18N",
            width: "120",
          },
          {
            label: "lang.rms.fed.listOperation",
            prop: "expandOperation",
            slotName: "expandOperation",
            width: "320",
            className: "operation-btn",
          },
        ],
      },
    };
  },
  computed: {
    btnDisabled() {
      return uploadStatus => {
        if (uploadStatus === undefined) {
          return false;
        }
        return ![2, 3].includes(uploadStatus);
      };
    },
    floods() {
      this.updateTimestamp = Date.now();
      return this.rowData.floods || [];
    },
  },
  methods: {
    ...mapMutations("mapManagement", ["showDialog"]),
    // 导入楼层
    importFloor(floorId) {
      this.showDialog({
        currentComponent: "dialogImportFloor",
        title: this.$t("lang.rms.fed.importFloor"),
        rowData: { ...this.rowData },
        floorId,
      });
    },
    // 绘制地图
    drawFloor(floorId) {
      this.$router.push({
        path: "/warehouseManage/drawMapFloor",
        query: {
          mapId: this.rowData.id,
          floorId,
          status: this.rowData.status,
        },
      });
    },
    // 导出楼层
    exportFloor(floorId) {
      this.showDialog({
        currentComponent: "dialogExportFloor",
        title: this.$t("lang.rms.fed.exportFloor"),
        rowData: { ...this.rowData },
        floorId,
      });
      // $req
      //   .post("/athena/map/manage/exportMap", {
      //     mapId: this.rowData.id,
      //     floorId,
      //   })
      //   .then(res => {
      //     const data = res.data;
      //     let url = data.url ? data.url : data;
      //     if ($req.isDev) window.open($req.API_URL + url);
      //     else window.open(window.location.origin + url);
      //   });
    },
    // 编辑 & 查看
    editViewFloor(floorId) {
      console.log("rowData", this.rowData);
      const { status, id } = this.rowData;
      // 5为激活态，激活的地图不允许编辑，只能复制编辑
      // if (status === 5) {
      //   this.$alert('当前地图正在应用，无法编辑，如需编辑请复制当前地图进行编辑。', this.$t('lang.rms.fed.optionWarning'), {
      //     confirmButtonText: '确定',
      //   });
      //   return
      // }
      this.getMap(id, floorId);
      this.$router.push({
        path: "/warehouseManage/editMap",
        query: {
          mapId: id,
          floorId,
          status,
        },
      });
    },
    getMap(mapId, floorId) {
      $req.postParams("/athena/map/draw/getMap", { mapId, floorId }).then(res => {
        if (!res.data.compressData && res.data.base64Text) this.getImg(res.data);
      });
    },
    getImg(data) {
      const newData = data;
      const printing = (base64, newImage) => {
        // 获取压缩后的base64大小
        newData.width = newImage.width;
        newData.height = newImage.height;
        newData.compressData = base64;
        // this.updateBg(newData);
      };
      dealImage(data.base64Text, printing);
    },
    updateBg(val) {
      const data = {
        mapId: val.mapId,
        floorId: val.floorId,
        robotType: val.robotType,
        display: val.display,
        imageData: val.base64Text,
        compressData: val.compressData,
        x: val.offsetX,
        y: val.offsetY,
        yaw: val.yaw,
        resolution: val.resolution,
        width: val.width,
        height: val.height,
        id: val.backgroundId,
      };
      $req
        .post("/athena/map/draw/importBackgroundImage", data)
        .then(res => {
          this.initFun();
        })
        .catch(e => {
          console.log(e);
        });
    },
    // 导入蒙层
    handleImportMask(floorId) {
      console.log(this.rowData);
      // this.menuClick("importMask");
      // let floorInfo = {
      //     mapId: row.mapId,
      //     floorId: row.floorId
      // };
      // setTimeout(() => {
      //     TeventBus.$emit("dialogRow", floorInfo);
      // }, 400);
      this.showDialog({
        currentComponent: "dialogImportMask",
        title: this.$t("lang.rms.fed.updateMaskResource"),
        rowData: this.rowData,
        floorId,
      });
    },
    // 删除楼层
    deleteFloor(floorId) {
      this.$geekConfirm(this.$t("lang.rms.fed.whetherOrNotToDeleteFloor")).then(() => {
        $req
          .postParams("/athena/map/draw/deleteFloor", {
            mapId: this.rowData.id,
            floorId,
          })
          .then(res => {
            this.$message({
              message: this.$t(res.msg),
              type: "success",
              duration: 8000,
            });
            this.$emit("refreshList");
          });
      });
    },
    getStatusClassName(uploadStatus) {
      let className = "";
      switch (uploadStatus) {
        case 0:
          className = "ready";
          break;
        case 1:
        case 2:
          className = "uploading";
          break;
        case 3:
          className = "error";
          break;
      }

      return className;
    },
  },
};
</script>
<style lang="less" scoped>
//去除滚动条
.expend-table {
  ::-webkit-scrollbar-thumb {
    display: none !important;
  }
}
.uploading {
  color: #2ac039;
}
.error {
  color: #d9001b;
}
.ready {
  color: #ef973b;
}
</style>
