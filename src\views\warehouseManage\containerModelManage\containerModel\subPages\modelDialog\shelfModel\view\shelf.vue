<template>
  <div class="shelf">
    <!-- 货架 -->
    <div class="shelfBox">
      <span class="shelfContent">{{ $t("lang.rms.fed.shelfCenter") }}</span>
      <div v-if="auxiliary" class="auxiliary-w">
        <span class="auxiliaryContent">{{ shelfData.width }}mm</span>
      </div>
      <div v-if="auxiliary" class="auxiliary-h">
        <span class="auxiliaryContent">{{ shelfData.length }}mm</span>
      </div>

      <!-- 4个脚 -->
      <div class="leftTopFoot foot">
        <div v-if="auxiliary" class="auxiliary-foot-w">
          <span class="auxiliaryContent">{{ shelfData.legWidth }}mm</span>
        </div>
        <div v-if="auxiliary" class="auxiliary-foot-h">
          <span class="auxiliaryContent">{{ shelfData.legLength }}mm</span>
        </div>
      </div>
      <div class="leftBottomFoot foot" />
      <div class="rightTopFoot foot" />
      <div class="rightBottomFoot foot" />

      <div class="F">
        {{ $t("lang.rms.fed.faceF") }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 辅助线
    auxiliary: {
      type: Boolean,
      default() {
        return false;
      },
    },
    shelfData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>

<style scoped lang="less">
.F {
  position: absolute;
  top: calc(100% + 3px);
  text-align: center;
  font-weight: 600;
  width: 100%;
  font-size: 18px;
}
.shelf {
  width: 100%;
  height: 100%;
  position: relative;

  .shelfContent {
    top: 50%;
    left: 50%;
    position: absolute;
    transform: translate(-50%, -50%);
    padding-top: 25px;

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #616060;
      width: 10px;
      height: 10px;
      border-radius: 50%;
    }
  }

  .shelfBox {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 3px solid #9e9e9e;
    border-radius: 2px;

    .auxiliary-w {
      position: absolute;
      left: 0;
      top: 20%;
      width: 100%;
      border-top: 3px dotted #ccc;
      text-align: center;
      line-height: 20px;
    }

    .auxiliary-h {
      position: absolute;
      top: 0;
      left: 20%;
      height: 100%;
      border-right: 3px dotted #ccc;

      .auxiliaryContent {
        position: absolute;
        top: 50%;
        transform: translateX(-50%);
        left: 55px;
        width: 100px;
        // writing-mode: vertical-rl;
      }
    }
  }

  .foot {
    border: 2px solid #9e9e9e;
    width: 60px;
    height: 60px;
    position: absolute;
    border-radius: 5px;

    &.leftTopFoot {
      top: 10px;
      left: 10px;
    }

    &.leftBottomFoot {
      left: 10px;
      bottom: 10px;
    }

    &.rightTopFoot {
      top: 10px;
      right: 10px;
    }

    &.rightBottomFoot {
      bottom: 10px;
      right: 10px;
    }

    .auxiliary-foot-w {
      position: absolute;
      height: 100%;
      left: 100%;
      width: 10px;
      border-right: 3px dotted #9e9e9e;

      .auxiliaryContent {
        position: absolute;
        top: 50%;
        transform: translateX(-50%);
        left: 65px;
        line-height: 0;
        width: 100px;
      }
    }

    .auxiliary-foot-h {
      position: absolute;
      width: 100%;
      top: 100%;
      height: 10px;
      border-bottom: 3px dotted #9e9e9e;

      .auxiliaryContent {
        text-align: center;
        line-height: 35px;
      }
    }
  }
}
</style>
