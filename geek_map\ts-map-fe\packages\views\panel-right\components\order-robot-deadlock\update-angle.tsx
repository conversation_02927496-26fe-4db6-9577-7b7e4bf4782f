/* ! <AUTHOR> at 2022/09/08 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CloseOutlined } from "@ant-design/icons";
import { Card, Select, Button } from "antd";
import { getMap2D } from "../../../../singleton";
import OrderPanelGrid from "../common/order-panel-grid";

const { Option } = Select;
type deadRobot = { code: code; errorCodes: Array<any>; taskId: any };
type PropsOrderData = {
  visible: boolean;
  currentRobot: deadRobot;
  onCancel: () => void;
};
function DeadRobotUpdateAngle(props: PropsOrderData) {
  const { t } = useTranslation();
  const [angle, setAngle] = useState("");

  // 初始
  useEffect(() => {
    if (!props.visible) return;
    return () => {
      setAngle("");
    };
  }, [props.visible]);

  const controlHandler = () => {
    const reqMsg = "ForcedTaskRequestMsg";
    const resMsg = "ForcedTaskResponseMsg";
    const map2D = getMap2D();
    map2D.mapWorker
      .reqSocket(reqMsg, {
        robotId: props.currentRobot?.code,
        instruction: "FORCE_ROBOT_TURN",
        turnAngle: angle,
      })
      .then(res => {
        if (res.msgType !== resMsg) return;
        _$utils.wsCmdResponse(res?.body || {});
      });

    props.onCancel();
  };

  return (
    props.visible && (
      <Card
        size="small"
        type="inner"
        title={t("lang.rms.fed.rotationAngle")}
        extra={<CloseOutlined onClick={props.onCancel} />}
        actions={[
          <Button
            type="primary"
            onClick={controlHandler}
            size="small"
            style={{ float: "right", marginRight: 6 }}
          >
            {t("lang.rms.fed.confirm")}
          </Button>,
        ]}
        className="component-operate-detail"
      >
        <OrderPanelGrid
          style={{ border: 0, margin: 0 }}
          items={[
            {
              label: t("lang.rms.fed.rotationAngle"),
              node: (
                <Select style={{ width: "100%" }} onChange={value => setAngle(value)}>
                  <Option value="0">0</Option>
                  <Option value="90">90</Option>
                  <Option value="180">180</Option>
                  <Option value="-90">-90</Option>
                </Select>
              ),
            },
          ]}
        />
      </Card>
    )
  );
}

export default DeadRobotUpdateAngle;
