export default [
  {
    title: "外部障碍物",
    lang: "lang.rms.fed.externalObstacle",
    name: "external-obstacle",
    isShow: true,
    isActive: false,
    isSwitch: true,
  },
  {
    title: "预占区域(包络)",
    lang: "lang.rms.fed.showAllRobotsOccupyAreas",
    name: "show-preempted-area",
    isShow: true,
    isActive: false,
    isSwitch: true,
  },
  {
    title: "显示/隐藏 路径全选",
    lang: "lang.rms.fed.displayAllPath",
    name: "display-all-path",
    isShow: true,
    isActive: false,
    isSwitch: true,
  },
  {
    title: "显示/隐藏 单元格热度",
    lang: "lang.rms.fed.cellHeatDisplay",
    name: "cell-hot",
    isShow: true,
    isActive: false,
    isSwitch: true,
  },
  {
    title: "显示/隐藏 货架热度",
    lang: "lang.rms.fed.shelfHeatDisplay",
    name: "shelf-heat-display",
    isShow: true,
    isActive: false,
    isSwitch: true,
  },
  {
    title: "显示/隐藏 空载方向",
    lang: "lang.rms.fed.showUnloadRoadDirect",
    name: "show-unload-road-direct",
    isShow: true,
    isActive: false,
    isSwitch: true,
  },
  {
    title: "显示/隐藏 负载方向",
    lang: "lang.rms.fed.showLoadRoadDirect",
    name: "show-load-road-direct",
    isShow: true,
    isActive: false,
    isSwitch: true,
  },
  {
    title: "显示/隐藏 单元格中心点",
    lang: "lang.rms.fed.showCellLocationPoint",
    name: "bumadian",
    isShow: true,
    isActive: false,
    isSwitch: true,
  },
  // {
  //   title: "显示/隐藏 货架F面",
  //   lang: "lang.rms.fed.showShelfF",
  //   name: "F",
  //   isShow: true,
  //   isActive: false,
  //   isSwitch: true,
  // },
  {
    title: "显示/隐藏 工作站ID",
    lang: "lang.rms.fed.showStationID",
    name: "show-station-ID",
    isShow: true,
    isActive: false,
    isSwitch: true,
  },
];
