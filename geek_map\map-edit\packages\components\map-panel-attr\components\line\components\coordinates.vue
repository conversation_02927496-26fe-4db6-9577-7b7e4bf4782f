<template>
  <div class="coordinates">
    <div class="coordinateItem" v-for="(item, index) in length" :key="index">
      <div class="content">
        <BaseForm
          :form-item="getFromItemFn(index)"
          :form-data="innerValue[index]"
          @update:form-data="data => updateFormData(index, data)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import BaseForm from "../../base/fromBase.vue";
// points {x, y};

const props = defineProps<{
  length: number;
  modelValue: { x: number; y: number }[];
}>();

const innerValue = ref(
  new Array(props.length).map((_, index) => {
    return {
      x: props.modelValue[index].x,
      y: props.modelValue[index].y,
    };
  }),
);

function getFromItemFn(index: number) {
  return [
    {
      prop: "x",
      label: `x${index}`,
      component: "elInputNumber",
      min: -100000,
      max: 100000,
      step: 0.1,
      precision: 3,
    },
    {
      prop: "y",
      label: `y${index}`,
      component: "elInputNumber",
      min: -100000,
      max: 100000,
      step: 0.1,
      precision: 3,
    },
  ];
}

function updateFormData(index: number, data: any) {}
</script>

<style scoped lang="scss">
.functionstyle {
  width: 100%;

  .functionItem {
    position: relative;
    border: 1px dashed #ccc;
    margin-bottom: 10px;
    padding: 10px;
    .title {
      font-size: 14px;
      font-weight: 900;
    }

    .close {
      position: absolute;
      top: 10px;
      right: 10px;
      height: 16px;
      line-height: 16px;
      cursor: pointer;
    }
  }
}
</style>
