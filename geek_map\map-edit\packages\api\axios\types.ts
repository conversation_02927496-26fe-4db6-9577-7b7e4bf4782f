export type ErrorMessageMode = "none" | "modal" | "message" | undefined;

export interface RequestOptions {
  joinParamsToUrl?: boolean;
  formatDate?: boolean;
  isTransformRequestResult?: boolean;
  isReturnNativeResponse?: boolean;
  apiUrl?: string;
  errorMessageMode?: ErrorMessageMode;
  joinTime?: boolean;
}

export type Result<T = any> = {
  code: number;
  data: T;
  msg?: string;
  message?: string;
};

// multipart/form-data: upload file
export interface UploadFileParams {
  data?: any;
  name?: string;
  file: File | Blob;
  filename?: string;
  [key: string]: any;
}
