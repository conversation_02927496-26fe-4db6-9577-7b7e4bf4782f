<template>
  <div
    id="stageContainer"
    class="canvas-map-image"
    @mousedown.stop="_mousedown"
    @mousemove.stop="_mousemove"
    @mouseup.stop="_mouseup"
  >
    <div id="stage" class="stage">
      <canvas id="canvas" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'FloorCanvas',
  props: {
    zoom: {
      type: Number,
      require: true
    }
  },
  data() {
    return {
      $stageContainer: null,
      $stage: null,
      $canvas: null,
      $img: null,

      padding: 40, // 初始地图与边界的间隔
      canvasParams: {
        screenWidth: 0,
        screenHeight: 0,
        wordWidth: 0,
        wordHeight: 0
      },
      isDrag: false,
      dragPoint: {
        transX: 0,
        transY: 0,
        originX: 0,
        originY: 0,
        x: 0,
        y: 0
      }
    }
  },
  mounted() {
    this.$stageContainer = document.getElementById('stageContainer')
    this.$stage = document.getElementById('stage')
    this.$canvas = document.getElementById('canvas')
    this.$img = new Image()

    Object.assign(this.canvasParams, {
      screenWidth: this.$stageContainer.clientWidth,
      screenHeight: this.$stageContainer.clientHeight
    })
  },
  activated() {
    this.$stageContainer.addEventListener('mousewheel', this._mouseWheel, false)
  },
  deactivated() {
    this.$stageContainer.removeEventListener('mousewheel', this._mouseWheel, false)
  },
  methods: {
    drawImage(url) {
      this.$img.src = url
      this.$img.onload = this.__initImage.bind(this)
    },
    scaleImage() {
      const $stage = this.$stage
      const scale = this.zoom / 100
      $stage.style.transform = `scale(${scale})`
    },

    _mouseWheel(event) {
      let delta = 0
      if (!event) event = window.event
      if (event.wheelDelta) {
        // IE、chrome浏览器使用的是wheelDelta，并且值为“正负120”
        delta = event.wheelDelta / 120
        if (window.opera) delta = -delta // 因为IE、chrome等向下滚动是负值，FF是正值，为了处理一致性，在此取反处理
      } else if (event.detail) {
        // FF浏览器使用的是detail,其值为“正负3”
        delta = -event.detail / 3
      }
      if (delta) {
        this.$emit('update:zoom', this.zoom + delta)
        this.scaleImage()
      }
    },
    _mousedown(e) {
      this.isDrag = true
      Object.assign(this.dragPoint, {
        originX: e.clientX,
        originY: e.clientY
      })
    },
    _mousemove(e) {
      if (!this.isDrag) return

      Object.assign(this.dragPoint, {
        x: e.clientX,
        y: e.clientY
      })
      const { transX, transY, originX, originY, x, y } = this.dragPoint
      const cx = originX - x
      const cy = originY - y

      const $canvas = this.$canvas
      const zoom = this.zoom / 100
      const tx = transX - cx / zoom
      const ty = transY - cy / zoom
      $canvas.style.transform = `translate(${tx}px,${ty}px)`

      Object.assign(this.dragPoint, {
        originX: e.clientX,
        originY: e.clientY,
        transX: tx,
        transY: ty
      })
    },
    _mouseup() {
      this.isDrag = false
    },

    __drawImage() {
      const img = this.$img
      const $ctx = this.$canvas.getContext('2d')
      const { wordWidth, wordHeight } = this.canvasParams
      $ctx.clearRect(0, 0, wordWidth, wordHeight)
      $ctx.drawImage(img, 0, 0, wordWidth, wordHeight)
    },
    __initImage() {
      const $img = this.$img
      const $stage = this.$stage
      const $canvas = this.$canvas
      const w = $img.width
      const h = $img.height
      Object.assign(this.canvasParams, {
        wordWidth: w,
        wordHeight: h
      })
      const { screenWidth, screenHeight } = this.canvasParams
      $stage.style.width = w + 'px'
      $stage.style.height = h + 'px'
      $stage.style.marginLeft = (screenWidth - w) / 2 + 'px'
      $stage.style.marginTop = (screenHeight - h) / 2 + 'px'
      $canvas.width = w
      $canvas.height = h
      this.__initZoom()
      this.__drawImage()
      this.$nextTick(this.scaleImage)
    },
    __initZoom() {
      if (this.zoom !== -1) return

      const { wordWidth, wordHeight, screenWidth, screenHeight } = this.canvasParams
      const padding = this.padding * 2
      const swp = screenWidth - padding
      const shp = screenHeight - padding

      const scale = Math.floor(Math.min(swp / wordWidth, shp / wordHeight) * 1000) / 1000

      const zoom = scale * 100
      const min = zoom > 10 ? 10 : zoom
      this.$emit('update:zoom', zoom)
      this.$emit('setZoomMin', min)
    }
  }
}
</script>

<style lang="less" scoped>
.canvas-map-image {
  position: relative;
  width: 100%;
  height: 100%;
  background: #eee;
  overflow: hidden;

  > .stage {
    margin: auto;

    > canvas {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
