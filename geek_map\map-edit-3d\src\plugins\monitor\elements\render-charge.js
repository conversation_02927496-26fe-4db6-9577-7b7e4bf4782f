import { ANGLE } from "../constant/monitor";
class RenderCharge {
  constructor(options) {
    this.monitor = options.monitor;
  }
  // create rack
  async create(charges) {
    charges.map(i => this.__formatCharges(i));
    this.__renderCharges(stations);
  }
  destory() {}

  __formatCharges(charge) {
    let change = {};
    change.radAngle = ANGLE[charge.chargerDir] * (Math.PI / 180);
    Object.assign(station, change);
  }
  __renderStations(charges) {
    if (!charges || !charges.length) return;
    const { Store, Map3d } = this.monitor;
    const chargeData = Store.getModelData("CHARGE");
    const isInit = !chargeData || !chargeData.length;
    const params = [
      charges,
      {
        category: "CHARGE",
        useModelName: "charger",
      },
    ];
    isInit ? Map3d.initModel(...params) : Map3d.appendModelData(...params);
  }
}

export default RenderCharge;
