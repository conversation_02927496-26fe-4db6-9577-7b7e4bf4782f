/* ! <AUTHOR> at 2022/08/27 */
import * as PIXI from "pixi.js";

/** cell状态 */
class LayerCellStatus implements MRender.Layer {
  private floorId: floorId;
  private utils: any;
  private lockedVColor: any = null;
  private stoppedShader: any;
  private lockedShader: any;
  private container: PIXI.Container;

  private lockedGeometries: any = {};
  private stoppedGeometries: any = {};
  private meshList: Array<any> = [];
  private fragment: number;
  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "cellStatus";
    container.zIndex = utils.getLayerZIndex("cell");
    container.interactiveChildren = false;
    container.visible = true;
    this.container = container;

    this.floorId = floorId;
    this.utils = utils;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.lockedVColor = utils.getShaderColor("LOCKED");
    this.lockedShader = utils.getShader("rect");
    this.stoppedShader = utils.getShader("icon", utils.getResources("cellPause"));
  }

  render(): void {
    const _this = this;
    const utils = _this.utils;
    const fragment = _this.fragment;

    const lockedGeometries = Object.values(_this.lockedGeometries);
    for (let i = 0, len = Math.ceil(lockedGeometries.length / fragment); i < len; i++) {
      const arr = lockedGeometries.slice(i * fragment, i * fragment + fragment);
      let mesh = utils.createMesh(arr, _this.lockedShader);
      mesh.name = "lockedCell";
      mesh.mapType = "lockedCell";
      mesh.interactive = mesh.buttonMode = false;
      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }

    const stoppedGeometries = Object.values(_this.stoppedGeometries);
    for (let i = 0, len = Math.ceil(stoppedGeometries.length / fragment); i < len; i++) {
      const arr = stoppedGeometries.slice(i * fragment, i * fragment + fragment);
      let mesh = utils.createMesh(arr, _this.stoppedShader);
      mesh.name = "stoppedCell";
      mesh.mapType = "stoppedCell";
      mesh.interactive = mesh.buttonMode = false;
      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }
  }

  // todo cellFlag update
  update(cells: Array<cellData>): void {
    const _this = this;
    const utils = this.utils;

    let item, options, code;
    for (let i = 0, len = cells.length; i < len; i++) {
      item = cells[i];
      options = utils.formatCell(item);
      code = options["code"];

      if (options["cellFlag"] === "LOCKED") {
        if (_this.stoppedGeometries[code]) delete _this.stoppedGeometries[code];
        _this.drawLocked(options);
      } else if (options["cellFlag"] === "STOPPED") {
        if (_this.lockedGeometries[code]) delete _this.lockedGeometries[code];
        _this.drawStopped(options);
      } else {
        if (_this.stoppedGeometries[code]) delete _this.stoppedGeometries[code];
        if (_this.lockedGeometries[code]) delete _this.lockedGeometries[code];
      }
    }

    this.repaint(true);
    this.render();
  }

  drawLocked(options: mCellData): void {
    const _this = this;
    const geometry = _this.utils.drawGeometry("rect", options["position"], _this.lockedVColor);
    _this.lockedGeometries[options["code"]] = geometry;
  }

  drawStopped(options: mCellData): void {
    const _this = this;
    const geometry = _this.utils.drawGeometry("icon", options["position"]);
    _this.stoppedGeometries[options["code"]] = geometry;
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(isFlagUpdate = false): void {
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];

    if (isFlagUpdate) return;
    this.lockedGeometries = {};
    this.stoppedGeometries = {};
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.lockedVColor = null;
    this.lockedShader = null;
    this.stoppedShader = null;
    this.container = null;
    this.utils = null;
    this.lockedGeometries = null;
    this.stoppedGeometries = null;
    this.fragment = null;
    this.floorId = undefined;
  }
}
export default LayerCellStatus;
