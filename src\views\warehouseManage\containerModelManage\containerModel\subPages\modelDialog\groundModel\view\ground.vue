<template>
  <div ref="shelf" class="shelf">
    <div class="shelfCh" :style="{ width: `${offset}px`, height: `${offset}px` }">
      <!-- 托盘 -->
      <div class="tray" />
      <!-- 货架 -->
      <div class="shelfBox">
        <span class="shelfContent" :class="{ auxiliary }">
          {{ $t("lang.rms.fed.centerGroundSupport") }}
        </span>
        <span v-if="auxiliary" class="auxiliary-tray">{{ groundData.offsetY }}mm</span>
        <div v-if="auxiliary" class="auxiliary-w">
          <span class="auxiliaryContent">{{ groundData.width }}mm</span>
        </div>
        <div v-if="auxiliary" class="auxiliary-h">
          <span class="auxiliaryContent">{{ groundData.length }}mm</span>
        </div>

        <!-- 4个脚 -->
        <div class="leftTopFoot foot">
          <div v-if="auxiliary" class="auxiliary-foot-w">
            <span class="auxiliaryContent">{{ groundData.legWidth }}mm</span>
          </div>
          <div v-if="auxiliary" class="auxiliary-foot-h">
            <span class="auxiliaryContent">{{ groundData.legLength }}mm</span>
          </div>
        </div>
        <div class="leftBottomFoot foot" />
        <div class="rightTopFoot foot" />
        <div class="rightBottomFoot foot" />
      </div>

      <div class="uploadStyle">
        <i class="el-icon-upload2" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";

export default {
  props: {
    // 辅助线
    auxiliary: {
      type: Boolean,
      default() {
        return false;
      },
    },
    groundData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      offset: 0,
    };
  },
};
</script>

<style scoped lang="less">
.shelf {
  width: 100%;
  height: 100%;
  position: relative;
  word-break: initial;

  .auxiliary-tray {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    left: 50%;
    height: 76px;
    border-right: 2px dotted #00bcd4;
    margin-top: -36px;
    width: 0px;
    line-height: 76px;
    text-indent: 10px;
  }

  .shelfCh {
    width: 80% !important;
    height: 100% !important;
    position: relative;
    margin: 0 auto;
  }

  .tray {
    position: absolute;
    top: 70px;
    left: 20px;
    right: 20px;
    bottom: 90px;
    border-left: 3px solid #00bcd4;
    border-right: 3px solid #00bcd4;

    &::before {
      content: "";
      height: 50px;
      border: 2px solid #00bcd4;
      display: inline-block;
      position: absolute;
      left: -21px;
      bottom: -40px;
      transform: rotate(45deg);
    }

    &::after {
      content: "";
      height: 50px;
      border: 2px solid #00bcd4;
      display: inline-block;
      position: absolute;
      right: -21px;
      bottom: -40px;
      transform: rotate(-45deg);
    }
  }

  .shelfContent {
    top: 50%;
    left: 50%;
    position: absolute;
    transform: translate(-50%, -50%);
    padding-top: 50px;

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #616060;
      width: 30px;
      height: 30px;
      border-radius: 50%;
    }

    &.auxiliary::before {
      content: "";
      position: absolute;
      top: -40px;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 30px;
      height: 30px;
      border-radius: 50%;
      border: 2px dotted #00bcd4;
    }
  }

  .shelfBox {
    position: absolute;
    top: 80px;
    left: 80px;
    right: 80px;
    bottom: 80px;
    border: 3px solid #9e9e9e;
    border-radius: 2px;

    .auxiliary-w {
      position: absolute;
      left: 0;
      top: 25%;
      width: 100%;
      border-top: 3px dotted #ccc;
      text-align: center;
      line-height: 20px;
    }

    .auxiliary-h {
      position: absolute;
      top: 0;
      left: 25%;
      height: 100%;
      border-right: 3px dotted #ccc;

      .auxiliaryContent {
        position: absolute;
        top: 50%;
        transform: translateX(-50%);
        left: 25px;
        // writing-mode: vertical-rl;
      }
    }
  }

  .foot {
    border: 2px solid #9e9e9e;
    width: 60px;
    height: 60px;
    position: absolute;
    border-radius: 5px;

    &.leftTopFoot {
      top: 5px;
      left: 5px;
    }

    &.leftBottomFoot {
      left: 5px;
      bottom: 5px;
    }

    &.rightTopFoot {
      top: 5px;
      right: 5px;
    }

    &.rightBottomFoot {
      bottom: 5px;
      right: 5px;
    }

    .auxiliary-foot-w {
      position: absolute;
      height: 100%;
      left: 100%;
      width: 10px;
      border-right: 3px dotted #9e9e9e;

      .auxiliaryContent {
        position: absolute;
        top: 50%;
        transform: translateX(-50%);
        left: 32px;
        line-height: 0;
      }
    }

    .auxiliary-foot-h {
      position: absolute;
      width: 100%;
      top: 100%;
      height: 10px;
      border-bottom: 3px dotted #9e9e9e;

      .auxiliaryContent {
        text-align: center;
        line-height: 35px;
      }
    }
  }
}

.uploadStyle {
  position: absolute;
  left: 50%;
  transform: translate(-50%, 0);
  bottom: 0;
  font-size: 60px;
  color: #00bcd4;
}
</style>
