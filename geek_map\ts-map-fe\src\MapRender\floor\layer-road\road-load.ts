/* ! <AUTHOR> at 2022/08/27 */
import * as PIX<PERSON> from "pixi.js";

class LayerRoadLoad implements MRender.Layer {
  private floorId: floorId;
  private mapCore: any;
  private fillStyle: any = new PIXI.FillStyle();
  private lineStyle: any = new PIXI.LineStyle();
  private container: PIXI.Container;
  private meshList: Array<any> = [];
  private rendered: boolean = false;
  private arrowSize: number = 3;
  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    const fillStyle = this.fillStyle;
    fillStyle.color = utils.getOriginColor("LOAD_CELL_DIR");
    fillStyle.visible = true;
    fillStyle.alpha = 0.8;
    this.lineStyle.visible = false;

    let container = new PIXI.Container();
    container.name = "load";
    container.zIndex = utils.getLayerZIndex("load");
    container.interactiveChildren = false;
    container.visible = false;
    this.container = container;

    let lineArrowSize = utils.getSettings("lineArrowSize") || 3;
    this.arrowSize = Number(((lineArrowSize / 100) * 8).toFixed(3));

    this.mapCore = mapCore;
    this.floorId = floorId;
  }

  render(): void {
    const mapCore = this.mapCore;
    const segments: mSegment[] = mapCore.mapData.segment.getByFloorId(this.floorId);
    if (!segments.length) return;

    const fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    for (let i = 0, len = Math.ceil(segments.length / fragment); i < len; i++) {
      const arr = segments.slice(i * fragment, i * fragment + fragment);

      const graphicsGeometry = this.resolveLoad(arr);

      if (!graphicsGeometry) continue;

      const graphics = new PIXI.Graphics(graphicsGeometry);
      this.meshList.push(graphics);
      this.container.addChild(graphics);
    }
    this.rendered = true;
  }

  toggle(isShow: boolean): void {
    if (isShow && !this.rendered) {
      this.render();
    }
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];
    this.rendered = false;
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.fillStyle = null;
    this.lineStyle = null;
    this.meshList = null;
    this.mapCore = null;
    this.rendered = null;
    this.floorId = undefined;
  }

  private resolveLoad(arr: Array<mSegment>): any {
    const _this = this;
    const fillStyle = _this.fillStyle,
      lineStyle = _this.lineStyle;

    let sum = 0;
    let graphicsGeometry: any = new PIXI.GraphicsGeometry();
    let lineWidth;

    for (let i = 0, len = arr.length; i < len; i++) {
      const options = arr[i];
      const dirs = options?.loadDirs || 0;
      if (dirs === 0) continue;

      if (!lineWidth) lineWidth = options["lineWidth"];
      const segmentType = options?.segmentType;
      let shapeResults;
      if (segmentType === "S_LINE") {
        shapeResults = _this.formatLineArrow(options, dirs);
      } else if (segmentType === "BEZIER") {
        shapeResults = _this.formatBezierArrow(options, dirs);
      } else continue;

      shapeResults.forEach((result: Array<any>) => {
        ++sum;
        graphicsGeometry.drawShape(new PIXI.Polygon(result), fillStyle, lineStyle);
      });
    }

    if (sum <= 0) return null;
    else {
      graphicsGeometry.BATCHABLE_SIZE = sum;
      return graphicsGeometry;
    }
  }

  private formatLineArrow(options: mSegment, dirs: number): any {
    const _this = this;
    const pos = 0.32;
    const size = _this.arrowSize;
    const [p1, p2] = options["paths"];
    const angle = options["angle"];

    let shapeResults: any = [];
    if (dirs === 1 || dirs === 2) {
      const x = p1.x + (p2.x - p1.x) * Math.abs(1 - pos);
      const y = p1.y + (p2.y - p1.y) * Math.abs(1 - pos);
      const data = _this.getShapeData(x, y, angle + 180, 45, size);

      shapeResults.push(data);
    }

    if (dirs === 2 || dirs === 3) {
      const x = p1.x + (p2.x - p1.x) * pos;
      const y = p1.y + (p2.y - p1.y) * pos;
      const data = _this.getShapeData(x, y, angle, 45, size);

      shapeResults.push(data);
    }
    return shapeResults;
  }

  private formatBezierArrow(options: mSegment, dirs: number): any {
    const _this = this;
    const utils = _this.mapCore.utils;
    const size = _this.arrowSize;
    const points = utils.getCurvePoint(options["paths"], 100);

    let shapeResults: any = [];
    if (dirs === 1 || dirs === 2) {
      const p1 = points(60);
      const p2 = points(59);

      let angle = 0;
      if (p1.x === p2.x) angle = -90;
      else if (p1.y === p2.y) angle = 0;
      else angle = (Math.atan2(p2.y - p1.y, p2.x - p1.x) * 180) / Math.PI;

      const x = p1.x;
      const y = p1.y;
      const data = _this.getShapeData(x, y, angle, 45, size);
      shapeResults.push(data);
    }

    if (dirs === 2 || dirs === 3) {
      const p1 = points(30);
      const p2 = points(31);

      let angle = 0;
      if (p1.x === p2.x) angle = -90;
      else if (p1.y === p2.y) angle = 0;
      else angle = (Math.atan2(p2.y - p1.y, p2.x - p1.x) * 180) / Math.PI;

      const x = p1.x;
      const y = p1.y;
      const data = _this.getShapeData(x, y, angle, 45, size);
      shapeResults.push(data);
    }
    return shapeResults;
  }

  private getShapeData(x: number, y: number, angle: number, theta: number, size: number) {
    const angle1 = ((angle + theta) * Math.PI) / 180;
    const angle2 = ((angle - theta) * Math.PI) / 180;
    const topX = size * Math.cos(angle1);
    const topY = size * Math.sin(angle1);
    const botX = size * Math.cos(angle2);
    const botY = size * Math.sin(angle2);
    return [
      x + topX,
      y + topY,
      x,
      y,
      x + botX,
      y + botY,
      (x + topX + x + botX + x) / 3,
      (y + topY + y + botY + y) / 3,
    ];
  }
}
export default LayerRoadLoad;
