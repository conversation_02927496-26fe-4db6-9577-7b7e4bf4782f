/* ! <AUTHOR> at 2022/08/29 */
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import zh_cn from "antd/es/locale/zh_CN";
import en_us from "antd/es/locale/en_US";
import ja_jp from "antd/es/locale/ja_JP";
import zh_hk from "antd/es/locale/zh_HK";
import zh_tw from "antd/es/locale/zh_TW";
import he_il from "antd/es/locale/he_IL";
import de_de from "antd/es/locale/de_DE";
import nl_be from "antd/es/locale/nl_BE";
import fr_fr from "antd/es/locale/fr_FR";
import ko_kr from "antd/es/locale/ko_KR";
import es_es from "antd/es/locale/es_ES";

const antdLang: any = {
  zh_cn,
  en_us,
  ja_jp,
  zh_hk,
  zh_tw,
  he_il,
  de_de,
  nl_be,
  fr_fr,
  ko_kr,
  es_es,
};
i18n.use(initReactI18next).init({
  lng: "zh_cn",
  interpolation: {
    escapeValue: false,
    prefix: "{",
    suffix: "}",
  },
});

export { i18n };
export default {
  timer: null,
  languages: [],
  getAntdLang(localLang: string = null): any {
    if (!localLang) {
      localLang = _$utils.getLocalLang();
    }
    return antdLang[localLang];
  },
  changeLang() {
    const localLang = _$utils.getLocalLang();
    const languages = this.languages;
    if (languages.includes(localLang)) {
      i18n.changeLanguage(localLang);
      return;
    }

    _$utils.reqLangMsg(localLang).then((res: any) => {
      i18n.addResourceBundle(localLang, "translation", res?.data || {});
      i18n.changeLanguage(localLang);
      this.languages.push(localLang);
    });
  },
};
