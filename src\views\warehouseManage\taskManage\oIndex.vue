<template>
  <geek-main-structure class="task-manage">
    <div class="form-content">
      <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    </div>
    <div class="table-content">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="taskId" :label="$t('lang.rms.fed.taskId')" width="60" />
        <el-table-column prop="taskType" :label="$t('lang.rms.fed.taskType')" width="175" />
        <el-table-column prop="priority" :label="$t('lang.rms.web.monitor.robot.robotPriority')" />
        <el-table-column prop="taskState" :label="$t('lang.rms.web.monitor.robot.taskState')" />
        <el-table-column
          prop="taskSourceType"
          :label="$t('lang.rms.web.monitor.robot.taskSourceType')"
        />
        <el-table-column prop="robotId" :label="$t('lang.rms.fed.robot') + 'ID'" />
        <el-table-column prop="shelfCode" :label="$t('lang.rms.web.monitor.robot.taskShelfCode')" />
        <el-table-column
          prop="startPoint"
          :label="$t('lang.rms.web.monitor.robot.startPoint')"
          width="140"
        />
        <el-table-column
          prop="endPoint"
          :label="$t('lang.rms.web.monitor.robot.endPoint')"
          width="140"
        />
        <el-table-column
          prop="createTime"
          :label="$t('lang.rms.web.monitor.robot.taskCreateTime')"
        />
        <el-table-column
          prop="updateTime"
          :label="$t('lang.rms.web.monitor.robot.taskUpdateTime')"
        />
      </el-table>
      <div style="text-align: right">
        <geek-pagination
          :current-page="page.currentPage"
          :page-size="page.pageSize"
          :total-page="recordCount"
          @currentPageChange="currentPageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
    </div>
  </geek-main-structure>
</template>

<script>
export default {
  name: "TaskManage",
  data() {
    return {
      form: {
        taskId: "",
        taskType: "",
        taskState: "",
        robotId: "",
        shelfCode: "",
        stationId: "",
        chargeStationId: "",
        createTime: "",
        updateTime: "",
      },
      params: {},
      page: {
        pageSize: 10,
        currentPage: 1,
      },
      recordCount: 0,
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          taskId: {
            label: this.$t("lang.rms.fed.taskId"),
            default: "",
            tag: "input",
            placeholder: this.$t("lang.rms.fed.pleaseEnterTaskId"),
          },
          taskType: {
            label: this.$t("lang.rms.fed.taskType"),
            tag: "select",
            default: "",
            placeholder: this.$t("lang.rms.fed.selectTaskType"),
            options: [],
          },
          taskState: {
            label: this.$t("lang.rms.web.monitor.robot.taskState"),
            default: "",
            tag: "select",
            placeholder: this.$t(" lang.rms.fed.selectTaskStatus"),
            options: [],
          },
          robotId: {
            label: this.$t("lang.mb.robotManage.robotId"),
            default: "",
            tag: "input",
            placeholder: this.$t("lang.rms.fed.pleaseEnterTheRobotID"),
          },
          shelfCode: {
            label: this.$t("lang.rms.fed.shelfNumber"),
            default: "",
            tag: "input",
            placeholder: this.$t("lang.rms.fed.pleaseEnterShelfCode"),
          },
          stationId: {
            label: this.$t("lang.rms.fed.stationId"),
            default: "",
            tag: "input",
            placeholder: this.$t("lang.rms.web.station.stationIdPlaceHolder"),
          },
          chargeStationId: {
            label: this.$t("lang.rms.fed.chargerId"),
            default: "",
            tag: "input",
            placeholder: this.$t("lang.rms.fed.pleaseEnterChargerId"),
          },
          createTime: {
            label: this.$t("lang.rms.web.monitor.robot.taskCreateTime"),
            default: "",
            valueFormat: "yyyy-MM-dd",
            type: "daterange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": this.$t("lang.rms.fed.startTime"),
            "end-placeholder": this.$t("lang.rms.fed.endTime"),
            placeholder: this.$t("lang.rms.fed.pleaseChoose"),
          },
          updateTime: {
            label: this.$t("lang.rms.web.monitor.robot.taskUpdateTime"),
            default: "",
            valueFormat: "yyyy-MM-dd",
            type: "daterange",
            tag: "date-picker",
            "range-separator": "-",
            "start-placeholder": this.$t("lang.rms.fed.startTime"),
            "end-placeholder": this.$t("lang.rms.fed.endTime"),
            placeholder: this.$t("lang.rms.fed.pleaseChoose"),
          },
        },
        rules: [],
        operations: [
          {
            label: this.$t("lang.rms.fed.query"),
            handler: "on-query",
            type: "primary",
          },
          {
            label: this.$t("lang.rms.fed.reset"),
            handler: "on-reset",
            type: "default",
          },
        ],
      },
      taskTypeList: [],
      taskStateList: [],
      Createpicker: {
        shortcuts: [
          {
            text: this.$t("lang.rms.fed.tabLastWeek"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: this.$t("lang.rms.fed.tabLastMonth"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: this.$t("lang.rms.fed.tabLastThreeMonths"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      Updatepicker: {
        shortcuts: [
          {
            text: this.$t("lang.rms.fed.tabLastWeek"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: this.$t("lang.rms.fed.tabLastThreeMonths"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: this.$t("lang.rms.fed.tabLastThreeMonths"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      tableData: [],
    };
  },
  activated() {
    $req.get("/athena/task/monitor/querySearchConditionValue").then(res => {
      const taskTypeList = [];
      const taskStateList = [];
      res.data.taskType.forEach(item => {
        taskTypeList.push({
          label: item,
          value: item,
        });
      });
      res.data.taskState.forEach(item => {
        taskStateList.push({
          label: item,
          value: item,
        });
      });

      this.formConfig.configs.taskType.options = taskTypeList;
      this.formConfig.configs.taskState.options = taskStateList;
    });
  },
  methods: {
    // YYYY-MM-DD  00:00:00
    getTargetTime(time) {
      if (!time) {
        return "";
      }
      const start = time[0] + " 00:00:00";
      const end = time[1] + " 23:59:59";
      return [start, end];
    },
    getTableList() {
      var temporaryCreateTime = Array.isArray(this.form.createTime)
        ? this.getTargetTime(this.form.createTime)
        : ["", ""];
      var temporaryUpdateTime = Array.isArray(this.form.updateTime)
        ? this.getTargetTime(this.form.updateTime)
        : ["", ""];
      const data = {
        taskId: this.form.taskId,
        taskType: this.form.taskType,
        taskState: this.form.taskState,
        robotId: this.form.robotId,
        shelfCode: this.form.shelfCode,
        stationId: this.form.stationId,
        chargeStationId: this.form.chargeStationId,
        // createTime: temporaryCreateTime ? temporaryCreateTime.join('-') : '',
        // updateTime: temporaryUpdateTime ? temporaryUpdateTime.join('-') : '',
        createTimeStart: temporaryCreateTime[0],
        createTimeStop: temporaryCreateTime[1],
        updateTimeStart: temporaryUpdateTime[0],
        updateTimeStop: temporaryUpdateTime[1],
        pageSize: this.page.pageSize,
        currentPage: this.page.currentPage,
      };

      $req.get("/athena/task/monitor/conditions", data).then(res => {
        var result = res.data;
        if (result != null) {
          this.tableData = result.recordList;
          this.page.currentPage = result.currentPage || 1;
          this.recordCount = result.pageCount;
        }
      });
    },
    // 分页
    currentPageChange(val) {
      this.page.currentPage = val;
      this.getTableList();
    },
    // 改变每页显示条数
    pageSizeChange(val) {
      this.page.pageSize = val;
      this.getTableList();
    },
    onQuery(val) {
      this.page.currentPage = 1;

      this.form = Object.assign(this.form, val);
      // this.params.createTime = this.params.createTime && this.params.createTime.join("-");
      this.getTableList();
    },
    onReset(val) {
      this.page.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
  },
};
</script>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>

<style lang="less" scoped>
.el-select {
  width: 100%;
}

.table-content {
  margin-top: 15px;
}

.btnwarp {
  text-align: left;
  padding: 43px 0 0;
}

.pageBean {
  text-align: center;
  padding: 20px 0 0;
}

.rowData {
  width: 21%;
  margin-right: 20px;
}

.left {
  float: left;
  margin-right: 20px;
}
</style>
