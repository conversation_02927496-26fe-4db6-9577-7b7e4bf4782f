/* ! <AUTHOR> at 2021/01 */

/**
 * text: 显示文案
 * apiUrl: 接口api请求后缀
 * getElementLang: 获取element_ui lang
 */

export default {
  zh_cn: {
    name: "zh_cn",
    text: "简体中文",
    apiUrl: "zh_cn",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/zh-CN");
    },
    getGeekElementLang() {
      return require("element-geek/locale/lang/zh-CN");
    },
  },
  en_us: {
    name: "en_us",
    text: "English",
    apiUrl: "en_us",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/en");
    },
    getGeekElementLang() {
      return require("element-geek/locale/lang/en");
    },
  },
  ja_jp: {
    name: "ja_jp",
    text: "日本語",
    apiUrl: "ja_jp",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/ja");
    },
    getGeekElementLang() {
      return require("element-geek/locale/lang/ja");
    },
  },
  zh_hk: {
    name: "zh_hk",
    text: "繁体中文(香港)",
    apiUrl: "zh_hk",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/zh-TW");
    },
    getGeekElementLang() {
      return require("element-geek/locale/lang/zh-CN");
    },
  },
  zh_tw: {
    name: "zh_tw",
    text: "繁体中文(台湾地区)",
    apiUrl: "zh_hk",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/zh-TW");
    },
    getGeekElementLang() {
      return require("element-geek/locale/lang/zh-CN");
    },
  },
  he_il: {
    name: "he_il",
    text: "עברית (ישראל)",
    apiUrl: "he_il",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/he");
    },
    getGeekElementLang() {
      return require("element-geek/locale/lang/en");
    },
  },
  de_de: {
    name: "de_de",
    text: "Deutsch",
    apiUrl: "de_de",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/de");
    },
    getGeekElementLang() {
      return require("element-geek/locale/lang/de");
    },
  },
  nl_be: {
    name: "nl_be",
    text: "Nederlands (België)",
    apiUrl: "nl_be",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/nl");
    },
    getGeekElementLang() {
      return require("element-geek/locale/lang/en");
    },
  },
  fr_fr: {
    name: "fr_fr",
    text: "français",
    apiUrl: "fr_fr",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/fr");
    },
    getGeekElementLang() {
      return require("element-geek/locale/lang/en");
    },
  },
  ko_kr: {
    name: "ko_kr",
    text: "한국인",
    apiUrl: "ko_kr",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/ko");
    },
  },
  es_es: {
    name: "es_es",
    text: "Español",
    apiUrl: "es_es",
    getElementLang: () => {
      return require("element-ui/lib/locale/lang/es");
    },
  },
};
