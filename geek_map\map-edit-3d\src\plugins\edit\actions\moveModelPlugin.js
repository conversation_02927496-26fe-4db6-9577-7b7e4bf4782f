import Base from "../../../core/abstractPlugin";
import hoverPluginInstance from "./hoverPlugin";
import selectPluginInstance from "./selectPlugin";

class MoveModelPlugin extends Base {
  constructor(options) {
    super(options);
    this.selectModel = null;
    this.selectData = null;
    this.active = false;
    this.PluginName = "movePlugin";
    this.isAdsorbCell = false; // 设备模型是否自动吸附cell
  }
  activated() {
    this.active = true;
    const transformControl = this.Map3d.transformControl;
    transformControl.addEventListener("mouseDown", this._moveStart.bind(this));
    transformControl.addEventListener("objectChange", this._move.bind(this));
    transformControl.addEventListener("mouseUp", this._moveEnd.bind(this));
  }
  deactivated() {
    this._resetValue();
  }
  destroyed() {
    this.active = false;
    this._resetValue();
  }
  /**
   * cell类型不支持移动， 后续考虑开放
   * @param {*} moveData
   * @returns
   */
  begin(moveData) {
    const { category } = moveData;
    if (category === "cell" || !this.active) return;
    this.selectData = moveData;
    this.selectModel = this.Map3d.modelInstances[this.selectData.uuid];
    this.Map3d.transformControl.attach(this.selectModel.model);
    this.isAdsorbCell = this.selectModel.isAdsorb;
  }
  pause() {
    this.selectModel && this.Map3d.transformControl.detach();
  }
  recovery() {
    this.selectModel && this.Map3d.transformControl.attach(this.selectModel.model);
  }
  off() {
    if (this.selectModel) {
      const transformControl = this.Map3d.transformControl;
      transformControl.detach();
      this.selectModel = null;
      this.selectData = null;
    }
  }
  isMoveStatus(moveData) {
    if (!this.active || !this.selectData) return false;
    return moveData.uuid === this.selectData.uuid;
  }
  _resetValue() {
    const transformControl = this.Map3d.transformControl;
    transformControl.removeEventListener("mouseDown", this._moveStart.bind(this));
    transformControl.removeEventListener("objectChange", this._move.bind(this));
    transformControl.removeEventListener("mouseUp", this._moveEnd.bind(this));
    this.selectModel && transformControl.detach();
    this.active = false;
    this.selectModel = null;
    this.selectData = null;
  }
  _moveStart() {
    this.Map3d.disabledPlugin([hoverPluginInstance.PluginName, selectPluginInstance.PluginName]);
    // 隐藏select选中状态；
    selectPluginInstance.hide();
  }
  _move() {
    if (!this.isAdsorbCell) return;
    const { x, z: y } = this.selectModel.model.position;
    hoverPluginInstance.hoverByPoint({ x, y });
  }
  _moveEnd() {
    // 处理插件间的关系
    hoverPluginInstance.hidden();
    setTimeout(() => {
      this.Map3d.enablePlugin([hoverPluginInstance.PluginName, selectPluginInstance.PluginName]);
    }, 300);
    // 更新附着节点
    const { x, z: y } = this.selectModel.model.position;
    const { category, uuid } = this.selectData;
    const oldValue = this.Store.findModelByUuid(category, uuid);
    // 设备只有location, startBounds 计算有误
    let attrs = { cellCode: "", location: { x, y: -y }, startBounds: { x, y: -y } };
    if (this.isAdsorbCell) {
      const cell = this.Store.findSelectCell({ x, y });
      if (!cell) {
        this.selectModel.model.position.set(...this.selectModel.position);
        this.Emitter.emit("illegal:move");
        return;
      } else {
        attrs = cell;
      }
    }
    let { cellCode, location, startBounds } = attrs;
    const newValue = { ...oldValue, cellCode, location, startBounds };
    this.Map3d.command.exec("update", { oldValue, newValue });
    this.Emitter.emit("after:move", JSON.parse(JSON.stringify([newValue])));
  }
}

export default new MoveModelPlugin();
