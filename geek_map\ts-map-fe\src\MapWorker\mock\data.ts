/* ! <AUTHOR> at 2021/06 */
const Store: any = {};
// todo 以下为demo测试数据 ，在这里保存一份以备不时之需
Store.display.devices = {
  1: {
    deviceInfoId: 1,
    location: {
      x: 21.527,
      y: 3.255,
      z: 1,
    },
    deviceInfoCode: "S1",
    deviceInfoName: "S1",
    state: 0,
    type: 1,
    connectStatus: 0,
    systemStop: 0,
    zone: 1,
    zoneStop: 0,
  },
  2: {
    deviceInfoId: 2,
    location: {
      x: 22.677,
      y: 4.605,
      z: 1,
    },
    deviceInfoCode: "S2",
    deviceInfoName: "S2",
    state: 1,
    type: 1,
    connectStatus: 0,
    systemStop: 0,
    zone: 1,
    zoneStop: 0,
  },
  3: {
    deviceInfoId: 3,
    location: {
      x: 22.677,
      y: 3.255,
      z: 1,
    },
    deviceInfoCode: "S3",
    deviceInfoName: "S3",
    state: 2,
    type: 1,
    connectStatus: 0,
    systemStop: 0,
    zone: 1,
    zoneStop: 0,
  },
};
Store.display.racks = {
  R0000009: {
    rackCode: "R0000009",
    location: { x: 21.527, y: 5.955, z: 1 },
    locationCode: "02950815",
    logicId: 6,
    layers: 5,
    boxNum: 0,
    lattices: [
      {
        latticeCode: "L0000061",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 1,
        height: 220,
        fetchDirs: [0],
        locationCode: "02950815",
        location: {
          x: 7.43,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000062",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 1,
        height: 220,
        fetchDirs: [2],
        locationCode: "02950815",
        location: {
          x: 7.43,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
      {
        latticeCode: "L0000063",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 2,
        height: 670,
        fetchDirs: [0],
        locationCode: "02950815",
        location: {
          x: 7.43,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000064",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 2,
        height: 670,
        fetchDirs: [2],
        locationCode: "02950815",
        location: {
          x: 7.43,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
      {
        latticeCode: "L0000065",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 3,
        height: 1120,
        fetchDirs: [0],
        locationCode: "02950815",
        location: {
          x: 7.43,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000066",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 3,
        height: 1120,
        fetchDirs: [2],
        locationCode: "02950815",
        location: {
          x: 7.43,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
      {
        latticeCode: "L0000067",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 4,
        height: 1570,
        fetchDirs: [0],
        locationCode: "02950815",
        location: {
          x: 7.43,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000068",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 4,
        height: 1570,
        fetchDirs: [2],
        locationCode: "02950815",
        location: {
          x: 7.43,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
    ],
    rackType: 2,
  },
  R0000019: {
    rackCode: "R0000019",
    location: {
      x: 41.128,
      y: 5.955,
      z: 1,
    },
    locationCode: "02550835",
    logicId: 6,
    layers: 5,
    boxNum: 4,
    lattices: [
      {
        latticeCode: "L0000216",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000104",
          boxStatus: "VACANT",
          location: {
            x: 3.549,
            y: 10.57,
            z: 1,
          },
          placeLatticeCode: "L0000216",
          currentLatticeCode: "L0000216",
          locationCode: "02550835",
        },
        layer: 1,
        height: 220,
        fetchDirs: [0],
        locationCode: "02550835",
        location: {
          x: 3.549,
          y: 10.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 0,
      },
      {
        latticeCode: "L0000217",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000117",
          boxStatus: "VACANT",
          location: {
            x: 3.549,
            y: 10.57,
            z: 1,
          },
          placeLatticeCode: "L0000217",
          currentLatticeCode: "L0000217",
          locationCode: "02550835",
        },
        layer: 2,
        height: 670,
        fetchDirs: [0],
        locationCode: "02550835",
        location: {
          x: 3.549,
          y: 10.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 0,
      },
      {
        latticeCode: "L0000218",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000116",
          boxStatus: "VACANT",
          location: {
            x: 3.549,
            y: 10.57,
            z: 1,
          },
          placeLatticeCode: "L0000218",
          currentLatticeCode: "L0000218",
          locationCode: "02550835",
        },
        layer: 3,
        height: 1120,
        fetchDirs: [0],
        locationCode: "02550835",
        location: {
          x: 3.549,
          y: 10.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 0,
      },
      {
        latticeCode: "L0000219",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000110",
          boxStatus: "VACANT",
          location: {
            x: 3.549,
            y: 10.57,
            z: 1,
          },
          placeLatticeCode: "L0000219",
          currentLatticeCode: "L0000219",
          locationCode: "02550835",
        },
        layer: 4,
        height: 1570,
        fetchDirs: [0],
        locationCode: "02550835",
        location: {
          x: 3.549,
          y: 10.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 0,
      },
    ],
    rackType: 1,
  },
  R0000008: {
    rackCode: "R0000008",
    location: {
      x: 42.278,
      y: 5.955,
      z: 1,
    },
    locationCode: "02750815",
    logicId: 6,
    layers: 5,
    boxNum: 0,
    lattices: [
      {
        latticeCode: "L0000051",
        latticeStatus: "VACANT",
        latticeFlag: "LOCKED",
        layer: 1,
        height: 220,
        fetchDirs: [0],
        locationCode: "02750815",
        location: {
          x: 5.33,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000052",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 1,
        height: 220,
        fetchDirs: [2],
        locationCode: "02750815",
        location: {
          x: 5.33,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
      {
        latticeCode: "L0000053",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 2,
        height: 670,
        fetchDirs: [0],
        locationCode: "02750815",
        location: {
          x: 5.33,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000054",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 2,
        height: 670,
        fetchDirs: [2],
        locationCode: "02750815",
        location: {
          x: 5.33,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
      {
        latticeCode: "L0000055",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 3,
        height: 1120,
        fetchDirs: [0],
        locationCode: "02750815",
        location: {
          x: 5.33,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000056",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 3,
        height: 1120,
        fetchDirs: [2],
        locationCode: "02750815",
        location: {
          x: 5.33,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
      {
        latticeCode: "L0000057",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 4,
        height: 1570,
        fetchDirs: [0],
        locationCode: "02750815",
        location: {
          x: 5.33,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000058",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 4,
        height: 1570,
        fetchDirs: [2],
        locationCode: "02750815",
        location: {
          x: 5.33,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
    ],
    rackType: 2,
  },
  R0000018: {
    rackCode: "R0000018",
    location: { x: 43.428, y: 5.955, z: 1 },
    locationCode: "03550825",
    logicId: 6,
    layers: 5,
    boxNum: 7,
    lattices: [
      {
        latticeCode: "L0000147",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000177",
          boxStatus: "VACANT",
          location: {
            x: 13.73,
            y: 10.07,
            z: 1,
          },
          placeLatticeCode: "L0000147",
          currentLatticeCode: "L0000147",
          locationCode: "03550825",
        },
        layer: 4,
        height: 1570,
        fetchDirs: [0],
        locationCode: "03550825",
        location: {
          x: 13.73,
          y: 10.07,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000148",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000111",
          boxStatus: "VACANT",
          location: {
            x: 13.73,
            y: 10.07,
            z: 1,
          },
          placeLatticeCode: "L0000148",
          currentLatticeCode: "L0000148",
          locationCode: "03550825",
        },
        layer: 4,
        height: 1570,
        fetchDirs: [2],
        locationCode: "03550825",
        location: {
          x: 13.73,
          y: 10.07,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
      {
        latticeCode: "L0000141",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000160",
          boxStatus: "VACANT",
          location: {
            x: 13.73,
            y: 10.07,
            z: 1,
          },
          placeLatticeCode: "L0000141",
          currentLatticeCode: "L0000141",
          locationCode: "03550825",
        },
        layer: 1,
        height: 220,
        fetchDirs: [0],
        locationCode: "03550825",
        location: {
          x: 13.73,
          y: 10.07,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000142",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000184",
          boxStatus: "VACANT",
          location: {
            x: 13.73,
            y: 10.07,
            z: 1,
          },
          placeLatticeCode: "L0000142",
          currentLatticeCode: "L0000142",
          locationCode: "03550825",
        },
        layer: 1,
        height: 220,
        fetchDirs: [2],
        locationCode: "03550825",
        location: {
          x: 13.73,
          y: 10.07,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
      {
        latticeCode: "L0000143",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 2,
        height: 670,
        fetchDirs: [0],
        locationCode: "03550825",
        location: {
          x: 13.73,
          y: 10.07,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000144",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000152",
          boxStatus: "VACANT",
          location: {
            x: 13.73,
            y: 10.07,
            z: 1,
          },
          placeLatticeCode: "L0000144",
          currentLatticeCode: "L0000144",
          locationCode: "03550825",
        },
        layer: 2,
        height: 670,
        fetchDirs: [2],
        locationCode: "03550825",
        location: {
          x: 13.73,
          y: 10.07,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
      {
        latticeCode: "L0000145",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000150",
          boxStatus: "VACANT",
          location: {
            x: 13.73,
            y: 10.07,
            z: 1,
          },
          placeLatticeCode: "L0000145",
          currentLatticeCode: "L0000145",
          locationCode: "03550825",
        },
        layer: 3,
        height: 1120,
        fetchDirs: [0],
        locationCode: "03550825",
        location: {
          x: 13.73,
          y: 10.07,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 2,
      },
      {
        latticeCode: "L0000146",
        latticeStatus: "OCCUPIED",
        latticeFlag: "NORMAL",
        relateBox: {
          boxCode: "R000181",
          boxStatus: "VACANT",
          location: {
            x: 13.73,
            y: 10.07,
            z: 1,
          },
          placeLatticeCode: "L0000146",
          currentLatticeCode: "L0000146",
          locationCode: "03550825",
        },
        layer: 3,
        height: 1120,
        fetchDirs: [2],
        locationCode: "03550825",
        location: {
          x: 13.73,
          y: 10.07,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 1,
      },
    ],
    rackType: 2,
  },
  R0000007: {
    rackCode: "R0000007",
    location: { x: 44.578, y: 5.955, z: 1 },
    locationCode: "02550815",
    logicId: 6,
    layers: 5,
    boxNum: 0,
    lattices: [
      {
        latticeCode: "L0000206",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 1,
        height: 220,
        fetchDirs: [0],
        locationCode: "02550815",
        location: {
          x: 3.549,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 0,
      },
      {
        latticeCode: "L0000207",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 2,
        height: 670,
        fetchDirs: [0],
        locationCode: "02550815",
        location: {
          x: 3.549,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 0,
      },
      {
        latticeCode: "L0000208",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 3,
        height: 1120,
        fetchDirs: [0],
        locationCode: "02550815",
        location: {
          x: 3.549,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 0,
      },
      {
        latticeCode: "L0000209",
        latticeStatus: "VACANT",
        latticeFlag: "NORMAL",
        layer: 4,
        height: 1570,
        fetchDirs: [0],
        locationCode: "02550815",
        location: {
          x: 3.549,
          y: 9.57,
          z: 1,
        },
        isOuter: 1,
        relativeIndex: 0,
      },
    ],
    rackType: 1,
  },
};
Store.display.logicAreas = {
  1: {
    logicId: 1,
    logicCode: "1",
    logicName: "1",
    description: "1",
    systemState: "RUNNING",
    systemStateDescription: "lang.rms.system.status.running",
    cellCodes: [1, 2, 3],
    stopRobotSize: 1,
  },
  2: {
    logicId: 2,
    logicCode: "2",
    logicName: "2",
    description: "1",
    systemState: "STOP",
    systemStateDescription: "lang.rms.system.status.stop",
    cellCodes: [1, 2, 3],
    stopRobotSize: 1,
  },
  3: {
    logicId: 3,
    logicCode: "3",
    logicName: "3",
    description: "1",
    systemState: "FIRESTOP",
    systemStateDescription: "lang.rms.system.status.fire.stop",
    cellCodes: [1, 2, 3],
    stopRobotSize: 1,
  },
};
Store.display.trafficAreas = {
  1: {
    floorId: 1,
    areaId: 1,
    areaCode: "Test",
    areaName: "Test",
    boundaryPoints: [
      { x: 0, y: 0 },
      { x: 23.827, y: 7.305 },
      { x: 26.636, y: 15.305 },
    ],
    grantedRobots: [5000], // 已准入机器人列表, 如 [5000, 5010]
    pendingRobots: [5001], // 待准入机器人列表,如 [5000, 5010]
  },
};
Store.display.robots = {
  1191707: {
    errorCode: 0,
    errorCount: 0,
    errorList: [],
    headOffset: 0.5,
    id: 1191707,
    length: 1.09,
    location: { x: 22.677, y: 10.005, z: 1 },
    locationCell: "00450075",
    locationIndex: { x: 0, y: 0, z: 1 },
    onloadShelfCode: "0",
    // onloadRack: '',
    path: [
      { x: 22.677, y: 10.005, z: 1 },
      { x: 23.827, y: 10.005, z: 1 },
      { x: 23.827, y: 8.655, z: 1 },
      { x: 23.827, y: 6.655, z: 1 },
    ],
    posConfirmed: false,
    powerPercent: 90,
    radAngle: 1.5707963705062866,
    robotDisplayState: "NORMAL",
    robotPathMode: "GO_REST",
    robotSeries: "M",
    robotState: "NORMAL",
    robotStopState: "NORMAL",
    robotType: "M1000L",
    tailOffset: 0.5,
    taskId: 129,
    taskType: "GO_SOMEWHERE_TO_STAY",
    warnCount: 0,
    width: 0.83,
  },
  1191706: {
    errorCode: 0,
    errorCount: 0,
    errorList: [],
    headOffset: 0.5,
    id: 1191706,
    length: 1.09,
    location: { x: 44.578, y: 1.905, z: 1 },
    locationCell: "00450075",
    locationIndex: { x: 0, y: 0, z: 1 },
    onloadShelfCode: "0",
    path: [
      { x: 44.578, y: 1.905, z: 1 },
      { x: 44.578, y: 3.255, z: 1 },
      { x: 44.578, y: 5.405, z: 1 },
    ],
    posConfirmed: false,
    powerPercent: 90,
    radAngle: 1.5707963705062866,
    robotDisplayState: "NORMAL",
    robotPathMode: "GO_REST",
    robotSeries: "M",
    robotState: "NORMAL",
    robotStopState: "NORMAL",
    robotType: "M1000L",
    tailOffset: 0.5,
    taskId: 129,
    taskType: "GO_SOMEWHERE_TO_STAY",
    warnCount: 0,
    width: 0.83,
  },
};
Store.display.shelves = {
  A000001: {
    length: 0.88,
    location: { x: 41.128, y: 10.005, z: 1 },
    lockedState: "LOCKED",
    radAngle: 180,
    robotId: 0,
    score: 0,
    shelfCode: "A000001",
    shelfStatus: "IN_WAREHOUSE",
    width: 0.88,
  },
  A000002: {
    length: 0.88,
    location: { x: 0, y: 0, z: 1 },
    lockedState: "NORMAL",
    radAngle: 180,
    robotId: 0,
    score: 20,
    shelfCode: "A000002",
    shelfStatus: "IN_WAREHOUSE",
    width: 0.88,
  },
  A000003: {
    length: 0.88,
    location: { x: 5, y: 5, z: 1 },
    lockedState: "NORMAL",
    radAngle: 180,
    robotId: 0,
    score: 30,
    shelfCode: "A000003",
    shelfStatus: "IN_WAREHOUSE",
    width: 0.88,
  },
};
Store.display.charges = {
  1: {
    chargerId: 1,
    chargerStatus: "NORMAL",
    chargerDir: "NORTH",
    cellCode: "04750325",
    location: {
      x: 160.35,
      y: 51.345,
      z: 1,
    },
    cellLength: 1.2,
    cellWidth: 1,
    startBoundLocation: {
      x: 159.625,
      y: 50.77,
      z: 1,
    },
  },
  2: {
    chargerId: 2,
    chargerStatus: "NORMAL",
    chargerDir: "NORTH",
    cellCode: "05450305",
    location: {
      x: 170.5,
      y: 50.195,
      z: 1,
    },
    cellLength: 1.2,
    cellWidth: 1,
    startBoundLocation: {
      x: 169.775,
      y: 49.62,
      z: 1,
    },
  },
};
Store.display.stations = {
  1: {
    isWorking: true,
    location: { x: 32.445, y: 11.673, z: 1 },
    placeDir: "EAST",
    stationId: 1,
    stopButtonPressed: false,
    totalOperationTime: 0,
  },
  2: {
    isWorking: true,
    location: { x: 30.538, y: 10.525, z: 1 },
    placeDir: "EAST",
    stationId: 2,
    stopButtonPressed: false,
    totalOperationTime: 0,
  },
};
Store.display.knockAreas = [
  {
    logicId: 1,
    logicName: "aaa",
    logicCode: "EAST",
    stopRobotSize: 4,
    cellCodes: [],
    knockAreaApex: [
      {
        x: 16.825,
        y: 29.375,
        z: 1,
      },
      {
        x: 16.825,
        y: 31.375,
        z: 1,
      },
      {
        x: 18.825,
        y: 31.375,
        z: 1,
      },
      {
        x: 18.825,
        y: 29.375,
        z: 1,
      },
    ],
  },
];

Store.display.speedLimitArea = [
  {
    areaId: 1,
    areaName: "aaa",
    areaCode: "EAST",
    areaType: "REAL_TIME_SPEED_LIMIT_AREA",
    areaDesc: "lang.rms.fed.realTimeSpeedLimitArea.active.isFalse",
    active: true,
    floor: 0,
    floorId: 1,
    xxxxxx: [
      {
        x: 16.825,
        y: 29.375,
        z: 1,
      },
      {
        x: 16.825,
        y: 31.375,
        z: 1,
      },
      {
        x: 18.825,
        y: 31.375,
        z: 1,
      },
      {
        x: 18.825,
        y: 29.375,
        z: 1,
      },
    ],
  },
];
