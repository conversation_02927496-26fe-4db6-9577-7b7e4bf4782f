/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { Button, Modal, message, AutoComplete } from "antd";
import { getMap2D, $eventBus, checkBtn } from "../../../../singleton";

import RobotGoTo from "./robot-go-to";
import RobotUpdateFloor from "./robot-update-floor";
import RobotDetail from "./robot-detail";

const { confirm } = Modal;
type PropsOrderData = {
  isCurrent: boolean;
};
function OrderRobot(props: PropsOrderData) {
  const { t } = useTranslation();
  const [robotId, setRobotId] = useState("");
  const [robotData, setRobotData] = useState<robotData>(null);
  const [cellData, setCellData] = useState<cellData>(null);
  const [operation, setOperation] = useState<"goTo" | "updateFloor">(undefined);
  const [robotResults, setRobotResults] = useState([]);

  // 接收wsDataQuery
  useEffect(() => {
    if (!props.isCurrent) return;
    $eventBus.on("wsDataQueryRightTab", wsData => {
      if (!props.isCurrent || !wsData) return;
      const { params, data } = wsData;
      switch (params.layer) {
        case "robot":
          if (data) setRobotData(data);
          else setRobotData(null);
          break;
        case "cell":
          if (data) setCellData(data);
          else setCellData(null);
          break;
      }
    });
    return () => {
      $eventBus.off("wsDataQueryRightTab");
    };
  }, [props.isCurrent]);

  // 地图点击
  useEffect(() => {
    if (!props.isCurrent) return;

    const map2D = getMap2D();
    $eventBus.on("mapClick", (data: MRender.clickParams) => {
      const layer = data?.layer;
      switch (layer) {
        case "robot":
          setRobotData(null);
          setRobotId(data.code.toString());
          map2D.mapWorker.reqQuery({ layer, code: data.code });
          break;
        case "cell":
          const cellData = map2D.mapWorker.getQueryData({ layer, code: data.code });
          setCellData(cellData);
          break;
      }
    });
    return () => {
      $eventBus.off("mapClick");
    };
  }, [props.isCurrent]);

  // 清空数据 地图切换可点击层
  useEffect(() => {
    if (!props.isCurrent) return;
    setOperation(undefined);
    const map2D = getMap2D();
    map2D.mapRender.triggerLayers(["robot"]);
    return () => {
      clearRobot();
    };
  }, [props.isCurrent]);

  const wsRobotCmdReq = (cmd: any) => {
    const map2D = getMap2D();
    const msgType = "RobotInstructionRequestMsg";
    map2D.mapWorker.reqSocket(msgType, cmd).then(res => {
      if (res.msgType !== "RobotInstructionResponseMsg") return;
      _$utils.wsCmdResponse(res?.body || {});
    });
  };

  const controlHandler = (cmd: string) => {
    if (!robotId) {
      const msg = t("lang.rms.api.result.task.robotIdNull"); // 机器人编号不能为空
      message.error(msg);
      return;
    }
    let instruction = "";
    let warningText = "";
    switch (cmd) {
      case "goTo": // 去某处
      case "updateFloor": // 更新楼层
        setOperation(cmd);
        return;
      case "goCharging": // 去充电
        instruction = "GO_CHARGING";
        warningText = t("lang.rms.fed.confirmToCharge");
        break;
      case "addRobot": // 加入
        instruction = "RECOVER_IN_SYSTEM";
        break;
      case "remove": // 移除
        instruction = "REMOVE_FROM_SYSTEM";
        break;
      case "restartRobot": // 重启
        instruction = "RESET";
        break;
      case "unlock": // 解除
        instruction = "UNLOCK";
        break;
    }

    setOperation(undefined);
    if (warningText) {
      confirm({
        icon: <ExclamationCircleOutlined />,
        content: warningText,
        onOk() {
          wsRobotCmdReq({ instruction, robotId: robotId });
        },
      });
    } else {
      wsRobotCmdReq({ instruction, robotId: robotId });
    }
  };

  // 机器人清除
  const clearRobot = () => {
    setRobotId("");
    setRobotData(null);
    setCellData(null);
    setRobotResults([]);
    const map2D = getMap2D();
    map2D.mapWorker.stopQuery();
    map2D.mapRender.clearSelects();
    map2D.mapRender.enableMultiClick(false);
  };

  // 机器人选中
  const robotSelect = (value: string) => {
    if (!value) return;
    setRobotId(value);
    const map2D = getMap2D();
    map2D.mapWorker.reqQuery({ layer: "robot", code: value });
    map2D.mapRender.trigger("click", { robot: [value] });
    map2D.mapRender.setEleCenter({ layer: "robot", code: value });
  };

  // 机器人模糊查询
  const searchResult = (query: string) => {
    if (!props.isCurrent) return;
    if (query.length < 1) setRobotResults([]);
    else {
      const map2D = getMap2D();
      map2D.mapWorker
        .reqSocket("QueryInstructionRequestMsg", {
          instruction: "ROBOT_ID",
          robotId: query,
        })
        .then(res => {
          if (!["QueryInstructionResponseMsg"].includes(res.msgType)) return;
          const data = res.body?.data || [];
          const result = data.map((item: any) => {
            return { value: item.toString() };
          });
          setRobotResults(result);
        });
    }
  };

  return (
    <div
      style={props.isCurrent ? {} : { transform: "translate(-9999px, 0px)", position: "absolute" }}
      className="map2d-order-group-component"
    >
      <AutoComplete
        style={{ width: "100%" }}
        placeholder={t("lang.rms.fed.pleaseEnterTheRobotID")}
        allowClear={true}
        value={robotId}
        options={robotResults}
        onSearch={searchResult}
        onChange={(value: string) => setRobotId(value)}
        onSelect={robotSelect}
        onClear={() => {
          setOperation(undefined);
          clearRobot();
        }}
      />
      <div className="component-btn-group">
        {checkBtn("MonitorRobotGoSomeWhere") && (
          <Button type="primary" block onClick={() => controlHandler("goTo")}>
            {t("lang.rms.fed.goTo")}
          </Button>
        )}
        {checkBtn("MonitorRobotGoCharging") && (
          <Button type="primary" block onClick={() => controlHandler("goCharging")}>
            {t("lang.rms.fed.goCharging")}
          </Button>
        )}
        {checkBtn("MonitorRobotUpdateFloor") && (
          <Button type="primary" block onClick={() => controlHandler("updateFloor")}>
            {t("lang.rms.fed.updateFloor")}
          </Button>
        )}
        {checkBtn("MonitorRobotJoin") && (
          <Button type="primary" block onClick={() => controlHandler("addRobot")}>
            {t("lang.rms.fed.addRobot")}
          </Button>
        )}
        {checkBtn("MonitorRobotRemove") && (
          <Button type="primary" block onClick={() => controlHandler("remove")}>
            {t("lang.rms.fed.remove")}
          </Button>
        )}
        {checkBtn("MonitorRobotRestart") && (
          <Button type="primary" block onClick={() => controlHandler("restartRobot")}>
            {t("lang.rms.fed.restartRobot")}
          </Button>
        )}
        {checkBtn("MonitorRobotUnlock") && (
          <Button type="primary" block onClick={() => controlHandler("unlock")}>
            {t("lang.rms.fed.unlock")}
          </Button>
        )}
      </div>

      <RobotGoTo
        visible={operation === "goTo"}
        robotId={robotId || null}
        cellData={cellData}
        onCancel={() => {
          setOperation(undefined);
          setCellData(null);
        }}
      />

      <RobotUpdateFloor
        visible={operation === "updateFloor"}
        robotId={robotId || null}
        onCancel={() => {
          setOperation(undefined);
          setCellData(null);
        }}
      />

      <RobotDetail robot={robotData} />
    </div>
  );
}

export default OrderRobot;
