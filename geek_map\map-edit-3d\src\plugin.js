import BasePlugin from "./core/abstractPlugin";
import mapDataInstance from "./store/data";
import eventInstance from "./core/behavior";
import mitt from "mitt";

let plugin = {};
let enabledPluginList = [];

class PluginClass {
  constructor(options) {
    eventInstance.init(options.dom);
    this.Emitter = mitt();
    // plugin
    this._plugin = options.plugin || [];
  }

  getPlugin(pluginName) {
    return !pluginName ? Object.values(plugin) : plugin[pluginName];
  }
  // 激活插件
  enablePlugin(pluginName) {
    let nameList = [].concat(pluginName);
    for (let i = 0; i < nameList.length; i++) {
      let name = nameList[i];
      const index = enabledPluginList.findIndex(i => i === name);
      if (!~index) {
        const instant = plugin[name];
        if (instant) {
          enabledPluginList.push(name);
          instant.activated();
        }
      }
    }
  }

  // 插件失效；
  disabledPlugin(pluginName) {
    let nameList = [].concat(pluginName);
    for (let i = 0; i < nameList.length; i++) {
      let name = nameList[i];
      const instant = plugin[name];
      const index = enabledPluginList.findIndex(i => i === name);
      if (!!~index) {
        enabledPluginList.splice(index, 1);
        instant.deactivated();
      }
    }
  }

  // 注册插件
  registerPlugin(pluginObj) {
    const pluginArr = [].concat(pluginObj);
    for (let i = 0; i < pluginArr.length; i++) {
      const instant = pluginArr[i];
      if (!instant.PluginName)
        throw new Error(`>>> [注册Map3D插件] 未提供PluginName,命名规则'{功能/行为....}Plugin'`);
      if (instant instanceof BasePlugin) {
        instant.Map3d = this;
        instant.Store = mapDataInstance;
        instant.EventInstance = eventInstance;
        instant.$dom = this.$dom;
        instant.Emitter = this.Emitter;
        instant.created();
        plugin[instant.PluginName] = instant;
      } else {
        throw new Error(`>>> 注册[Map3D]插件未扩展基类 class ${key}`);
      }
    }
  }

  removePlugins(pluginName) {
    const nameList = [].concat(pluginName);
    for (let i = 0; i < nameList.length; i++) {
      const name = nameList[i];
      const instant = plugin[name];
      const index = enabledPluginList.findIndex(i => i === name);
      enabledPluginList.splice(index, 1);
      instant.destroyed();
      delete plugin[name];
    }
  }
}

export default PluginClass;
