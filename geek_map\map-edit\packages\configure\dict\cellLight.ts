export const LIGHT_MODEL_DICT_LIGHT = "1";
export const LIGHT_MODEL_DICT_EXTINCT = "2";
export const LIGHT_MODEL_DICT_TWINKLE = "3";
export const LIGHT_MODEL_DICT = [
  // 常亮
  {
    label: "lang.rms.api.result.edit.map.light",
    value: LIGHT_MODEL_DICT_LIGHT,
  },
  // 常灭
  {
    label: "lang.rms.api.result.edit.map.extinct",
    value: LIGHT_MODEL_DICT_EXTINCT,
  },
  // 一秒闪烁
  {
    label: "lang.rms.api.result.edit.map.twinkle",
    value: LIGHT_MODEL_DICT_TWINKLE,
  },
];

export const LIGHT_COLOR_DICT_RED = "1";
export const LIGHT_COLOR_DICT_YELLOW = "2";
export const LIGHT_COLOR_DICT_BLUE = "3";
export const LIGHT_COLOR_DICT_GREEN = "4";
export const LIGHT_COLOR_DICT_CYAN = "5";
export const LIGHT_COLOR_DICT_PURPLE = "6";
export const LIGHT_COLOR_DICT_WHITE = "7";

export const LIGHT_COLOR_DICT = [
  // 红色
  {
    label: "lang.rms.api.result.edit.map.red",
    value: LIGHT_COLOR_DICT_RED,
  },
  // 黄色
  {
    label: "lang.rms.api.result.edit.map.yellow",
    value: LIGHT_COLOR_DICT_YELLOW,
  },
  // 蓝色
  {
    label: "lang.rms.api.result.edit.map.blue",
    value: LIGHT_COLOR_DICT_BLUE,
  },
  // 绿色
  {
    label: "lang.rms.api.result.edit.map.green",
    value: LIGHT_COLOR_DICT_GREEN,
  },
  // 青色
  {
    label: "lang.rms.api.result.edit.map.cyan",
    value: LIGHT_COLOR_DICT_CYAN,
  },
  // 紫色
  {
    label: "lang.rms.api.result.edit.map.purple",
    value: LIGHT_COLOR_DICT_PURPLE,
  },
  // 白色
  {
    label: "lang.rms.api.result.edit.map.white",
    value: LIGHT_COLOR_DICT_WHITE,
  },
];
