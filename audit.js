const process = require("child_process");
const { curly } = require("node-libcurl");
const tls = require('tls')
const fs = require('fs')
const path = require('path')

const certFilePath = path.join(__dirname, 'cert.pem')
const tlsData = tls.rootCertificates.join('\n')
fs.writeFileSync(certFilePath, tlsData)

process.exec('npm audit', { encoding: 'utf-8' }, (error, stdout, stderr) => {
  if (stdout) {
    const rulestArr = stdout.split('\n')
    const highSeverityArr = []
    rulestArr.forEach((item, index) => {
      if (item.includes('Severity: high')) {
        highSeverityArr.push([rulestArr[index - 1], rulestArr[index], rulestArr[index + 1]])
      }

      if (item.includes('high severity vulnerabilities')) {
        highSeverityArr.push(rulestArr[index])
      }
    })

    const allRulest = highSeverityArr.pop();

    const codes = highSeverityArr.map(item => {
      if (typeof item === 'string') {
        return '`' + item + '`';
      } else {
        return item.map(item => '`' + item + '`').join('\r\n') + '\r\n---------------------\r\n'
      }
    }).join('\r\n')

    const option = {
      msgtype: 'markdown',
      markdown: {
        content: [
          `<font color="info">执行npm audit结果</font>`,
          codes,
          `<font color="info">${allRulest || '执行异常'}</font>`
        ].join('\r\n')
      }
    }

    curly.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b176e452-7299-4d1e-8993-65c5aeb27f2d', {
      postFields: JSON.stringify(option),
      httpHeader: ['Content-Type: application/json'],
      caInfo: certFilePath,
      verbose: true,
    }).then(({ statusCode, data, headers }) => {
    }).catch((e) => {
      console.log(e);
    });
  }
});
