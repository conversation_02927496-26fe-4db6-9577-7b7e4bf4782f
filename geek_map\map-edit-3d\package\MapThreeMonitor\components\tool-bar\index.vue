<template>
  <div class="tool-bar-content">
    <div class="ctrl-btn" @click="ctrlVisible">
      <i :class="isVisible ? 'el-icon-s-fold' : 'el-icon-s-unfold'" />
    </div>
    <div id="toolBtnList" :class="['btn-list', btnListClass]">
      <el-tooltip
        v-for="(item, index) in btnList"
        v-show="item.isShow"
        :key="index"
        effect="dark"
        :content="$t(item.text)"
        placement="bottom"
      >
        <div :class="['btn-item', item.active ? 'btn-active' : '']" @click="triggerMapEvent(item)">
          <svg-icon :symbolId="item.icon"></svg-icon>
        </div>
      </el-tooltip>
      <div class="floor-select">
        <span class="title">{{ $t("lang.rms.fed.floor") }}</span>
        <el-select
          v-model="currentFloor"
          size="mini"
          :placeholder="$t('lang.rms.fed.pleaseChoose')"
          style="width: 80px"
          @change="changeFloor"
        >
          <el-option v-for="item in floorIds" :key="item" :label="item" :value="item" />
        </el-select>

        <!-- 货架层 过滤选择 -->
        <template v-if="rackLayers && rackLayers.length > 0">
          <el-popover v-model="rackLayerVisible" placement="bottom" width="100" trigger="manual">
            <el-tree
              ref="rackLayerTreeDom"
              :props="props"
              node-key="name"
              :data="rackLayerTreeData"
              :default-checked-keys="rackLayers"
              show-checkbox
              :default-expand-all="true"
              @check="handleCheckChange"
            >
            </el-tree>
            <el-button
              slot="reference"
              size="mini"
              type="primary"
              style="margin-left: 10px"
              @click="rackLayerVisible = !rackLayerVisible"
            >
              {{ $t("lang.rms.fed.choose") + $t("lang.rms.fed.rackShelfLayer") }}
            </el-button>
          </el-popover>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import toolBarList from "./config/toolBarList.js";
import svgIcon from "../svgIcon.vue";

export default {
  name: "ToolBar",
  components: { svgIcon },
  props: ["floorIds", "rackLayers", "defaultCurrentFloor"],
  data() {
    return {
      btnList: [],
      isVisible: true,
      currentFloor: 1,
      props: {
        label: "name",
        children: "children",
      },
      rackLayerVisible: false,
    };
  },
  computed: {
    btnListClass() {
      return this.isVisible ? "btn-list-max-width" : "";
    },
    rackLayerTreeData() {
      let racklayers = this.rackLayers;
      let data = [];
      racklayers.forEach(item => {
        data.push({
          name: item,
        });
      });
      return [{ name: "ALL", children: data }];
    },
  },
  mounted() {
    this.btnList = this.formatToolBar(toolBarList);
    this.currentFloor = this.defaultCurrentFloor;
  },
  destroyed() {
    this.btnList = null;
  },
  methods: {
    ctrlVisible() {
      this.isVisible = !this.isVisible;
    },
    // 触发地图事件
    triggerMapEvent(item) {
      const event = item.event;
      switch (event) {
        case "zoomOut":
        case "zoomIn":
        case "fullScreen":
        case "isShowTopView":
          item.active = false;
          break;
        case "to2D":
        case "isShowRobotPath":
        case "isShowRack":
        case "toggleMapShelfHeat":
        case "isShowLegend":
        case "isShowTaskBox":
        case "isShowLattice":
          item.active = !item.active;
          break;
      }
      this.$emit("handleToolBar", item);
    },

    // 切换楼层
    changeFloor(floor) {
      this.$emit("changeFloor", floor);
    },

    formatToolBar(list) {
      let toolbar = JSON.parse(JSON.stringify(list));
      toolbar.forEach(item => {
        if (item.event === "isShowRack" || item.event === "isShowTaskBox") {
          item.isShow = this.rackLayers !== null;
        }
      });
      return toolbar;
    },

    handleCheckChange(data, checked) {
      const selectData = this.$refs.rackLayerTreeDom.getCheckedKeys();
      const layers = selectData.filter(item => item !== "ALL");
      this.$emit("MapRackLayerChange", { layer: layers });
    },
  },
};
</script>

<style lang="scss" scoped>
.flex-row {
  display: flex;
  flex-direction: row;
}

.btn-active {
  background: #6486f3 !important;
  color: #ffffff;
  font-weight: bold;
}

.btn-style {
  height: 30px;
  width: 30px;
  min-width: 30px;
  border-radius: 3px;
  line-height: 30px;
  text-align: center;
  font-size: 20px;
  cursor: pointer;

  &:hover {
    color: #ffffff;
    font-weight: bold;
    background: #97aef8;
  }
}

.tool-bar-content {
  position: absolute;
  top: 3px;
  left: 3px;
  height: 40px;
  background: #ffffff;
  color: #575b5f;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: center;
  padding: 0 5px;

  .ctrl-btn {
    flex: 0 0 30px;
    margin: 0 5px;
    height: 30px;
    width: 30px;
    min-width: 30px;
    border-radius: 3px;
    line-height: 30px;
    text-align: center;
    font-size: 20px;
    cursor: pointer;

    &:hover {
      color: #ffffff;
      font-weight: bold;
      background: #97aef8;
    }
  }

  .btn-list-max-width {
    width: 100% !important;
  }

  .btn-list {
    overflow: hidden;
    width: 0;
    transition: width 0.3s ease-in-out;
    display: flex;
    flex-direction: row;

    .floor-select {
      border-left: 2px solid #95a0ac;
      padding-left: 10px;
      margin-left: 10px;
      display: flex;
      flex-direction: row;
      .title {
        height: 30px;
        width: auto;
        min-width: 30px;
        line-height: 30px;
        text-align: center;
        font-size: 13px;
        cursor: pointer;
        padding-right: 5px;
        padding-left: 5px;
      }
    }

    .btn-item {
      margin: 0 5px;
      height: 30px;
      width: 30px;
      min-width: 30px;
      border-radius: 3px;
      line-height: 30px;
      text-align: center;
      font-size: 20px;
      cursor: pointer;

      &:hover {
        color: #ffffff;
        font-weight: bold;
        background: #97aef8;
      }
    }
  }
}

.floor-content {
  width: 100px;
  padding: 10px;
}
</style>
