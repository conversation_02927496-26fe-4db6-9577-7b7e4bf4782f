const log4js = require("log4js");
const execSync = require("child_process").execSync; // 同步子进程
const buildConfig = require("../../config/common.conf");

function gitBranch() {
  let logger = log4js.getLogger("git.branch");
  logger.level = "debug";

  try {
    const branch = execSync("git rev-parse --abbrev-ref HEAD").toString().replace(/\n$/g, "");
    const linkPath = buildConfig.linkPath;
    for (let key in linkPath) {
      let p = linkPath[key];
      execSync(`cd ${p} && git pull`).toString();
      execSync(`cd ${p} && git checkout ${branch}`).toString();
      execSync(`cd ${p} && git pull`).toString();
    }

    logger.info("分支切换成功！", branch);
  } catch (e) {
    logger.error(e);
  }
}

gitBranch();
