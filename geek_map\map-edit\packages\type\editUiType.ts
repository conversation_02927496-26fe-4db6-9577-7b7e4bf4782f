/**********************************
 *             按钮组相关           *
 * ********************************/
interface ToolPanelItem {
  icon?: string; // 图标
  name: string;
  title?: string;
  className?: string;
  describe?: string; // 详情
  eventName?: string; // 事件名称, 点击时
  active?: boolean; // 是否选中, 注意, 如果这里的isSelect为false, 且当前没有children数据, 则该参数不生效
  isSelect?: boolean; // 是否是可切换选中状态
  group?: string; // 组, isSelect 为true时生效, 一组中只能有一个选中状态
  /**
   * 禁用相关
   * 注意这里如果返回false, 则始终保持不被禁用
   * 如果要沿用全局禁用相关逻辑请直接调用 return ;
   */
  disabled?(): boolean | undefined;
  data?: { [key: string]: any };
  children?: ToolPanelItem[];
  [key: string]: any;
}

interface ToolPanelConf {
  align?: "top" | "left"; // 组件的展示位置, 仅支持 TOP/LEFT
  effect?: "dark" | "light"; // 弹出层样式
  className?: string; // 附加的className
  defActive?: boolean; // 是否默认选中
  model?: "popper" | "expand"; // 模式, 悬浮或拓展
  border?: boolean; // 当前按钮组是否有border
}

export interface ToolPanelType {
  /**
   * 第一层不需要加 因为第一层没有按钮, 后面必须加, 表示当前按钮的配置
   */
  option?: ToolPanelItem;
  /**
   * 当前按钮组的配置, 如果没有children, 则不生效
   */
  config?: ToolPanelConf;
  /**
   * 当前按钮组的内容
   */
  children?: ToolPanelType[];
  //添加区域特有
  areaOptions?: any
}

/**********************************
 *             图标组相关           *
 * ********************************/
export interface IconConfType {
  icon: string;
  name: string;
  size?: number;
  eventName?: string;
  type?: "hover" | "click" | "focus" | "contextmenu"; // 触发条件, 点击或移入
  innerType?: "descriptions" | "none"; // 目前仅支持了 descriptions
  innerData?: {
    label: string;
    value: string;
  }[];
  color?: string;
}

/**********************************
 *             右侧属性区           *
 * ********************************/
export interface FormItemExPropsType {
  prop?: string;
  span?: number;
  label?: string;
  condition?: boolean;
  onlyComponent?: boolean; // 是否忽略label展示, 为了解决checked的显示效果
  describe?: string; // 描述
  component?: string;
  watch?: Function; // 动态attr
  divider?: boolean; // 是否是分割线
  isTitle?: boolean;
  type?: string;
  pattern?: any;
  [key: string]: any;
}

export interface FromDataType {
  [key: string]: any;
}

export interface NodeAttrEditConf {
  name: string;
  tabTitle: string;
  formItem: FormItemExPropsType[];
  [key: string]: any;
}
