<template>
  <section>
    <geek-customize-form :form-config="formConfig" @on-query="onQuery" @on-reset="onReset" />
    <geek-customize-table
      :table-config="tableConfig"
      :data="tableData"
      :page="tablePage"
      @page-change="pageChange"
      style="margin-top: 10px"
    >
      <template #operation="{ row }">
        <el-button type="primary" size="mini" class="btn-opt" @click="rowDetail(row)">
          {{ $t("lang.rms.fed.textDetails") }}
        </el-button>
        <el-button v-if="row.resendMsg" type="primary" size="mini" class="btn-opt" @click="rowReCall(row)">
          {{ $t("lang.common.retry") }}
        </el-button>
      </template>
    </geek-customize-table>
    <el-dialog
      :title="$t('lang.rms.fed.textDetails')"
      :visible.sync="msgDialog"
      :before-close="closeDialog"
      center
      :append-to-body="true"
      width="50%"
    >
      <el-form label-position="top" label-width="80px" :model="rowData" class="padding_20">
        <el-form-item :label="$t('lang.rms.fed.callbackSuccessChannelIds')">
          <el-input :value="rowData.successChannelIds" :rows="2" readonly="readonly" />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.errorDesc')">
          <el-input :value="rowData.errorDesc" type="textarea" :rows="8" readonly="readonly" />
        </el-form-item>
        <el-form-item :label="$t('lang.rms.fed.msgContent')">
          <el-input :value="rowData.content" type="textarea" :rows="8" readonly="readonly" />
        </el-form-item>
      </el-form>
    </el-dialog>
  </section>
</template>
<script>
export default {
  name: "CallbackMsg",
  data() {
    return {
      // 搜索内容
      form: {
        taskId: "",
        channelId: "",
        channelType: "",
      },
      formConfig: {
        attrs: {
          labelWidth: "100px",
          inline: true,
        },
        configs: {
          taskId: {
            label: "lang.rms.fed.taskId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          channelId: {
            label: "lang.rms.fed.channelId",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
          channelType: {
            label: "lang.rms.fed.channelType",
            default: "",
            tag: "input",
            placeholder: "lang.rms.fed.pleaseEnter",
          },
        },
        operations: [
          {
            label: "lang.rms.fed.query",
            handler: "on-query",
            type: "primary",
          },
          {
            label: "lang.rms.fed.reset",
            handler: "on-reset",
            type: "default",
          },
        ],
      },

      tableData: [],
      tablePage: {
        currentPage: 1,
        pageSize: 10,
        pageCount: 0,
      },
      tableConfig: {
        attrs: { index: true },
        columns: [
          {
            label: "lang.rms.fed.taskId",
            align: "center",
            prop: "taskId",
          },
          {
            label: "lang.rms.fed.channelId",
            align: "center",
            prop: "channelId",
          },
          {
            label: "lang.rms.fed.channelType",
            prop: "channelType",
          },
          {
            label: "lang.rms.fed.clientCode",
            prop: "clientCode",
          },
          {
            label: "lang.rms.fed.warehouseCode",
            prop: "warehouseCode",
          },
          {
            label: "lang.rms.fed.msgType",
            prop: "msgType",
          },
          {
            label: "lang.rms.fed.callbackMsgStatus",
            prop: "status",
            formatter: (row, column, cellValue, index) => {
              const statusList = {
                0: "lang.rms.fed.callbackMsgStatusSending",
                1: "lang.rms.fed.callbackMsgStatusSuccess",
                2: "lang.rms.fed.callbackMsgStatusFail",
                3: "lang.rms.fed.callbackMsgStatusIgnore",
              };
              return this.$t(statusList[cellValue]);
            },
          },
          {
            label: "lang.rms.fed.retryTimes",
            prop: "retryTimes",
          },
          {
            label: "lang.rms.fed.createTime",
            prop: "createTime",
            formatter: (row, column, cellValue, index) => {
              if (!cellValue) return "--";
              return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.fed.updateTime",
            prop: "updateTime",
            formatter: (row, column, cellValue, index) => {
              if (!cellValue) return "--";
              return $utils.Tools.formatDate(cellValue, "yyyy-MM-dd hh:mm:ss");
            },
          },
          {
            label: "lang.rms.fed.listOperation",
            width: "80",
            slotName: "operation",
            "class-name": "operation-btn",
            fixed: "right",
            align: "center",
          },
        ],
      },

      // 当前数据总数
      rowData: {},
      isEdit: false,
      // 报文内容弹框
      msgDialog: false,
      queryTimer: null,
    };
  },
  activated() {
    this.getTableList();
  },
  deactivated() {
    this.queryTimer && clearTimeout(this.queryTimer)
  },
  methods: {
    closeDialog() { 
      this.msgDialog = false;
      this.rowData = {};
    },

    // 编辑
    rowDetail(data) {
      this.rowData = data;
      this.msgDialog = true;
    },
    rowReCall(row) {
      console.log(row);
      const { id } = row;
      const postObj = {
        id,
        onlyCallbackFailedChannel: 1, // 取值范围 0或1，1=只回调失败的通道: 0=回调全部通道: 默认为1
      };
      let config = {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      };
      $req.post("/athena/apiCallback/retrySendCallbackMsg", postObj, config).then((data = {}) => {
        // console.log(data)
        const { code } = data;
        if (code === 0) {
          this.getTableList();
        }
      });
    },
    pageChange(page) {
      this.tablePage = page;
      this.getTableList();
    },
    onQuery(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign(this.form, val);
      this.getTableList();
    },
    onReset(val) {
      this.tablePage.currentPage = 1;
      this.form = Object.assign({}, val);
      this.getTableList();
    },
    getTableList() {
      if (this.queryTimer) {
        clearTimeout(this.queryTimer);
        this.queryTimer = null;
      }

      const { taskId, channelId, channelType } = this.form;
      const { currentPage, pageSize } = this.tablePage;

      let data = { language: $utils.Data.getLocalLang() };
      if (taskId) data.taskId = taskId;
      if (channelId) data.channelId = channelId;
      if (channelType) data.channelType = channelType;
      // 回调消息列表
      $req
        .post(`/athena/apiCallback/callbackMsgPageList?currentPage=${currentPage}&pageSize=${pageSize}`, data)
        .then(res => {
          let result = res?.data;
          if (!result) return;
          this.tableData = result.recordList || [];
          this.tablePage = Object.assign({}, this.tablePage, {
            currentPage: result.currentPage || 1,
            pageCount: result.pageCount || 0,
          });
        });
      
        this.queryTimer = setTimeout(() => {
          this.getTableList();
        }, 2000)
    },
    
  },
};
</script>
<style scoped lang="less">
</style>