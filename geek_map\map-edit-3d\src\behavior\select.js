import Base from "../core/abstractPlugin";
import * as THREE from "three";
import { Line2 } from "three/examples/jsm/lines/Line2.js";
import { LineMaterial } from "three/examples/jsm/lines/LineMaterial.js";
import { LineGeometry } from "three/examples/jsm/lines/LineGeometry.js";
import boxVertex from "../utils/boxVertex";
import { CELL_SPACE, MODEL_GROUP_PREFIX } from "../constant/elements";

const lookForModelGroup = mesh => {
  if (mesh.name.includes(MODEL_GROUP_PREFIX)) {
    return mesh;
  }
  if (mesh.type === "Scene") {
    return null;
  }
  return lookForModelGroup(mesh.parent);
};

class SelectElement extends Base {
  constructor(options) {
    super(options);
    this._selectCallBackArr = [];
    this._unSelectCallBackArr = [];
    this._raycaster = new THREE.Raycaster();
    this._pointer = new THREE.Vector2();
    this._limitName = [];
    this._selectGeo = null;
    this._selectCellGeo = null;
    this.PluginName = "selectPlugin";
    this.selectElementData = [];
    this.isAdsorbCell = false; // 是否自动吸附cell;
  }
  set selectCategory(name) {
    this._limitName = [].concat(name);
  }
  // 禁用操作
  deactivated() {
    this.EventInstance.off("selectElement");
  }
  // 激活操作
  activated() {
    this.EventInstance.add("selectElement", {
      clickHandle: event => this._selectElement(event),
    });
  }
  // 销毁对象
  destoryed() {
    this._limitName = null;
    this._selectCallBackArr = null;
    this._unSelectCallBackArr = null;
    this.selectElementData = null;
  }
  // 选中模型、节点
  triggerSelect(selectVal) {
    const { category } = selectVal || {};
    this.hide();
    if (!selectVal || !this._limitName.includes(category)) {
      this._unSelectCallBackArr.map(i => i(null));
      return;
    }
    if (category === "cell") {
      this._selectCallBackArr.map(i => i([{ ...selectVal, category: "cell" }]));
    } else {
      let params = [selectVal];
      if (this.isAdsorbCell) {
        const selectCell = this.Store.findCellByCellCode(selectVal.cellCode) || null;
        selectCell && (params = Object.assign(params, { ...selectCell, category: "cell" }));
      }
      this._selectCallBackArr.map(i => i(params));
    }
  }
  triggerCancelSelect() {
    this.hide();
    this._unSelectCallBackArr.map(i => i());
  }
  select(fn) {
    this._selectCallBackArr.push(fn);
  }
  unselect(fn) {
    this._unSelectCallBackArr.push(fn);
  }
  hide() {
    if (this._selectGeo) {
      this.Map3d.scene.remove(this._selectGeo);
      this._selectGeo = null;
    }
    if (this._selectCellGeo) {
      this.Map3d.scene.remove(this._selectCellGeo);
      this._selectCellGeo = null;
    }
  }
  renderModelSelect(model) {
    const box = new THREE.Box3();
    box.setFromObject(model);
    const helper = new THREE.Box3Helper(box, 0x67c23a);
    // helper.computeLineDistances();
    helper.name = "select";
    this._selectGeo = helper;
    this.Map3d.scene.add(helper);
  }
  updateModelSelect(modelData) {
    const { uuid } = modelData;
    const isSelect = !!~this.selectElementData.find(i => i.uuid === uuid);
    if (!isSelect) return;
    this.Map3d.scene.remove(this._selectGeo);
    this.renderModelSelect(this.Map3d.modelInstances[uuid].model);
  }
  renderCellSelect(cell) {
    const { width, length, startBounds } = cell;
    // ==== 两种方式画线 1. 可以画粗线  2.只能1像素线
    // 方法一
    const lineGeo = new LineGeometry();
    const vertexs = boxVertex(length * (1 - CELL_SPACE), width * (1 - CELL_SPACE), 0);
    lineGeo.setPositions(vertexs);
    const lineMaterial = new LineMaterial({
      color: 0xff4400,
      linewidth: 2,
    });
    lineMaterial.resolution.set(this.Map3d.$dom.offsetWidth, this.Map3d.$dom.offsetHeight);
    const line = new Line2(lineGeo, lineMaterial);
    line.rotateX(-Math.PI / 2);
    line.position.set(startBounds.x + length / 2, 0.01, -(startBounds.y + width / 2));
    this._selectCellGeo = line;
    this.Map3d.scene.add(line);
    // 方法二
    // const mesh = new THREE.Mesh(
    //   new THREE.PlaneGeometry(width, length),
    //   new THREE.MeshBasicMaterial(0xff0000),
    // );
    // mesh.position.set(startBounds.x + length / 2, 0.01, -(startBounds.y + width / 2));
    // mesh.rotateX(-Math.PI / 2);
    // mesh.visible = false;
    // this.Map3d.scene.add(mesh);
    // const helper3 = new THREE.BoxHelper(mesh, 0xff0000);
    // // helper3.position.set(startBounds.x + length / 2, 0.01, -(startBounds.y + width / 2));
    // // helper3.rotateX(-Math.PI / 2);
    // helper3.computeLineDistances();
    // this.Map3d.scene.add(helper3);
  }
  _selectByCell(point) {
    const { x, z } = point;
    const cell = this.Store.findSelectCell({ x, y: z });
    return cell;
  }
  _selectByModel(mesh) {
    if (!mesh) return false;
    const cell = this.Store.findCellByCellCode(
      this.Map3d.modelInstances[mesh.userData.uuid]._data.cellCode,
    );
    return cell;
  }
  _selectElementNeedAdsorb(intersect) {
    let selectElementArr = [];
    const cell =
      intersect.object.name === "floorBox"
        ? this._selectByCell(intersect.point)
        : this._selectByModel(lookForModelGroup(intersect.object));
    if (!cell) return selectElementArr;
    for (let i = 0; i < this._limitName.length; i++) {
      const category = this._limitName[i];
      if (category === "cell") {
        selectElementArr.push({ ...cell, category });
      } else {
        const arr = this.Store.findModelByCellCode(category, cell.cellCode) || [];
        arr.length &&
          (selectElementArr = selectElementArr.concat(arr.map(i => ({ ...i, category }))));
      }
    }
    return selectElementArr;
  }
  _selectElementForbidAdsorb(intersect) {
    const mesh = lookForModelGroup(intersect.object);
    if (!mesh) return;
    const { category, uuid } = this.Map3d.modelInstances[mesh.userData.uuid]._data;
    if (!this._limitName.includes(category)) return [];
    const data = this.Store.findModelByUuid(category, uuid);
    return [data];
  }
  // select通过原生的射线方式，无法拾取group；
  _selectElement(event) {
    this._pointer.set(
      (event.offsetX / this.$dom.offsetWidth) * 2 - 1,
      -(event.offsetY / this.$dom.offsetHeight) * 2 + 1,
    );
    this._raycaster.setFromCamera(this._pointer, this.Map3d.camera.get());
    const intersects = this._raycaster.intersectObjects(
      [
        this.Map3d.scene.getObjectByName("floorBox"),
        ...Object.values(this.Map3d.modelInstances).map(i => i.model),
      ],
      true,
    );
    // const intersects = this._raycaster.intersectObject(
    //   this.Map3d.scene.getObjectByName("floorBox"),
    //   false,
    // );
    this.hide();
    if (intersects.length) {
      const intersect = intersects[0];
      let selectElementArr =
        intersect.object.name === "floorBox" || this.isAdsorbCell
          ? this._selectElementNeedAdsorb(intersect)
          : this._selectElementForbidAdsorb(intersect);
      if (selectElementArr.length) {
        this.selectElementData = selectElementArr;
        this._selectCallBackArr.map(i => i(selectElementArr));
      } else {
        this._unSelectCallBackArr.map(i => i(intersect.point));
      }
    } else {
      this._unSelectCallBackArr.map(i => i(null));
    }
  }
}

export default SelectElement;
