import PoppickStation from "./stations/PoppickStation";
import { ANGLE } from "../constant/monitor";
class RenderStation {
  constructor(options) {
    this.monitor = options.monitor;
    const Map3d = this.monitor.Map3d;
    this.dispatch = options.dispatch || {
      renderStation() {},
    };
    this.stationInstance = { "station-7": new PoppickStation(Map3d) };
  }
  // create rack
  async create(stations) {
    this.monitor.Emitter.on("after:renderModel", this.__afterRenderStation.bind(this));
    stations.map(i => this.__formatStations(i));
    this.__renderStations(stations);
  }
  destory() {
    this.monitor.Emitter.off("after:renderModel", this.__afterRenderStation.bind(this));
    this.stationInstance = {};
  }
  // update rack
  update(stations) {
    stations.map(i => this.__formatStations(i));
    this.__updateStations(stations);
  }
  // 获取所有可点击的mesh;
  getStationMesh() {
    const { Store } = this.monitor;
    const robotsData = Store.getModelData("STATION").filter(i => i.stationTypeValue === 7);
    return robotsData.reduce((pre, cur) => {
      const station = this.stationInstance[`station-${cur.stationTypeValue}`];
      return station ? pre.concat(station.getStationMesh(cur)) : pre;
    }, []);
  }

  hoverStation(mesh) {
    if (!mesh) return;
    const hover = mesh.getObjectByName("hover");
    hover && (hover.visible = true);
  }
  cancelHoverStation(mesh) {
    if (!mesh) return;
    const hover = mesh.getObjectByName("hover");
    hover && (hover.visible = false);
  }
  selectStation(mesh) {
    if (!mesh) return;
    const select = mesh.getObjectByName("select");
    select && (select.visible = true);
  }
  cancelSelectStation(mesh) {
    if (!mesh) return;
    const select = mesh.getObjectByName("select");
    select && (select.visible = false);
  }

  __formatStations(station) {
    let change = {};
    if (station.stationTypeValue === 7) {
      change.location = station.location3D || station.location;
    } else {
      station.stationTypeValue = 2;
      if (station.placeDir) station.direction = ANGLE[station.placeDir];
    }
    change.radAngle = station.direction * (Math.PI / 180);
    delete station.direction;
    Object.assign(station, change);
  }
  __afterRenderStation({ category, data: models }) {
    if (category !== "STATION" || !models.length) return;
    // P40- 初始化box  RS - 初始化box
    for (let i = 0, v; (v = models[i++]); ) {
      const stationInstance = this.stationInstance[`station-${v.stationTypeValue}`];
      stationInstance && stationInstance.afterRender(v);
    }
    this.dispatch.renderStation && this.dispatch.renderStation();
  }
  __renderStations(stations) {
    if (!stations || !stations.length) return;
    const { Store, Map3d } = this.monitor;
    const stationsData = Store.getModelData("STATION");
    const isInit = !stationsData || !stationsData.length;
    const params = [
      stations,
      {
        category: "STATION",
        useModelName(item) {
          return `STATION-${item.stationTypeValue}`;
        },
      },
    ];
    isInit ? Map3d.initModel(...params) : Map3d.appendModelData(...params);
  }
  __updateStations(stations) {
    const { Store } = this.monitor;
    const nStationIds = stations.map(i => i.stationId);
    const stationsDate = Store.getModelData("STATION");
    if (!stationsDate) return;
    // 工作站场景下没有删除、新增
    for (let i = 0, ov; (ov = stationsDate[i++]); ) {
      const findIndex = nStationIds.findIndex(a => a === ov.stationId);
      if (!~findIndex) continue;
      const nv = stations[findIndex];
      const key = `station-${ov.stationTypeValue}`;
      this.stationInstance[key] && this.stationInstance[key].update(ov, nv);
      Store.updateModelData("STATION", { ...ov, ...nv });
    }
  }
}

export default RenderStation;
