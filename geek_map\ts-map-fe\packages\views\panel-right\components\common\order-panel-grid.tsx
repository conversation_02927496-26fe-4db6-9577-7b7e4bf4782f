import { CSSProperties, ReactNode } from "react";

type PropsGridData = {
  items: Array<{ label: string; node: ReactNode }>;
  style?: CSSProperties;
};
function OrderGrid(props: PropsGridData) {
  return (
    <table className="component-flex-grid" style={props.style}>
      <tbody>
        {props.items.map((item, index) => {
          return (
            <tr key={index}>
              <td className="item-label" colSpan={1}>
                {item.label}
              </td>
              <td className="item-value" colSpan={1}>
                {item.node}
              </td>
            </tr>
          );
        })}
      </tbody>
    </table>
  );
}
export default OrderGrid;
