/* ! <AUTHOR> at 2022/09/06 */
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { Button, message, Modal } from "antd";
import { getMap2D, checkBtn } from "../../../../singleton";

import OrderGrid from "../common/order-grid";

const { confirm } = Modal;
type PropsOrderData = {
  visible: boolean;
  charger: chargerData;
  allowedFullChargeStatus: string;
};
function ChargerCommon(props: PropsOrderData) {
  const { t } = useTranslation();
  const [vacantCount, setVacantCount] = useState("--");
  const [totalCount, setTotalCount] = useState("--");
  const [data, setData] = useState(null);

  // 显隐
  useEffect(() => {
    if (!props.visible) {
      setData(null);
      return;
    }
    getChargerStatistics();
    return () => {
      setData(null);
    };
  }, [props.visible]);

  useEffect(() => {
    if (!props.charger) {
      setData(null);
      return;
    }
    setData(props.charger);
  }, [props.charger]);

  const getChargerStatistics = () => {
    const url = "/athena/equipment/charger/statistics";
    _$utils.reqGet(url).then(res => {
      const data = res?.data || {};
      if (data.hasOwnProperty("totalCount")) setTotalCount(data.totalCount);
      if (data.hasOwnProperty("vacantCount")) setVacantCount(data.vacantCount);
    });
  };

  const allowedFullCharge = () => {
    if (!data?.chargerId) {
      const msg = t("lang.rms.api.result.parameter.chargerIdNull"); // 单元格编号不能为空
      message.error(msg);
      return;
    }
    const url = "/athena/equipment/charger/updateAllowedFullCharge";
    _$utils
      .reqPost(url, {
        chargerId: data?.chargerId,
        chargerState: props.allowedFullChargeStatus === "DISABLED" ? "ENABLED" : "DISABLED",
      })
      .then(res => {
        console.log(res);
        if (res.code === 0) {
          const msg = t(res.msg);
          message.success(msg);
        }
      });
  };

  const controlHandler = (cmd: string) => {
    if (!data?.chargerId) {
      const msg = t("lang.rms.api.result.parameter.chargerIdNull"); // 单元格编号不能为空
      message.error(msg);
      return;
    }

    const map2D = getMap2D();
    let instruction = "";
    switch (cmd) {
      case "disable":
        instruction = "DISABLED";
        break;
      case "enable":
        instruction = "ENABLE";
        break;
      case "restart":
        instruction = "RESTART";
        break;
    }

    let warningText = t("lang.rms.fed.confirmTheOperation");
    confirm({
      icon: <ExclamationCircleOutlined />,
      content: warningText,
      onOk() {
        const reqMsg = "ChargerInstructionRequestMsg";
        const resMsg = "ChargerInstructionResponseMsg";
        map2D.mapWorker.reqSocket(reqMsg, { instruction, chargerId: data?.chargerId }).then(res => {
          if (res.msgType !== resMsg) return;
          _$utils.wsCmdResponse(res?.body || {});
        });
      },
    });
  };

  return (
    props.visible && (
      <>
        <p style={{ color: "#0da0f9", textIndent: 3, fontSize: 14, paddingTop: 8 }}>
          <label>{t("lang.rms.fed.chargerIdle")}</label> <span>{vacantCount}</span>
          <em> / </em>
          <label>{t("lang.rms.fed.chargerTotal")}</label> <span>{totalCount}</span>
        </p>

        <div className="component-btn-group">
          {checkBtn("MonitorChargeDisable") && (
            <Button
              type="primary"
              block
              disabled={data?.managementStatus === "DISABLED"}
              onClick={() => controlHandler("disable")}
            >
              {t("lang.rms.fed.chargerDisable")}
            </Button>
          )}
          {checkBtn("MonitorChargeDisable") && (
            <Button
              type="primary"
              block
              disabled={data?.managementStatus === "ENABLED"}
              onClick={() => controlHandler("enable")}
            >
              {t("lang.rms.fed.chargerEnable")}
            </Button>
          )}
          {checkBtn("MonitorChargeRestart") && (
            <Button type="primary" block onClick={() => controlHandler("restart")}>
              {t("lang.rms.fed.function.restart")}
            </Button>
          )}

          <Button type="primary" block onClick={() => allowedFullCharge()}>
            {props.allowedFullChargeStatus === "DISABLED"
              ? t("lang.rms.monitor.charger.allowedFullCharge")
              : t("lang.rms.monitor.charger.noFullCharge")}
          </Button>
        </div>

        {data && (
          <OrderGrid
            items={[
              {
                label: t("lang.rms.fed.chargerId"),
                value: data?.chargerId || "--",
              },
              {
                label: t("lang.rms.fed.coordinate"),
                value: data?.location || "--",
              },
              {
                label: t("lang.rms.fed.chargerRobotID"),
                value: data?.robotId !== -1 ? data.robotId : "--",
              },
              {
                label: t("lang.rms.fed.chargerManagementStatus"),
                value: data?.managementStatusMsg ? t(data.managementStatusMsg) : "--",
              },
              {
                label: t("lang.rms.fed.chargerConnectionStatus"),
                value: data?.connectionStatus ? t(data.connectionStatus) : "--",
              },
              {
                label: t("lang.rms.fed.chargerWorkingState"),
                value: data?.chargerStatus || "--",
              },
              {
                label: t("lang.rms.fed.chargerAbnormalState"),
                value: data?.errorStatus ? t(data.errorStatus) : "--",
              },
              {
                label: t("lang.rms.fed.chargerTaskID"),
                value: data?.taskId !== -1 ? data.taskId : "--",
              },
              {
                label: t("lang.rms.fed.chargerInteractiveMode"),
                value: data?.interactiveMode ? t(data.interactiveMode) : "--",
              },
              {
                label: t("lang.rms.fed.chargerNodeEncoding"),
                value: data?.cellCode || "--",
              },
              {
                label: t("lang.rms.fed.chargerLostTimeoutOffline"),
                value: data?.offLineTime || "--",
              },
              {
                label: t("lang.rms.fed.chargerAdaptedRobot"),
                value: data?.robotType || "--",
              },
              {
                label: t("lang.rms.fed.chargerRealtimeVoltage"),
                value: data.hasOwnProperty("batteryVoltage") ? data.batteryVoltage : "--",
              },
              {
                label: t("lang.rms.fed.chargerRealtimeCurrent"),
                value: data.hasOwnProperty("batteryDischargeCurrent")
                  ? data.batteryDischargeCurrent
                  : "--",
              },
              {
                label: t("lang.rms.fed.chargerRealtimeTemperature"),
                value: data.hasOwnProperty("batteryTemperature") ? data.batteryTemperature : "--",
              },
              {
                label: t("lang.rms.fed.chargerContactorStatus"),
                value: data?.concatStatus ? t(data.concatStatus) : "--",
              },
              {
                label: t("lang.rms.fed.chargerFanStatus"),
                value: data?.blowerStatus ? t(data.blowerStatus) : "--",
              },
              {
                label: t("lang.rms.fed.chargerSensorStatus"),
                value: data?.sensorStatus ? t(data.sensorStatus) : "--",
              },
              {
                label: t("lang.rms.fed.chargerSoftwareVersion"),
                value: data?.softVersion || "--",
              },
              {
                label: t("lang.rms.fed.chargerChargingTimes"),
                value: data?.chargeCounts || "--",
              },
              {
                label: t("lang.rms.fed.chargerChargesCompleted"),
                value: data?.chargeFinishCounts || "--",
              },
            ]}
          />
        )}
      </>
    )
  );
}

export default ChargerCommon;
