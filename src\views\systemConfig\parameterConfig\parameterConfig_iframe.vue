<template>
  <div class="iframeDiv">
    <iframe class="iframeClass" :src="iframeUrl" frameborder="0" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      language: "",
      iframeUrl: "",
    };
  },
  activated() {
    // http://dev-5259.local.k8s.ops.geekplus.cc
    this.iframeUrl = "/athena/configs/configs.html?lang=" + $utils.Data.getLocalLang();
  },
};
</script>

<style scoped>
.iframeClass {
  width: 100%;
  height: 100%;
}
.iframeDiv {
  width: 100%;
  height: 100%;
}
</style>
