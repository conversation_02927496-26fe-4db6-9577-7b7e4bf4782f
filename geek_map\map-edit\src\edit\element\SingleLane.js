import { Graphics,Sprite,Texture,Rectangle,FillStyle,LineStyle,GraphicsGeometry } from "pixi.js";
import DashLine from "./baseElement/DashLine";
//选中态
import { colorConfig } from "../config";
const { INACTIVE_AREA, ACTIVE_AREA } = colorConfig;
import * as turf from "@turf/turf";
import { cad2pixi, pixi2cad, toFixed } from "../utils/utils";
import {COEFFICIENT} from '../config'
export default class SingleLane {
  //创建
  static add_old(data) {
    const { id, cellDirections } = data;
    if (!cellDirections.length) return null;
    //使用turf buffer 有个限制，如果超过了经纬度，会进行自动折算为（-90 ~ 90  -180 - 180）
    const ratio = 0.04;
    const $g = new Graphics();
    $g.interactive = true;
    $g.interactiveChildren = true;
    $g.cursor = "pointer";
    $g.beginFill(0xfd7070, 0.3);
    const bufferArr = cellDirections.map(item => {
      const { end, start } = item;
      const { locationX: x1, locationY: y1, width: w1, length: l1 } = start;
      const { locationX: x2, locationY: y2, width: w2, length: l2 } = end;
      // console.log(id,start.cellCode,end.cellCode);
      // const pos1 = [x1 - l1 / 2,y1 + w1 / 2].map(p => p * ratio)
      // const pos2 = [x1 + l1 / 2,y1 + w1 / 2].map(p => p * ratio)
      // const pos3 = [x2 - l2 / 2,y2 - w2 / 2].map(p => p * ratio)
      // const pos4 = [x2 + l2 / 2,y2 - w2 / 2].map(p => p * ratio)
      // return turf.polygon([[pos1,pos2,pos3,pos4,pos1]])
      //先缩小
      const lineData = [
        [toFixed(x1 * ratio), toFixed(y1 * ratio)],
        [toFixed(x2 * ratio, 7), toFixed(y2 * ratio, 7)],
      ];
      const line = turf.lineString(lineData);
      const buffer = turf.buffer(line, 40 * ratio, { steps: 4 });
      return buffer;
    });
    let union = bufferArr[0];
    for (let i = 1, len = bufferArr.length; i < len; i++) {
      try {
        union = turf.union(union, bufferArr[i]);
      } catch (e) {
        console.log("完犊子", e, cellDirections[i]);
        union = bufferArr[i];
      }
    }
    union.geometry.coordinates.forEach(arr => {
      const paths = arr.map(item => {
        return cad2pixi({ x: item[0] / ratio, y: item[1] / ratio });
      });
      DashLine.render($g, paths);
      $g.lineStyle({ alpha: 0, width: 1 });
      // $g.beginFill(0xfffff, 0.2)
      $g.drawPolygon(paths);
      $g.endFill();
    });
    $g.type = "area";
    $g.isSingleLane = true;
    $g.id = id;
    return $g;
  }
  static add(data) {
    // const { id, cellDirections } = data;
    // if (!cellDirections?.length) return null;
    const { id, nodeList } = data;
    if (!nodeList?.length) return null;
    //初始化绘制样式
    const fillStyle = new FillStyle()
    const lineStyle = new LineStyle();
    // '#ea4e88'
    fillStyle.color = ACTIVE_AREA
    fillStyle.visible = true;
    fillStyle.alpha = 0.6;
    lineStyle.color = ACTIVE_AREA
    lineStyle.width = 2
    lineStyle.visible = true;
    lineStyle.alpha = 1;
    const len = Object.values(nodeList).length
    const renderShape = (item) => {
      const trans = cad2pixi(item)
      const {x,y,width,length} = trans
      const pixiW = width / COEFFICIENT / 1000
      const pixiL = length / COEFFICIENT / 1000
      const rect = new Rectangle(x - pixiL / 2,y - pixiW / 2,pixiL,pixiW)
      return rect
    }
    const graphicsGeometry =  new GraphicsGeometry();
    nodeList.forEach(item => {
      const {locationX,locationY,width,length} = item
      const obj = {x:locationX,y:locationY,width,length}
      graphicsGeometry.drawShape(renderShape(obj),fillStyle,lineStyle)
    })
    graphicsGeometry.BATCHABLE_SIZE = len
    const $g = new Graphics(graphicsGeometry);
    $g.interactive = true;
    $g.interactiveChildren = true;
    $g.cursor = "pointer";
    $g.type = "area";
    $g.isSingleLane = true;
    $g.id = id;
    return $g;
  }
  //更新
  static update($el, item) {}
}
