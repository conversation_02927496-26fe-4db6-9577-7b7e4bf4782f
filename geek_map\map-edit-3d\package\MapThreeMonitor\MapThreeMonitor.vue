<template>
  <div class="ui-mapEdit3D">
    <div
      class="edit3d-panel"
      v-loading="mapLoading"
      element-loading-text="拼命加载中"
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(255, 255, 255, 0.4)"
    >
      <div class="mian-box">
        <div id="mapBox" ref="mapBox" class="ui-mapEdit3D__box"></div>
        <!-- 操作工具bar -->
        <ToolBar
          v-if="!mapLoading"
          :floor-ids="floorIds"
          :rack-layers="rackLayers"
          :default-current-floor="currentFloor"
          @changeFloor="changeFloor"
          @handleToolBar="handleToolBar"
          @MapRackLayerChange="handlerRackLayerChange"
        />
        <!-- 帮助面板 -->
        <help-panel v-if="isHelpPanelShow" :theme="theme" />
        <right-panel
          v-if="!mapLoading"
          ref="rightPanel"
          :rack-layers="rackLayers"
          :select-info="null"
          :detail-data="detailValue"
          @changeSearchType="handleChangeSearchType"
          @searchSelect="handleSearchSelect"
          @clearSelect="handleClearSelect"
        ></right-panel>
      </div>
    </div>
  </div>
</template>
<script>
import MAPEDIT3D, { MointorPlugin } from "../../src/index";
import ToolBar from "./components/tool-bar/index.vue";
import HelpPanel from "./components/help-panel.vue";
import RightPanel from "./components/right-panel/index.vue";
import "./components/tool-bar/icon/iconfont.js";
let map = null;
// const baseUrl = process.env.NODE_ENV === "production" ? "./static/" : "/";

export default {
  props: {
    baseUrl: {
      type: String,
      required: true,
      default() {
        return "/";
      },
    },
    modelPath: {
      type: Object,
      required: true,
      validator(val) {
        const requireKey = ["P40", "P800", "shelf", "conveyor"];
        const keys = Object.keys(val);
        return !requireKey.some(k => !keys.includes(k));
      },
    },
    token: {
      required: true,
      type: [Boolean, String],
      default() {
        return false;
      },
    },
    theme: Object,
  },
  name: "MapThreeMonitor",
  components: { ToolBar, HelpPanel, RightPanel },
  data() {
    return {
      mapLoading: true,
      isHelpPanelShow: false,
      floorIds: [],
      rackLayers: [],
      curShowRackLayers: [],
      currentFloor: 1,
      detailValue: null,
    };
  },
  mounted() {
    const GeekEditor3DMapConfig = this.modelPath;
    const GeekEditor3dGlobalConfig = this.theme;
    map = new MAPEDIT3D({
      dom: this.$refs.mapBox,
      baseUrl: this.baseUrl,
      config: {
        // 加载模型支持 modelMap，或者调用map.registerModel
        modelMap: {
          P40: {
            path: [GeekEditor3DMapConfig.P40.body, GeekEditor3DMapConfig.P40.plant],
            insetShape: "P40",
          },
          RS: {
            path: [
              GeekEditor3DMapConfig.RS.body,
              GeekEditor3DMapConfig.RS.tray,
              GeekEditor3DMapConfig.RS.lifting,
              GeekEditor3DMapConfig.RS.lattice,
            ],
            insetShape: "RS",
          },
          P800: {
            path: [GeekEditor3DMapConfig.P800.body, GeekEditor3DMapConfig.P800.tray],
            insetShape: "P800",
          },
          SHELF: {
            path: GeekEditor3DMapConfig.shelf,
            scale: 4,
            setAttributes(model) {
              model.rotation.y = 90 * (Math.PI / 180);
              return model;
            },
          },
          charger: {
            path: GeekEditor3DMapConfig.charger,
            scale: 0.1,
          },
          [`STATION-2`]: {
            path: GeekEditor3DMapConfig.monitor,
            insetShape: "station2",
          },
          [`STATION-7`]: {
            path: GeekEditor3DMapConfig.conveyor,
            setAttributes(model) {
              model.position.y = 0.3;
              model.rotation.y = -Math.PI / 2;
              return model;
            },
          },
        },
        constants: {
          theme: {
            ...GeekEditor3dGlobalConfig.MapTheme,
            ...GeekEditor3dGlobalConfig.Theme,
          },
        },
        controlMode: "Map", // Orbit
      },
    });
    // 加载mointor
    map.registerPlugin(new MointorPlugin({ wsUrl: this.getWsUrl() }));
    this.listenEvent();
  },
  destroyed() {
    map && map.desotry();
    map = null;
  },
  methods: {
    listenEvent() {
      // map.Emitter.on("after:initMap", () => (this.mapLoading = false));
      map.Emitter.on("floorIds:ready", floorIds => (this.floorIds = floorIds));
      map.Emitter.on("after:renderRack", data => {
        this.rackLayers = data.layers;
        this.curShowRackLayers = data.layers;
      });
      map.Emitter.on("selected:element", data => {
        this.detailValue = data.data;
      });
      map.Emitter.on("after:renderReady", () => {
        this.mapLoading = false;
        map.enablePlugin("monitorPlugin");
      });
    },
    getWsUrl() {
      let protocol = window.location.protocol === "http:" ? "ws" : "wss";
      let hostname = window.location.host;
      if ($req && $req.isDev) {
        hostname = new URL($req.API_URL).hostname;
      }
      const token = this.token ? `?token=${this.token}` : "";
      return `${protocol}://${hostname}/athena-monitor${token}`;
    },
    // 切换楼层
    changeFloor(floor) {
      this.currentFloor = floor;
    },
    // 切换toolbar
    handleToolBar(item) {
      let { event, active } = item;
      const monitorPlugin = map.getPlugin("monitorPlugin");
      switch (event) {
        case "to2D": // 切换为2D
          this.$emit("redirect2D");
          break;
        case "fullScreen":
          const monitor = document.querySelector("#mapBox");
          this.launchFullScreen(monitor);
          break;
        case "zoomIn":
          monitorPlugin.zoomIn();
          break;
        case "zoomOut":
          monitorPlugin.zoomOut();
          break;
        case "isShowRobotPath":
          monitorPlugin.showRobotPath(item.active);
          break;
        case "toggleMapShelfHeat":
          monitorPlugin.showBoxHot(item.active);
          break;
        case "isShowTaskBox":
          monitorPlugin.showTaskBoxs(item.active);
          break;
        case "isShowLattice":
          monitorPlugin.showRacksLattices(item.active);
          break;
        case "isShowTopView":
          monitorPlugin.showTopView();
          break;
        case "isShowLegend":
          this.isHelpPanelShow = active;
          break;
        default:
          break;
      }
    },
    handlerRackLayerChange({ layer }) {
      const monitor = map.getPlugin("monitorPlugin");
      this.curShowRackLayers = layer;
      monitor.showRackLayers(layer);
      monitor.showRackLatticeLayer(layer);
    },
    handleChangeSearchType(type) {
      const monitor = map.getPlugin("monitorPlugin");
      monitor.changeActionType(type);
    },
    handleSearchSelect(data) {
      const monitor = map.getPlugin("monitorPlugin");
      monitor.select(data);
      if (["box", "lattice"].includes(data.actionType)) {
        const { layer = -1 } = this.detailValue;
        if (!!~layer) {
          monitor.showRackLayers([layer]);
          monitor.showRackLatticeLayer([layer]);
        }
      }
      setTimeout(() => {
        const location = this.detailValue.location;
        if (!location) return;
        map.camera.flyTo([location.x, 0, -location.y], 45);
      }, 500);
      setTimeout(() => {
        monitor && monitor.showRackLayers(this.curShowRackLayers);
        monitor && monitor.showRackLatticeLayer(this.curShowRackLayers);
      }, 3500);
    },
    handleClearSelect(data) {
      const monitor = map.getPlugin("monitorPlugin");
      monitor.clearSelect(data);
    },
    // 全屏
    launchFullScreen(element) {
      if (element.requestFullscreen) {
        element.requestFullscreen();
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.ui-mapEdit3D,
.ui-mapEdit3D__box {
  height: 100%;
  position: relative;
}
.ui-mapEdit3D {
  margin: -12px;
  .header {
    background: #fff;
  }
  .mian-box {
    height: calc(100vh - 46px);
    display: flex;
    position: relative;
    overflow: hidden;
    .ui-mapEdit3D__box {
      flex: 1;
      background: #fff;
    }
    .aside {
      width: 250px;
      background: #fff;
      overflow-x: hidden;
      overflow-y: auto;
      border-left: 1px solid rgb(238, 238, 238);
      box-shadow: -3px 6px 10px 0 rgba(0, 0, 0, 0.1);
      position: relative;
    }
  }
}
.edit3d-panel {
  overflow-y: hidden;
}
</style>
