import {cad2pixi, isHoverNode, pixi2cad, toFixed} from "../utils/utils";
import DeviceEvent from "../event/DeviceEvent";
import LayerManager from "../layerManager/LayerManager";
import Device from './Device'
import Control from "../control/Control";
import {defaultConfig,COEFFICIENT} from '../config'
const {SAFE} = defaultConfig
export default class Safe extends Device {
  static name = 'safe'
  static w = SAFE.width
  static l = SAFE.length
  //给元素绑定事件
  static bindEvent($el){
    const {onDragEnd,onDragStart} = DeviceEvent
    function dragMove(e) {
      const {selected,data} = this
      if(selected && data){
        const p = data.getLocalPosition(this.parent);
        const hoverNode = isHoverNode(p)
        if(hoverNode){
          const {x,y,nodeId,cellCode} = hoverNode
          const elAttr = {x,y,cellCode}
          if(!cellCode) elAttr.mapEditItemId = nodeId
          Object.assign(this,elAttr)
        }else{
          // const {x,y,nodeId,cellCode} = this
          const {x,y} = p
          const elAttr = {x,y,cellCode:null}
          Object.assign(this,elAttr)
        }
      }
    }
    //更新
    const updateSafe = ($el,e) => {
      if(!$el.selected || !$el.dragging){
        Control.enableDrag(true)
        return
      }
      onDragEnd($el,e)
      const {id,cellCode,x,y,width,height,mapEditItemId} = $el
      //将pixi坐标转为cad坐标
      const location = pixi2cad({x:toFixed(x),y:toFixed(y)})
      //计算startBounds
      const realW = width * COEFFICIENT
      const realH = height  * COEFFICIENT
      const startBounds = {x:toFixed(location.x - realW / 2),y:toFixed(location.y - realH / 2)}
      const updateOp = {
        id:'SAFE',
        data:[{id,cellCode,location,startBounds,mapEditItemId:(cellCode ? null : mapEditItemId)}]
      }
      LayerManager.updateElements(updateOp)
    }
    $el
      .on('pointerdown', function(e){
        onDragStart(this,e)
      })
      .on('pointerup', function(e){
        updateSafe(this,e)
      })
      .on('pointerupoutside', function(e){
        updateSafe(this,e)
      })
      .on('pointermove', dragMove)
  }
}
