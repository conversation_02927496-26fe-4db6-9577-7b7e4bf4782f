<template>
  <span class="pwd-eye" :class="{ show: isShow }" @click="showPwd" />
</template>

<script>
export default {
  name: "PasswordEye",
  props: {
    isShow: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    showPwd() {
      this.$emit("changeEye");
    },
  },
};
</script>

<style lang="less" scoped>
.pwd-eye {
  position: absolute;
  right: 0;
  top: 0;
  width: 48px;
  height: 48px;
  z-index: 1;
  cursor: pointer;
  background-image: url(~@imgs/login/icon-eye-invisible.png);
  background-size: 18px;
  background-repeat: no-repeat;
  background-position: 50% 50%;

  &.show {
    background-image: url(~@imgs/login/icon-eye.png);
  }
}
</style>
