/* ! <AUTHOR> at 2022/08/27 */
import * as PIXI from "pixi.js";
class LayerCellElevator implements MRender.Layer {
  private floorId: floorId;
  private utils: any;
  private geometries: Array<any> = [];
  private meshList: Array<any> = [];
  private container: PIXI.Container;
  private fragment: number;
  private elevatorTexture: any;
  init(mapCore: MRender.MainCore, floorId: floorId): void {
    const utils = mapCore.utils;

    let container = new PIXI.Container();
    container.name = "elevator";
    container.zIndex = utils.getLayerZIndex("cell");
    container.interactiveChildren = false;
    container.visible = true;

    this.container = container;
    this.fragment = mapCore.mapConfig.getRenderConfig("fragmentLength");
    this.elevatorTexture = utils.getResources("elevator");
    this.utils = utils;
    this.floorId = floorId;
  }

  render(): void {
    const _this = this;
    const geometries = _this.geometries;
    if (!geometries.length) return;

    const utils = _this.utils;
    const fragment = this.fragment;
    const shader = utils.getShader("icon", this.elevatorTexture);

    for (let i = 0, len = Math.ceil(geometries.length / fragment); i < len; i++) {
      const arr = geometries.slice(i * fragment, i * fragment + fragment);
      let mesh = utils.createMesh(arr, shader);
      mesh.name = "elevator";
      mesh.mapType = "elevator";
      mesh.interactive = mesh.buttonMode = false;
      _this.meshList.push(mesh);
      _this.container.addChild(mesh);
    }
    this.geometries = [];
  }

  drawGeometry(options: mCellData): void {
    const geometry = this.utils.drawGeometry("icon", options["position"]);
    this.geometries.push(geometry);
  }

  toggle(isShow: boolean): void {
    this.container.visible = isShow;
  }

  getContainer(): any {
    return this.container;
  }

  repaint(): void {
    this.meshList.forEach(mesh => mesh.destroy(true));
    this.meshList = [];
    this.geometries = [];
  }

  destroy(): void {
    this.repaint();
    this.container.destroy({ children: true });
    this.container = null;
    this.geometries = null;
    this.meshList = null;
    this.utils = null;
    this.elevatorTexture = null;
    this.floorId = undefined;
  }
}
export default LayerCellElevator;
