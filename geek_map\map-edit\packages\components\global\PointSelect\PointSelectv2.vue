<template>
  <div class="pointSelect">
    <el-select-v2
      :disabled="isSelBtnDisabled"
      v-model="values"
      :options="options"
      placeholder="请选择"
      style="width: 240px"
      :multiple="multiple"
      collapse-tags
      collapse-tags-tooltip
      @visible-change="visibleChange"
      @change="valueChange"
    />
    <el-button :disabled="isSelBtnDisabled" class="button" type="primary" size="mini" @click="handle">
      {{ isSelectModel ? $t("完成") : $t("选择") }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
/**
 * 选点 - 这里是下拉 + 选点的模式, 单选 or 多选
 */
import { ref, Ref, watch, toRefs, nextTick, useAttrs, computed,onMounted } from "vue";
import { useAttrStore } from "@packages/store/attr";
import { ElMessage } from "element-plus";
import { useEditMap } from "@packages/hook/useEdit";
import { addEventListener, removeEventListener } from "@packages/hook/useEvent"

const props = defineProps<{
  // CellCode
  modelValue: string | string[];
  // 多选/单选
  multiple: boolean;
  // 数据列表
  options: { label: string; value: any; nodeId: string; [key:string]: any }[];
  // 禁用
  isMultiplePointDisable: boolean;
  //是否全局可选
  allEnableSelect: boolean | false;
}>();

const editMap = useEditMap();
const attrs = useAttrs();
const propsRef = toRefs(props);
// 是否处于正在选择点位的模式
const isSelectModel = ref(false);
const values: Ref<string | string[]> = ref(getDef());
const attrStore = useAttrStore();
let addOps:any = ref([])
watch(propsRef.modelValue, value => {
  values.value = value;
});
//初始化时执行
onMounted(() => {
  if(props.allEnableSelect){
    const cellCodes = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue]
    const nodeIds = editMap?.value?.getNodeIdsByCellCodes([...cellCodes])
    addOps.value = cellCodes.map((cellCode,index) => {
      return {label: cellCode,nodeId:nodeIds[index],value:cellCode}
    })
  }
})
const emits = defineEmits<{
  (event: "update:modelValue", value: string | string[]): void;
  (event: "selectHandle", value: string | string[]): void;
  (event: "change", value: string | string[]): void;
  (event: "visibleChange", visible: boolean): void;
}>();
//渲染选择状态
const renderSelected = () => {
  if(isSelectModel){
    const nodeIds = editMap?.value?.getNodeIdsByCellCodes(values.value)
    editMap?.value?.fnChoose(nodeIds)
  }
}
function handle() {
  const isSel = !isSelectModel.value;
  if (isSel) {
    ElMessage.success("请点击地图上的点位, 选择完成后点击完成");
    addEventListener('map:Escape', resetStatus);
    attrStore?.setFnChooseMode()
    isSelectModel.value = true;
    const info = attrStore?.selectNodes[0]
    const enableClickedNodeIds = props.options.map(item => item.nodeId)
    editMap?.value?.changeMode({
      action:'FN_CHOOSE',
      options:{
        //是否是多选态
        multiple:props.multiple,
        //当前选中的单元格
        selectedNodeId: info?.nodeId,
        //允许点击的单元格
        enableClickedNodeIds
        // enableClickedNodeIds: []
      }
    })
    //判断是否有选择值
    renderSelected()
  }else{
    const chooseArr = editMap?.value?.fnChooseFinish()
    const cellCodes = chooseArr?.map(item => {
      const {properties} = item
      return properties.cellCode
    })
    values.value = props.multiple ? cellCodes : cellCodes[0];
    emits("update:modelValue", values.value);
    emits("change", values.value);
    nextTick(() => {
      emits("selectHandle", values.value);
    });
    resetStatus();
  }
}

/**
 * 获取默认的value数据
 */
function getDef(): string | string[] {
  const value = props.modelValue;
  if (value) return value;
  return props.multiple ? [] : ''
}

/**
 * 根据cellCode解析出可用的nodeId
 */
function getNodeIds(cellCodes: string | string[]): string[] {
  if (!cellCodes) return [];
  let list = [];
  if (typeof cellCodes === 'string') {
    list.push (cellCodes);
  } else {
    list = cellCodes;
  }
  const dataList = list.map(cellCode => {
    if(!props.allEnableSelect){
      return props.options?.find(item => {
        return item.value === cellCode;
      })
    }else{
      return addOps.value.find(item => {
        return item.value === cellCode;
      })
    }
  }).filter((value: any) => !!value);

  return dataList.map((item: any) => {
    return item.nodeId;
  });
}

function visibleChange(visible: boolean) {
  emits("visibleChange", visible);
}

// 禁用, 如果当前处于地图选点模式, 但是当前组件不处于 则禁用, 以防止不同场景下的问题
const isSelBtnDisabled = computed(() => {
  if (attrStore.isMultipleSelectMode || attrStore.isSingleSelectMode) {
    return !isSelectModel.value;
  } else if (props.isMultiplePointDisable && attrStore.selectNodes.length > 1) {
    return true;
  }
  return false;
});

function resetStatus() {
  isSelectModel.value = false;
  editMap?.value?.resetMode()
  //设置当前编辑点
  editMap?.value?.setSelected({
    layerName: 'CELL',
    id: attrStore?.selectNodes[0].nodeId,
  });
  removeEventListener('map:Escape', resetStatus);
  attrStore?.clearFnChooseMode()
}

function valueChange() {
  renderSelected()
  emits("update:modelValue", values.value);
  emits("change", values.value);
  nextTick(() => {
    emits("selectHandle", values.value);
  });
}
</script>

<style scoped lang="scss">
.pointSelect {
  width: 100%;
  border: 1px Dashed #333;
  padding: 5px;
  border-radius: 1px;
  position: relative;
  box-sizing: border-box;

  .pintItem {
    width: 100%;
    justify-content: space-between;
  }

  .button {
    height: 24px;
    width: 40px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
  }
}
</style>
