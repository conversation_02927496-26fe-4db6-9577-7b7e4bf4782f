import Flatbush from "flatbush";
import * as THREE from "three";

let weakmap = new WeakMap();

// 支持自动生成hitArea,或者自己传
const getHitArea = object => {
  if (object.hitArea) return object.hitArea;
  const { startBounds: sb, width: w, length: l } = object;
  const { x, y } = sb;
  return [x, y, x + l, y + w];
};

const addFastSearch = (data, neighbors = 0.01) => {
  const index = new Flatbush(data.length);
  for (let i = 0, item; (item = data[i++]); ) {
    const [minX, minY, maxX, maxY] = getHitArea(item);
    index.add(minX, minY, maxX, maxY);
  }
  index.finish();
  return function (point) {
    const ids = index.neighbors(point.x, point.y, neighbors, neighbors);
    if (!ids.length) return [];
    return ids.map(i => data[i]);
  };
};

class Search {
  constructor(Map3d) {
    this.Map3d = Map3d;
    this.raycaster = new THREE.Raycaster();
    this.pointer = new THREE.Vector2();
    weakmap = new WeakMap();
  }
  add(object3D, config) {
    const { data, neighbors = 0.01 } = config;
    weakmap.set(object3D, addFastSearch(data, neighbors));
  }
  update(object3D, config) {
    this.add(object3D, config);
  }
  remove(object3D) {}
  get(object3Ds, event) {
    if (!object3Ds || !object3Ds.length) throw new Error("map-edit-3d: search方法object3d必传！");
    this.pointer.set(
      (event.offsetX / this.Map3d.$dom.offsetWidth) * 2 - 1,
      -(event.offsetY / this.Map3d.$dom.offsetHeight) * 2 + 1,
    );
    this.raycaster.setFromCamera(this.pointer, this.Map3d.camera.get());
    const intersects = this.raycaster.intersectObjects([].concat(object3Ds), true);
    if (intersects.length) {
      const { point, object: mesh } = intersects[0];
      const location = { x: point.x, y: -point.z };
      const fastSearch = weakmap.get(mesh);
      if (!fastSearch) return { point: location, mesh };
      return { data: fastSearch(location), mesh, point: location };
    }
    return null;
  }
  destory() {
    weakmap = null;
  }
}

export default Search;
