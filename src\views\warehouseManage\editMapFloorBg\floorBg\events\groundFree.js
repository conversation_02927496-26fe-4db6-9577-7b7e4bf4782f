/* ! <AUTHOR> at 2021/07 */

class groundFree {
  constructor($vm) {
    this.$vm = $vm;

    this.$fabric = null;
    this.options = null;

    this.isDrag = false;
    this.point = { x: 0, y: 0 };
    this.groundColor = "#ffffff";
  }

  init() {
    this.$fabric = this.$vm.$fabric;
    let layer = this.$vm.layers["ground"];
    let actionAttr = this.$vm.actionAttr;
    this.options = {
      layer2d: layer.canvas2d,
      image: layer.image,
      centerPointX: layer.width / 2,
      centerPointY: layer.height / 2,
      boundary: actionAttr.groundBoundary,
      rotate: actionAttr.angle,
    };
  }

  start(e) {
    this.isDrag = true;

    const { layer2d, centerPointX, centerPointY, rotate, boundary } = this.options;
    const { x, y } = e.absolutePointer;

    layer2d.lineWidth = boundary;
    layer2d.fillStyle = this.groundColor;
    layer2d.strokeStyle = this.groundColor;

    // 无轮是如何绘制的 先旋再说
    layer2d.translate(centerPointX, centerPointY);
    layer2d.rotate((-rotate * Math.PI) / 180);
    layer2d.translate(-centerPointX, -centerPointY);

    layer2d.beginPath();
    layer2d.moveTo(x, y);
  }

  move(e) {
    if (!this.isDrag) return;
    const { x, y } = e.absolutePointer;
    this.point = { x, y };
    this.runRepairHandler();
  }

  end(e) {
    this.isDrag = false;
    const { layer2d, centerPointX, centerPointY, rotate } = this.options;
    const { x, y } = e.absolutePointer;
    this.point = { x, y };

    this.runRepairHandler();
    layer2d.closePath();

    // 恢复旋转点
    layer2d.translate(centerPointX, centerPointY);
    layer2d.rotate((rotate * Math.PI) / 180);
    layer2d.translate(-centerPointX, -centerPointY);
  }

  setBoundary(boundary) {
    this.options.boundary = boundary;
  }

  runRepairHandler() {
    const { layer2d, image, boundary } = this.options;
    const { x, y } = this.point;

    // 绘制直线
    layer2d.lineTo(x, y);
    layer2d.stroke();
    layer2d.closePath();

    // 绘制圆心
    layer2d.beginPath();
    layer2d.arc(x, y, boundary / 2, Math.PI * 2, 0, true);
    layer2d.fill();
    layer2d.closePath();

    // 等待下一次绘制
    layer2d.beginPath();
    layer2d.moveTo(x, y);

    image.render(layer2d);
    this.$fabric.requestRenderAll();
  }
}

export default groundFree;
