import * as THREE from "three";
import mapDataInstance from "../store/data";
import Base from "./base";
/**
 *  定制glb模型得特殊处理
 */
class Models extends Base {
  constructor(options) {
    super(options);
    this._object = options.object;
    this._data = options.data;
    this._group = new THREE.Group();
    this._category = options.category;
    this._group.add(this._object);
    this.position = [];
    this._render();
  }
  get model() {
    return this._group;
  }
  update(nv) {
    this._data = nv;
    this._render();
    mapDataInstance.updateModelData(this._category, nv);
  }
  _render() {
    const { angle, cellCode } = this._data;
    const { width, length, startBounds } = mapDataInstance.findCellByCellCode(cellCode);
    const ratio = this.fit(this._group, { width, length });
    // this._gltf.scene.scale.set(ratio, ratio, ratio);
    // this._gltf.scene.rotateX(-Math.PI / 2);
    // this._gltf.scene.rotation.y = (Math.PI / 180) * angle;
    // this._gltf.scene.rotation.x = Math.PI / 2;
    // this._gltf.scene.rotation.x = -Math.PI / 2;
    // this._gltf.scene.position.set(startBounds.x + length / 2, 0, -(startBounds.y + width / 2));
    this._group.scale.set(ratio, ratio, ratio);
    this._group.rotateY((Math.PI / 180) * angle);
    this.position = [startBounds.x + length / 2, 0, -(startBounds.y + width / 2)];
    this._group.position.set(...this.position);
  }
}

export default Models;
