/* ! <AUTHOR> at 2023/01/31 */
const fs = require("fs");
const { resolve } = require("path");
const log4js = require("log4js");

let logger = log4js.getLogger("auto.config");
logger.level = "debug";

const CONF_ENV = process.env.CONF_ENV || ""; // 环境变量
const APP_CONF_PATH = resolve("./config/_conf/app.config.js"); // 自动生成的app config名称地址
const BUILD_CONF_PATH = resolve("./config/_conf/build.config.js"); // 自动生成的webpack config名称地址
const ENV_CONF_PATH = {
  dev: resolve("./config/app.dev.conf.js"),
  pro: resolve("./config/app.pro.conf.js"),
  common: resolve("./config/common.conf.js"),
};
/** ======================= start build config ======================= **/
module.exports = () => {
  if (!CONF_ENV) {
    logger.error(">>>>>>>>>>>>>>请在package.json中设置CONF_ENV环境变量!!!");
    return;
  }
  if (fs.existsSync(APP_CONF_PATH)) fs.unlinkSync(APP_CONF_PATH); // 如果已存在此文件，则删除
  if (fs.existsSync(BUILD_CONF_PATH)) fs.unlinkSync(BUILD_CONF_PATH); // 如果已存在此文件，则删除

  const confPath = ENV_CONF_PATH[CONF_ENV];
  logger.info("开始构建配置文件：", confPath);

  const appConfig = require(confPath);
  fs.writeFileSync(APP_CONF_PATH, `export default ${JSON.stringify(appConfig, null, "\t")}`);
  // logger.log("appConfig::", appConfig);

  const buildConfig = Object.assign(appConfig, require(ENV_CONF_PATH["common"]));
  fs.writeFileSync(BUILD_CONF_PATH, `module.exports = ${JSON.stringify(buildConfig, null, "\t")}`);
  // logger.log("config::", buildConfig);

  logger.info("配置文件构建完成!");
};
/** ======================= end build config ======================= **/
