/* ! <AUTHOR> at 2022/07/11 */
import MapView from "./view";
import MapFloor from "../floor/floor";
import Utils from "../utils/utils";
import MapConfig from "../store/mapConfig/index";
import MapData from "../store/mapData/index";
import MeshData from "../store/meshData/index";
import MapEvent from "../events";

class MapCore implements MRender.MainCore {
  mapView: MapView;
  utils: Utils = new Utils();
  mapData: MapData = new MapData();
  mapConfig: MapConfig = new MapConfig();
  meshData: MeshData = new MeshData();
  mapFloors: { [propName: floorId]: MapFloor } = {};
  mapEvent: MapEvent = new MapEvent(this);
  /** 是否第一次渲染 */
  private _isFirstPaint = false;
  /** 是否请求货架热度 */
  private _isReqShelfHeat = false;
  constructor($dom: HTMLElement) {
    this.mapView = new MapView(this, $dom);
  }

  init(mapReady: MRender.mapReady) {
    const _this = this;
    _this.mapView.loadResources().then((resources: any) => {
      _this.utils.setResources(resources);
      _this.mapEvent.init();

      this.mapView.renderAll();
      mapReady && mapReady();
    });
  }

  renderFloors(floorsData: floorsData): void {
    this.repaint();

    const mapConfig = this.mapConfig;

    for (let key in floorsData) {
      const floorData = floorsData[key];
      if (!floorData) continue;

      const floorId = floorData.floorId;
      const mapFloor = new MapFloor(this, floorData);

      this.mapFloors[floorId] = mapFloor;
    }

    if (!this._isFirstPaint) {
      this._isFirstPaint = true;
      const isSingleFloor = Object.keys(this.mapFloors).length === 1;
      mapConfig.setRenderConfig("isSingleFloor", isSingleFloor);
    }
    this._arrangeFloors();
  }
  renderDisplays(displays: displays): void {
    if (displays.robots) this._renderRobots(displays.robots, false); // 机器人
    if (displays.shelves) this._renderShelves(displays.shelves, false); // 货架
    if (displays.racks) this._renderRacks(displays.racks, false); // rack
    if (displays.stations) this._renderStations(displays.stations, false);
    if (displays.chargers) this._renderChargers(displays.chargers, false);
    if (displays.devices) this._renderDevices(displays.devices, false);
    if (displays.dmpDevices) this._renderDmpDevices(displays.dmpDevices, false);
    if (displays.knockAreas) this._renderKnockAreas(displays.knockAreas, false); // 机器人相撞区域
    if (displays.realtimeObstacles) {
      this._renderRealtimeObstacles(displays.realtimeObstacles, false); // 外部障碍物
    }

    this.mapView.renderAll();
  }
  updateDisplays(displays: displays): void {
    // update时 固定货架即rack无需update 因此没有rack更新
    if (displays.cells) this._updateCellFlags(displays.cells); // 机器人
    if (displays.robots) this._renderRobots(displays.robots); // 机器人
    if (displays.shelves) this._renderShelves(displays.shelves); // 货架
    else if (this._isReqShelfHeat) {
      this._renderShelves("shelfHeat");
      this._isReqShelfHeat = false;
    }
    if (displays.racks) this._renderRacks(displays.racks); // rack
    if (displays.chargers) this._renderChargers(displays.chargers);
    if (displays.devices) this._renderDevices(displays.devices); // 设备
    if (displays.dmpDevices) this._renderDmpDevices(displays.dmpDevices);
    if (displays.stations) this._renderStations(displays.stations); // 设备
    if (displays.hasOwnProperty("knockAreas")) this._renderKnockAreas(displays.knockAreas); // 机器人相撞区域
    if (displays.realtimeObstacles) this._renderRealtimeObstacles(displays.realtimeObstacles); // 外部障碍物

    this.mapView.renderAll();
  }

  toggleLayer(
    layerName: MRender.toggleLayerName,
    isShow: boolean,
    data?: shelfHeatApiData | Array<code>,
  ): void {
    if (!layerName) return;

    const mapConfig = this.mapConfig;
    switch (layerName) {
      case "location":
        mapConfig.setLayerVisible("cellLocation", isShow);
        this._toggleLayers("location");
        return;
      case "load":
        mapConfig.setLayerVisible("mapLoad", isShow);
        this._toggleLayers("load");
        return;
      case "unload":
        mapConfig.setLayerVisible("mapUnload", isShow);
        this._toggleLayers("unload");
        return;
      case "cellHeat":
        mapConfig.setLayerVisible("cellHeat", isShow);
        this._toggleLayers("cellHeat");
        return;
      case "realtimeObstacle":
        mapConfig.setLayerVisible("realtimeObstacle", isShow);
        this._toggleLayers("realtimeObstacle");
        return;
      case "shelfHeat":
        if (isShow) {
          mapConfig.data.setShelfHeatData(data as shelfHeatApiData);
        }
        mapConfig.setLayerVisible("shelfHeat", isShow);
        this._isReqShelfHeat = true;
        return;
      case "robotTrail":
        mapConfig.setLayerVisible("robotTrail", isShow);
        return;
      case "robotOccupy":
        mapConfig.setLayerVisible("robotOccupy", isShow);
        return;

      case "stopArea":
        this.mapData.stopArea.toggleStopArea(data as Array<code>);
        return;
      case "speedLimitArea":
        this.mapData.speedLimitArea.toggleSpeedLimitArea(data as Array<code>);
        return;
    }
  }
  triggerLayers(layerNames: MRender.layerName[]): void {
    layerNames = layerNames || [];
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      mapFloor.triggerLayers(layerNames);
    }
  }

  renderCellColor(params: MRender.mapColorParam) {
    const { clear, color, codes } = params;

    let floorCells: { [propName: floorId]: mCellData[] };
    if (codes && codes.length) {
      floorCells = this.mapData.cell.getFloorDataByCodes(codes);
    }

    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;

      if (clear) {
        // 清除color
        mapFloor.layerCell.layerCellFeature.repaint("color");
      } else {
        if (!floorCells || !floorCells[floorId]) continue;
        mapFloor.layerCell.layerCellFeature.render(floorCells[floorId], color);
      }
    }
  }

  renderCellFilter(params: MRender.mapSearchFilterParam) {
    const { clear, type, floorCells } = params;
    const mapFloors = this.mapFloors;
    console.log("renderCellFilter::type", type);
    let color = 0xf76965;
    if (type === "CELL_SIZE_TYPE") color = 0xdddddd;

    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      if (clear) {
        // 清除color
        mapFloor.layerCell.layerCellFeature.repaint("filter");
        continue;
      }
      if (!floorCells || !floorCells[floorId]) continue;
      mapFloor.layerCell.layerCellFeature.renderFilter(floorCells[floorId], color);
    }
  }
  renderCellText(isRender: boolean, params?: MRender.mapCellTextParam) {
    const mapFloors = this.mapFloors;
    const codes = Object.keys(params);
    let floorCells: { [propName: floorId]: mCellData[] };
    if (codes && codes.length) {
      floorCells = this.mapData.cell.getFloorDataByCodes(codes);
    }

    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      if (!isRender) {
        // 清除color
        mapFloor.layerCell.layerCellText.repaint();
        continue;
      }
      if (!floorCells || !floorCells[floorId]) continue;
      mapFloor.layerCell.layerCellText.render(floorCells[floorId], params);
    }
  }

  renderShelfColor(params: MRender.mapColorParam) {
    //TODO
    console.log(111111, "renderShelfColor TODO");
  }

  renderAreaFilter(params: MRender.mapSearchFilterParam) {
    const { clear, areaData } = params;

    let formatData: any = {};
    if (clear) {
      this.mapData.stopArea.repaint();
      this.mapData.speedLimitArea.repaint();
    } else {
      let area, floorId;
      for (let i = 0, len = areaData.length; i < len; i++) {
        area = areaData[i];
        if (!area) continue;
        floorId = area.floorId;
        if (!formatData[floorId]) formatData[floorId] = [];
        formatData[floorId].push(area);
      }
    }

    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      if (clear) {
        // 清除color
        mapFloor.layerAreas.repaint("filterAreas");
        continue;
      }
      mapFloor.layerAreas.renderAreas("filterAreas", formatData[floorId] || []);
    }
  }

  /** 渲染急停区域（逻辑区） */
  renderAreaStop(areaData: Array<any>, isInit = true) {
    if (isInit) {
      let formatData: any = {};
      let area, floorId;
      for (let i = 0, len = areaData.length; i < len; i++) {
        area = areaData[i];
        if (!area) continue;
        floorId = area.floorId;
        if (!formatData[floorId]) formatData[floorId] = [];
        formatData[floorId].push(area);
      }
      const mapFloors = this.mapFloors;
      let mapFloor: MapFloor;
      for (let floorId in mapFloors) {
        mapFloor = mapFloors[floorId];
        if (!mapFloor) continue;

        mapFloor.layerAreas.renderAreas("stopAreas", formatData[floorId] || []);
      }
    } else {
      let areaIds = [];
      for (let i = 0, len = areaData.length; i < len; i++) {
        const area = areaData[i];
        if (!area) continue;
        areaIds.push(area.areaId);
      }
      this.mapData.stopArea.showStopArea(areaIds);
    }
  }

  /** 渲染限速区  */
  renderAreaSpeedLimit(areaData: Array<any>, isInit = true) {
    // debugger;
    if (isInit) {
      let formatData: any = {};
      let area, floorId;
      for (let i = 0, len = areaData.length; i < len; i++) {
        area = areaData[i];
        if (!area) continue;
        floorId = area.floorId;
        if (!formatData[floorId]) formatData[floorId] = [];
        formatData[floorId].push(area);
      }
      const mapFloors = this.mapFloors;
      let mapFloor: MapFloor;
      for (let floorId in mapFloors) {
        mapFloor = mapFloors[floorId];
        if (!mapFloor) continue;
        mapFloor.layerAreas.renderAreas("speedLimitAreas", formatData[floorId] || []);
      }
    } else {
      let areaIds = [];
      for (let i = 0, len = areaData.length; i < len; i++) {
        const area = areaData[i];
        if (!area) continue;
        areaIds.push(area.areaId);
      }
      this.mapData.speedLimitArea.showSpeedLimitArea(areaIds);
    }
  }

  repaint(): void {
    this.mapConfig.uninstall();
    this.mapData.uninstall();
    this.meshData.uninstall();
    this.mapEvent.uninstall();
    this.mapView.renderAll();

    for (let key in this.mapFloors) {
      const mapFloor: any = this.mapFloors[key];
      if (!mapFloor) continue;
      mapFloor.destroy();
    }
    this.mapFloors = {};
    this._isReqShelfHeat = false;
  }

  destroy(): void {
    this.repaint();
    this.mapView.destroy();
    this.mapView = null;
    this.mapEvent.destroy();
    this.mapEvent = null;
    this.mapConfig.destroy();
    this.mapConfig = null;
    this.mapData.destroy();
    this.mapData = null;
    this.meshData.destroy();
    this.meshData = null;
    this.utils.destroy();
    this.utils = null;
    this.mapFloors = null;
    this._isFirstPaint = null;
    this._isReqShelfHeat = null;
  }

  /** 更新单元格 */
  private _updateCellFlags(cells: { [propName: floorId]: Array<cellData> }, isUpdate = true) {
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      if (!cells[floorId]) continue;

      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      mapFloor.renderCells(cells[floorId], isUpdate);
    }
  }
  /** 渲染机器人 */
  private _renderRobots(robots: { [propName: floorId]: Array<robotData> }, isUpdate = true) {
    const mapFloors = this.mapFloors;

    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      mapFloor.renderRobots(robots[floorId], isUpdate);
    }
  }
  /** 渲染货架 */
  private _renderShelves(
    shelves: { [propName: floorId]: Array<shelfData> } | "shelfHeat",
    isUpdate = true,
  ) {
    const mapFloors = this.mapFloors;
    const isHeat = this.mapConfig.getLayerVisible("shelfHeat") as boolean; // 是否显示货架热度

    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;

      if (shelves === "shelfHeat") {
        const shelves: mShelfData[] = this.mapData.shelf.getByFloorId(floorId);
        mapFloor.layerShelf.renderHeat(isHeat, shelves);
      } else {
        mapFloor.renderShelves(shelves[floorId], isUpdate);
      }
    }
  }
  /** 渲染固定货架 */
  private _renderRacks(racks: { [propName: floorId]: Array<rackData> }, isUpdate = true) {
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      mapFloor.renderRacks(racks[floorId], isUpdate);
    }
  }
  /** 渲染工作站 */
  private _renderStations(stations: { [propName: floorId]: Array<stationData> }, isUpdate = true) {
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      mapFloor.renderStations(stations[floorId], isUpdate);
    }
  }
  /** 渲染充电站 */
  private _renderChargers(chargers: { [propName: floorId]: Array<chargerData> }, isUpdate = true) {
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      mapFloor.renderChargers(chargers[floorId], isUpdate);
    }
  }
  /** 渲染设备 */
  private _renderDevices(devices: { [propName: floorId]: Array<deviceData> }, isUpdate = true) {
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      mapFloor.renderDevices(devices[floorId], isUpdate);
    }
  }
  private _renderDmpDevices(dmpDevices: { [propName: floorId]: Array<any> }, isUpdate = true) {
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      mapFloor.renderDmpDevices(dmpDevices[floorId], isUpdate);
    }
  }
  /** 渲染机器人防撞区域 */
  private _renderKnockAreas(
    knockAreas: { [propName: floorId]: Array<knockAreaData> },
    isUpdate = true,
  ) {
    knockAreas = knockAreas || {};
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor, floorAreas: Array<knockAreaData>;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;

      floorAreas = knockAreas[floorId] || [];
      if (isUpdate) mapFloor.layerAreas.updateAreas("knockAreas", floorAreas);
      else mapFloor.layerAreas.renderAreas("knockAreas", floorAreas);
    }
  }
  /** 渲染外部障碍物 */
  private _renderRealtimeObstacles(
    realtimeObstacles: { [propName: floorId]: Array<realtimeObstacleData> },
    isUpdate = true,
  ) {
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor, floorAreas: Array<realtimeObstacleData>;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;

      floorAreas = realtimeObstacles[floorId] || [];
      if (isUpdate) mapFloor.layerAreas.renderAreas("realtimeObstacles", floorAreas);
      else mapFloor.layerAreas.renderAreas("realtimeObstacles", floorAreas);
    }
  }

  private _toggleLayers(layerName: MRender.toggleLayerName) {
    const mapFloors = this.mapFloors;
    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      mapFloor.toggleLayer(layerName);
    }
  }

  /** 排列地图楼层 */
  private _arrangeFloors() {
    const mapFloors = this.mapFloors;
    const mapConfig = this.mapConfig;
    const isSingleFloor = mapConfig.getRenderConfig("isSingleFloor");
    if (isSingleFloor) {
      this._resolveSingleFloor(mapFloors);
      return;
    }

    const floorSpace = this.utils.getSettings("floorSpace");

    let offsetX = 0;
    let offsetY = 0;
    let mapFloor: MapFloor;

    let config: any = {};
    let RMSConfig = localStorage.getItem("Geek_RMSConfig");
    if (RMSConfig) config = JSON.parse(RMSConfig);
    let mapAngle = config?.mapRotateAngle || 0;
    if (mapAngle <= -180) mapAngle = mapAngle + 360;
    else if (mapAngle > 180) mapAngle = mapAngle - 360;

    // 归一化角度
    let angle = 0;
    if (mapAngle >= -135 && mapAngle < -45) {
      angle = -90; // 下南
    } else if (mapAngle >= -45 && mapAngle < 45) {
      angle = 0; // 右东
    } else if (mapAngle >= 45 && mapAngle < 135) {
      angle = 90; // 上北
    } else {
      angle = 180; // 默认左西
    }

    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;
      const layerFloor = mapFloor.getLayerFloor();
      const { x, y, width, height } = layerFloor.getLocalBounds(); //
      console.log("x: " + x + "  \n y: " + y + "  \n width: " + width + "  \n height: " + height);

      switch (angle) {
        case 90:
          layerFloor.position.set(offsetX - y, offsetY - width - x);
          offsetX = offsetX + height + floorSpace;
          break;
        case 180:
          layerFloor.position.set(offsetX - x, offsetY + y);
          offsetX = offsetX + width + floorSpace;
          break;
        case -90:
          layerFloor.position.set(offsetX - y, offsetY + x);
          offsetX = offsetX + height + floorSpace;
          break;
        default:
          layerFloor.position.set(offsetX - x, offsetY - height - y);
          offsetX = offsetX + width + floorSpace;
      }
    }
    this.mapView.setMapCenter();
  }

  private _resolveSingleFloor(mapFloors: { [propName: floorId]: MapFloor }) {
    const angle = this.mapConfig.getRenderConfig("mapAngle");

    let mapFloor: MapFloor;
    for (let floorId in mapFloors) {
      mapFloor = mapFloors[floorId];
      if (!mapFloor) continue;

      const layerFloor = mapFloor.getLayerFloor();
      if (angle !== 0) {
        layerFloor.angle = angle; // 设置角度
      }
    }

    this.mapView.setMapCenter();
  }
}

export default MapCore;
